package com.cet.eem.bll.datatransport.model.mesdata;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.bll.datatransport.model.ModelDef;
import com.cet.eem.model.model.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import java.time.LocalDateTime;

@Getter
@Setter
@ModelLabel(ModelDef.PASS_POINT_INFO)
public class MesPassPointVo extends BaseEntity {
    public LocalDateTime time;
    public String skidno;
    public String stationcode;
    public String vin;
    public Long passtime;

    public MesPassPointVo(){
        super.modelLabel = ModelDef.PASS_POINT_INFO;
    }
}

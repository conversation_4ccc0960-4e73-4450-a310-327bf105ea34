package com.cet.eem.compressorservice.model.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * @ClassName : Operation
 * <AUTHOR> yangy
 * @Date: 2022-04-02 10:32
 */
@Getter
@Setter
public class Operation {
    Integer total;
    Integer functionTotal;
    String modelLabel;

    public Operation() {
    }

    public Operation(Integer total, String modelLabel) {
        this.total = total;
        this.modelLabel = modelLabel;
    }

    public Integer getFunctionTotal() {
        if (Objects.isNull(functionTotal)) {
            return 0;
        }
        return functionTotal;
    }
}

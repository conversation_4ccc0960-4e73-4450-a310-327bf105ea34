package com.cet.eem.constforecast.model.vo;

import com.cet.eem.common.definition.ModelLabelDef;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @className ObjectCostValueVo
 * @description 成本值
 * @date 2020/5/13 13:48
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ObjectCostValueVo {

    private final String modelLabel = ModelLabelDef.OBJECT_COST_VALUE;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 聚合周期
     */
    private Integer aggregationcycle;

    /**
     * 维度配置id
     */
    private String dimtagids;

    /**
     * 能源类型
     */
    private Integer energytype;

    /**
     * 时间
     */
    private Long logtime;

    /**
     * 对象模型
     */
    private Long objectid;

    /**
     * 对象模型标识
     */
    private String objectlabel;

    /**
     * 非自然天
     */
    private Integer unnaturalday;

    /**
     * 非自然月
     */
    private Integer unnaturalmonth;

    /**
     * 值
     */
    private Double value;

    /**
     * 对象模型的名称
     */
    private String objectName;

    /*
     * 单位名称 元、万元，数据值已根据单位适配
     * */
    private String unitName;
}
package com.cet.eem.bll.achievementrate.model.def;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/15 14:52
 */
public interface AchievementRateType {
    //总能耗达成率
    int TOTAL_ENERGY_CONSUMPTION_ACHIEVEMENT_RATE=1;
    //总成本达成率
    int TOTAL_COST_ACHIEVEMENT_RATE=2;
    //单台能耗达成率
    int ACHIEVEMENT_RATE_OF_ENERGY_CONSUMPTION_PER_UNIT=3;
    //单台成本达成率
    int UNIT_COST_ACHIEVEMENT_RATIO=4;

    List<Integer> TYPES= Arrays.asList(TOTAL_ENERGY_CONSUMPTION_ACHIEVEMENT_RATE,TOTAL_COST_ACHIEVEMENT_RATE,ACHIEVEMENT_RATE_OF_ENERGY_CONSUMPTION_PER_UNIT,UNIT_COST_ACHIEVEMENT_RATIO);
}

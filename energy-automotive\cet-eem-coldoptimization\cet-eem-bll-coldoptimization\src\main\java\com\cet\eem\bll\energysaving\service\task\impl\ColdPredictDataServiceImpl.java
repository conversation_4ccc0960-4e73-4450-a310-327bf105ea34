package com.cet.eem.bll.energysaving.service.task.impl;

import com.cet.eem.bll.common.dao.poi.EemPoiRecordDao;
import com.cet.eem.bll.common.dao.project.ProjectDao;
import com.cet.eem.bll.common.model.domain.object.organization.Project;
import com.cet.eem.bll.common.model.domain.subject.energysaving.ColdPredict;

import com.cet.eem.bll.energysaving.dao.weather.ColdPredictDao;
import com.cet.eem.bll.energysaving.dao.weather.RefrigeratingSystemDao;
import com.cet.eem.bll.energysaving.model.config.system.RefrigeratingSystem;
import com.cet.eem.bll.energysaving.model.dataentryquery.DataLogDataWrite;
import com.cet.eem.bll.energysaving.model.dataentryquery.DataQueryParam;
import com.cet.eem.bll.energysaving.model.weather.ColdLoadType;
import com.cet.eem.bll.energysaving.model.weather.ForecastBasicWeatherQueryVo;
import com.cet.eem.bll.energysaving.model.weather.PredictDataType;
import com.cet.eem.bll.energysaving.service.predict.LgbModelPredictService;
import com.cet.eem.bll.energysaving.service.task.ColdPredictDataService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.DataLogData;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.tool.ModelServiceUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : ColdPredictDataServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-07-26 14:12
 */
@Slf4j
@Service
public class ColdPredictDataServiceImpl implements ColdPredictDataService {
    @Value("${cet.eem.task.energy-saving.endCold.startTime}")
    private String startTime;
    @Value("${cet.eem.task.energy-saving.pipeLoss.startTime}")
    private String startTimeOfPipe;
    @Value("${cet.eem.task.energy-saving.systemPower.startTime}")
    private String startTimeOfSystem;

    @Autowired
    RefrigeratingSystemDao refrigeratingSystemDao;
    @Autowired
    LgbModelPredictService lgbModelPredictService;
    public static final Integer END_COLD_POI_TYPE = 21;
    public static final Integer PIPE_LOSS_POI_TYPE = 22;
    public static final Integer SYSTEM_POWER_POI_TYPE = 23;
    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    ProjectDao projectDao;
    @Autowired
    ColdPredictDao coldPredictDao;

    @Override
    public void saveEndColdPredictData() {
        log.info("开始转存末端冷量预测结果数据。");
        long beginTime = System.currentTimeMillis();


        long count = 0;

        try {
            count = queryEndColdPredictData(END_COLD_POI_TYPE);
        } catch (Exception e) {
            log.error("[转存末端冷量预测结果数据]转存末端冷量预测结果异常", e);
        }

        long overTime = System.currentTimeMillis();
        log.info("本次转存末端冷量预测结果数据：{}，耗时(s)：{}", count, (overTime - beginTime) * CommonUtils.DOUBLE_CONVERSION_COEFFICIENT / 1000);
        log.info("结束转存末端冷量预测结果数据。");
    }

    private Map<Long, Project> assembleRoomIdWithProject() {
        List<Project> projects = projectDao.selectAll();
        if (CollectionUtils.isEmpty(projects)) {
            return Collections.emptyMap();
        }
        Map<Long, Project> map = new HashMap<>();
        List<Long> ids = projects.stream().map(Project::getId).collect(Collectors.toList());
        List<Map<String, Object>> maps = modelServiceUtils.queryWithChildren(NodeLabelDef.PROJECT, ids, Collections.singletonList(NodeLabelDef.ROOM));
        List<BaseVo> baseVos = JsonTransferUtils.transferList(maps, BaseVo.class);
        for (BaseVo baseVo : baseVos) {
            if (CollectionUtils.isNotEmpty(baseVo.getChildren())) {
                for (BaseVo baseVo1 : baseVo.getChildren()) {
                    Project project1 = projects.stream().filter(project -> Objects.equals(project.getId(), baseVo.getId())).findAny().orElse(new Project());
                    map.put(baseVo1.getId(), project1);
                }
            }
        }
        return map;
    }

    @Override
    public void savePipeLineLossPredictData() {
        log.info("开始转存管道损失预测结果数据。");
        long beginTime = System.currentTimeMillis();
        long count = 0;

        try {
            count = queryEndColdPredictData(PIPE_LOSS_POI_TYPE);
        } catch (Exception e) {
            log.error("[转存管道损失预测结果数据]转存转存管道预测结果异常", e);
        }

        long overTime = System.currentTimeMillis();
        log.info("本次转存管道损失预测结果数据：{}，耗时(s)：{}", count, (overTime - beginTime) * CommonUtils.DOUBLE_CONVERSION_COEFFICIENT / 1000);
        log.info("结束转存管道损失预测结果数据。");
    }

    @Override
    public void saveTotalSystemPowerPredictData() {
        log.info("开始转存系统总功率预测结果数据。");
        long beginTime = System.currentTimeMillis();
        long count = 0;

        try {
            count = queryEndColdPredictData(SYSTEM_POWER_POI_TYPE);
        } catch (Exception e) {
            log.error("[转存系统总功率预测结果数据]转存转存管道预测结果异常", e);
        }

        long overTime = System.currentTimeMillis();
        log.info("本次转存系统总功率预测结果数据：{}，耗时(s)：{}", count, (overTime - beginTime) * CommonUtils.DOUBLE_CONVERSION_COEFFICIENT / 1000);
        log.info("结束转存系统总功率预测结果数据。");
    }

    /**
     * 解析全局开始时间
     *
     * @return 全局开始时间
     */
    public LocalDateTime parsePercentStartTime(String startTime) {
        if (StringUtils.isBlank(startTime)) {
            return LocalDateTime.now();
        }
        return TimeUtil.parse(startTime, TimeUtil.LONG_TIME_FORMAT);

    }

    private LocalDateTime getStartTimeByType(Integer poiType) {
        if (Objects.equals(poiType, END_COLD_POI_TYPE)) {
            return parsePercentStartTime(startTime);
        } else if (Objects.equals(poiType, PIPE_LOSS_POI_TYPE)) {
            return parsePercentStartTime(startTimeOfPipe);
        }
        return parsePercentStartTime(startTimeOfSystem);

    }


    /**
     * @return
     */
    private long queryEndColdPredictData(Integer poiType) throws Exception {
        LocalDateTime time = getStartTimeByType(poiType);
        List<RefrigeratingSystem> refrigeratingSystems = refrigeratingSystemDao.selectAll();
        if (CollectionUtils.isEmpty(refrigeratingSystems)) {
            return 0;
        }
        refrigeratingSystems = refrigeratingSystems.stream().filter(it ->
                Boolean.TRUE.equals(it.getUseAi())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(refrigeratingSystems)) {
            return 0;
        }
        Map<Long, Project> roomIdWithProject = assembleRoomIdWithProject();
        long count = 0L;
        for (RefrigeratingSystem refrigeratingSystem : refrigeratingSystems) {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();

            long l = queryEndColdPredictDataSystemBatch(time, refrigeratingSystem.getRoomId(), roomIdWithProject.get(refrigeratingSystem.getRoomId()), poiType);

            stopWatch.stop();
            count += l;
            log.info("每个系统制冷数据转存执行时间:{}", stopWatch.getLastTaskTimeMillis());
        }

        return count;
    }

    private List<ColdPredict> transDataType(DataLogDataWrite predictData, Project project, Long roomId, Integer coldType, Integer dataType
    ) {
        List<ColdPredict> result = new ArrayList<>();
        List<DataLogData> data = predictData.getDataLogData();

        Map<Long, List<DataLogData>> map = data.stream().collect(Collectors.groupingBy(DataLogData::getTime));

        for (Map.Entry<Long, List<DataLogData>> entry : map.entrySet()) {
            List<DataLogData> value = entry.getValue();
            if (CollectionUtils.isNotEmpty(value)) {
                ColdPredict predict = new ColdPredict();
                predict.setValue(value.get(0).getValue());
                predict.setLogTime(TimeUtil.timestamp2LocalDateTime(value.get(0).getTime()));
                predict.setProjectId(project.getId());
                predict.setPredictDataType(dataType);
                predict.setColdLoadType(coldType);
                predict.setRoomId(roomId);
                predict.setProjectId(project.getId());
                result.add(predict);
            }
        }
        return result;
    }

    private DataLogDataWrite getReturnDataByType(Integer poiType, DataQueryParam query, Long projectId) throws IllegalAccessException, InstantiationException {
        if (Objects.equals(poiType, END_COLD_POI_TYPE)) {
            return lgbModelPredictService.getEndColdPredictData(query, projectId);
        } else if (Objects.equals(poiType, PIPE_LOSS_POI_TYPE)) {
            return lgbModelPredictService.getPipelineLossColdPredictData(query, projectId);
        } else if (Objects.equals(poiType, SYSTEM_POWER_POI_TYPE)) {
            return lgbModelPredictService.getTotalSystemPowerPredict(query, projectId);
        }
        return new DataLogDataWrite();
    }

    /**
     * @return
     */
    private long queryEndColdPredictDataSystemBatch(LocalDateTime time, Long roomId, Project project, Integer poiType) throws Exception {
        List<LocalDateTime> timeRange = TimeUtil.getTimeRange(time, LocalDateTime.now(), AggregationCycle.ONE_HOUR);
        long count = 0;
        for (LocalDateTime time1 : timeRange) {
            if (Objects.equals(poiType, END_COLD_POI_TYPE)) {
                count += queryEndColdPredictDataSystem(time1, roomId, project, poiType);
            } else if (Objects.equals(poiType, PIPE_LOSS_POI_TYPE)) {
                count += queryPipeLossPredictDataSystem(time1, roomId, project, poiType);
            } else if (Objects.equals(poiType, SYSTEM_POWER_POI_TYPE)) {
                count += queryTotalPowerDataSystem(time1, roomId, project, poiType);
            }
        }
        return count;
    }

    /**
     * @return
     */
    private long queryEndColdPredictDataSystem(LocalDateTime time, Long roomId, Project project, Integer poiType) throws Exception {
        DataQueryParam query = assembleTime(time);
        log.info("本次转存查询入参是{}", JsonTransferUtils.toJSONString(query));
        DataLogDataWrite predictData = getReturnDataByType(poiType, query, project.getId());
        List<ColdPredict> old = coldPredictDao.queryColdPredictData(Collections.singletonList(ColdLoadType.END),
                Collections.singletonList(PredictDataType.COLD_LOAD), query.getStartTimePredict(), query.getEndTimePredict(), roomId);
        List<ColdPredict> predicts = transDataType(predictData, project, roomId, ColdLoadType.END, PredictDataType.COLD_LOAD);
        log.info("本次转存调用算法返回值是{}", JsonTransferUtils.toJSONString(predicts));
        log.info("本次转存查询到的历史数据是{}", JsonTransferUtils.toJSONString(old));
        return checkOldData(predicts, old);
    }

    /**
     * @return
     */
    private long queryPipeLossPredictDataSystem(LocalDateTime time, Long roomId, Project project, Integer poiType) throws Exception {
        DataQueryParam query = assembleTime(time);
//        DataQueryParam query = assembleFifTime(time);
        DataLogDataWrite predictData = getReturnDataByType(poiType, query, project.getId());
        List<ColdPredict> old = coldPredictDao.queryColdPredictData(Collections.singletonList(ColdLoadType.TOTAL),
                Collections.singletonList(PredictDataType.LOSS), query.getStartTimePredict(), query.getEndTimePredict(), roomId);
        List<ColdPredict> predicts = transDataType(predictData, project, roomId, ColdLoadType.TOTAL, PredictDataType.LOSS);
        return checkOldData(predicts, old);
    }

    /**
     * @return
     */
    private long queryTotalPowerDataSystem(LocalDateTime time, Long roomId, Project project, Integer poiType) throws Exception {
        DataQueryParam query = assembleTime(time);
        DataLogDataWrite predictData = getReturnDataByType(poiType, query, project.getId());
        List<ColdPredict> old = coldPredictDao.queryColdPredictData(Collections.singletonList(ColdLoadType.TOTAL),
                Collections.singletonList(PredictDataType.POWER), query.getStartTimePredict(), query.getEndTimePredict(), roomId);
        List<ColdPredict> predicts = transDataType(predictData, project, roomId, ColdLoadType.TOTAL, PredictDataType.POWER);
        return checkOldData(predicts, old);
    }

    @Override
    public DataQueryParam assembleTime(LocalDateTime now) {
        DataQueryParam query = new DataQueryParam();
        //历史时间
        LocalDateTime firstTimeOfHour = TimeUtil.getFirstTimeOfHour(now);
        List<LocalDateTime> timeRangeForFifteen = TimeUtil.getTimeRange(firstTimeOfHour, TimeUtil.addDateTimeByCycle(firstTimeOfHour, AggregationCycle.ONE_HOUR, 3), AggregationCycle.FIFTEEN_MINUTES);
        int i = 0;
        for (LocalDateTime time : timeRangeForFifteen) {
            if (Boolean.TRUE.equals(time.isAfter(now))) {
                if (Objects.equals(i, 0)) {
                    query.setStartTimePredict(time);
                } else if (Objects.equals(i, 4)) {
                    query.setEndTimePredict(time);
                }
                i++;
            }
        }
        //考虑now是整点的情况，不能赋值now
        query.setEndTime(query.getStartTimePredict());
        query.setStartTime(TimeUtil.addDateTimeByCycle(query.getEndTime(), AggregationCycle.ONE_DAY, -1));
        return query;
    }

    private DataQueryParam assembleFifTime(LocalDateTime now) {
        DataQueryParam query = new DataQueryParam();
        //历史时间
        LocalDateTime firstTimeOfHour = TimeUtil.getFirstTimeOfHour(now);
        List<LocalDateTime> timeRangeForFifteen = TimeUtil.getTimeRange(firstTimeOfHour, TimeUtil.addDateTimeByCycle(firstTimeOfHour, AggregationCycle.ONE_HOUR, 3), AggregationCycle.FIVE_MINUTES);
        int i = 0;
        for (LocalDateTime time : timeRangeForFifteen) {
            if (Boolean.TRUE.equals(time.isAfter(now))) {
                if (Objects.equals(i, 0)) {
                    query.setStartTimePredict(time);
                } else if (Objects.equals(i, 12)) {
                    query.setEndTimePredict(time);
                }
                i++;
            }
        }
        //考虑now是整点的情况，不能赋值now
        query.setEndTime(query.getStartTimePredict());
        query.setCycle(AggregationCycle.FIVE_MINUTES);
        query.setStartTime(TimeUtil.addDateTimeByCycle(query.getEndTime(), AggregationCycle.ONE_DAY, -1));
        return query;
    }

    private long checkOldData(List<ColdPredict> weatherPredicts, List<ColdPredict> old) {

        // 匹配数据库中已经有的数据
        for (ColdPredict weather : weatherPredicts) {
            Optional<ColdPredict> any = old.stream().filter(item ->
                    Objects.equals(item.getLogTime(), weather.getLogTime()) &&
                            Objects.equals(weather.getRoomId(), item.getRoomId())).findAny();
            any.ifPresent(item -> weather.setId(item.getId()));
        }
        List<ColdPredict> write = coldPredictDao.write(weatherPredicts);
        log.info("本次转存最后入库结果是{}", JsonTransferUtils.toJSONString(weatherPredicts));
        //再写入
        return write.size();
    }
}
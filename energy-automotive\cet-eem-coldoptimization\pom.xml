<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cet.eem</groupId>
        <artifactId>eem-base-service-parent</artifactId>
        <version>4.0.4.42</version>
        <relativePath/>
    </parent>
    <groupId>com.cet.eem</groupId>
    <artifactId>cet-eem-coldoptimization</artifactId>
    <version>3.2.26</version>
    <packaging>pom</packaging>

    <modules>
        <module>cet-eem-bll-coldoptimization</module>
        <module>cet-eem-coldoptimizationservice</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <!-- 数据访问层 -->
        <dependency>
            <groupId>com.cet.eem</groupId>
            <artifactId>cet-eem-dal-starter</artifactId>
        </dependency>

        <!-- eem基础业务层 -->
        <dependency>
            <groupId>com.cet.eem</groupId>
            <artifactId>cet-eem-bll-base-starter</artifactId>
        </dependency>

        <!-- eem核心业务层 -->
        <dependency>
            <groupId>com.cet.eem</groupId>
            <artifactId>cet-eem-bll-core-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-math3</artifactId>
            <version>3.6.1</version>
        </dependency>

    </dependencies>

    <!--发布配置管理-->
    <distributionManagement>
        <repository>
            <id>*************_9086</id>
            <name>releases</name>
            <url>http://*************:9086/repository/cet/</url>
        </repository>
        <snapshotRepository>
            <id>*************_9086</id>
            <name>snapshots</name>
            <url>http://*************:9086/repository/cet/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <version>3.16.0</version> <!-- 使用最新版本 -->
                <configuration>
                    <skip>true</skip>
                </configuration>
                <executions>
                    <execution>
                        <id>pmd</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
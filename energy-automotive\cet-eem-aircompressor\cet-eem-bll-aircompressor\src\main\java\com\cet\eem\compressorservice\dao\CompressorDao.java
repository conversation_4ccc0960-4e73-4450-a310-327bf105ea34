package com.cet.eem.compressorservice.dao;

import com.cet.eem.bll.common.model.demand.vo.ObjectLabel;
import com.cet.eem.bll.common.model.domain.object.event.SystemEvent;
import com.cet.eem.bll.common.model.domain.perception.logicaldevice.PecEventExtendVo;
import com.cet.eem.compressorservice.model.vo.Equipments;
import com.cet.eem.compressorservice.model.vo.RoomVo;

import java.util.List;

public interface CompressorDao {

    List<RoomVo> queryByRoomtype(Long projectId, Integer roomtype);

    List<Equipments> queryByRoomId(Long roomId);

    List<Equipments> queryByRoomIdList(List<Long> roomIdList);

    List<SystemEvent> querySysEventByLabels(List<ObjectLabel> labels, Long st);

    List<PecEventExtendVo> queryPecEventByLabels(List<ObjectLabel> labels, Long st, Long end);
}

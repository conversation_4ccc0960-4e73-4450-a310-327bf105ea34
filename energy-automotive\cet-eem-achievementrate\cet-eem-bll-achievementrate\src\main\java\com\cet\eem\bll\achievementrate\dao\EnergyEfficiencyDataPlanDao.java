package com.cet.eem.bll.achievementrate.dao;

import com.cet.eem.bll.achievementrate.model.EnergyEfficiencyParam;
import com.cet.eem.bll.achievementrate.model.data.EnergyEfficiencyDataPlan;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.dao.BaseModelDao;

import java.util.Collection;
import java.util.List;

/**
 * @ClassName : EnergyEfficiencyDataPlanDao
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-11-21 17:12
 */
public interface EnergyEfficiencyDataPlanDao extends BaseModelDao<EnergyEfficiencyDataPlan> {
         List<EnergyEfficiencyDataPlan> query(Long st, Long et, Collection<Long> setIds, Collection<BaseVo> nodes,Integer cycle);
         List<EnergyEfficiencyDataPlan> query(EnergyEfficiencyParam param);
         void insertData(Collection<EnergyEfficiencyDataPlan> plans);
         void writeEnergyEffData(Collection<EnergyEfficiencyDataPlan> plans);
}
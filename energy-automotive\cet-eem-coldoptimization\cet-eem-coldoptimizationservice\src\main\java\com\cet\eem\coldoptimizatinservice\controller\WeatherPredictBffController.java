package com.cet.eem.coldoptimizatinservice.controller;

import com.cet.eem.bll.common.model.domain.subject.energysaving.WeatherPredict;
import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.bll.energysaving.model.weather.CurrentWeather;
import com.cet.eem.bll.energysaving.model.weather.ForecastBasicWeatherDataVo;
import com.cet.eem.bll.energysaving.model.weather.ForecastBasicWeatherQueryVo;
import com.cet.eem.bll.energysaving.model.weather.QueryParam;
import com.cet.eem.bll.energysaving.service.weather.WeatherCrawlingDataService;
import com.cet.eem.common.model.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/13
 */
@Api(value = "WeatherPredictController", tags = {"天气接口"})
@RestController
@RequestMapping(value = "/eem/v1/weather")
public class WeatherPredictBffController {
    @Autowired
    WeatherCrawlingDataService weatherCrawlingDataService;

    @ApiOperation(value = "查询天气预测基础数据，包括天气预报数据和")
    @PostMapping(value = "/basic", produces = "application/json")
    public Result<List<ForecastBasicWeatherDataVo>> queryForecastBasicWeatherData(
            @RequestBody @ApiParam(name = "query", value = "查询条件", required = true) ForecastBasicWeatherQueryVo query) throws Exception {

        return Result.ok(weatherCrawlingDataService.queryWeather(query, GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "写入天气预测基础数据")
    @PutMapping(value = "/measure/predict", produces = "application/json")
    public Result<Object> writeWeatherPredict(
            @RequestBody @ApiParam(name = "dataList", value = "需要保存的数据", required = true) List<WeatherPredict> dataList) {
        weatherCrawlingDataService.writeWeatherPredict(dataList);
        return Result.ok();
    }

    @ApiOperation(value = "查询当前天气数据")
    @PostMapping(value = "/currentWeather", produces = "application/json")
    public Result<CurrentWeather> queryCurrentWeather(@RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) QueryParam queryParam) {
        return Result.ok(weatherCrawlingDataService.queryWeatherCurrentData(queryParam,GlobalInfoUtils.getProjectId()));
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cet-eem-coldoptimizationdatatransport</artifactId>
        <groupId>com.cet.eem</groupId>
        <version>1.13.0034-am.branch_1.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cet-eem-coldoptimizationdatatransportservice</artifactId>
    <packaging>war</packaging>
    <dependencies>
        <dependency>
            <groupId>com.cet.eem</groupId>
            <artifactId>cet-eem-bll-coldoptimizationdatatransport</artifactId>
            <version>1.13.0034-am.branch_1.2</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <properties>
        <java.version>1.8</java.version>
        <spring-cloud.version>Greenwich.SR1</spring-cloud.version>
        <docker.registry>*************/eem-autom</docker.registry>
        <tag>v${project.version}</tag>
        <!--是否跳过测试打包 -->
        <skipTests>true</skipTests>
        <dockerHost>http://*************:2375</dockerHost>
        <tomcat.version>9.0.40</tomcat.version>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>1.0.0</version>
                <executions>
                    <execution>
                        <id>build-image</id>
                        <phase>package</phase>
                        <goals>
                            <goal>build</goal>
                        </goals>
                        <configuration>
                            <dockerHost>${dockerHost}</dockerHost>
                            <dockerDirectory>${project.basedir}</dockerDirectory>
                            <imageName>${docker.registry}/${project.artifactId}:${tag}</imageName>
                            <buildArgs>
                                <WAR_FILE>${project.build.finalName}</WAR_FILE>
                            </buildArgs>
                            <resources>
                                <resource>
                                    <targetPath>/</targetPath>
                                    <directory>${project.build.directory}</directory>
                                    <include>${project.build.finalName}.war</include>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>push-image</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>push</goal>
                        </goals>
                        <configuration>
                            <dockerHost>${dockerHost}</dockerHost>
                            <serverId>harbor</serverId>
                            <imageName>${docker.registry}/${project.artifactId}:${tag}</imageName>
                            <registryUrl>*************</registryUrl>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <buildArgs>
                        <artifactId>${project.artifactId}</artifactId>
                        <VERSION>${project.version}</VERSION>
                    </buildArgs>
                </configuration>
            </plugin>
            <!--pmd校验-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <version>3.8</version>
                <configuration>
                    <rulesets>
                        <!-- <ruleset>rulesets/java/ali-comment.xml</ruleset>
                        <ruleset>rulesets/java/ali-concurrent.xml</ruleset>
                        <ruleset>rulesets/java/ali-constant.xml</ruleset>
                        <ruleset>rulesets/java/ali-exception.xml</ruleset>
                        <ruleset>rulesets/java/ali-flowcontrol.xml</ruleset>
                        <ruleset>rulesets/java/ali-naming.xml</ruleset>
                        <ruleset>rulesets/java/ali-oop.xml</ruleset>
                        <ruleset>rulesets/java/ali-orm.xml</ruleset>
                        <ruleset>rulesets/java/ali-other.xml</ruleset>
                        <ruleset>rulesets/java/ali-set.xml</ruleset> -->
                    </rulesets>
                    <printFailingErrors>true</printFailingErrors>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <!-- https://mvnrepository.com/artifact/com.alibaba.p3c/p3c-pmd -->
                    <dependency>
                        <groupId>com.alibaba.p3c</groupId>
                        <artifactId>p3c-pmd</artifactId>
                        <version>1.3.5</version>
                    </dependency>
                </dependencies>
            </plugin>
            <!--静态代码检测的插件，未使用，先保留-->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <version>2.5.1</version>
                <configuration>
                    <threshold>High</threshold>
                    <effort>Default</effort>
                    <findbugsXmlOutput>true</findbugsXmlOutput>
                    <!--<findbugsXmlWithMessages>true</findbugsXmlWithMessages>-->
                    <xmlOutput>true</xmlOutput>
                    <!--<formats><format>html</format></formats>-->
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-report-plugin</artifactId>
                <version>3.0.0-M4</version>
            </plugin>
        </plugins>
    </build>

</project>
package com.cet.eem.fusion.transformer.core.controller;

import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.fusion.transformer.core.entity.bo.EquipmentCondition;
import com.cet.eem.fusion.transformer.core.entity.bo.EquipmentForm;
import com.cet.eem.fusion.transformer.core.entity.vo.OverviewDataVo;
import com.cet.eem.fusion.transformer.core.service.TransformerOverviewService;
import com.cet.eem.solution.common.def.common.PluginInfoDef;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.entity.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR> yangy
 * @ClassName : TransformerOverviewController
 * @Date: 2022-03-15 15:26
 */
@Validated
@RestController
@Api(value = PluginInfoDef.Transformer.INTERFACE_PREFIX + "/v1/overview", tags = "变压器总览接口")
@RequestMapping(value = PluginInfoDef.Transformer.PLUGIN_NAME_PREFIX + "/v1/overview")
public class TransformerOverviewController {

    @Autowired
    TransformerOverviewService transformerOverviewService;

    @GetMapping("/standingBook")
    @ApiOperation(value = "总览台账信息")
    public ApiResult<OverviewDataVo> getOverviewData() {
        return Result.ok(transformerOverviewService.aboveData(GlobalInfoUtils.getProjectId()));
    }

    @GetMapping("/standingBookForDayEvent")
    @ApiOperation(value = "总览台账信息-日事件")
    public ApiResult<OverviewDataVo> getOverviewDataForDayEvent() {
        return Result.ok(transformerOverviewService.aboveDataForDayEvent(GlobalInfoUtils.getProjectId()));
    }
    
    @GetMapping("/standingBookForMonthEvent")
    @ApiOperation(value = "总览台账信息-月时间")
    public ApiResult<OverviewDataVo> getOverviewDataForMonthEvent() {
        return Result.ok(transformerOverviewService.aboveDataForMonthEvent(GlobalInfoUtils.getProjectId()));
    }
    
    @GetMapping("/standingBookForStatus")
    @ApiOperation(value = "总览台账信息-状态统计")
    public ApiResult<OverviewDataVo> getOverviewDataForStatus() {
        return Result.ok(transformerOverviewService.aboveDataForStatus(GlobalInfoUtils.getProjectId()));
    }

    @PostMapping("/equipmentStatus")
    @ApiOperation(value = "总览设备状态监测")
    public ApiResult<List<EquipmentCondition>> equipmentStatus(@RequestBody EquipmentForm form) {
        return Result.ok(transformerOverviewService.equipmentStatus(form,GlobalInfoUtils.getProjectId()));
    }
}

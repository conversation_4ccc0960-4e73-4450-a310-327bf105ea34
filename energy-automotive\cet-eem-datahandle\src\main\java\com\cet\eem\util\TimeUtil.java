package com.cet.eem.util;



import com.cet.eem.def.AggregationCycle;
import org.jetbrains.annotations.NotNull;


import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Year;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>Convert LocalDateTime To String:
 * <pre>{@code}
 *      SUPPER_LONG_TIME_FORMAT.format(LocalDateTime.now());
 * </pre>
 * <p>Convert String To LocalDateTime
 * <pre>{@code}
 *
 * </pre>
 *
 * @ClassName : TimeUtil
 * @Description : LocalDateTime时间工具类
 * <AUTHOR> zhangh
 * @Date: 2020-12-14 16:02
 */
public class TimeUtil {
    private TimeUtil() {
    }

    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    public static final String YYYY_MM = "yyyy-MM";

    public static final String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";

    public static final String YYYY_MM_DD_SLASH = "yyyy/MM/dd";

    /**
     * 超长时间格式
     */
    public static final DateTimeFormatter SUPPER_LONG_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss:SSS");
    /**
     * 长时间格式
     */
    public static final DateTimeFormatter LONG_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    /**
     * 精确到分钟的时间格式
     */
    public static final DateTimeFormatter MINUTE_TIME_FORMAT = DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM);
    /**
     * 精确到小时的时间格式
     */
    public static final DateTimeFormatter HOUR_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH");
    /**
     * 日期格式
     */
    public static final DateTimeFormatter DATE_TIME_FORMAT = DateTimeFormatter.ofPattern(YYYY_MM_DD);
    /**
     * 精确到月的时间格式
     */
    public static final DateTimeFormatter MONTH_TIME_FORMAT = DateTimeFormatter.ofPattern(YYYY_MM);
    /**
     * 年时间格式
     */
    public static final DateTimeFormatter YEAR_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy");
    /**
     * 精确到小时的时间格式
     */
    public static final DateTimeFormatter SECONDTIMEFORMAT = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    /**
     * 精确到小时的时间格式
     */
    public static final DateTimeFormatter MILLISECONDTIMEFORMAT = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");

    /**
     * 精确到小时的时间格式
     */
    public static final DateTimeFormatter HOURTIMEFORMAT = DateTimeFormatter.ofPattern("yyyyMMddHH");
    /**
     * 日期格式
     */
    public static final DateTimeFormatter DATETIMEFORMAT = DateTimeFormatter.ofPattern("yyyyMMdd");
    /**
     * 精确到月的时间格式
     */
    public static final DateTimeFormatter MONTHTIMEFORMAT = DateTimeFormatter.ofPattern("yyyyMM");

    /**
     * 天的时间戳
     */
    public static final Long DAY = 24L * 3600 * 1000;
    /**
     * 年的时间戳
     */
    public static final Long YEAR = 365L * 24 * 3600 * 1000;
    /**
     * 分钟的时间戳
     */
    public static final long MINUTE = 60 * 1000L;
    /**
     * 小时的时间戳
     */
    public static final Long HOUR = 3600L * 1000;
    /**
     * 秒的时间戳
     */
    public static final Long SECOND = 1000L;

    public static final int FEBRUARY = 2;

    public static final int MONTH_COUNT = 12;
    /**
     * 第一天的日期
     */
    public static final int FIRST_DAY = 1;

    /**
     * 系统初始化时间
     */
    public static final long INIT_TIME = -28800000L;

    /**
     * 返回指定时间格式字符串
     *
     * @param localDateTime
     * @param format
     * @return
     */
    public static String format(@NotNull LocalDateTime localDateTime, @NotNull String format) {
        DateTimeFormatter pattern = DateTimeFormatter.ofPattern(format);
        return pattern.format(localDateTime);
    }

    /**
     * 返回指定时间格式字符串
     *
     * @param dt
     * @param format
     * @return
     */
    public static String format(@NotNull Long dt, @NotNull String format) {
        DateTimeFormatter pattern = DateTimeFormatter.ofPattern(format);
        return pattern.format(timestamp2LocalDateTime(dt));
    }


    /**
     * 返回指定时间格式字符串
     *
     * @param localDateTime
     * @param pattern
     * @return
     */
    public static String format(LocalDateTime localDateTime, DateTimeFormatter pattern) {
        return pattern.format(localDateTime);
    }

    /**
     * 返回指定时间格式字符串
     *
     * @param dt
     * @param pattern
     * @return
     */
    public static String format(Long dt, DateTimeFormatter pattern) {
        return pattern.format(timestamp2LocalDateTime(dt));
    }

    /**
     * 时间字符串转时间
     *
     * @param dateTimeString
     * @param pattern
     * @return
     */
    public static LocalDateTime parse(String dateTimeString, DateTimeFormatter pattern) {
        // 年
        if (YEAR_TIME_FORMAT.equals(pattern)) {
            Year year = Year.parse(dateTimeString, pattern);
            return LocalDateTime.of(year.getValue(), 1, 1, 0, 0);
        }

        // 年月
        if (MONTH_TIME_FORMAT.equals(pattern) || MONTHTIMEFORMAT.equals(pattern)) {
            YearMonth yearMonth = YearMonth.parse(dateTimeString, pattern);
            return LocalDateTime.of(yearMonth.getYear(), yearMonth.getMonth(), 1, 0, 0);
        }

        if (DATETIMEFORMAT.equals(pattern) ||
                DATE_TIME_FORMAT.equals(pattern)) {
            LocalDate date = LocalDate.parse(dateTimeString, pattern);
            return LocalDateTime.of(date, LocalTime.MIN);
        }

        return LocalDateTime.parse(dateTimeString, pattern);
    }

    /**
     * LocalDateTime转unix时间戳 毫秒
     *
     * @param localDateTime
     * @return
     */
    public static long localDateTime2timestamp(LocalDateTime localDateTime) {
        return localDateTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
    }

    /**
     * LocalDateTime转unix时间戳 毫秒
     *
     * @param localDate
     * @return
     */
    public static long localDateToTimestamp(LocalDate localDate) {
        return localDate.atStartOfDay(ZoneOffset.of("+8")).toInstant().toEpochMilli();
    }

    /**
     * 时间戳转LocalDateTime
     *
     * @param timestamp
     * @return
     */
    public static LocalDateTime timestamp2LocalDateTime(Long timestamp) {
        return Instant.ofEpochMilli(timestamp).atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static LocalDateTime addCycle(LocalDateTime startTime, Integer aggregationCycle) {
        switch (aggregationCycle) {
            case 1:
                return startTime.plusSeconds(1L);
            case 2:
                return startTime.plusSeconds(3L);
            case 3:
                return startTime.plusMinutes(1L);
            case 4:
                return startTime.plusMinutes(5L);
            case 5:
                return startTime.plusMinutes(10L);
            case 6:
                return startTime.plusMinutes(30L);
            case 7:
                return startTime.plusHours(1L);
            case 8:
                return startTime.plusHours(2L);
            case 9:
                return startTime.plusHours(3L);
            case 10:
                return startTime.plusHours(6L);
            case 11:
                return startTime.plusHours(12L);
            case 12:
                return startTime.plusDays(1L);
            case 13:
                return startTime.plusWeeks(1L);
            case 14:
                return startTime.plusMonths(1L);
            case 15:
                return startTime.plusMonths(3L);
            case 16:
                return startTime.plusMonths(6L);
            case 17:
                return startTime.plusYears(1L);
            case 18:
                return startTime.plusMinutes(15L);
            default:
                return startTime;
        }
    }

    /**
     * 获取指定年的第一天
     *
     * @param localDate
     * @return
     */
    public static LocalDate getFirstDayOfThisYear(LocalDate localDate) {
        return LocalDate.of(localDate.getYear(), 1, 1);
    }

    /**
     * 获取指定年的第一天
     *
     * @param localDate
     * @return
     */
    public static LocalDateTime getFirstDayOfThisYear(LocalDateTime localDate) {
        LocalDate firstDay = localDate.toLocalDate().with(TemporalAdjusters.firstDayOfYear());
        return LocalDateTime.of(firstDay, LocalTime.MIN);
    }

    /**
     * 获取指定时间所属年的第一天
     *
     * @param time 时间
     * @return 所属年第一天的时间戳
     */
    public static Long getFirstDayOfThisYear(Long time) {
        LocalDateTime localDateTime = timestamp2LocalDateTime(time);
        if (Objects.isNull(localDateTime)) {
            return null;
        }
        LocalDateTime firstTimeOfDay = getFirstDayOfThisYear(localDateTime);
        return localDateTime2timestamp(firstTimeOfDay);
    }

    /**
     * 获取指定年的最后一天
     *
     * @param localDate
     * @return
     */
    public static LocalDateTime getLastDayOfThisYear(LocalDateTime localDate) {
        return localDate.with(TemporalAdjusters.lastDayOfYear());
    }

    /**
     * 获取指定年的第一天
     *
     * @param localDate
     * @return
     */
    public static LocalDateTime getFirstDayOfNextYear(LocalDateTime localDate) {
        localDate = localDate.plusYears(1);
        return localDate.with(TemporalAdjusters.firstDayOfYear());
    }

    /**
     * 获取指定年的第一天
     *
     * @param localDate
     * @return
     */
    public static LocalDate getFirstDayOfNextYear(LocalDate localDate) {
        localDate = localDate.plusYears(1);
        return localDate.with(TemporalAdjusters.firstDayOfYear());
    }


    /**
     * 获取指定年的第一天，返回值为时间戳
     *
     * @param localDate
     * @return
     */
    public static long getFirstTimeOfYear(LocalDate localDate) {
        return localDateToTimestamp(getFirstDayOfThisYear(localDate));
    }

    /**
     * 获取指定年的最后一天
     *
     * @param localDate
     * @return
     */
    public static LocalDate getLastDayOfThisYear(LocalDate localDate) {
        return LocalDate.of(localDate.getYear() + 1, 1, 1);
    }

    /**
     * 获取指定月的第一天
     *
     * @param localDate
     * @return
     */
    public static LocalDate getFirstDayOfThisMonth(LocalDate localDate) {
        return LocalDate.of(localDate.getYear(), localDate.getMonthValue(), 1);
    }

    /**
     * 获取指定月的第一天
     *
     * @param localDateTime
     * @return
     */
    public static LocalDateTime getFirstDayOfThisMonth(LocalDateTime localDateTime) {

        localDateTime = LocalDateTime.of(localDateTime.toLocalDate(), LocalTime.MIN);
        return localDateTime.with(TemporalAdjusters.firstDayOfMonth());
    }

    /**
     * 获取指定时间所属月的第一天
     *
     * @param time 时间戳格式
     * @return 所属月地第一天时间戳格式
     */
    public static Long getFirstDayOfThisMonth(Long time) {
        LocalDateTime firstTimeOfDay = getFirstDayOfThisMonth(timestamp2LocalDateTime(time));
        return localDateTime2timestamp(firstTimeOfDay);
    }

    /**
     * 获取指定月的最后一天
     *
     * @param localDateTime
     * @return
     */
    public static LocalDateTime getLastDayOfThisMonth(LocalDateTime localDateTime) {
        return localDateTime.with(TemporalAdjusters.lastDayOfMonth());
    }

    /**
     * 获取指定月的第一天
     *
     * @param localDateTime
     * @return
     */
    public static LocalDateTime getFirstDayOfNextMonth(LocalDateTime localDateTime) {
        return getFirstDayOfThisMonth(localDateTime).plusMonths(1);
    }

    /**
     * 获取指定月的第一天，返回值为时间戳
     *
     * @param localDate
     * @return
     */
    public static long getFirstTimeOfMonth(LocalDate localDate) {
        return localDateToTimestamp(getFirstDayOfThisMonth(localDate));
    }

    /**
     * 获取指定月的最后一天
     *
     * @param localDate
     * @return
     */
    public static LocalDate getLastDayOfThisMonth(LocalDate localDate) {
        LocalDate nextMonth = localDate.plusMonths(1L);
        return LocalDate.of(nextMonth.getYear(), nextMonth.getMonthValue(), 1);
    }

    /**
     * 获取指定月的下个月第一天，返回值为时间戳
     *
     * @param localDate
     * @return
     */
    public static long getFirstTimeOfNextMonth(LocalDate localDate) {
        return localDateToTimestamp(getLastDayOfThisMonth(localDate));
    }

    /**
     * 获取一天的开始时间
     *
     * @param localDate
     * @return
     */
    public static LocalDateTime getTheStartOfDay(LocalDate localDate) {
        return LocalDateTime.of(localDate, LocalTime.MIN);
    }

    /**
     * 获取小时的开始时间
     *
     * @param localDateTime
     * @return
     */
    public static LocalDateTime getFirstTimeOfHour(LocalDateTime localDateTime) {
        LocalDate date = localDateTime.toLocalDate();
        LocalTime time = LocalTime.of(localDateTime.getHour(), 0);
        return LocalDateTime.of(date, time);
    }

    /**
     * 获取当前天开始时间
     *
     * @param localDate
     * @return
     */
    public static LocalDateTime getFirstTimeOfDay(LocalDateTime localDate) {
        return LocalDateTime.of(localDate.toLocalDate(), LocalTime.MIN);
    }

    /**
     * 获取当前天开始时间
     *
     * @param localDate
     * @return
     */
    public static Long getFirstTimeOfDay(Long localDate) {
        LocalDateTime firstTimeOfDay = getFirstTimeOfDay(timestamp2LocalDateTime(localDate));
        return localDateTime2timestamp(firstTimeOfDay);
    }

    /**
     * 获取下一天的开始时间
     *
     * @param localDate
     * @return
     */
    public static LocalDateTime getFirstTimeOfNextDay(LocalDate localDate) {
        localDate = localDate.plusDays(1);
        return LocalDateTime.of(localDate, LocalTime.MIN);
    }

    /**
     * 获取下一天的开始时间
     *
     * @param localDateTime
     * @return
     */
    public static LocalDateTime getFirstTimeOfNextDay(LocalDateTime localDateTime) {
        LocalDate localDate = localDateTime.toLocalDate();
        localDate = localDate.plusDays(1);
        return LocalDateTime.of(localDate, LocalTime.MIN);
    }

    /**
     * 生成时间区间
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param cycle     周期
     * @return
     */
    public static List<LocalDateTime> generateTimeValueOfPeriod(LocalDateTime startTime, LocalDateTime endTime, int cycle) {

        List<LocalDateTime> result = new ArrayList<>();
        if (cycle == AggregationCycle.ONE_YEAR) {
            for (LocalDateTime i = startTime; i.isBefore(endTime); i = i.plusYears(1L)) {
                result.add(i);
            }
        } else if (cycle == AggregationCycle.ONE_MONTH) {
            for (LocalDateTime i = startTime; i.isBefore(endTime); i = i.plusMonths(1L)) {
                result.add(i);
            }
        } else if (cycle == AggregationCycle.ONE_DAY) {
            for (LocalDateTime i = startTime; i.isBefore(endTime); i = i.plusDays(1L)) {
                result.add(i);
            }
        } else if (cycle == AggregationCycle.ONE_HOUR) {
            for (LocalDateTime i = startTime; i.isBefore(endTime); i = i.plusHours(1L)) {
                result.add(i);
            }
        } else if (cycle == AggregationCycle.THREE_MONTHS) {
            for (LocalDateTime i = startTime; i.isBefore(endTime); i = i.plusMonths(3L)) {
                result.add(i);
            }
        } else if (cycle == AggregationCycle.SEVEN_DAYS) {
            for (LocalDateTime i = startTime; i.isBefore(endTime); i = i.plusWeeks(1L)) {
                result.add(i);
            }
        } else if (cycle == AggregationCycle.THIRTY_MINUTES) {
            for (LocalDateTime i = startTime; i.isBefore(endTime); i = i.plusMinutes(30L)) {
                result.add(i);
            }
        } else if (cycle == AggregationCycle.FIVE_MINUTES) {
            for (LocalDateTime i = startTime; i.isBefore(endTime); i = i.plusMinutes(5L)) {
                result.add(i);
            }
        } else if (cycle == AggregationCycle.FIFTEEN_MINUTES) {
            for (LocalDateTime i = startTime; i.isBefore(endTime); i = i.plusMinutes(15L)) {
                result.add(i);
            }
        } else if (cycle == AggregationCycle.SIX_MONTHS) {
            for (LocalDateTime i = startTime; i.isBefore(endTime); i = i.plusMonths(6L)) {
                result.add(i);
            }
        }
        return result;
    }

    /**
     * 生成时间区间(两边都包含)
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param cycle     周期
     * @return
     */
    public static List<LocalDateTime> generateTimeValueOfPeriodBothSide(LocalDateTime startTime, LocalDateTime endTime, int cycle) {
        List<LocalDateTime> result = new ArrayList<>();
        if (cycle == AggregationCycle.ONE_YEAR) {
            for (LocalDateTime i = startTime; !i.isAfter(endTime); i = i.plusYears(1L)) {
                result.add(i);
            }
        } else if (cycle == AggregationCycle.ONE_MONTH) {
            for (LocalDateTime i = startTime; !i.isAfter(endTime); i = i.plusMonths(1L)) {
                result.add(i);
            }
        } else if (cycle == AggregationCycle.ONE_DAY) {
            for (LocalDateTime i = startTime; !i.isAfter(endTime); i = i.plusDays(1L)) {
                result.add(i);
            }
        } else if (cycle == AggregationCycle.ONE_HOUR) {
            for (LocalDateTime i = startTime; !i.isAfter(endTime); i = i.plusHours(1L)) {
                result.add(i);
            }
        } else if (cycle == AggregationCycle.THREE_MONTHS) {
            for (LocalDateTime i = startTime; !i.isAfter(endTime); i = i.plusMonths(3L)) {
                result.add(i);
            }
        } else if (cycle == AggregationCycle.SEVEN_DAYS) {
            for (LocalDateTime i = startTime; !i.isAfter(endTime); i = i.plusWeeks(1L)) {
                result.add(i);
            }
        } else if (cycle == AggregationCycle.THIRTY_MINUTES) {
            for (LocalDateTime i = startTime; !i.isAfter(endTime); i = i.plusMinutes(30L)) {
                result.add(i);
            }
        } else if (cycle == AggregationCycle.FIVE_MINUTES) {
            for (LocalDateTime i = startTime; i.isAfter(endTime); i = i.plusMinutes(5L)) {
                result.add(i);
            }
        } else if (cycle == AggregationCycle.FIFTEEN_MINUTES) {
            for (LocalDateTime i = startTime; i.isAfter(endTime); i = i.plusMinutes(15L)) {
                result.add(i);
            }
        }
        return result;
    }

    /**
     * 生成时间区间
     *
     * @param st
     * @param et
     * @param cycle
     * @return
     */
    public static List<Long> getTimeRange(long st, long et, int cycle) {
        LocalDateTime startTime = timestamp2LocalDateTime(st);
        LocalDateTime endTime = timestamp2LocalDateTime(et);
        List<LocalDateTime> localDateTimes = generateTimeValueOfPeriod(startTime, endTime, cycle);
        List<Long> result = new ArrayList<>();
        for (LocalDateTime localDateTime : localDateTimes) {
            result.add(localDateTime2timestamp(localDateTime));
        }

        return result;
    }

    /**
     * 获取两个时间之间间隔了多少个周期
     *
     * @param lastTime
     * @param thisTime
     * @param cycle
     * @return
     */
    public static int getTimeInterval(long lastTime, long thisTime, int cycle) {
        if(Objects.equals(lastTime,thisTime)){
            return 0;
        }
        //如果上一时间为0 说明当前数据是第一条
        if(Objects.equals(lastTime,0)){
            return 1;
        }
        int index;
        if (thisTime >= lastTime) {
            List<Long> timeRange = getTimeRange(lastTime, thisTime, cycle);
            index = timeRange.size();
        } else {
            List<Long> timeRange = getTimeRange(thisTime, lastTime, cycle);
            index = -timeRange.size();
        }
        return index;
    }

    public static List<Long> getTimeRange(Long st, Long et, int cycle, int step) {
        List<Long> result = new ArrayList<>();
        for (Long time = st; time < et; ) {
            result.add(time);
            time = addDateTimeByCycle(time, cycle, step);
        }
        return result;
    }

    public static List<LocalDateTime> getTimeRange(LocalDateTime st, LocalDateTime et, int cycle, int step) {
        List<LocalDateTime> result = new ArrayList<>();
        for (LocalDateTime time = st; time.isBefore(et); ) {
            result.add(time);
            time = addDateTimeByCycle(time, cycle, step);
        }
        return result;
    }

    /**
     * 生成时间区间
     *
     * @param st
     * @param et
     * @param cycle
     * @return
     */
    public static List<LocalDateTime> getTimeRange(LocalDateTime st, LocalDateTime et, int cycle) {
        return generateTimeValueOfPeriod(st, et, cycle);
    }

    /**
     * 生成时间区间(包含两边)
     *
     * @param st
     * @param et
     * @param cycle
     * @return
     */
    public static List<Long> getTimeRangeBothSide(long st, long et, int cycle) {
        LocalDateTime startTime = timestamp2LocalDateTime(st);
        LocalDateTime endTime = timestamp2LocalDateTime(et);
        List<Long> result = new ArrayList<>();
        if (startTime == null || endTime == null) {
            result = Collections.emptyList();
        } else {
            List<LocalDateTime> localDateTimes = generateTimeValueOfPeriodBothSide(startTime, endTime, cycle);
            for (LocalDateTime localDateTime : localDateTimes) {
                result.add(localDateTime2timestamp(localDateTime));
            }
        }


        return result;
    }

    /**
     * 根据聚合周期对时间进行增减
     *
     * @param dt
     * @param cycle
     * @param step
     * @return
     */
    public static Long addDateTimeByCycle(long dt, int cycle, int step) {
        LocalDateTime localDateTime = timestamp2LocalDateTime(dt);
        localDateTime = addDateTimeByCycle(localDateTime, cycle, step);
        return localDateTime2timestamp(localDateTime);
    }

    /**
     * 根据聚合周期对时间进行增减
     *
     * @param dt
     * @param cycle
     * @param step
     * @return
     */
    public static LocalDateTime addDateTimeByCycle(LocalDateTime dt, int cycle, int step) {
        switch (cycle) {
            case AggregationCycle.ONE_HOUR:
                return dt.plusHours(step);
            case AggregationCycle.ONE_DAY:
                return dt.plusDays(step);
            case AggregationCycle.SEVEN_DAYS:
                return dt.plusDays(7L * step);
            case AggregationCycle.ONE_MONTH:
                return dt.plusMonths(step);
            case AggregationCycle.THREE_MONTHS:
                return dt.plusMonths(3L * step);
            case AggregationCycle.ONE_YEAR:
                return dt.plusYears(step);
            case AggregationCycle.THIRTY_MINUTES:
                return dt.plusMinutes(30L * step);
            case AggregationCycle.FIFTEEN_MINUTES:
                return dt.plusMinutes(15L * step);
            case AggregationCycle.ONE_MINUTE:
                return dt.plusMinutes(step);
            default:
                break;
        }
        return dt;
    }

//    /**
//     * 获取同环比对应的时间
//     *
//     * @param time      需要转换的时间戳
//     * @param cycle     时间类型
//     * @param querytype 查询类型 1 同比 2环比，参考常量{@link QueryType}
//     * @return 同环比时间
//     */
//    public static LocalDateTime convertTime(LocalDateTime time, Integer cycle, Integer querytype) {
//        long timestamp = localDateTime2timestamp(time);
//        long result = convertTime(timestamp, cycle, querytype);
//        return timestamp2LocalDateTime(result);
//    }

//    /**
//     * 获取同环比对应的时间
//     *
//     * @param timestamp 需要转换的时间戳
//     * @param cycle     时间类型
//     * @param querytype 查询类型 1 同比 2环比，参考常量{@link QueryType}
//     * @return 同环比时间
//     */
//    public static long convertTime(long timestamp, Integer cycle, Integer querytype) {
//        if (querytype == QueryType.CURRENT) {
//            return timestamp;
//        }
//        long result = 0;
//        switch (cycle) {
//            case AggregationCycle.ONE_YEAR:
//                result = addDateTimeByCycle(timestamp, AggregationCycle.ONE_YEAR, -1);
//                break;
//            case AggregationCycle.ONE_MONTH:
//                if (querytype == QueryType.YEAR_ON_YEAR) {
//                    result = addDateTimeByCycle(timestamp, AggregationCycle.ONE_YEAR, -1);
//                    break;
//                } else if (querytype == QueryType.MONTH_ON_MONTH) {
//                    result = addDateTimeByCycle(timestamp, AggregationCycle.ONE_MONTH, -1);
//                    break;
//                }
//                break;
//            case AggregationCycle.ONE_DAY:
//                if (querytype == QueryType.YEAR_ON_YEAR) {
//                    result = addDateTimeByCycle(timestamp, AggregationCycle.ONE_MONTH, -1);
//                    break;
//                } else if (querytype == QueryType.MONTH_ON_MONTH) {
//                    result = addDateTimeByCycle(timestamp, AggregationCycle.ONE_DAY, -1);
//                    break;
//                }
//                break;
//            case AggregationCycle.ONE_HOUR:
//                if (querytype == QueryType.YEAR_ON_YEAR) {
//                    result = addDateTimeByCycle(timestamp, AggregationCycle.ONE_YEAR, -1);
//                    break;
//                } else if (querytype == QueryType.MONTH_ON_MONTH) {
//                    result = addDateTimeByCycle(timestamp, AggregationCycle.ONE_HOUR, -1);
//                    break;
//                }
//                break;
//            default:
//                break;
//        }
//        return result;
//    }

    /**
     * 格式化时间区间
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 时间区间值
     */
    public static String formatDuration(Long startTime, Long endTime) {
        if (startTime == null || endTime == null) {
            return "--";
        }

        long duration = endTime - startTime;
        StringBuilder builder = new StringBuilder();

        long day = duration / DAY;
        if (day > 0) {
            builder.append(day).append("天");
            duration = duration % DAY;
        }

        long hour = duration / HOUR;
        if (hour > 0) {
            builder.append(hour).append("时");
            duration = duration % HOUR;
        } else {
            if (builder.length() > 0) {
                builder.append("0时");
            }
        }

        long minute = duration / MINUTE;
        if (minute > 0) {
            builder.append(minute).append("分");
            duration = duration % MINUTE;
        } else {
            if (builder.length() > 0) {
                builder.append("0分");
            }
        }

        long second = duration / SECOND;
        if (second > 0) {
            builder.append(second).append("秒");
            duration = duration % SECOND;
        } else {
            if (builder.length() > 0) {
                builder.append("0秒");
            }
        }

        if (duration > 0) {
            builder.append(duration).append("毫秒");
        }

        return builder.toString();
    }

//    public static String parseTime(Long time) {
//        if (time == null) {
//            return CommonUtils.BLANK_STR;
//        }
//
//        StringBuilder builder = new StringBuilder();
//        long val = time / TimeUtil.HOUR;
//        builder.append(val).append("h");
//        time = time % TimeUtil.HOUR;
//        if (time == 0) {
//            return builder.toString();
//        }
//
//        val = time / TimeUtil.MINUTE;
//        builder.append(val).append("min");
//        return builder.toString();
//    }

    /**
     * 获取指定时间指定周期的开始时间
     *
     * @param time  时间
     * @param cycle 周期
     * @return 指定时间在指定周期的开始时间
     */
    public static Long getCycleStartTime(Long time, Integer cycle) {
        LocalDateTime cycleStartTime = getCycleStartTime(timestamp2LocalDateTime(time), cycle);
        return localDateTime2timestamp(cycleStartTime);
    }

    public static Long getCycleEndTime(Long time, Integer cycle) {
        return addDateTimeByCycle(getCycleStartTime(time, cycle), cycle, 1);
    }

    /**
     * 获取指定时间指定周期的开始时间
     *
     * @param time  时间
     * @param cycle 周期
     * @return 指定时间在指定周期的开始时间
     */
    public static LocalDateTime getCycleStartTime(LocalDateTime time, Integer cycle) {
        if (Objects.equals(AggregationCycle.ONE_HOUR, cycle)) {
            return getFirstTimeOfHour(time);
        } else if (Objects.equals(AggregationCycle.SEVEN_DAYS, cycle)) {
            return getFirstTimeOfWeek(time);
        } else if (Objects.equals(AggregationCycle.ONE_DAY, cycle)) {
            return getFirstTimeOfDay(time);
        } else if (Objects.equals(AggregationCycle.ONE_MONTH, cycle)) {
            return getFirstDayOfThisMonth(time);
        } else if (Objects.equals(AggregationCycle.ONE_YEAR, cycle)) {
            return getFirstDayOfThisYear(time);
        } else {
            return getFirstDayOfThisYear(time);
        }
    }

    public static LocalDateTime getFirstTimeOfWeek(LocalDateTime time) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.setTimeInMillis(localDateTime2timestamp(time));
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Long newTime = calendar.getTimeInMillis();
        return timestamp2LocalDateTime(newTime);
    }

    /**
     * 获取最早的预测开始时间
     *
     * @param minTime 预测开始时间
     * @param time    单个周期预测开始时间
     * @return 最早的预测开始时间
     */
    public static LocalDateTime getMinStartTime(LocalDateTime minTime, LocalDateTime time) {
        if (minTime == null) {
            minTime = time;
        } else {
            if (minTime.isAfter(time)) {
                minTime = time;
            }
        }
        return minTime;
    }

    public static Long formatHourTime(Long time) {
        return formatTargetTime(time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:00:00"));
    }

    public static Long formatDayTime(Long time) {
        return formatTargetTime(time, DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00"));
    }

    public static Long formatMonthTime(Long time) {
        return formatTargetTime(time, DateTimeFormatter.ofPattern("yyyy-MM-01 00:00:00"));
    }

    public static Long formatYearTime(Long time) {
        return formatTargetTime(time, DateTimeFormatter.ofPattern("yyyy-01-01 00:00:00"));
    }

    /**
     * 匹配目标格式的时间
     * 比如：2021-11-10 03:15:00，如果cycle是7，那么格式化后的数据为2021-11-10 03:00:00
     *
     * @param time
     * @param cycle
     * @return
     */
    public static Long formatTargetTime(Long time, Integer cycle) {
        if (cycle.equals(AggregationCycle.ONE_HOUR))
            return formatHourTime(time);
        if (cycle.equals(AggregationCycle.ONE_DAY))
            return formatDayTime(time);
        if (cycle.equals(AggregationCycle.ONE_MONTH))
            return formatMonthTime(time);
        if (cycle.equals(AggregationCycle.ONE_YEAR))
            return formatYearTime(time);
        return time;
    }

    private static Long formatTargetTime(Long time, DateTimeFormatter formatter) {
        LocalDateTime localDateTime = timestamp2LocalDateTime(time);
        if (Objects.isNull(localDateTime)) {
            return null;
        }
        String format = localDateTime.format(formatter);
        return localDateTime2timestamp(parse(format, LONG_TIME_FORMAT));
    }

    public static String formatExcelTimeString(Integer cycle, Long logTime) {
        String timeStr = TimeUtil.format(logTime, TimeUtil.LONG_TIME_FORMAT);
        if (cycle.equals(AggregationCycle.ONE_MONTH))
            timeStr = TimeUtil.format(logTime, DateTimeFormatter.ofPattern(YYYY_MM));
        if (cycle.equals(AggregationCycle.ONE_DAY))
            timeStr = TimeUtil.format(logTime, DateTimeFormatter.ofPattern(YYYY_MM_DD));
        if (cycle.equals(AggregationCycle.ONE_HOUR))
            timeStr = TimeUtil.format(logTime, DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM));
        return timeStr;
    }

    /**
     * 获取系统的最小时间，1970-1-1
     *
     * @return
     */
    public static LocalDateTime getSystemMinTime() {
        return LocalDateTime.of(1970, 1, 1, 0, 0);
    }

    /**
     * 获取年份
     *
     * @param dt 时间
     * @return 年份
     */
    public static Integer getYear(@NotNull LocalDateTime dt) {
        return dt.getYear();
    }


    /**
     * 获取年份
     *
     * @param dt 时间
     * @return 年份
     */
    public static Integer getYear(@NotNull Long dt) {
        LocalDateTime localDateTime = timestamp2LocalDateTime(dt);
        return getYear(localDateTime);
    }
}

package com.cet.eem.def;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/8/17 12:04
 */
@Getter
public enum EnergyEventType {

    DATALOG_LOSS(723, 2, "数据缺失"),
    DATALOG_ERROR(724, 2, "数据异常"),
    AUTO_REPAIR(725, 2, "自动补录"),
    CHANGE_METER(726, 2, "自动换表"),
    AUTO_PURSUIT(727,2,"自动追捕");

    //事件类型
    private final Integer type;
    //事件等级
    private final Integer level;
    //事件描述
    private final String description;

    EnergyEventType(Integer type, Integer level, String description) {
        this.type = type;
        this.level = level;
        this.description = description;
    }

    public static String getDescriptionByType(Integer type) {
        return Arrays.stream(EnergyEventType.values()).filter(item -> Objects.equals(item.getType(), type))
                .map(EnergyEventType::getDescription)
                .findFirst()
                .orElse(null);
    }

    public static Integer getLevelByType(Integer type) {
        return Arrays.stream(EnergyEventType.values()).filter(item -> Objects.equals(item.getType(), type))
                .map(EnergyEventType::getLevel)
                .findFirst()
                .orElse(null);
    }
}

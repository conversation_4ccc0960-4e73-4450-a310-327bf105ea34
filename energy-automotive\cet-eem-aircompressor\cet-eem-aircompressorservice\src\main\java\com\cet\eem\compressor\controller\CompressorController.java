package com.cet.eem.compressor.controller;

import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.common.model.Result;
import com.cet.eem.compressorservice.model.vo.*;
import com.cet.eem.compressorservice.service.CompressorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName : CompressorController
 * <AUTHOR> yangy
 * @Date: 2022-04-02 10:04
 */
@Api(value = "TransformerAnalysisController", tags = "空压机概览")
@RequestMapping(value = "/eem/v1/airCompressor")
@RestController
public class CompressorController {

    @Autowired
    CompressorService compressorService;

    @GetMapping("/compressorRoom")
    @ApiOperation(value = "获取空压机房")
    public Result<List<RoomVo>> getCompressorRoom() {
        Long projectId = GlobalInfoUtils.getProjectId();
        return Result.ok(compressorService.getCompressorRoom(projectId));
    }

    @GetMapping("/standingBook")
    @ApiOperation(value = "获取台账信息")
    public Result<StandingBook> getStandingBook(@RequestParam("roomId") Long roomId) {

        return Result.ok(compressorService.getStandingBook(roomId));
    }

    @GetMapping("/airManager")
    @ApiOperation(value = "获取空压总管数据")
    public Result<AirManagerData> getAirManager(@RequestParam("roomId") Long roomId) {

        return Result.ok(compressorService.getAirManagerData(roomId));
    }

    @PostMapping("/electricalRatio")
    @ApiOperation(value = "电器比分析")
    public Result<EnergyResult> getElectricalRatio(@RequestBody ElectricalRatioForm form) {

        return Result.ok(compressorService.getElectricalRatio(form));
    }

    @GetMapping("/loadingAnalysis")
    @ApiOperation(value = "卸载分析")
    public Result<List<LoadingAnalysisData>> getLoadingAnalysis(@RequestParam("roomId") Long roomId) {

        return Result.ok(compressorService.getLoadingAnalysis(roomId));
    }

    @PostMapping("/unloadingRate")
    @ApiOperation(value = "卸载率")
    public Result<List<UnloadingRate>> getUnloadingRate(@RequestBody UnloadingRateForm form) {

        return Result.ok(compressorService.getUnloadingRate(form));
    }
}

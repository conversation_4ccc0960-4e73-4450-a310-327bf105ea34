package com.cet.eem.bll.energysaving.handle;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.cet.eem.bll.energysaving.model.config.EquipmentCurveVo;
import com.cet.eem.bll.energysaving.model.config.OperatingEfficiencyCurveVo;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.event.exception.ExpertException;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * @ClassName : OperatingEfficiencyCurveListener
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-27 14:22
 */
public class OperatingEfficiencyCurveListener extends AnalysisEventListener<OperatingEfficiencyCurveVo> {
    /**
     * 错误信息
     */
    StringBuilder errorMsgBuilder = new StringBuilder();
    List<EquipmentCurveVo> equipmentCurveVos;

    public OperatingEfficiencyCurveListener(List<EquipmentCurveVo> equipmentCurveVos) {
        this.equipmentCurveVos = equipmentCurveVos;
    }

    @Override
    public void invoke(OperatingEfficiencyCurveVo operatingEfficiencyCurveVo, AnalysisContext analysisContext) {
        EquipmentCurveVo data = new EquipmentCurveVo();
        String errMsg = "";
        if (StringUtils.isEmpty(operatingEfficiencyCurveVo.getColdPercent())) {
            errMsg += "冷负荷率不能为空；";
        } else {
            String coldPercent = operatingEfficiencyCurveVo.getColdPercent();
            coldPercent = coldPercent.replace(" ", "");
            Double value = CommonUtils.parseDouble(coldPercent);
            if (Objects.isNull(value)) {
                errorMsgBuilder.append("理论运行效率曲线导入模板有误");
                errorMsgBuilder.append(errMsg).append("\r\n");
                return;
            }
            if (value < 0) {
                errMsg += "冷负荷率不能为小于0；";
            } else {
                data.setColdPercent(value);
            }
        }
        if (StringUtils.isEmpty(operatingEfficiencyCurveVo.getColdLoad())) {
            errMsg += "冷负荷不能为空；";
        } else {
            String coldLoad = operatingEfficiencyCurveVo.getColdLoad();
            coldLoad = coldLoad.replace(" ", "");
            Double value = CommonUtils.parseDouble(coldLoad);
            if (value < 0) {
                errMsg += "冷负荷不能为小于0；";
            } else {
                data.setColdLoad(value);
            }
        }
        if (StringUtils.isEmpty(operatingEfficiencyCurveVo.getPower())) {
            errMsg += "功率不能为空；";
        } else {
            String power = operatingEfficiencyCurveVo.getPower();
            power = power.replace(" ", "");
            Double value = CommonUtils.parseDouble(power);
            if (value < 0) {
                errMsg += "功率不能为小于0；";
            } else {
                data.setPower(value);
            }
        }
        if (StringUtils.isEmpty(operatingEfficiencyCurveVo.getCop())) {
            errMsg += "cop不能为空；";
        } else {
            String cop = operatingEfficiencyCurveVo.getCop();
            cop = cop.replace(" ", "");
            Double value = CommonUtils.parseDouble(cop);
            if (value < 0) {
                errMsg += "cop不能为小于0；";
            } else {
                data.setCop(value);
            }
        }
        if (StringUtils.isNotBlank(errMsg)) {
            errorMsgBuilder.append("运行效率曲线中序号为[").append(operatingEfficiencyCurveVo.getNumber()).append("]的行数据不合法：");
            errorMsgBuilder.append(errMsg).append("\r\n");
            return;
        }
        equipmentCurveVos.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 处理数据完成后的操作
        if (errorMsgBuilder.length() > 0) {
            errorMsgBuilder.delete(errorMsgBuilder.length() - 2, errorMsgBuilder.length());
            throw new ExpertException(errorMsgBuilder.toString());
        }
    }
}
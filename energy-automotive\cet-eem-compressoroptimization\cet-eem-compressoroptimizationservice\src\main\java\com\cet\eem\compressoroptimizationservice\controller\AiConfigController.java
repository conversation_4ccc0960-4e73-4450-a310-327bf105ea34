package com.cet.eem.compressoroptimizationservice.controller;

import com.cet.eem.auth.aspect.OperationPermission;
import com.cet.eem.bll.common.def.OperationAuthDef;
import com.cet.eem.bll.common.log.annotation.OperationLog;
import com.cet.eem.bll.common.log.constant.EEMOperationLogType;
import com.cet.eem.bll.common.log.constant.EnumOperationSubType;
import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.bll.compressoroptimization.def.AuthAndLogDef;
import com.cet.eem.bll.compressoroptimization.model.config.CompressorConfig;
import com.cet.eem.bll.compressoroptimization.model.config.CompressorControl;
import com.cet.eem.bll.compressoroptimization.model.config.CompressorSystem;
import com.cet.eem.bll.compressoroptimization.service.config.AiCompressorConfigService;
import com.cet.eem.common.model.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * @ClassName : AiConfigController
 * @Description : ai智控设置
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-17 09:57
 */
@Api(value = "AiConfigController", tags = {"ai智控设置"})
@RestController
@RequestMapping(value = "/eem/v1/compressorOptimization/config")
public class AiConfigController {
    @Autowired
    AiCompressorConfigService aiCompressorConfigService;
    @ApiOperation("查询智控开关的状态")
    @PostMapping(value = "/systemUseAi")
    public Result<CompressorSystem> querySystemUseAi(@RequestParam(required = false) Long systemId) {
        return Result.ok(aiCompressorConfigService.querySystemUseAi(systemId, GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation("查询空压机的智控开关设置")
    @PostMapping(value = "/compressor/useAi")
    public Result<List<CompressorSystem>> queryCompressorUseAi(@RequestParam(required = false) Long systemId) {
        return Result.ok(aiCompressorConfigService.queryCompressorUseAi(systemId,GlobalInfoUtils.getProjectId()));

    }

    @ApiOperation("查询系统配置")
    @PostMapping(value = "/system")
    public Result<CompressorConfig> queryCompressorConfig(@RequestParam(required = false) Long systemId) {
        return Result.ok(aiCompressorConfigService.queryCompressorConfig(systemId,GlobalInfoUtils.getProjectId()));

    }

    @ApiOperation("编辑智控开关的状态")
    @PutMapping(value = "/systemUseAi")
    @OperationPermission(authNames = {AuthAndLogDef.SYSTEM_USE_AI_UPDATE})
    @OperationLog(operationType = AuthAndLogDef.AUTH_COMPRESSOR, subType = EnumOperationSubType.UPDATE, description = "【编辑智控开关的状态】")
    public Result<CompressorSystem> editSystemUseAi(@RequestBody CompressorSystem compressorSystem) {
        return Result.ok(aiCompressorConfigService.editSystemUseAi(compressorSystem,GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation("编辑空压机的智控开关设置")
    @OperationPermission(authNames = {AuthAndLogDef.COMPRESSOR_USE_AI_UPDATE})
    @OperationLog(operationType = AuthAndLogDef.AUTH_COMPRESSOR, subType = EnumOperationSubType.UPDATE, description = "【编辑空压机的智控开关设置】")
    @PutMapping(value = "/compressor/useAi")
    public Result<List<CompressorSystem>> editCompressorUseAi(@RequestBody List<CompressorSystem> compressorControls) {
        return Result.ok(aiCompressorConfigService.editCompressorUseAi(compressorControls,GlobalInfoUtils.getProjectId()));

    }

    @ApiOperation("编辑系统普通配置")
    @OperationPermission(authNames = {AuthAndLogDef.SYSTEM_NORMAL_CONFIG_UPDATE})
    @OperationLog(operationType = AuthAndLogDef.AUTH_COMPRESSOR, subType = EnumOperationSubType.UPDATE, description = "【编辑系统普通配置】")
    @PutMapping(value = "/system/normal")
    public Result<CompressorConfig> editCompressorNormalConfig(@RequestBody CompressorConfig compressorConfig, @RequestParam(required = false) Boolean isNormalConfig) {
        return Result.ok(aiCompressorConfigService.editCompressorConfig(compressorConfig,true,GlobalInfoUtils.getProjectId()));

    }
    @ApiOperation("编辑系统高级配置")
    @OperationPermission(authNames = {AuthAndLogDef.SYSTEM_HIGH_CONFIG_UPDATE})
    @OperationLog(operationType = AuthAndLogDef.AUTH_COMPRESSOR, subType = EnumOperationSubType.UPDATE, description = "【编辑系统高级配置】")
    @PutMapping(value = "/system")
    public Result<CompressorConfig> editCompressorConfig(@RequestBody CompressorConfig compressorConfig, @RequestParam(required = false) Boolean isNormalConfig) {
        return Result.ok(aiCompressorConfigService.editCompressorConfig(compressorConfig,false,GlobalInfoUtils.getProjectId()));

    }
}
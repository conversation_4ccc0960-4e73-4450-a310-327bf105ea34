package com.cet.eem.depletion.model.Constant;

public interface Constant {
    //电泳池
    String CATAPHORESIS = "cataphoresis";
    //过车信息表
    String PASSPOINTINFO = "passpointinfo";
    //升温信息表
    String TEMPPRISEINFO = "tempriseinfo";
    //空耗信息表
    String DEPLETIONPARTITION = "depletionpartition";
    //空耗能耗表
    String DEPLETIONCONSUMPTION = "depletionconsumption";
    //空耗类型-总空耗
    Integer WASTETYPE_ALL = 0;
    //空耗类型-升温空耗
    int DEPLTIONETYPE_HEATING = 1;
    //空耗类型-过车空耗
    int DEPLTIONETYPE_PASSING = 2;
    //空耗类型-停产空耗
    int DEPLTIONETYPE_STOPPING = 3;
    //空耗类型-过线空耗
    int DEPLTIONETYPE_OVER = 4;
    //空耗类型-进车空耗
    int DEPLTIONETYPE_ENTER = 5;

    String DEPLETIONTYPE = "depletiontype";

    String LOGTIME = "logtime";

    String OBJECTID = "objectid";

    String OBJECTLABEL = "objectlabel";

    String PASSTIME = "passtime";

    String PASS_POINT_INFO = "passpointinfo";

    String STATIONCODE = "stationcode";

    //事件等级，其他-0
    Integer EVENTCLASSES = 0;
    //事件等级 一版-3
    Integer EVENTCLASSES_COMMON = 3;

    //事件类型，开关闭合-3
    Integer EVENTTYPE_OPEN = 3;

    //事件类型，开关打开-4
    Integer EVENTTYPE_CLOSE = 4;

    String AVERAGEPASSTIME = "averagepasstime";

    //空耗线体类型，电泳池-1
    Integer LINEBODYTYPE_CATA = 1;

    //空耗线体类型，线体-2
    Integer LINEBODYTYPE_LINE = 2;

    //空耗线体类型，空调-3
    Integer LINEBODYTYPE_AIRCONDITION = 3;

    String DEPLETIONID = "depletionid";

    String LINEBODYTYPE = "linebodytype";

    Long ONE_DAY = 86400000L;
    Long ONE_S = 1000L;
}

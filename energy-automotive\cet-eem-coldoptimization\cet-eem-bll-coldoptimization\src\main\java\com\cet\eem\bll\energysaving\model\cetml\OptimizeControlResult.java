package com.cet.eem.bll.energysaving.model.cetml;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : OptimizeControlResult
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-06-08 09:51
 */
@Getter
@Setter
public class OptimizeControlResult {

    @JsonProperty("logtime")
    private Long logTime;
    @JsonProperty("plate_enabled")
    private Boolean plateEnabled;
    @JsonProperty("cooling_enabled")
    private Boolean coolingEnabled;
    @JsonProperty("cooling_results")
    private CoolingResult coolingResult;

}
package com.cet.eem.bll.energysaving.dao.aioptimization;

import com.cet.eem.bll.energysaving.model.config.EquipmentCurve;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.dao.BaseModelDao;

import java.util.List;

/**
 * @ClassName : EquipmentCurveDao
 * @Description : 效率运行曲线的dao
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-24 16:50
 */
public interface EquipmentCurveDao extends BaseModelDao<EquipmentCurve> {
    /**
     * 通过节点去查
     * @param label
     * @param id
     * @param type
     * @return
     */
    List<EquipmentCurve> queryEquipmentCurve(String label,Long id,List<Integer> type);

    /**
     * 批量查询
     * @param baseVos
     * @param type
     * @return
     */
    List<EquipmentCurve> queryCurveList(List<BaseVo> baseVos,List<Integer> type);
}
package com.cet.eem.depletion.model.Constant;

import com.cet.eem.bll.common.def.quantity.FrequencyDef;
import com.cet.eem.bll.common.def.quantity.PhasorDef;
import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;
import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;
import com.cet.eem.common.constant.EnergyTypeDef;
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;

public class QuantityDef {

    /**
     * 电泳池-实时温度物理量
     * */
    public static QuantitySearchVo getTempOfWaterTank() {
        return new QuantitySearchVo(6000567,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.INSTANTANEOUS,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                1);
    }

    /**
     * 空调温度
     * */
    public static QuantitySearchVo getTempOfAirCondition() {
        return new QuantitySearchVo(6000465,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.INSTANTANEOUS,
                FrequencyDef.NONE,
                PhasorDef.RETURN,
                1);
    }

    /**
     * 空调达标温度设定
     * @return
     */
    public static QuantitySearchVo getAirConditionerTemperatureSetting() {
        return new QuantitySearchVo(6000001,
                QuantityCategoryDef.TEMP, //物理量类别
                191, //物理量类型
                FrequencyDef.NONE,//频率
                PhasorDef.RETURN,//相别21
                1); //能源
    }

    /**
     * 空调出风湿度设定
     * @return
     */
    public static QuantitySearchVo getAirConditioningHumiditySetting() {
        return new QuantitySearchVo(6000002,
                QuantityCategoryDef.HUMIDITY,
                191,
                FrequencyDef.NONE,
                PhasorDef.RETURN,
                1);
    }
    /**
     * 空调湿度
     * */
    public static QuantitySearchVo getHumidityOfAirCondition() {
        return new QuantitySearchVo(6000466,
                QuantityCategoryDef.HUMIDITY,
                QuantityTypeDef.INSTANTANEOUS,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                1);
    }

    /**
     * 电泳池-达标温度物理量
     * */
    public static QuantitySearchVo getStandardTempOfWaterTank() {
        return new QuantitySearchVo(6000467,
                QuantityCategoryDef.TEMP,
                192,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                1);
    }

    /**
     * 电泳池-产线开机时刻
     * */
    public static QuantitySearchVo getProductionLineStartupTime() {
        return new QuantitySearchVo(6004008,
                QuantityCategoryDef.DEVICE_WORK_TIME,
                189,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                1);
    }

    /**
     * 电泳池-产线关机时刻
     * */
    public static QuantitySearchVo getProductionLineShutDownTime() {
        return new QuantitySearchVo(6004009,
                QuantityCategoryDef.DEVICE_WORK_TIME,
                190,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                1);
    }

    public static QuantitySearchVo getPower() {
        return new QuantitySearchVo(4000004,
                QuantityCategoryDef.ELECTRIC,
                QuantityTypeDef.P_INTG_POS,
                FrequencyDef.NONE,
                PhasorDef.TOTAL,
                EnergyTypeDef.ELECTRIC);
    }

    public static QuantitySearchVo getEnergyAccumulation(Integer energyType) {
        return new QuantitySearchVo(10000001,
                21,
                QuantityTypeDef.INSTANTANEOUS,
                1,
                PhasorDef.TOTAL,
                energyType);
    }
}

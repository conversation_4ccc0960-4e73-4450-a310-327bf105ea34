package com.cet.eem.compressorservice.model.vo;

import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * @ClassName : ElectricalRatioForm
 * <AUTHOR> yangy
 * @Date: 2022-04-06 11:41
 * 电器比分析form.
 */
@Getter
@Setter
public class ElectricalRatioForm {
    Long roomId;

    Integer aggregationCycle;

    Long startTime;

    Long endTime;

    Integer type;

    public Integer getAggregationCycle() {
        if (Objects.equals(aggregationCycle, AggregationCycle.ONE_MONTH)) {
            return AggregationCycle.ONE_DAY;
        } else if (Objects.equals(aggregationCycle, AggregationCycle.ONE_DAY)) {
            return AggregationCycle.ONE_HOUR;
        }else if (Objects.equals(aggregationCycle,AggregationCycle.ONE_YEAR)){
            return AggregationCycle.ONE_MONTH;
        }
        return aggregationCycle;
    }
}

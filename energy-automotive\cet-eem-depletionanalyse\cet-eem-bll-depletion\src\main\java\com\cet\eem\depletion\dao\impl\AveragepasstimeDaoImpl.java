package com.cet.eem.depletion.dao.impl;

import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.depletion.dao.AveragepasstimeDao;
import com.cet.eem.depletion.model.AveragePassTimeDto;
import com.cet.eem.depletion.model.Constant.Constant;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AveragepasstimeDaoImpl implements AveragepasstimeDao {
    @Autowired
    ModelServiceUtils modelServiceUtils;


    @Override
    public List<AveragePassTimeDto> queryByParam(Long objId, String label) {
        QueryCondition condition = new QueryConditionBuilder<>(Constant.AVERAGEPASSTIME)
                .eq(ColumnDef.OBJECTID, objId)
                .eq(ColumnDef.OBJECTLABEL, label)
                .build();
        return modelServiceUtils.query(condition, AveragePassTimeDto.class);
    }
}

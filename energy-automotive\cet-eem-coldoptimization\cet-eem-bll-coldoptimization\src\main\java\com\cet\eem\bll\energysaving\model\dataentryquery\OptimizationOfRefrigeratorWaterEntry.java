package com.cet.eem.bll.energysaving.model.dataentryquery;


import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : OptimizationOfRefrigeratorWaterEntry
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-14 11:34
 */
@Getter
@Setter
public class OptimizationOfRefrigeratorWaterEntry {
    private Long projectId;
    private Long systemId;
    /**
     * 冷量需求预测值
     */
    private List<DatalogValue> coldPredict;
    /**
     * 总冷负荷值
     */
    private List<DatalogValue> totalColdLoad;
    /**
     * 冷冻水总管回水温度
     */
    private List<SingleDataEntry> endReturnWaterTemp;
    /**
     * 冷冻水总管流量
     */
    private List<SingleDataEntry> freezingPipelineFlow;

    /**
     * 历史出水温度设定
     */
    private List<SingleDataEntry> returnWaterSet;
}
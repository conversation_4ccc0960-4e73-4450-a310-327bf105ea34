package com.cet.eem.depletion.dao.impl;

import com.cet.eem.bll.common.model.batchnodes.PipeLine;
import com.cet.eem.common.constant.EnumDataTypeId;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.feign.DeviceDataService;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.model.event.EventCondition;
import com.cet.eem.common.model.event.EventLogVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.depletion.dao.DepletionDataDao;
import com.cet.eem.depletion.model.AveragePassTimeDto;
import com.cet.eem.depletion.model.CataphoresisDto;
import com.cet.eem.depletion.model.Constant.Constant;
import com.cet.eem.depletion.model.DepletionPartitionDto;
import com.cet.eem.depletion.model.MesPassPointDto;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;
import com.cet.eem.quantity.service.QuantityManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
public class DepletionDataDaoImpl implements DepletionDataDao {

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    QuantityManageService quantityManageService;

    @Autowired
    DeviceDataService deviceDataService;

    @Override
    public List<CataphoresisDto> queryCataphoresis(List<Integer> lineBodyType) {
        QueryCondition queryCondition = new QueryConditionBuilder<>(Constant.CATAPHORESIS).in(Constant.LINEBODYTYPE, lineBodyType).build();
        return modelServiceUtils.query(queryCondition, CataphoresisDto.class);
    }

    @Override
    public List<MesPassPointDto> queryMesPassPointData(Long startTime, Long endTime, List<String> stationCode) {
        QueryCondition queryCondition = new QueryConditionBuilder<>(Constant.PASS_POINT_INFO)
                .ge(Constant.PASSTIME, startTime)
                .lt(Constant.PASSTIME, endTime)
                .in(Constant.STATIONCODE, stationCode)
                .build();
        return modelServiceUtils.query(queryCondition, MesPassPointDto.class);
    }

    @Override
    public Map<Integer, List<TrendDataVo>> queryQuantityData(List<BaseVo> baseVos, Long startTime, Long endTime, List<QuantitySearchVo> quantitySearchVo) {
        QuantityDataBatchSearchVo searchVo = getQuantityDataBatchSearchVo(baseVos, quantitySearchVo, startTime, endTime);
        return quantityManageService.queryDataLogBatch(searchVo);
    }

    @Override
    public List<DepletionPartitionDto> queryDepletionPartition(Long startTime, Long endTime) {
        QueryCondition queryCondition = new QueryConditionBuilder<>(Constant.DEPLETIONPARTITION)
                .ge(Constant.LOGTIME, startTime)
                .lt(Constant.LOGTIME, endTime)
                .build();
        return modelServiceUtils.query(queryCondition, DepletionPartitionDto.class);
    }

    @Override
    public List<PipeLine> queryPipeLine(List<Long> idList) {
        QueryCondition queryCondition = new QueryConditionBuilder<>(NodeLabelDef.PIPELINE).in(ColumnDef.ID, idList).build();
        return modelServiceUtils.query(queryCondition, PipeLine.class);
    }


    @Override
    public List<Long> queryProductionLineEventTime(Long startTime, Long endTime, EventCondition condition) {
        List<EventLogVo> data = deviceDataService.queryEventData(0, 20000, startTime, endTime, condition).getData();
        if (Objects.isNull(data) || data.isEmpty()){
            return new ArrayList<>();
        }
        return data.stream().map(EventLogVo::getEventTime).collect(Collectors.toList());
    }

    private QuantityDataBatchSearchVo getQuantityDataBatchSearchVo(List<BaseVo> nodes, List<QuantitySearchVo> quantitySearchVo, Long startTime, Long endTime) {
        QuantityDataBatchSearchVo searchVo = new QuantityDataBatchSearchVo();
        searchVo.setStartTime(startTime);
        searchVo.setEndTime(endTime);
        searchVo.setAggregationCycle(AggregationCycle.FIVE_MINUTES);
        searchVo.setDataTypeId(EnumDataTypeId.REALTIME.getId());
        searchVo.setNodes(nodes);
        searchVo.setQuantitySettings(quantitySearchVo);
        return searchVo;
    }

    @Override
    public List<AveragePassTimeDto> queryAveragePassTime(){
        QueryCondition queryCondition = new QueryConditionBuilder<>(Constant.AVERAGEPASSTIME).build();
        return modelServiceUtils.query(queryCondition, AveragePassTimeDto.class);
    }
}

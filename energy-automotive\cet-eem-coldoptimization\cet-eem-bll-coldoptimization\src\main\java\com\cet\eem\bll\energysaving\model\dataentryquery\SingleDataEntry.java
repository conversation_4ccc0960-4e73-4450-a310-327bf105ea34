package com.cet.eem.bll.energysaving.model.dataentryquery;

import com.cet.eem.common.model.datalog.DataLogData;
import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : SingleDataEntry
 * @Description : 单个设备输入数据
 * <AUTHOR> jiangzixuan
 * @Date: 2022-07-15 08:56
 */
@Getter
@Setter
public class SingleDataEntry {
    private Long objectId;
    private String objectLabel;
    /**
     * 冷冻管瞬时冷量
     */
    private List<DatalogValue> dataList;
}
package com.cet.eem.bll.energysaving.feign;

import com.cet.eem.bll.energysaving.model.cetml.ColdLoadQueryParam;
import com.cet.eem.bll.energysaving.model.cetml.ColdMachineControlParam;
import com.cet.eem.bll.energysaving.model.cetml.CopQueryParam;
import com.cet.eem.bll.energysaving.model.cetml.PumpFitParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @ClassName : CetmlNewFeignService
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-06-07 13:34
 */
@FeignClient(value = "cetml-service", url = "${cet.eem.service.url.cetml-service:''}")
public interface CetmlNewFeignService {
    /**
     * 冷负荷预测
     * @param coldLoadQueryParams
     * @return
     */
    @PostMapping(value = {"/cetml/spotlight/cold_load/predict"})
    String clodLoadPredict(@RequestBody List<ColdLoadQueryParam> coldLoadQueryParams);
    /**
     * 冷机优化控制
     * @param coldLoadQueryParams
     * @return
     */
    @PostMapping(value = {"/cetml/spotlight/cooler"})
    String clodMachineControl(@RequestBody List<ColdMachineControlParam> coldLoadQueryParams);
    /**
     * cop拟合
     * @param copQueryParams
     * @return
     */
    @PostMapping(value = {"/cetml/spotlight/copfit"})
    String cop(@RequestBody List<CopQueryParam> copQueryParams);
    /**
     * 冷冻泵功率和总管流量拟合接口（针对单个冷机）
     * @param pumpFitParams
     * @return
     */
    @PostMapping(value = {"/cetml/spotlight/pumpfit"})
    String pumpFit(@RequestBody List<PumpFitParam> pumpFitParams);
}

package com.cet.eem.bll.energysaving.model.weather;

import com.cet.eem.weather.vo.WeatherMainDataWithTime;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : CurrentWeather
 * @Description :
 * <AUTHOR> jiang<PERSON>xuan
 * @Date: 2021-12-15 09:52
 */
@Getter
@Setter
public class CurrentWeather extends WeatherMainDataWithTime {
    @JsonProperty("citycode")
    private Long cityCode;

    private Double lon;
    private Double lat;
    private String cityname;
    private Double uvi;
    private Double rain;
}
package com.cet.eem.bll.energysaving.handle;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @ClassName : AiScheduleConfig
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-09-19 15:48
 */
@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "cet.eem.task.energy-saving.control")
@Slf4j
public class AiScheduleConfig {

    /**
     * 没有配置延时的情况的等待时间
     */
    private Integer delayed;
    /**
     * 反应时间
     */
    private Integer operation;
    /**
     * 每次等待一次遥控、调执行后的等待时间
     */
    private Integer wait;
    /**
     * 重试时间
     */
    private Integer retryCount;

}
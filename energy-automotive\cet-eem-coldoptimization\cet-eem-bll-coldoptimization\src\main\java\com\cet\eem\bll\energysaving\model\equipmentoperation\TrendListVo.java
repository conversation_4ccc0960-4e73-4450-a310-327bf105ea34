package com.cet.eem.bll.energysaving.model.equipmentoperation;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : TrendDataVo
 * @Description : 设备运行曲线第二种返回值
 * <AUTHOR> ji<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-06-16 16:20
 */
@Getter
@Setter
public class TrendListVo {
    private String objectLabel;
    private Long objectId;
    private String objectName;
    private List<BasicData> basicData;
    private List<BasicData> basicDataList;
}
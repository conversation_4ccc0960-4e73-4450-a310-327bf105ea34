package com.cet.eem.bll.compressoroptimization.model.strategy;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.bll.compressoroptimization.def.CompressorOptimizationLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : StrategyObjectConfig
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-17 13:46
 */
@Getter
@Setter
@ModelLabel(CompressorOptimizationLabelDef.STRATEGY_OBJECT_CONFIG)
public class StrategyObjectConfig extends BaseEntity {
    @ApiModelProperty("空压机节点id")
    @JsonProperty(ColumnDef.OBJECTID)
    private Long objectId ;
    @ApiModelProperty("空压机节点label")
    @JsonProperty(ColumnDef.OBJECTLABEL)
    private String objectLabel ;
    @ApiModelProperty("策略发出时的系统压力")
    @JsonProperty("systempressurecurrent")
    private Double systemPressureCurrent ;
    @ApiModelProperty("建议设定的空压机压力下限")
    @JsonProperty("advicepressuremin")
    private Double advicePressureMin ;
    @ApiModelProperty("建议设定的空压机压力上限")
    @JsonProperty("advicepressuremax")
    private Double advicePressureMax ;
    @ApiModelProperty("用户id")
    @JsonProperty(ColumnDef.USERID)
    private Long userId ;
    @ApiModelProperty("操作人名称")
    @JsonProperty(ColumnDef.OPERATOR)
    private String operator ;
//    @ApiModelProperty("操作时间")
//    @JsonProperty("operationtime")
//    private Long operationTime ;
    @ApiModelProperty("是否执行成功")
    @JsonProperty("isexecutesuccess")
    private Boolean isExecuteSuccess ;
   public StrategyObjectConfig(){
       this.modelLabel=CompressorOptimizationLabelDef.STRATEGY_OBJECT_CONFIG;
   }
}
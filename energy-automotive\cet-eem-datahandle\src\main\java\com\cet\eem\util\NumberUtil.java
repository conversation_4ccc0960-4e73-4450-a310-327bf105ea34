package com.cet.eem.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2024/8/29 8:15
 */
public class NumberUtil {

    public static int compare(double v1, double v2, int scale) {
        // 使用BigDecimal来定义数值，并设置精度为小数点后两位
        BigDecimal bd1 = new BigDecimal(Double.toString(v1)).setScale(scale, RoundingMode.HALF_UP);
        BigDecimal bd2 = new BigDecimal(Double.toString(v2)).setScale(scale, RoundingMode.HALF_UP);
        return bd1.compareTo(bd2);
    }

}

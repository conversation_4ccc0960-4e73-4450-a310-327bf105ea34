package com.cet.eem;

import com.cet.eem.annotation.EnableModel;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(scanBasePackages = {"com.cet.electric.powermanage.**", "com.cet.eem.*.**", "com.cet.electric.*.**"
        , "com.cet.powercloud.**", "com.cet.eem.datatransportservice", "com.cet.eem.bll.datatransport.*"})
@EnableDiscoveryClient
@EnableCaching
@EnableFeignClients(basePackages = {"com.cet.eem.*", "com.cet.electric.*.**", "com.cet.powercloud.**"})
@EnableModel
@EnableScheduling
@MapperScan(value = {"com.cet.eem.bll.datatransport.mapper", "com.cet.eem.bll.energy.mapping"})
public class MesDataTransportApplication {
    public static void main(String[] args) {
        SpringApplication.run(MesDataTransportApplication.class, args);
    }
}

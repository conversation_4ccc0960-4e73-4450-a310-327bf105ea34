package com.cet.eem.depletion.dao.impl;

import com.cet.eem.depletion.dao.PasspointinfoDao;
import com.cet.eem.depletion.model.Constant.Constant;
import com.cet.eem.depletion.model.PassPointInfoDto;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PasspointinfoDaoImpl implements PasspointinfoDao {
    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Override
    public List<PassPointInfoDto> queryByParam(Long st, Long end, String stationCode) {
        QueryCondition condition = new QueryConditionBuilder<>(Constant.PASSPOINTINFO)
                .ge(Constant.PASSTIME, st)
                .lt(Constant.PASSTIME, end)
                .eq(Constant.STATIONCODE, stationCode)
                .build();
        return modelServiceUtils.query(condition, PassPointInfoDto.class);
    }
}

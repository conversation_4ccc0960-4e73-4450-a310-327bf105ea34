{"groups": [{"name": "cet.eem.task.energy-saving.control", "type": "com.cet.eem.bll.energysaving.handle.AiScheduleConfig", "sourceType": "com.cet.eem.bll.energysaving.handle.AiScheduleConfig"}, {"name": "cet.eem.task.energy-saving.curvefitting", "type": "com.cet.eem.bll.energysaving.config.colddata.CurveFittingConfig", "sourceType": "com.cet.eem.bll.energysaving.config.colddata.CurveFittingConfig"}, {"name": "cet.eem.task.energy-saving.end-cold-capacity", "type": "com.cet.eem.bll.energysaving.config.colddata.ColdEndCapacityConfig", "sourceType": "com.cet.eem.bll.energysaving.config.colddata.ColdEndCapacityConfig"}, {"name": "cet.eem.task.energy-saving.loss", "type": "com.cet.eem.bll.energysaving.config.colddata.LossCapacityConfig", "sourceType": "com.cet.eem.bll.energysaving.config.colddata.LossCapacityConfig"}], "properties": [{"name": "cet.eem.task.energy-saving.control.delayed", "type": "java.lang.Integer", "description": "没有配置延时的情况的等待时间", "sourceType": "com.cet.eem.bll.energysaving.handle.AiScheduleConfig"}, {"name": "cet.eem.task.energy-saving.control.operation", "type": "java.lang.Integer", "description": "反应时间", "sourceType": "com.cet.eem.bll.energysaving.handle.AiScheduleConfig"}, {"name": "cet.eem.task.energy-saving.control.retry-count", "type": "java.lang.Integer", "description": "重试时间", "sourceType": "com.cet.eem.bll.energysaving.handle.AiScheduleConfig"}, {"name": "cet.eem.task.energy-saving.control.wait", "type": "java.lang.Integer", "description": "每次等待一次遥控、调执行后的等待时间", "sourceType": "com.cet.eem.bll.energysaving.handle.AiScheduleConfig"}, {"name": "cet.eem.task.energy-saving.curvefitting.internal", "type": "java.lang.String", "description": "定时任务执行时间", "sourceType": "com.cet.eem.bll.energysaving.config.colddata.CurveFittingConfig"}, {"name": "cet.eem.task.energy-saving.curvefitting.rr", "type": "java.lang.Double", "description": "曲线拟合验证R平方", "sourceType": "com.cet.eem.bll.energysaving.config.colddata.CurveFittingConfig"}, {"name": "cet.eem.task.energy-saving.curvefitting.time-range", "type": "java.lang.Integer", "description": "定时记录往前查询范围，单位为month", "sourceType": "com.cet.eem.bll.energysaving.config.colddata.CurveFittingConfig", "defaultValue": 0}, {"name": "cet.eem.task.energy-saving.end-cold-capacity.coef", "type": "java.lang.Double", "description": "单位转换系数", "sourceType": "com.cet.eem.bll.energysaving.config.colddata.ColdEndCapacityConfig"}, {"name": "cet.eem.task.energy-saving.end-cold-capacity.max-query-day", "type": "java.lang.Integer", "description": "最大查询跨度天数", "sourceType": "com.cet.eem.bll.energysaving.config.colddata.ColdEndCapacityConfig"}, {"name": "cet.eem.task.energy-saving.end-cold-capacity.overall-start-time", "type": "java.lang.String", "description": "全局开始时间", "sourceType": "com.cet.eem.bll.energysaving.config.colddata.ColdEndCapacityConfig"}, {"name": "cet.eem.task.energy-saving.end-cold-capacity.specific-volume", "type": "java.lang.Double", "description": "比容值", "sourceType": "com.cet.eem.bll.energysaving.config.colddata.ColdEndCapacityConfig"}, {"name": "cet.eem.task.energy-saving.loss.max-query-day", "type": "java.lang.Integer", "description": "最大查询跨度天数", "sourceType": "com.cet.eem.bll.energysaving.config.colddata.LossCapacityConfig"}, {"name": "cet.eem.task.energy-saving.loss.overall-start-time", "type": "java.lang.String", "description": "全局开始时间", "sourceType": "com.cet.eem.bll.energysaving.config.colddata.LossCapacityConfig"}, {"name": "cet.eem.task.energy-saving.loss.temp-limit", "type": "java.lang.Double", "description": "温度最大值，超出过滤", "sourceType": "com.cet.eem.bll.energysaving.config.colddata.LossCapacityConfig"}], "hints": []}
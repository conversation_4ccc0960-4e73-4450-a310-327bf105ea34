package com.cet.eem.bll.energysaving.model.dataentryquery;

import com.cet.eem.common.model.datalog.DataLogData;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : RefrigerateOfExchangerVo
 * @Description : 板式换热器制冷量求解查询返回值
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-14 09:22
 */
@Getter
@Setter
public class RefrigerateOfExchangerVo {
    /**
     * 历史冷却水供水温度
     */
    private Double  coolingWaterSupplyTemp;
    /**
     * 历史冷冻水回水温度
     */
    private Double freezingWaterReturnTemp;
    /**
     * 仅开板换时总冷量表读数（板换是手动阀，通过板换上的水流开关判断板换的开启）
     */
    private Double totalCoolingCapacity;
    private Long logTime;
}
package com.cet.eem.bll.achievementrate.model.pojo;

import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/3/15 14:38
 */
@Data
public class AchievementRate {
    private Long objectid;
    private String objectlabel;
    private Double rate;
    private Integer type;
    private Long logtime;
    private Integer aggregationcycle;
    private Long projectid;
    private Long updatetime;
    private Integer energytype;
    private Integer producttype;
    private Long Indicatortype;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AchievementRate that = (AchievementRate) o;
        return Objects.equals(objectid, that.objectid) && Objects.equals(objectlabel, that.objectlabel) && Objects.equals(type, that.type)
                && Objects.equals(logtime, that.logtime) && Objects.equals(aggregationcycle, that.aggregationcycle) && Objects.equals(energytype, that.energytype) && Objects.equals(producttype, that.producttype) && Objects.equals(Indicatortype, that.Indicatortype);
    }

    @Override
    public int hashCode() {
        return Objects.hash(objectid, objectlabel, type, logtime, aggregationcycle, energytype, producttype, Indicatortype);
    }
}

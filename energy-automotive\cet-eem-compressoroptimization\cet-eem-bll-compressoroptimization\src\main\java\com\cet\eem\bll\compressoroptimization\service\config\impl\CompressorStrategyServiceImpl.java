package com.cet.eem.bll.compressoroptimization.service.config.impl;

import com.cet.eem.bll.common.dao.node.NodeDao;
import com.cet.eem.bll.compressoroptimization.dao.AiCompressorStrategyDao;
import com.cet.eem.bll.compressoroptimization.dao.CompressorConfigDao;
import com.cet.eem.bll.compressoroptimization.def.CompressorStrategyType;
import com.cet.eem.bll.compressoroptimization.model.config.CompressorConfig;
import com.cet.eem.bll.compressoroptimization.model.strategy.AiCompressorStrategyWithLayer;
import com.cet.eem.bll.compressoroptimization.model.strategy.StrategyObjectConfig;
import com.cet.eem.bll.compressoroptimization.model.strategy.StrategyQueryParam;
import com.cet.eem.bll.compressoroptimization.model.strategy.StrategyReturnVo;
import com.cet.eem.bll.compressoroptimization.service.config.CompressorStrategyService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.ibm.icu.text.DecimalFormat;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName : CompressorStrategyServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-24 15:46
 */
@Service
public class CompressorStrategyServiceImpl implements CompressorStrategyService {
    @Autowired
    AiCompressorStrategyDao aiCompressorStrategyDao;
    @Autowired
    NodeDao nodeDao;
    @Autowired
    CompressorConfigDao compressorConfigDao;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    public static final Integer QUERY_ALL = 0;

    @Override
    public StrategyReturnVo queryRealtime(Long systemId, Long projectId) {
        //查询当天的策略，按时间倒序排列
        List<AiCompressorStrategyWithLayer> aiCompressorStrategyWithLayers = aiCompressorStrategyDao.queryCompressorStrategy(
                TimeUtil.getFirstTimeOfDay(System.currentTimeMillis()),
                null, systemId, projectId, CompressorStrategyType.AI);
        if (CollectionUtils.isEmpty(aiCompressorStrategyWithLayers)) {
            return new StrategyReturnVo();
        }
        AiCompressorStrategyWithLayer strategyWithLayer = aiCompressorStrategyWithLayers.get(0);
        //查询系统配置详情，配置相应的策略信息描述
        CompressorConfig config = compressorConfigDao.queryCompressorConfig(systemId, projectId);
        String desc = assembleDesc(strategyWithLayer, config);
        StrategyReturnVo returnVo = new StrategyReturnVo();
        BeanUtils.copyProperties(strategyWithLayer, returnVo);
        returnVo.setDesc(desc);
        return returnVo;
    }


    @Override
    public List<StrategyReturnVo> queryHistory(StrategyQueryParam queryParam, Long projectId) {
        Integer queryType = null;
        if (!Objects.equals(queryParam.getQueryType(), QUERY_ALL)) {
            queryType = queryParam.getQueryType();
        }
        //查询策略信息
        List<AiCompressorStrategyWithLayer> aiCompressorStrategyWithLayers = aiCompressorStrategyDao.queryCompressorStrategy(
                TimeUtil.localDateTime2timestamp(queryParam.getStartTime()),
                TimeUtil.localDateTime2timestamp(queryParam.getEndTime()), queryParam.getSystemId(), projectId, queryType);
        if (CollectionUtils.isEmpty(aiCompressorStrategyWithLayers)) {
            return Collections.emptyList();
        }
        //查询空压系统配置
        CompressorConfig config = compressorConfigDao.queryCompressorConfig(queryParam.getSystemId(), projectId);
        List<StrategyReturnVo> result = new ArrayList<>();
        for (AiCompressorStrategyWithLayer layer : aiCompressorStrategyWithLayers) {
            StrategyReturnVo returnVo = new StrategyReturnVo();
            BeanUtils.copyProperties(layer, returnVo);
            //按策略类型拼接
            if (Objects.equals(layer.getStrategyType(), CompressorStrategyType.AI)) {
                returnVo.setDesc(assembleDesc(layer, config));
            } else {
                returnVo.setDesc(assembleNoneAiDesc(layer));
            }
            if (CollectionUtils.isNotEmpty(layer.getStrategyObjectConfigs())) {
                returnVo.setIsExecuteSuccess(layer.getStrategyObjectConfigs().get(0).getIsExecuteSuccess());
            }
            result.add(returnVo);
        }
        return result;

    }

    private String assembleNoneAiDesc(AiCompressorStrategyWithLayer strategyWithLayer) {
        List<StrategyObjectConfig> strategyObjectConfigs = strategyWithLayer.getStrategyObjectConfigs();
        if (CollectionUtils.isEmpty(strategyObjectConfigs)) {
            return StringUtils.EMPTY;
        }
        //时间，操作者”关闭了AI智控，现处于非AI智控模式”的内容；
        String format = TimeUtil.format(strategyWithLayer.getOperationTime(), TimeUtil.LONG_TIME_FORMAT);
        return String.format(Locale.getDefault(), "%s，%s关闭了AI智控，现处于非AI智控模式。", format,
                strategyWithLayer.getStrategyObjectConfigs().get(0).getOperator());
//        return format + "，" + strategyWithLayer.getStrategyObjectConfigs().get(0).getOperator() + "关闭了AI智控，现处于非AI智控模式。";
    }

    private String assembleDesc(AiCompressorStrategyWithLayer strategyWithLayer, CompressorConfig config) {
        List<StrategyObjectConfig> strategyObjectConfigs = strategyWithLayer.getStrategyObjectConfigs();
        if (CollectionUtils.isEmpty(strategyObjectConfigs)) {
            return StringUtils.EMPTY;
        }
        //有低于下限和高于上限的说法
        StrategyObjectConfig strategyObjectConfig = strategyObjectConfigs.get(0);
        Double current = strategyObjectConfig.getSystemPressureCurrent();
        Double advicePressureMax = config.getSystemPressureMax();
        Double advicePressureMin = config.getSystemPressureMin();
        Double power = null;
        String powerCompare = "";
        if (current < advicePressureMin) {
            powerCompare = "低于";
            power = advicePressureMin;
        } else if (current > advicePressureMax) {
            powerCompare = "高于";
            power = advicePressureMax;
        }
        List<BaseVo> nodes = strategyObjectConfigs.stream().filter(strategyObjectConfig1 -> Objects.nonNull(strategyObjectConfig1.getObjectId()))
                .map(strategyObjectConfig1 -> new BaseVo(strategyObjectConfig1.getObjectId(), strategyObjectConfig1.getObjectLabel()))
                .collect(Collectors.toList());
        List<BaseVo> baseVos = nodeDao.queryNodeName(nodes);
        List<String> names = baseVos.stream().map(BaseVo::getName).collect(Collectors.toList());
        String join = StringUtils.join(names, "、");
        return String.format(Locale.getDefault(), "系统压力%s%sbar，请开启%s，%s", powerCompare,
                power, join,assemblePowerWithDevice(baseVos, strategyObjectConfigs));

    }

    private String assemblePowerWithDevice(List<BaseVo> baseVos, List<StrategyObjectConfig> strategyObjectConfigs) {
        Map<BaseVo, StrategyObjectConfig> objectConfigMap = strategyObjectConfigs.stream()
                .collect(Collectors.toMap(strategyObjectConfig ->
                        new BaseVo(strategyObjectConfig.getObjectId(), strategyObjectConfig.getObjectLabel()), Function.identity()));
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < baseVos.size(); i++) {
            BaseVo baseVo = baseVos.get(i);
            StrategyObjectConfig strategyObjectConfig = objectConfigMap.get(new BaseVo(baseVo.getId(), baseVo.getModelLabel()));
            String end = "、";
            if (Objects.equals(i, baseVos.size() - 1)) {
                end = "。";
            }
            if (Objects.nonNull(strategyObjectConfig)) {
                String format = String.format(Locale.getDefault(), "请将%s的上下限压力设置为【%s-%sbar】%s", baseVo.getName(), handleDouble(strategyObjectConfig.getAdvicePressureMin()),
                        handleDouble(strategyObjectConfig.getAdvicePressureMax()),end);
                builder.append(format);
            }
        }

        return builder.toString();
    }
    private String handleDouble(Double value) {
        if (Objects.isNull(value)) {
            return CommonUtils.BLANK_STR;
        }
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(value);
    }
}
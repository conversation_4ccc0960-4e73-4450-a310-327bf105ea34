package com.cet.eem.constforecast.model.vo;

import com.cet.eem.bll.common.model.domain.object.physicalquantity.UserDefineUnit;
import lombok.Data;

import java.util.List;

/**
 * @ClassName : CostForecastResult
 * <AUTHOR> yangy
 * @Date: 2022-09-05 17:17
 */
@Data
public class CostForecastResult {
    List<FostForecast> datas;
    UserDefineUnit unit;
    List<AvgUnitPrice> unitPrices;

    public CostForecastResult(List<FostForecast> datas, UserDefineUnit unit, List<AvgUnitPrice> unitPrices) {
        this.datas = datas;
        this.unit = unit;
        this.unitPrices = unitPrices;
    }

    public CostForecastResult() {
    }
}

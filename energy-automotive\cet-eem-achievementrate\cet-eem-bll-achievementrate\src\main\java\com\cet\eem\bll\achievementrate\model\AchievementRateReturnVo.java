package com.cet.eem.bll.achievementrate.model;

import com.cet.eem.bll.common.model.input.TypeInDataDetailVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : AchievementRateReturnDataVo
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-11-20 15:04
 */
@Getter
@Setter
public class AchievementRateReturnVo {
    @ApiModelProperty("详细能耗数据")
    private List<TypeInDataDetailVo> details;
    /**
     * 能源类型
     */
    @ApiModelProperty(value = "能源类型")
    protected Integer energyType;
    @ApiModelProperty(value = "能源类型名称")
    private String energyTypeName;
    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    protected Integer productType;
    @ApiModelProperty(value = "产品类型名称")
    private String productTypeName;
    /**
     * 指标id
     */
    @ApiModelProperty(value = "指标id")
    protected Long effSetId;

    @ApiModelProperty(value = "指标名称")
    private String effSetName;

    @ApiModelProperty(value = "聚合周期")
    private Integer aggregationCycle;

    @ApiModelProperty(value = "聚合周期名称")
    private String aggregationCycleName;

    @ApiModelProperty(value = "单位")
    private String unit;
    /**
     * 排序用
     */
    private Long energyTypeId;
    private Long productId;
    /**
     * 0是普通，1是折标
     */
    private Integer isCoef;
}
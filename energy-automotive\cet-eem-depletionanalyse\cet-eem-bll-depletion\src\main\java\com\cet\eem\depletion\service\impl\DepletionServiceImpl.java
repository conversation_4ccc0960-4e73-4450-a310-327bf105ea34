package com.cet.eem.depletion.service.impl;

import com.cet.eem.common.constant.EnumDataTypeId;
import com.cet.eem.common.exception.ValidationException;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.DataLogData;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.model.event.EventCondition;
import com.cet.eem.common.model.event.EventLogVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.depletion.config.DepletionCfg;
import com.cet.eem.depletion.dao.*;
import com.cet.eem.depletion.model.*;
import com.cet.eem.depletion.model.Constant.Constant;
import com.cet.eem.depletion.model.Constant.DepletionType;
import com.cet.eem.depletion.model.Constant.EnumEnergyType;
import com.cet.eem.depletion.model.Constant.QuantityDef;
import com.cet.eem.depletion.model.vo.*;
import com.cet.eem.depletion.service.DepletionService;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;
import com.cet.eem.quantity.service.QuantityManageService;
import com.cet.eem.toolkit.CollectionUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DepletionServiceImpl implements DepletionService {

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    QuantityManageService quantityManageService;

    @Autowired
    DepletionDataDao depletionDataDao;

    @Autowired
    DepletionCfg depletionCfg;
    @Autowired
    DepletionpartitionDao depletionpartitionDao;
    @Autowired
    DepletionconsumptionDao depletionconsumptionDao;
    @Autowired
    TempriseinfoDao tempriseinfoDao;
    @Autowired
    CataphoresisDao cataphoresisDao;
    @Autowired
    PasspointinfoDao passpointinfoDao;
    @Autowired
    AveragepasstimeDao averagepasstimeDao;
    @Autowired
    PartittionCommonService commonService;

    public static final Long ONE_DAY = 86400000L;

    //默认节拍
    @Value("${cet.eem.depletion.takttime:77.4}")
    public Double TAKTTIME;
    @Value("${cet.eem.depletion.averagetime:3600}")
    public Long averageTime;
    @Value("${cet.eem.depletion.allowtime:1200}")
    public Long ALLOWTIME;

    @Override
    public WasteOverView depletionOverView(Long time, Long id, String modelLabel) {
        WasteOverView res = new WasteOverView();
        List<DepletionPartitionDto> depletionPartitionDatas = depletionpartitionDao.queryByParam(time, time + ONE_DAY, id, modelLabel);
        List<DepletionConsumptionDto> depletionConsumptionDatas = depletionconsumptionDao.queryByParam(time, time + ONE_DAY, id, modelLabel);

        res.setTotalDuration(depletionPartitionDatas.stream().mapToLong(DepletionPartitionDto::getDuration).sum());
        res.setWasteOfAll(getWasteDetailList(depletionConsumptionDatas, Constant.WASTETYPE_ALL));
        res.setWasteDurations(getWasteDurationList(depletionPartitionDatas));

        return res;
    }

    @Override
    public TempTrend depletionTempTrend(Long time, Long id, String modelLabel, Integer lineBobyType) throws IOException {
        TempTrend res = new TempTrend();
        List<Integer> partType = Arrays.asList(Constant.DEPLTIONETYPE_HEATING, Constant.DEPLTIONETYPE_ENTER, Constant.DEPLTIONETYPE_STOPPING);
        List<DepletionPartitionDto> allDatas = depletionpartitionDao.queryByParam(time, time + ONE_DAY, id, modelLabel);
        List<DepletionPartitionDto> depletionPartitionDatas = allDatas.stream().filter(item -> partType.contains(item.getDepletiontype())).collect(Collectors.toList());
        List<DepletionConsumptionDto> depletionConsumptionDatas = depletionconsumptionDao.queryByParam(time, time + ONE_DAY, id, modelLabel);
        Map<Integer, List<TrendDataVo>> integerListMap = new HashMap<>();
        if (Objects.equals(lineBobyType, Constant.LINEBODYTYPE_AIRCONDITION)) {
            integerListMap = queryQuantityData(Collections.singletonList(new BaseVo(id, Constant.CATAPHORESIS)), time,
                    ImmutableList.of(QuantityDef.getTempOfAirCondition()));
        } else {
            integerListMap = queryQuantityData(Collections.singletonList(new BaseVo(id, Constant.CATAPHORESIS)), time,
                    ImmutableList.of(QuantityDef.getTempOfWaterTank()));
        }
        List<DataLogData> dataLogDataList = dealDataLogDataList(integerListMap, time);
        DepletionCfgVo depletionCfgVo = getDepletionCfg(id, modelLabel);
        CataphoresisDto cataphoresisDto = getCataphoresisDto(id);
        List<PassPointInfoDto> passInfos = passpointinfoDao.queryByParam(time, time + ONE_DAY, depletionCfgVo.getStationCode());
        //查询电泳池的产线开机时间、产线关机时间、水池温度
        //Map<Integer, List<Long>> timeMap = getTimeMap(depletionCfgVo, time, time + ONE_DAY);
        List<Long> startTimeList = depletionDataDao.queryProductionLineEventTime(time, time + ONE_DAY, createEventCondition(depletionCfgVo, Constant.EVENTCLASSES, Constant.EVENTTYPE_OPEN));
        List<Long> endTimeList = depletionDataDao.queryProductionLineEventTime(time, time + ONE_DAY, createEventCondition(depletionCfgVo, Constant.EVENTCLASSES, Constant.EVENTTYPE_CLOSE));
        res.setTimeOfStart(getTimeList(startTimeList, time, dataLogDataList));
        res.setTimeOfStop(getTimeList(endTimeList, time, dataLogDataList));
        List<TempRiseInfoDto> tempRiseInfoDatas = tempriseinfoDao.queryByParam(time, time + ONE_DAY, id, modelLabel);
        //处理空耗区域
        List<WasteZone> wasteZones = getWasteZones(depletionPartitionDatas, depletionConsumptionDatas, dataLogDataList,
                passInfos, cataphoresisDto, startTimeList, time, tempRiseInfoDatas);

        res.setWasteZoneList(wasteZones);
        //没空耗数据时候 第一台车和最后一台车
        setCarTimes(res, startTimeList, endTimeList, passInfos, cataphoresisDto, time);
        res.setDataList(dataLogDataList);
        //原WasteOverView合并接口
        setWasteOverView(res, allDatas, depletionConsumptionDatas);
        return res;
    }

    /**
     * 原WasteOverView接口合并
     *
     * @param res
     */
    private void setWasteOverView(TempTrend res, List<DepletionPartitionDto> depletionPartitionDatas, List<DepletionConsumptionDto> depletionConsumptionDatas) {
        res.setTotalDuration(depletionPartitionDatas.stream().mapToLong(DepletionPartitionDto::getDuration).sum());
        res.setWasteOfAll(getWasteDetailList(depletionConsumptionDatas, Constant.WASTETYPE_ALL));
        res.setWasteDurations(getWasteDurationList(depletionPartitionDatas));
    }

    /**
     * 空耗处理
     */
    private List<WasteZone> getWasteZones(List<DepletionPartitionDto> depletionPartitionDatas, List<DepletionConsumptionDto> depletionConsumptionDatas,
                                          List<DataLogData> dataLogDataList, List<PassPointInfoDto> passInfos, CataphoresisDto cataphoresisDto, List<Long> startTimeList, Long time,
                                          List<TempRiseInfoDto> tempRiseInfoDatas) {
        List<Long> airReachedTimes = null;
        if (Objects.equals(cataphoresisDto.getLinebodytype(), Constant.LINEBODYTYPE_AIRCONDITION)) {
            airReachedTimes = commonService.queryAirReachedTimes(cataphoresisDto, time, time + ONE_DAY);
        }
        List<Long> finalAirReachedTimes = airReachedTimes;
        return depletionPartitionDatas.stream().map(item -> {
            WasteZone wasteZone = new WasteZone();
            List<DepletionConsumptionDto> collect1 = depletionConsumptionDatas.stream().filter(x -> Objects.equals(x.getDepletionid(), item.getId())).collect(Collectors.toList());
            wasteZone.setStartTime(item.getStarttime());
            wasteZone.setEndTime(item.getEndtime());
            wasteZone.setTempOfStart(getAppointTemp(dataLogDataList, item.getStarttime()));
            wasteZone.setTempOfEnd(getAppointTemp(dataLogDataList, item.getEndtime()));
            wasteZone.setDuration(item.getDuration());
            wasteZone.setDepletiontype(item.getDepletiontype());
            wasteZone.setDetails(getWasteDetailList(collect1, item.getDepletiontype()));
            //进车空耗 和结束空耗 第一辆车进车时间
            if (Objects.equals(item.getDepletiontype(), Constant.DEPLTIONETYPE_ENTER) || Objects.equals(item.getDepletiontype(),
                    Constant.DEPLTIONETYPE_STOPPING) || Objects.equals(item.getDepletiontype(), Constant.DEPLTIONETYPE_HEATING)) {
                setCarPassTime(item, wasteZone, passInfos, cataphoresisDto, startTimeList, finalAirReachedTimes, tempRiseInfoDatas);
            }
            return wasteZone;
        }).collect(Collectors.toList());

    }

    /**
     * 设置第一台车 最后一台车时间
     *
     * @param res
     * @param startTimeList
     * @param endTimeList
     */
    private void setCarTimes(TempTrend res, List<Long> startTimeList, List<Long> endTimeList, List<PassPointInfoDto> passInfos, CataphoresisDto cataphoresisDto, Long time) {
        if (CollectionUtils.isEmpty(passInfos)) {
            return;
        }
        passInfos = passInfos.stream().sorted(Comparator.comparing(PassPointInfoDto::getPasstime)).collect(Collectors.toList());
        List<DataLogData> firstCarTime = new ArrayList<>();
        List<DataLogData> latestCarTime = new ArrayList<>();
        //开关机都存在
        if (CollectionUtils.isNotEmpty(startTimeList) || CollectionUtils.isNotEmpty(endTimeList)) {
            if (Objects.isNull(startTimeList)){
                startTimeList = new ArrayList<>();
            }
            if (Objects.isNull(endTimeList)){
                endTimeList = new ArrayList<>();
            }
            startTimeList = startTimeList.stream().sorted().collect(Collectors.toList());
            endTimeList = endTimeList.stream().sorted().collect(Collectors.toList());
            int max = Math.max(startTimeList.size(), endTimeList.size());
            for (int i = 0; i < max; i++) {
                Long st = null;
                Long end = null;
                if (startTimeList.size() >= i + 1) {
                    st = startTimeList.get(i);
                }
                if (endTimeList.size() >= i + 1) {
                    end = endTimeList.get(i);
                }
                Long finalSt = st;
                Long finalEnd = end;
                List<PassPointInfoDto> pass = null;
                if (Objects.nonNull(st) && Objects.nonNull(end) && st < end) {
                    pass = passInfos.stream().filter(item -> finalSt <= item.getPasstime() && item.getPasstime() <= finalEnd).collect(Collectors.toList());
                }
                if (Objects.nonNull(end) && Objects.isNull(st)) {
                    pass = passInfos.stream().filter(item -> item.getPasstime() <= finalEnd).collect(Collectors.toList());
                }
                if (Objects.nonNull(st) && Objects.isNull(end)) {
                    pass = passInfos.stream().filter(item -> finalSt <= item.getPasstime()).collect(Collectors.toList());
                }
                if(Objects.nonNull(st) && Objects.nonNull(end) && st > end){
                    pass = passInfos.stream().filter(item -> item.getPasstime() >= finalSt).collect(Collectors.toList());
                }
                //没有开关机记录
                if (CollectionUtils.isNotEmpty(pass)) {
                    firstCarTime.add(new DataLogData(pass.get(0).getPasstime(), null));
                    latestCarTime.add(new DataLogData(pass.get(pass.size() - 1).getPasstime(), null));
                }
            }
        } else if (CollectionUtils.isEmpty(startTimeList) && CollectionUtils.isEmpty(endTimeList)) {
            firstCarTime.add(new DataLogData(passInfos.get(0).getPasstime(), null));
            latestCarTime.add(new DataLogData(passInfos.get(passInfos.size() - 1).getPasstime(), null));
        }
        res.setFirstCarTime(firstCarTime);
        res.setLatestCarTime(latestCarTime);
    }

    /**
     * 获取产线信息
     *
     * @param id
     * @return
     */
    private CataphoresisDto getCataphoresisDto(Long id) {
        List<CataphoresisDto> cataphoresisDtos = cataphoresisDao.queryById(id);
        if (CollectionUtils.isEmpty(cataphoresisDtos)) {
            throw new ValidationException("当前产线信息获取失败!请检查!");
        }
        return cataphoresisDtos.get(0);
    }

    /**
     * 获取开关机数据
     *
     * @param st
     * @param end
     * @return
     */
    private Map<Integer, List<Long>> getTimeMap(DepletionCfgVo depletionCfgVo, Long st, Long end) {
        List<EventLogVo> eventLogVos = commonService.queryEventLogVos(depletionCfgVo, st, end, Arrays.asList(Constant.EVENTTYPE_OPEN, Constant.EVENTTYPE_CLOSE), Collections.singletonList(Constant.EVENTCLASSES));
        return eventLogVos.stream().collect(Collectors.groupingBy(EventLogVo::getEventType, Collectors.mapping(EventLogVo::getEventTime, Collectors.toList())));
    }

    private DepletionCfgVo getDepletionCfg(Long id, String modelLabel) throws IOException {
        List<DepletionCfgVo> depletionCfgs = depletionCfg.getDepletionCfg();
        Optional<DepletionCfgVo> first = depletionCfgs.stream().filter(x -> Objects.equals(modelLabel, x.getObjectLabel()) && Objects.equals(id, x.getObjectID())).findFirst();
        if (!first.isPresent()) {
            throw new ValidationException("当前未获取到配置数据!请检查!");
        }
        return first.get();
    }

    /**
     * 进车空耗 和结束空耗 第一辆车进车时间
     */
    private void setCarPassTime(DepletionPartitionDto item, WasteZone wasteZone, List<PassPointInfoDto> passInfos,
                                CataphoresisDto cataphoresisDto, List<Long> startTimeList,
                                List<Long> airReachedTimes, List<TempRiseInfoDto> tempRiseInfoDatas) {
        Long allowTime = cataphoresisDto.getAllowtime();
        //进车空耗 第一辆车进车时间
        if (Objects.equals(item.getDepletiontype(), Constant.DEPLTIONETYPE_ENTER) || Objects.equals(item.getDepletiontype(), Constant.DEPLTIONETYPE_HEATING)) {
            Long firstCarTime = passInfos.stream().filter(pass -> Objects.equals(pass.getPasstime(), item.getEndtime())).map(PassPointInfoDto::getPasstime).findFirst().orElse(null);
            Long openTime = getOpenTime(startTimeList, item.getStarttime());
            wasteZone.setFirstCarTime(firstCarTime);
            List<TempRiseInfoDto> collectForTempRiseInfoDto = tempRiseInfoDatas.stream()
                    .filter(x -> Objects.equals(x.getEndtime(), item.getStarttime()-ALLOWTIME*1000)).collect(Collectors.toList());
            if (collectForTempRiseInfoDto.isEmpty()) {
                collectForTempRiseInfoDto = tempRiseInfoDatas.stream()
                        .filter(x -> Objects.equals(x.getStarttime(), item.getStarttime())).collect(Collectors.toList());
            }
            Long reachTime = 0L;
            if (Objects.equals(cataphoresisDto.getLinebodytype(), Constant.LINEBODYTYPE_AIRCONDITION)) {
                reachTime = commonService.getAirReachedTime(airReachedTimes, openTime, firstCarTime);
            } else {
                if (Objects.equals(item.getDepletiontype(), Constant.DEPLTIONETYPE_HEATING)) {
                    if (Objects.isNull(firstCarTime)) {
                        reachTime = firstCarTime;
                    } else if(CollectionUtils.isNotEmpty(collectForTempRiseInfoDto)){
                        TempRiseInfoDto tempRiseInfoDto = collectForTempRiseInfoDto.get(0);
                        reachTime = tempRiseInfoDto.getEndtime();
                    }
                } else {
                    reachTime = item.getStarttime() - (getVal(allowTime) * Constant.ONE_S);
                }
            }
            //空调温度达标时刻
            wasteZone.setReachTime(getReachTime(reachTime, openTime));
        }
        //结束空耗   Constant.DEPLTIONETYPE_STOPPING
        //线体停止时刻 - （最后一台进车时间 + 水平时间 + 允许时间）=空耗时长
        //线体停止时刻 - 空耗时长- 水平时间 - 允许时间=最后一台进车时间
        //线体停止时刻 - 空耗时长=空耗开始时间
        if (Objects.equals(item.getDepletiontype(), Constant.DEPLTIONETYPE_STOPPING)) {
            List<AveragePassTimeDto> passTimeDtos = averagepasstimeDao.queryByParam(cataphoresisDto.getId(), Constant.CATAPHORESIS);
            Long averageTime = getAverageTime(passTimeDtos);
            long carTime = item.getStarttime() - ((averageTime + getVal(allowTime)) * Constant.ONE_S);
            Long latestCarTime = passInfos.stream().filter(pass -> Objects.equals(pass.getPasstime(), carTime)).map(PassPointInfoDto::getPasstime).findFirst().orElse(null);
            //空耗开始时间  毫秒
            wasteZone.setLatestCarTime(latestCarTime);
        }
    }

    private Long getReachTime(Long reachTime, Long openTime) {
        if (Objects.isNull(reachTime) || Objects.isNull(openTime)) {
            return reachTime;
        }
        if (reachTime < openTime) {
            return openTime;
        }
        return reachTime;
    }

    /**
     * 计算数值 如果为空 转为0计算
     *
     * @param val
     * @return
     */
    private Long getVal(Long val) {
        return Objects.isNull(val) ? 0 : val;
    }

    private List<DataLogData> dealDataLogDataList(Map<Integer, List<TrendDataVo>> integerListMap, Long time) {
        List<List<TrendDataVo>> trendDataVoList = new ArrayList<>(integerListMap.values());
        //当定时记录为空时，返回一个只有时间戳的空list，用于前端回执x轴
        List<DataLogData> res = createEmptyDataLogDataList(time);
        if (trendDataVoList.isEmpty()) {
            return res;
        } else {
            List<TrendDataVo> trendDataVos = trendDataVoList.get(0);
            if (Objects.isNull(trendDataVos) || trendDataVos.isEmpty()) {
                return res;
            } else {
                return trendDataVos.get(0).getDataList();
            }
        }
    }

    private List<DataLogData> createEmptyDataLogDataList(Long time) {
        Long st = TimeUtil.getFirstTimeOfDay(time);
        Long et = st + ONE_DAY;
        List<DataLogData> res = new ArrayList<>();
        while (st <= et) {
            res.add(new DataLogData(st, null));
            st = st + 3600000L;
        }
        return res;
    }

    @Override
    public List<Detail> getDepletionDetails(Long time, Long id, String modelLabel, Integer depletionType) throws IOException {
        List<DepletionCfgVo> depletionCfgs = depletionCfg.getDepletionCfg();
        Optional<DepletionCfgVo> first = depletionCfgs.stream().filter(x -> Objects.equals(modelLabel, x.getObjectLabel()) && Objects.equals(id, x.getObjectID())).findFirst();
        if (!first.isPresent()) {
            return new ArrayList<>();
        }
        DepletionCfgVo depletionCfgVo = first.get();
        List<Detail> res = new ArrayList<>();
        switch (depletionType) {
            case Constant.DEPLTIONETYPE_HEATING: //没动
                res = getHeatingDetails(time, id, modelLabel, depletionType, depletionCfgVo);
                break;
            case Constant.DEPLTIONETYPE_PASSING: //改动
                res = getPassingDetails(time, id, modelLabel, depletionType, depletionCfgVo);
                break;
            case Constant.DEPLTIONETYPE_STOPPING: //没动
                res = getStoppingDetails(time, id, modelLabel, depletionType, depletionCfgVo);
                break;
            case Constant.DEPLTIONETYPE_OVER: //改动
                res = getOverDetails(time, id, modelLabel, depletionType, depletionCfgVo);
                break;
            case Constant.DEPLTIONETYPE_ENTER: //稍微改动
                res = getEnterDetails(time, id, modelLabel, depletionType, depletionCfgVo);
                break;
            default:
                break;
        }
        return res;
    }

    private List<Detail> getHeatingDetails(Long time, Long id, String modelLabel, Integer depletionType, DepletionCfgVo depletionCfgVo) {
        List<Detail> res = new ArrayList<>();

        List<TempRiseInfoDto> tempRiseInfoDatas = tempriseinfoDao.queryByParam(time, time + ONE_DAY, id, modelLabel);

        List<DepletionPartitionDto> depletionPartitionDatas = depletionpartitionDao.queryByParam(time, time + ONE_DAY, id, modelLabel, Collections.singletonList(depletionType));

        List<DepletionConsumptionDto> depletionConsumptionDatas = depletionconsumptionDao.queryByParam(time, time + ONE_DAY, id, modelLabel, Collections.singletonList(depletionType));

        List<PassPointInfoDto> passPointInfoDatas = passpointinfoDao.queryByParam(time, time + ONE_DAY, depletionCfgVo.getStationCode());
        List<EventLogVo> eventLogVos = commonService.queryEventLogVos(depletionCfgVo, time, time + ONE_DAY, Collections.singletonList(Constant.EVENTTYPE_OPEN), Collections.singletonList(Constant.EVENTCLASSES));
        dealHeatingData(tempRiseInfoDatas, depletionConsumptionDatas, depletionPartitionDatas, res, passPointInfoDatas, eventLogVos);

        return res;
    }

    /**
     * 处理升温空耗数据数据
     */
    private void dealHeatingData(List<TempRiseInfoDto> tempRiseInfoDatas, List<DepletionConsumptionDto> depletionConsumptionDatas,
                                 List<DepletionPartitionDto> depletionPartitionDatas, List<Detail> res, List<PassPointInfoDto> passPointInfoDatas, List<EventLogVo> eventLogVos) {
        List<Long> startTimes = eventLogVos.stream().map(EventLogVo::getEventTime).collect(Collectors.toList());
        for (DepletionPartitionDto item : depletionPartitionDatas) {
            Long passTime = passPointInfoDatas.stream().filter(pass -> Objects.equals(pass.getPasstime(), item.getEndtime())).map(PassPointInfoDto::getPasstime).findFirst().orElse(null);
            HeatingDetail heatingDetail = new HeatingDetail();
            List<TempRiseInfoDto> collectForTempRiseInfoDto = tempRiseInfoDatas.stream()
                    .filter(x -> Objects.equals(x.getStarttime(), item.getStarttime()-ALLOWTIME*1000)).collect(Collectors.toList());
            if (collectForTempRiseInfoDto.isEmpty()) {
                collectForTempRiseInfoDto = tempRiseInfoDatas.stream()
                        .filter(x -> Objects.equals(x.getStarttime(), item.getStarttime())).collect(Collectors.toList());
            }
            //升温持续时间 = 升温结束时间 - 升温开始时间
            if (CollectionUtils.isNotEmpty(collectForTempRiseInfoDto)) {
                TempRiseInfoDto tempRiseInfoDto = collectForTempRiseInfoDto.get(0);
                heatingDetail.setDurationOfHeating(tempRiseInfoDto.getDuration());
                heatingDetail.setTemperature(tempRiseInfoDto.getTemperature());
                Long endOfHeating = tempRiseInfoDto.getEndtime();
                endOfHeating = Objects.isNull(passTime) ? passTime : endOfHeating;
                heatingDetail.setEndOfHeating(endOfHeating);
            }
            //要改为开机时刻
            Long openTime = getOpenTime(startTimes, item.getStarttime());
            heatingDetail.setStartOfHeating(openTime);
            // 此处应取 过车信息 不是直接用endtime
            heatingDetail.setTimeOfPassing(passTime);
            List<DepletionConsumptionDto> collect = depletionConsumptionDatas.stream().filter(x -> Objects.equals(x.getDepletionid(), item.getId())).collect(Collectors.toList());
            heatingDetail.setDetails(getWasteDetailList(collect, Constant.DEPLTIONETYPE_HEATING));
            heatingDetail.setDurationOfWaste(item.getDuration());
            res.add(heatingDetail);
        }
    }

    private List<Detail> getPassingDetails(Long time, Long id, String modelLabel, Integer depletionType, DepletionCfgVo depletionCfgVo) {
        List<Detail> res = new ArrayList<>();
        List<DepletionPartitionDto> depletionPartitionDatas = depletionpartitionDao.queryByParam(time, time + ONE_DAY, id, modelLabel, Collections.singletonList(depletionType));
        List<Long> partitionIds = depletionPartitionDatas.stream().map(DepletionPartitionDto::getId).collect(Collectors.toList());
        List<DepletionConsumptionDto> consumptionDtos = depletionconsumptionDao.queryByDepletionid(partitionIds);

        List<CataphoresisDto> cataphoresisDtos = cataphoresisDao.queryById(id);
        List<PassPointInfoDto> passPointInfoDatas = passpointinfoDao.queryByParam(time, time + ONE_DAY, depletionCfgVo.getStationCode());

        dealPassingData(cataphoresisDtos, passPointInfoDatas, consumptionDtos, depletionPartitionDatas, res);

        return res;
    }

    /**
     * 处理过车空耗数据
     */
    private void dealPassingData(List<CataphoresisDto> cataphoresisDtos, List<PassPointInfoDto> passPointInfoDatas,
                                 List<DepletionConsumptionDto> depletionConsumptionDatas, List<DepletionPartitionDto> depletionPartitionDatas,
                                 List<Detail> res) {
        passPointInfoDatas = passPointInfoDatas.stream().sorted(Comparator.comparing(PassPointInfoDto::getPasstime).reversed()).collect(Collectors.toList());
        for (DepletionPartitionDto item : depletionPartitionDatas) {
            CataphoresisDto cataphoresisDto = cataphoresisDtos.get(0);
            PassingDetail passingDetail = new PassingDetail();
            Double taktTime = Objects.nonNull(cataphoresisDto.getTakttime()) ? cataphoresisDto.getTakttime() : TAKTTIME;
            passingDetail.setTaktTime(taktTime);
            List<PassPointInfoDto> collectForPassPointInfoDto = passPointInfoDatas.stream().filter(x -> Objects.equals(x.getPasstime(), item.getEndtime()))
                    .collect(Collectors.toList());
            List<DepletionConsumptionDto> collect = depletionConsumptionDatas.stream().filter(x -> Objects.equals(x.getDepletionid(), item.getId())).collect(Collectors.toList());
            passingDetail.setDetails(getWasteDetailList(collect, item.getDepletiontype()));
            passingDetail.setWasteType(item.getDepletiontype());
            passingDetail.setStartOfWaste(item.getStarttime());
            passingDetail.setDurationOfWaste(item.getDuration());
            if (CollectionUtils.isNotEmpty(collectForPassPointInfoDto)) {
                PassPointInfoDto passPointInfoDto = collectForPassPointInfoDto.get(0);
                passingDetail.setTimeOfPassing(passPointInfoDto.getPasstime());
                passingDetail.setVIN(passPointInfoDto.getVin());
            }
            PassPointInfoDto lastPass = passPointInfoDatas.stream().filter(pass -> pass.getPasstime() < item.getEndtime()).findFirst().orElse(null);
            //上一台车的过车时间
            if (Objects.nonNull(lastPass)) {
                passingDetail.setLastPasstime(lastPass.getPasstime());
                //实际过车节拍
                passingDetail.setRealTaktTime((item.getEndtime() - lastPass.getPasstime()) / Constant.ONE_S);
            }
            res.add(passingDetail);
        }
    }

    private List<Detail> getStoppingDetails(Long time, Long id, String modelLabel, Integer depletionType, DepletionCfgVo depletionCfgVo) throws JsonProcessingException {
        List<Detail> res = new ArrayList<>();

        List<DepletionPartitionDto> depletionPartitionDatas = depletionpartitionDao.queryByParam(time, time + ONE_DAY, id, modelLabel, Collections.singletonList(depletionType));
        List<Long> partitionIds = depletionPartitionDatas.stream().map(DepletionPartitionDto::getId).collect(Collectors.toList());
        List<DepletionConsumptionDto> depletionConsumptionDatas = depletionconsumptionDao.queryByDepletionid(partitionIds);

        List<CataphoresisDto> cataphoresisDtos = cataphoresisDao.queryById(id);

        List<PassPointInfoDto> passPointInfoDatas = passpointinfoDao.queryByParam(time, time + ONE_DAY, depletionCfgVo.getStationCode());

        List<AveragePassTimeDto> averagePassTimeDtos = averagepasstimeDao.queryByParam(id, modelLabel);

        dealStoppingData(cataphoresisDtos, passPointInfoDatas, depletionConsumptionDatas, depletionPartitionDatas, averagePassTimeDtos, res);

        return res;
    }

    /**
     * 处理结束空耗数据
     */
    private void dealStoppingData(List<CataphoresisDto> cataphoresisDtos, List<PassPointInfoDto> passPointInfoDatas,
                                  List<DepletionConsumptionDto> depletionConsumptionDatas, List<DepletionPartitionDto> depletionPartitionDatas,
                                  List<AveragePassTimeDto> averagePassTimeDtos, List<Detail> res) throws JsonProcessingException {
        for (DepletionPartitionDto item : depletionPartitionDatas) {
            CataphoresisDto cataphoresisDto = cataphoresisDtos.get(0);
            StoppingDetail stoppDetail = new StoppingDetail();
            Long averageTime = getAverageTime(averagePassTimeDtos);
            stoppDetail.setAverageTime(averageTime);
            stoppDetail.setTimeOfStopping(item.getEndtime());
            Optional<PassPointInfoDto> passList = passPointInfoDatas.stream()
                    .filter(x -> Objects.equals(x.getPasstime(), item.getStarttime()-ALLOWTIME*1000-averageTime*1000)).findAny();
            List<DepletionConsumptionDto> collect = depletionConsumptionDatas.stream().filter(x -> Objects.equals(x.getDepletionid(), item.getId()))
                    .collect(Collectors.toList());
            stoppDetail.setDurationOfWaste(item.getDuration());
            stoppDetail.setDetails(getWasteDetailList(collect, item.getDepletiontype()));
            if (passList.isPresent()) {
                PassPointInfoDto passPointInfoDto = passList.get();
                stoppDetail.setVIN(passPointInfoDto.getVin());
                stoppDetail.setTimeOfPassing(passPointInfoDto.getPasstime());
            }
            res.add(stoppDetail);
        }
    }

    private Long getAverageTime(List<AveragePassTimeDto> averagePassTimeDtos) {
        if (CollectionUtils.isEmpty(averagePassTimeDtos)) {
            return averageTime;
        }
        Long averagetime = averagePassTimeDtos.get(0).getAveragetime();
        return Objects.isNull(averagetime) ? averageTime : averagetime;
    }

    private List<Detail> getOverDetails(Long time, Long id, String modelLabel, Integer depletionType, DepletionCfgVo depletionCfgVo) {
        List<Detail> res = new ArrayList<>();

        List<DepletionPartitionDto> depletionPartitionDatas = depletionpartitionDao.queryByParam(time, time + ONE_DAY, id, modelLabel, Collections.singletonList(depletionType));
        List<DepletionConsumptionDto> depletionConsumptionDatas = depletionconsumptionDao.queryByParam(time, time + ONE_DAY, id, modelLabel, Collections.singletonList(depletionType));

        List<PassPointInfoDto> passPointInfoDatas = passpointinfoDao.queryByParam(time, time + ONE_DAY, depletionCfgVo.getStationCode());

        List<AveragePassTimeDto> averagePassTimeDtos = averagepasstimeDao.queryByParam(id, modelLabel);

        dealOverData(passPointInfoDatas, depletionConsumptionDatas, depletionPartitionDatas, averagePassTimeDtos, res);
        return res;
    }

    /**
     * 处理过线空耗数据
     */
    private void dealOverData(List<PassPointInfoDto> passPointInfoDatas,
                              List<DepletionConsumptionDto> depletionConsumptionDatas, List<DepletionPartitionDto> depletionPartitionDatas,
                              List<AveragePassTimeDto> averagePassTimeDtos, List<Detail> res) {
        for (DepletionPartitionDto item : depletionPartitionDatas) {
            Long averageTime = getAverageTime(averagePassTimeDtos);
            OverDetail overDetail = new OverDetail();
            overDetail.setAverageTime(averageTime);
            Optional<PassPointInfoDto> first = passPointInfoDatas.stream()
                    .filter(x -> Objects.equals(x.getPasstime(),
                            //过车时刻 + 平均时间 = 空耗开始时间
                            item.getStarttime() - averageTime * 1000)).findFirst();
            List<DepletionConsumptionDto> collect = depletionConsumptionDatas.stream().filter(x -> Objects.equals(x.getDepletionid(), item.getId())).collect(Collectors.toList());
            //空耗时长 = 出口AVI时刻（下一产线的进车时刻）空耗结束 - [入口AVI时刻（当前产线的进车时刻） + 水平时间] 空耗开始
            // 出口AVI时刻（下一产线的进车时刻）空耗结束 - 入口AVI时刻（当前产线的进车时刻） =空耗时长 +水平时间
            //实际过线耗时
            overDetail.setActualTime(item.getDuration() + averageTime);
            overDetail.setDetails(getWasteDetailList(collect, item.getDepletiontype()));
            overDetail.setDurationOfWaste(item.getDuration());
            if (first.isPresent()) {
                PassPointInfoDto passPointInfoDto = first.get();
                overDetail.setVIN(passPointInfoDto.getVin());
                overDetail.setTimeOfPassing(passPointInfoDto.getPasstime());
                overDetail.setLatestCarTime(passPointInfoDto.getPasstime());
            }
            res.add(overDetail);
        }
    }

    private List<Detail> getEnterDetails(Long time, Long id, String modelLabel, Integer depletionType, DepletionCfgVo depletionCfgVo) {
        List<Detail> res = new ArrayList<>();

        List<DepletionPartitionDto> depletionPartitionDatas = depletionpartitionDao.queryByParam(time, time + ONE_DAY, id, modelLabel, Collections.singletonList(depletionType));
        List<DepletionConsumptionDto> depletionConsumptionDatas = depletionconsumptionDao.queryByParam(time, time + ONE_DAY, id, modelLabel, Collections.singletonList(depletionType));
        List<PassPointInfoDto> passPointInfoDatas = passpointinfoDao.queryByParam(time, time + ONE_DAY, depletionCfgVo.getStationCode());
        List<CataphoresisDto> cataphoresisDtos = cataphoresisDao.queryById(id);
        CataphoresisDto cataphoresisDto = cataphoresisDtos.get(0);
        dealEnterData(depletionPartitionDatas, depletionConsumptionDatas, passPointInfoDatas, res, cataphoresisDto, time);
        return res;
    }

    /**
     * 处理进车空耗数据
     */
    private void dealEnterData(List<DepletionPartitionDto> depletionPartitionDatas, List<DepletionConsumptionDto> depletionConsumptionDatas,
                               List<PassPointInfoDto> passPointInfoDatas, List<Detail> res, CataphoresisDto cataphoresisDto, Long time) {
        List<Long> reachedTimes = null;
        boolean isAir = Objects.equals(cataphoresisDto.getLinebodytype(), Constant.LINEBODYTYPE_AIRCONDITION);
        List<EventLogVo> eventLogVos = commonService.queryEventLogVos(cataphoresisDto, time, time + ONE_DAY, Collections.singletonList(Constant.EVENTTYPE_OPEN));
        if (isAir) {
            reachedTimes = commonService.queryAirReachedTimes(cataphoresisDto, time, time + ONE_DAY);
        }
        List<Long> startTimes = eventLogVos.stream().map(EventLogVo::getEventTime).collect(Collectors.toList());
        for (DepletionPartitionDto item : depletionPartitionDatas) {
            EnterDetail enterDetail = new EnterDetail();
            enterDetail.setDurationOfWaste(item.getDuration());
            Optional<PassPointInfoDto> first = passPointInfoDatas.stream()
                    //空耗结束时间 = 进车时间
                    .filter(x -> Objects.equals(x.getPasstime(), item.getEndtime())).findFirst();
            List<DepletionConsumptionDto> collect = depletionConsumptionDatas.stream().filter(x -> Objects.equals(x.getDepletionid(), item.getId())).collect(Collectors.toList());
            enterDetail.setDetails(getWasteDetailList(collect, item.getDepletiontype()));
            Long openTime = getOpenTime(startTimes, item.getStarttime());
            enterDetail.setStartTime(openTime);
            if (first.isPresent()) {
                PassPointInfoDto passPointInfoDto = first.get();
                enterDetail.setVIN(passPointInfoDto.getVin());
                //过车时间
                enterDetail.setTimeOfPassing(item.getEndtime());
            }
            if (isAir) {
                Long reachedTime = commonService.getAirReachedTime(reachedTimes, openTime, item.getEndtime());
                //空耗时长 = 第一台进车时间（endtime）-温度达标时间- 允许时间(本身属性里取 allowtime)
                //水平过线耗时（？= 水平时间）  是什么  averagepasstime  里的averagetime
                //线体停止时刻 - （最后一台进车时间 + 水平时间 + 允许时间）=空耗时长
                enterDetail.setReachTime(reachedTime);
            }
            res.add(enterDetail);
        }
    }

    /**
     * 根据空号开始时间 获取开机时刻
     *
     * @param startTimes
     * @param khSt
     * @return
     */
    private Long getOpenTime(List<Long> startTimes, Long khSt) {
        if (CollectionUtils.isEmpty(startTimes)) {
            return null;
        }
        return startTimes.stream().sorted(Comparator.comparing(Long::longValue).reversed())
                .filter(time -> time <= khSt).findFirst().orElse(khSt);
    }

    /**
     * 从空耗能耗数据中，计算指定的能源类型的空耗和，并返回空耗详情
     */
    private List<WasteDetail> getWasteDetailList(List<DepletionConsumptionDto> depletionConsumptionDatas, Integer depletionType) {
        if (CollectionUtils.isEmpty(depletionConsumptionDatas)) {
            return Collections.emptyList();
        }
        List<DepletionConsumptionDto> collect;
        if (Objects.equals(depletionType, Constant.WASTETYPE_ALL)) {
            //空耗总览的话，过滤升温和过车空耗
            collect = depletionConsumptionDatas;
        } else {
            //反之，按照空耗类型过滤
            collect = depletionConsumptionDatas.stream().filter(x -> Objects.equals(x.getDepletiontype(), depletionType)).collect(Collectors.toList());
        }
        List<Integer> energyTypeList = depletionConsumptionDatas.stream().map(DepletionConsumptionDto::getEnergytype).distinct().collect(Collectors.toList());
        List<WasteDetail> res = new ArrayList<>();
        //按照不同能源类型汇总空耗详情
        for (Integer energyType : energyTypeList) {
            List<DepletionConsumptionDto> collectForEnergyType = collect.stream().filter(x -> Objects.equals(x.getEnergytype(), energyType)).collect(Collectors.toList());
            WasteDetail wasteDetail = new WasteDetail();
            wasteDetail.setEnergyType(energyType);
            wasteDetail.setUnit(EnumEnergyType.getUnit(energyType));
            wasteDetail.setEnergyName(EnumEnergyType.getEnergyName(energyType));
            wasteDetail.setValue(collectForEnergyType.stream().mapToDouble(DepletionConsumptionDto::getValue).sum());
            res.add(wasteDetail);
        }
        return res;
    }

    /**
     * 从空耗划分数据中，计算各类空耗的总时长
     */
    private List<WasteDuration> getWasteDurationList(List<DepletionPartitionDto> depletionPartitionDatas) {
        List<WasteDuration> res = new ArrayList<>();
        //按照实际生产顺序来整理空耗（因为枚举值已经搞乱顺序，只能这样处理了）
        List<Integer> depletionTypeList = Arrays.asList(Constant.DEPLTIONETYPE_HEATING, Constant.DEPLTIONETYPE_ENTER,
                Constant.DEPLTIONETYPE_OVER, Constant.DEPLTIONETYPE_PASSING, Constant.DEPLTIONETYPE_STOPPING);
        for (Integer depletionType : depletionTypeList) {
            List<DepletionPartitionDto> collect = depletionPartitionDatas.stream().filter(x -> Objects.equals(x.getDepletiontype(), depletionType)).collect(Collectors.toList());
            WasteDuration wasteDuration = new WasteDuration();
            if (collect.isEmpty()) {
                wasteDuration.setCount(0);
                wasteDuration.setDuration(0L);
            } else {
                wasteDuration.setCount(collect.size());
                wasteDuration.setDuration(collect.stream().mapToLong(DepletionPartitionDto::getDuration).sum());
            }
            wasteDuration.setDepletionType(depletionType);
            wasteDuration.setDepletionTypeName(DepletionType.getDepletionTypeName(depletionType));
            res.add(wasteDuration);
        }
        return res;
    }


    /**
     * 查询定时记录
     */
    private Map<Integer, List<TrendDataVo>> queryQuantityData(List<BaseVo> baseVos, Long time, List<QuantitySearchVo> quantitySearchVo) {
        QuantityDataBatchSearchVo searchVo = getQuantityDataBatchSearchVo(baseVos, quantitySearchVo, time);
        return quantityManageService.queryDataLogBatch(searchVo);
    }

    private QuantityDataBatchSearchVo getQuantityDataBatchSearchVo(List<BaseVo> nodes, List<QuantitySearchVo> quantitySearchVo, Long time) {
        QuantityDataBatchSearchVo searchVo = new QuantityDataBatchSearchVo();
        searchVo.setStartTime(time);
        searchVo.setEndTime(time + ONE_DAY);
        searchVo.setAggregationCycle(AggregationCycle.FIVE_MINUTES);
        searchVo.setDataTypeId(EnumDataTypeId.REALTIME.getId());
        searchVo.setNodes(nodes);
        searchVo.setQuantitySettings(quantitySearchVo);
        return searchVo;
    }

    /*
     * 获取指定时间戳对应的温度数据
     * */
    private Double getAppointTemp(List<DataLogData> tempOfWaterTankDataLogs, Long time) {
        if (tempOfWaterTankDataLogs.isEmpty()) {
            return null;
        }
        //直接按照时刻匹配对应时刻的温度
        Optional<DataLogData> first = tempOfWaterTankDataLogs.stream().filter(x -> Objects.equals(x.getTime(), time)).findFirst();
        if (first.isPresent()) {
            return first.get().getValue();
        } else {
            //匹配不到则寻找临近的第一个时刻的温度
            Optional<DataLogData> next = tempOfWaterTankDataLogs.stream().filter(x -> (x.getTime() > time - 300000L && x.getTime() < time + 300000L)).findFirst();
            return next.map(DataLogData::getValue).orElse(null);
        }

    }

    private List<DataLogData> getTimeList(List<Long> item, Long time, List<DataLogData> tempOfWaterTankDataLogs) {
        if (Objects.isNull(item) || item.isEmpty()) {
            return new ArrayList<>();
        }
        List<Long> collectForTime = item.stream().filter(x -> x > time).collect(Collectors.toList());
        if (collectForTime.isEmpty()) {
            return new ArrayList<>();
        }
        List<Long> collect = collectForTime.stream().map(x -> x).distinct().collect(Collectors.toList());
        return collect.stream().map(x -> {
            return new DataLogData(x, getAppointTemp(tempOfWaterTankDataLogs, x));
        }).collect(Collectors.toList());
    }

    private EventCondition createEventCondition(DepletionCfgVo item, Integer eventClasses, Integer eventType) {
        EventCondition eventCondition = new EventCondition();
        eventCondition.setChannelId(item.getChannelID());
        eventCondition.setStationId(item.getStationID());
        eventCondition.setEventClasses(Collections.singletonList(eventClasses));
        eventCondition.setEventTypes(Collections.singletonList(eventType));
        eventCondition.setKeyWord(item.getKeyWord());
        return eventCondition;
    }
}

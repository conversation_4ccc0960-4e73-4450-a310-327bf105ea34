package com.cet.eem.bll.compressoroptimization.dao.impl;

import com.cet.eem.bll.compressoroptimization.dao.AiCompressorStrategyDao;
import com.cet.eem.bll.compressoroptimization.model.strategy.AiCompressorStrategy;
import com.cet.eem.bll.compressoroptimization.model.strategy.AiCompressorStrategyWithLayer;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName : AiCompressorStrategyDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-24 15:52
 */
@Repository
public class AiCompressorStrategyDaoImpl extends ModelDaoImpl<AiCompressorStrategy> implements AiCompressorStrategyDao {

    @Override
    public List<AiCompressorStrategy> queryRealTime(Long startTime, Long endTime, Long systemId, Long projectId,Integer strategyType) {
        LambdaQueryWrapper<AiCompressorStrategy> wrapper = LambdaQueryWrapper.of(AiCompressorStrategy.class)
                .orderByDesc(AiCompressorStrategy::getOperationTime);
        if (Objects.nonNull(systemId)) {
            wrapper.eq(AiCompressorStrategy::getSystemId, systemId);
        }
        if (Objects.nonNull(startTime)) {
            wrapper.ge(AiCompressorStrategy::getOperationTime, startTime);
        }
        if (Objects.nonNull(endTime)) {
            wrapper.lt(AiCompressorStrategy::getOperationTime, endTime);
        }
        if (Objects.nonNull(strategyType)){
            wrapper.eq(AiCompressorStrategy::getStrategyType,strategyType);
        }
        wrapper.eq(AiCompressorStrategy::getProjectId, projectId);
        List<AiCompressorStrategy> aiCompressorStrategies = this.selectList(wrapper);
        if (CollectionUtils.isEmpty(aiCompressorStrategies)) {
            return Collections.emptyList();
        }
        return aiCompressorStrategies;
    }

    @Override
    public List<AiCompressorStrategyWithLayer> queryCompressorStrategy(Long startTime, Long endTime, Long systemId, Long projectId,Integer strategyType) {
        LambdaQueryWrapper<AiCompressorStrategy> wrapper = LambdaQueryWrapper.of(AiCompressorStrategy.class)
                .orderByDesc(AiCompressorStrategy::getOperationTime);
        if (Objects.nonNull(systemId)) {
            wrapper.eq(AiCompressorStrategy::getSystemId, systemId);
        }
        if (Objects.nonNull(startTime)) {
            wrapper.ge(AiCompressorStrategy::getOperationTime, startTime);
        }
        if (Objects.nonNull(endTime)) {
            wrapper.lt(AiCompressorStrategy::getOperationTime, endTime);
        }
        if (Objects.nonNull(strategyType)){
            wrapper.eq(AiCompressorStrategy::getStrategyType,strategyType);
        }
        wrapper.eq(AiCompressorStrategy::getProjectId, projectId);
        List<AiCompressorStrategyWithLayer> aiCompressorStrategies = this.selectRelatedList(AiCompressorStrategyWithLayer.class, wrapper);
        if (CollectionUtils.isEmpty(aiCompressorStrategies)) {
            return Collections.emptyList();
        }
        return aiCompressorStrategies;
    }
}
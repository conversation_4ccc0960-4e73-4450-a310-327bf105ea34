package com.cet.eem.energyevent.fegin;

import com.cet.eem.common.model.Result;
import com.cet.eem.common.model.ResultWithTotal;
import com.cet.eem.common.model.convergenceevent.ConvergenceEventCondition;
import com.cet.eem.common.model.convergenceevent.PecConvergenceEvent;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.model.datalog.TrendSearchListVo;
import com.cet.eem.common.model.event.EventCondition;
import com.cet.eem.common.model.event.EventLogVo;
import com.cet.eem.common.model.event.WaveLog;
import com.cet.eem.common.model.peccore.DeviceNodeInfo;
import com.cet.eem.common.model.peccore.GraphNodeVo;
import com.cet.eem.common.model.peccore.Meter;
import com.cet.eem.common.model.realtime.RealTimeValue;
import com.cet.eem.common.model.realtime.RltRowDataIDLogicalIndex;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.matterhorn.devicedataservice.common.entity.report.ReportQueryParam;
import com.cet.electric.matterhorn.devicedataservice.common.entity.station.DatalogMeasItemDTO;
import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/7/24 10:21
 */
@FeignClient(
        value = "device-data-service",
        url = "${cet.eem.service.url.device-data-service:''}"
)
public interface DeviceAutomService {
    @RequestMapping(
            value = {"/api/v1/realtimedata/{dataId}/{logicalId}"},
            method = {RequestMethod.POST}
    )
    Result<Object> realTimeData(@PathVariable("dataId") Integer var1, @PathVariable("logicalId") Integer var2, @RequestBody List<Long> var3);

    @RequestMapping(
            value = {"/api/event/v1/data"},
            method = {RequestMethod.POST}
    )
    ResultWithTotal<List<EventLogVo>> queryEventData(@RequestParam("index") Integer var1, @RequestParam("limit") Integer var2, @RequestParam("startTime") Long var3, @RequestParam("endTime") Long var4, EventCondition var5);

    @RequestMapping(
            value = {"/api/event/v1/data/matchwave"},
            method = {RequestMethod.POST}
    )
    ResultWithTotal<List<EventLogVo>> queryEventDataWithWave(@RequestBody EventCondition var1, @RequestParam("isPrecise") Boolean var2, @RequestParam("offset") Integer var3, @RequestParam("index") Integer var4, @RequestParam("limit") Integer var5, @RequestParam("startTime") Long var6, @RequestParam("endTime") Long var7, @RequestParam("maxEventTime") Long var8);

    @RequestMapping(
            value = {"/api/event/v1/count"},
            method = {RequestMethod.POST}
    )
    Result<Integer> queryEventDataCount(@RequestBody EventCondition var1, @RequestParam("startTime") Long var2, @RequestParam("endTime") Long var3);

    @RequestMapping(
            value = {"/api/comm/v1/stations"},
            method = {RequestMethod.GET}
    )
    Result<List<DeviceNodeInfo>> queryAllStations();

    @RequestMapping(
            value = {"/api/comm/v1/station/channels"},
            method = {RequestMethod.GET}
    )
    Result<List<DeviceNodeInfo>> queryChannels(@RequestParam("stationId") long var1);

    @RequestMapping(
            value = {"/api/comm/v1/channel/devices"},
            method = {RequestMethod.GET}
    )
    Result<List<DeviceNodeInfo>> queryDevices(@RequestParam("channelId") long var1);

    @RequestMapping(
            value = {"/api/v1/realtimedata/{deviceId}"},
            method = {RequestMethod.POST}
    )
    Result<List<RealTimeValue>> queryRealtimedataBatch(@PathVariable("deviceId") Long var1, @RequestBody List<RltRowDataIDLogicalIndex> var2);

    @RequestMapping(
            value = {"/api/v1/batch/datalog/span/group"},
            method = {RequestMethod.POST}
    )
    Result<List<TrendDataVo>> queryTrendCurveData(@RequestBody TrendSearchListVo var1);

    @RequestMapping(
            value = {"/api/v1/batch/datalog/span/group/appoint"},
            method = {RequestMethod.POST}
    )
    Result<List<TrendDataVo>> queryDataLogs(@RequestBody TrendSearchListVo var1, @RequestParam("fill") boolean var2, @RequestParam("p35") boolean var3, @RequestParam("fromDataBase") boolean var4);

    @RequestMapping(
            value = {"/api/comm/v1/device/infos"},
            method = {RequestMethod.POST}
    )
    Result<List<DeviceNodeInfo>> queryDeviceInfos(@RequestBody List<Long> var1);

    @RequestMapping(
            value = {"/api/comm/v1/stations"},
            method = {RequestMethod.GET}
    )
    Result<List<DeviceNodeInfo>> queryAllStationInfos();

    @RequestMapping(
            value = {"/api/comm/v1/channels"},
            method = {RequestMethod.GET}
    )
    Result<List<DeviceNodeInfo>> queryAllChannelInfos();

    @RequestMapping(
            value = {"/api/v1/realtimedata/{dataId}/{logicalId}"},
            method = {RequestMethod.POST}
    )
    ResultWithTotal<List<RealTimeValue>> queryRealtimedata(List<Long> var1, @PathVariable("dataId") Long var2, @PathVariable("logicalId") Integer var3);

    @RequestMapping(
            value = {"/api/event/v1/data/idrange/{year}"},
            method = {RequestMethod.POST}
    )
    ResultWithTotal<List<EventLogVo>> queryEventById(@RequestParam("startId") Long var1, @RequestParam("endId") Long var2, @RequestParam("index") Integer var3, @RequestParam("limit") Integer var4, @PathVariable("year") Integer var5, EventCondition var6);

    @RequestMapping(
            value = {"/api/wave/v1/data/idrange"},
            method = {RequestMethod.GET}
    )
    ResultWithTotal<List<WaveLog>> queryWaveById(@RequestParam("startId") Long var1, @RequestParam("endId") Long var2, @RequestParam("index") Integer var3, @RequestParam("limit") Integer var4, @RequestParam("year") Integer var5, @RequestParam("includeData") Boolean var6);

    @RequestMapping(
            value = {"/api/comm/v1/devices"},
            method = {RequestMethod.GET}
    )
    Result<List<DeviceNodeInfo>> queryAllDevices();

    @RequestMapping(
            value = {"/api/comm/v1/device/tree"},
            method = {RequestMethod.GET}
    )
    Result<List<Meter>> queryTreeNodes(@RequestParam("async") boolean var1, @RequestParam("parentNodeType") long var2, @RequestParam("parentNodeId") long var4);

    @RequestMapping(
            value = {"/api/comm/v1/graph/tree"},
            method = {RequestMethod.GET}
    )
    Result<List<GraphNodeVo>> queryGraphTree();

    @RequestMapping(
            value = {"/api/event/v1/alarmevents/idrange"},
            method = {RequestMethod.POST}
    )
    ResultWithTotal<List<PecConvergenceEvent>> queryConvergenceEventById(@RequestParam("startId") Long var1, @RequestParam("endId") Long var2, @RequestParam("index") Integer var3, @RequestParam("limit") Integer var4, @RequestParam("year") Integer var5, @RequestBody ConvergenceEventCondition var6);

    @RequestMapping(
            value = {"/api/event/v1/alarmevents/wave"},
            method = {RequestMethod.POST}
    )
    ResultWithTotal<List<PecConvergenceEvent>> queryConvergenceEventWithWave(@RequestBody ConvergenceEventCondition var1, @RequestParam("isPrecise") Boolean var2, @RequestParam("offset") Integer var3, @RequestParam("index") Integer var4, @RequestParam("limit") Integer var5, @RequestParam("startTime") Long var6, @RequestParam("endTime") Long var7, @RequestParam("maxEventTime") Long var8);

    @PostMapping({"/api/event/v1/alarmevents"})
    ResultWithTotal<List<PecConvergenceEvent>> queryAlarmEvents(@RequestParam("index") Integer var1, @RequestParam("limit") Integer var2, @RequestParam("startTime") Long var3, @RequestParam("endTime") Long var4, @RequestParam("maxEventTime") Long var5, @RequestBody ConvergenceEventCondition var6);

    @PostMapping({"/api/v1/devices/status"})
    ResultWithTotal<Map<Long, Integer>> queryDeviceStatus(@RequestBody List<Long> var1);

    @RequestMapping(
            value = {"/api/comm/v1/report/tree"},
            method = {RequestMethod.GET}
    )
    Result<List<GraphNodeVo>> queryReportTree();

    @RequestMapping(
            value = {"/api/comm/v1/report/children"},
            method = {RequestMethod.GET}
    )
    Result<List<GraphNodeVo>> queryReportTreeGrading(@RequestParam("parentNodeId") long var1, @RequestParam("parentNodeType") long var3);


    @PostMapping({"/api/report/v1/excel"})
    Response exportReportFile(@RequestBody ReportQueryParam var1);


    @PostMapping({"/api/comm/v1/devices"})
    Result<List<DeviceNodeInfo>> queryDevicesById(@RequestBody List<Long> var1);

    @RequestMapping(
            value = {"/api/comm/v1/device/datalog/points"},
            method = {RequestMethod.POST}
    )
    ApiResult<Map<Long, List<DatalogMeasItemDTO>>> getDeviceDatalogPoints(@RequestBody List<Long> deviceIds, @RequestParam(name = "paraTypeId",required = false) Integer paraTypeId);

}

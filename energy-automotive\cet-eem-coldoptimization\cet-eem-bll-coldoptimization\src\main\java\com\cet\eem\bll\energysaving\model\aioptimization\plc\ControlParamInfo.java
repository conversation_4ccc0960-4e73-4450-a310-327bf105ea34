package com.cet.eem.bll.energysaving.model.aioptimization.plc;

import com.cet.eem.bll.energysaving.model.aioptimization.AiStartStopStrategy;
import com.cet.eem.bll.energysaving.model.aioptimization.StrategyObjectMap;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.auth.user.UserVo;
import com.cet.electric.matterhorn.devicedataservice.common.query.control.MultiRemoteControlPara;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : ControlParamInfo
 * @Description : 控制信息入参详情
 * <AUTHOR> jiangzixuan
 * @Date: 2022-09-14 16:24
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ControlParamInfo {
    private MultiRemoteControlPara  controlParam;
    private Long systemId;
    private Long roomId;
    private UserVo userVo;
    private String password;
    private List<BaseVo> plcNodes;
    private List<StrategyObjectMap> strategyObjectMaps;
    private List<AiStartStopStrategy> strategies;
    private Integer strategyType;

}
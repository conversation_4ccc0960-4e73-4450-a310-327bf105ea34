package com.cet.eem.bll.energysaving.handle.optimizationstrategy;

import com.cet.eem.bll.common.log.service.CommonUtilsService;
import com.cet.eem.bll.common.model.domain.subject.energysaving.RefrigeratingSystem;
import com.cet.eem.bll.energysaving.handle.optimizationstrategy.judgeconfiguration.JudgeConfiguration;
import com.cet.eem.bll.energysaving.handle.optimizationstrategy.switchconfiguration.ColdWaterMainEngineSwitch;
import com.cet.eem.bll.energysaving.model.aioptimization.AiStartStopStrategy;
import com.cet.eem.bll.energysaving.model.aioptimization.StrategyObjectMap;
import com.cet.eem.bll.energysaving.model.config.ParameterConfig;
import com.cet.eem.bll.energysaving.model.def.ColdOptimizationLabelDef;
import com.cet.eem.bll.energysaving.model.def.ColdControlTypeDef;
import com.cet.eem.bll.energysaving.model.def.QuantityDef;
import com.cet.eem.bll.energysaving.model.weather.QueryParam;
import com.cet.eem.bll.energysaving.service.trend.OperationTrendService;
import com.cet.eem.common.constant.EnumDataTypeId;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.realtime.RealTimeValue;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import com.cet.eem.node.service.Topology1Service;
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;
import com.cet.eem.quantity.service.QuantityManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 冷机运行优化策略计算
 *
 * <AUTHOR>
 * @date 2022/07/12
 */
@Service
@Slf4j
public class OptimizationStrategyHandle {

    @Autowired
    QuantityManageService quantityManageService;

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    Topology1Service topology1Service;

    @Autowired
    OperationTrendService operationTrendService;

    @Autowired
    JudgeConfiguration judgeConfiguration;

    @Autowired
    ColdWaterMainEngineSwitch coldWaterMainEngineSwitch;

    @Autowired
    CommonUtilsService commonUtilsService;

    private static final String LOG_KEY = "[冷机运行优化策略计算]";

    @Value("${cet.eem.optimizationstrategy.query-interval}")
    private int interval;

    public static final Double UP = 1.0;
    public static final Double DOWN = 0.0;
    public static final Integer OPERATION_TYPE = 65;
    public static final Long userId = 1L;

    //记录本次运行中需要关闭的冷机
    private List<Long> close = null;

    /**
     * 计算冷机运行优化策略
     * 根据评审意见，定时记录往前查询的间隔要大于任务执行间隔
     * @param time 任务运行时刻的时间戳
     * */
    public void calcOptimizationStrategy(LocalDateTime time){

        log.info("{}：开始执行，启动时间：{}，定时记录往前查询{}min", LOG_KEY, time, interval);
        QueryCondition condition = new QueryConditionBuilder<>(ModelLabelDef.REFRIGERATING_SYSTEM).eq(ColdOptimizationLabelDef.USEAI, true).build();
        List<RefrigeratingSystem> refrigeratingSystems = modelServiceUtils.query(condition, RefrigeratingSystem.class);
        log.info("{}：共有{}个制冷系统", LOG_KEY, refrigeratingSystems.size());
        for (RefrigeratingSystem item : refrigeratingSystems){
            log.info("{}：现在计算的制冷系统是：{}，对应的房间是：{}", LOG_KEY, item.getId(), item.getRoomId());
            JudgeParam judgeParam = getJudgeParam(item);
            if (Objects.isNull(judgeParam) || judgeParam.getColdWaterMainEnginesInOperation().isEmpty()) {
                log.info("{}：现在计算的制冷系统是：{}，没有找到运行中冷机信息", LOG_KEY, item.getId());
                continue;
            }
            commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, LOG_KEY + "运行中的冷机有：" + judgeParam.getColdWaterMainEnginesInOperation()
                    + "，待机中的冷机有：" + judgeParam.getColdWaterMainEnginesInStandby(), null, userId);

            //先做设备故障判断
            judgeConfiguration.deviceAbnormalJudge(judgeParam, time);
            close = judgeParam.getColdWaterMainEnginesInTarget();
            commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, LOG_KEY + "冷机：" + close + "及其连锁发现设备故障信号", null, userId);
            closingJudge(time, judgeParam, close);
            beforeWrite(judgeParam);
            writeAiStartStopStrategy(judgeParam, time);
            log.info("{}：故障判断结束，判断结果：{}", LOG_KEY, judgeParam.getIsOptimization());

            //加机判断
            judgeConfiguration.initAddMachineConfiguration(item.getId(), ColdControlTypeDef.ADD, time, judgeParam);
            judgeConfiguration.executeJudge();
            log.info("{}：加机判断结束，判断结果：{}", LOG_KEY, judgeParam.getIsOptimization());

            //减机判断
            if (!judgeParam.getIsOptimization()){
                judgeConfiguration.initAddMachineConfiguration(item.getId(), ColdControlTypeDef.SUB, time, judgeParam);
                judgeConfiguration.executeJudge();
                log.info("{}：减机判断结束，判断结果：{}", LOG_KEY, judgeParam.getIsOptimization());
            }

            coldWaterMainEngineSwitch.calcColdWaterMainEngineSwitch(judgeParam, time);
            close.addAll(judgeParam.getColdWaterMainEnginesInTarget());
            close = close.stream().distinct().collect(Collectors.toList());
            closingJudge(time, judgeParam, close);
            beforeWrite(judgeParam);
            writeAiStartStopStrategy(judgeParam, time);
        }
    }

    private JudgeParam getJudgeParam(RefrigeratingSystem item) {
        List<BaseVo> coldWaterMainEngines = operationTrendService.queryColdWaterMainEngine(
                new QueryParam(item.getRoomId(), NodeLabelDef.ROOM), NodeLabelDef.COLD_WATER_MAINENGINE);
        if (CollectionUtils.isEmpty(coldWaterMainEngines)){
            log.info("{}：制冷系统id:{}，其下没有冷机，跳过本次执行", LOG_KEY, item.getId());
            return null;
        }
        log.info("{}：制冷系统id:{}，有冷机{}个", LOG_KEY, item.getId(), coldWaterMainEngines.size());

        //判断冷机启停状态
        List<RealTimeValue> currentPercentageDatas = quantityManageService.queryRealTimeBath(
                createQuantityDataBatchSearchVo(coldWaterMainEngines, new QueryParam(),
                        Collections.singletonList(QuantityDef.getDeviceStatus()))).get(QuantityDef.getDeviceStatus().getId());
        //得到在运行的冷机id和待机冷机的id
        List<Long> coldWaterMainEnginesInOperation = currentPercentageDatas.stream().filter(x -> Objects.equals(x.getValue(), UP)).map(RealTimeValue::getMonitoredId).collect(Collectors.toList());
        List<Long> coldWaterMainEnginesInStandby = currentPercentageDatas.stream().filter(x -> Objects.equals(x.getValue(), DOWN)).map(RealTimeValue::getMonitoredId).collect(Collectors.toList());

        JudgeParam judgeParam = new JudgeParam(coldWaterMainEngines, coldWaterMainEnginesInOperation, coldWaterMainEnginesInStandby, item.getId(), item.getRoomId());
        log.info("{}：运行中冷机有：{}", LOG_KEY, coldWaterMainEnginesInOperation);
        log.info("{}：待机中冷机有：{}", LOG_KEY, coldWaterMainEnginesInStandby);
        return judgeParam;
    }

    /**
     * 构建定时记录查询参数
     * */
    private QuantityDataBatchSearchVo createQuantityDataBatchSearchVo(List<BaseVo> deviceNodes, QueryParam
            query, List<QuantitySearchVo> quantitySearchVo) {
        QuantityDataBatchSearchVo aggregationDataBatch = new QuantityDataBatchSearchVo();
        aggregationDataBatch.setDataTypeId(EnumDataTypeId.REALTIME.getId());
        if (Objects.nonNull(query.getStartTime())) {
            aggregationDataBatch.setStartTime(TimeUtil.localDateTime2timestamp(query.getStartTime()));
            aggregationDataBatch.setEndTime(TimeUtil.localDateTime2timestamp(query.getEndTime()));
        }
        aggregationDataBatch.setQuantitySettings((quantitySearchVo));
        aggregationDataBatch.setNodes(deviceNodes);
        aggregationDataBatch.setAggregationCycle(query.getCycle());
        return aggregationDataBatch;
    }

    //写入策略和节点关联模型
    public void writeAiStartStopStrategy(JudgeParam judgeParam, LocalDateTime time){
        commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, LOG_KEY + "本轮待写入的策略涉及冷机：" + judgeParam.getColdWaterMainEnginesInTarget(), null, userId);
        if (CollectionUtils.isEmpty(judgeParam.getColdWaterMainEnginesInTarget()) || !judgeParam.getIsOptimization()){
            log.info("{}：本轮判断无需执行优化", LOG_KEY);
            return;
        }
        log.info("{}：开始写入策略，冷机ID有：{}", LOG_KEY, judgeParam.getColdWaterMainEnginesInTarget());
        AiStartStopStrategy aiStartStopStrategy = new AiStartStopStrategy(TimeUtil.localDateTime2timestamp(time.plusMinutes(interval)),
                judgeParam.getOptimizationType(), TimeUtil.localDateTime2timestamp(time), judgeParam.getRefrigeratingSystemID());
        aiStartStopStrategy.setModelLabel(ColdOptimizationLabelDef.AI_START_STOP_STRATEGY);
        List<AiStartStopStrategy> aiStartStopStrategies = modelServiceUtils.writeData(aiStartStopStrategy, AiStartStopStrategy.class);
        BaseVo parentNode = new BaseVo(aiStartStopStrategies.get(0).getId(), ColdOptimizationLabelDef.AI_START_STOP_STRATEGY);

        for (Long id : judgeParam.getColdWaterMainEnginesInTarget()){
            List<BaseVo> deviceChain = judgeConfiguration.getDeviceChain(NodeLabelDef.COLD_WATER_MAINENGINE, id, judgeParam.getRoomID());
            List<StrategyObjectMap> strategyObjectMapList = new ArrayList<>();
            for (BaseVo item : deviceChain){
                StrategyObjectMap strategyObjectMap = new StrategyObjectMap();
                strategyObjectMap.setModelLabel(ColdOptimizationLabelDef.STRATEGY_OBJECT_MAP);
                strategyObjectMap.setStrategyId(parentNode.getId());
                strategyObjectMap.setObjectId(item.getId());
                strategyObjectMap.setObjectLabel(item.getModelLabel());
                strategyObjectMapList.add(strategyObjectMap);
            }
            List<StrategyObjectMap> strategyObjectMapListRes = modelServiceUtils.writeData(strategyObjectMapList, StrategyObjectMap.class);
            List<BaseVo> childNode = new ArrayList<>();
            for (StrategyObjectMap a : strategyObjectMapListRes){
                BaseVo baseVo = new BaseVo(a.getId(), ColdOptimizationLabelDef.STRATEGY_OBJECT_MAP);
                childNode.add(baseVo);
            }

            modelServiceUtils.writeRelations(parentNode, childNode);
            log.info("{}：写入策略完成", LOG_KEY);
        }
    }

    /*
    * 在夏季时段内，需要保证现场至少有一台冷机在运行
    * 因为策略任务的一次运行中，可能存在多次关闭的情况，需要检查所有将关闭的冷机是否等于全部冷机，是则保留至少一台
    * */
    private void closingJudge(LocalDateTime time, JudgeParam judgeParam, List<Long> close){
        if (Objects.equals(judgeParam.getOptimizationType(), ColdControlTypeDef.SUB)){
//            ParameterConfig parameterConfig = queryParameterConfig(judgeParam);
//            if (Objects.isNull(parameterConfig)){return;}
//            if (!summerJudge(time, parameterConfig)){
//                log.info("{}：当前时间不处于系统配置的夏季时段", LOG_KEY);
//                return;
//            }
//            commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, LOG_KEY + "当前处于夏季时段，需要保留至少一台冷机在运行。运行中冷机有："
//                    + judgeParam.getColdWaterMainEnginesInOperation() + "，将要关闭的冷机有：" + close + "，从中进行保留", null, userId);
//            //判断将要关机的冷机是否包含运行中的冷机，包含则从将要关机的冷机中去掉一台
            if (isSubList(close, judgeParam.getColdWaterMainEnginesInOperation())){
                log.info("{}：将要关机的冷机包含运行中的冷机，需要保留至少一台冷机在运行", LOG_KEY);
                commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, LOG_KEY + "将要关机的冷机包含: " + close+ " 需要保留至少一台冷机在运行", null, userId);
                List<Long> coldWaterMainEnginesInTarget =  new ArrayList<>(judgeParam.getColdWaterMainEnginesInTarget());
                List<Long> res = new ArrayList<>(CollectionUtils.subtract(coldWaterMainEnginesInTarget, close));
                judgeParam.setColdWaterMainEnginesInTarget(res);
            }
        }
    }

    private ParameterConfig queryParameterConfig(JudgeParam judgeParam) {
        QueryCondition condition = new QueryConditionBuilder<>(ColdOptimizationLabelDef.PARAMETER_CONFIG)
                .eq(ColdOptimizationLabelDef.SYSTEM_ID, judgeParam.getRefrigeratingSystemID()).build();
        List<ParameterConfig> parameterConfigs = modelServiceUtils.query(condition, ParameterConfig.class);
        if (CollectionUtils.isEmpty(parameterConfigs)){
            log.error("{}：制冷系统ID{}没有找到对应的参数配置", LOG_KEY, judgeParam.getRefrigeratingSystemID());
            judgeParam.setColdWaterMainEnginesInTarget(null);
            return null;
        }
        //一个制冷系统只有一个参数配置
        return parameterConfigs.get(0);
    }

    /*
    * 判断当前是否处于系统配置的夏季时段
    * */
    private Boolean summerJudge(LocalDateTime time, ParameterConfig parameterConfig){
        if(Objects.isNull(parameterConfig)){
            return false;
        }
        if (time.getMonth().getValue() >= parameterConfig.getSummerStartMonth() && time.getMonth().getValue() <= parameterConfig.getSummerEndMonth()){
            return time.getDayOfMonth() >= parameterConfig.getSummerStartDay() && time.getDayOfMonth() <= parameterConfig.getSummerEndDay();
        }
        return false;
    }

    /*
    * 判断一个list是不是另一个list的子集
    * */
    private static <E> boolean isSubList(List<E> parent, List<E> children) {
        List<E> parent_1 = new ArrayList<>();
        parent_1.addAll(parent);
        List<E> children_1 = new ArrayList<>();
        children_1.addAll(children);
        int differ = parent_1.size() - children_1.size();
        parent_1.removeAll(children_1);
        if (differ > 0 && differ == parent_1.size()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 策略写入前的检查
     * 已开启的冷机不要再开，已关闭的冷机不要再关
     * */
    private void beforeWrite(JudgeParam judgeParam){
        List<Long> coldWaterMainEnginesInTarget = judgeParam.getColdWaterMainEnginesInTarget().stream().collect(Collectors.toList());
        List<Long> coldWaterMainEnginesInOperation = judgeParam.getColdWaterMainEnginesInOperation().stream().collect(Collectors.toList());
        List<Long> coldWaterMainEnginesInStandby = judgeParam.getColdWaterMainEnginesInStandby().stream().collect(Collectors.toList());
        if (Objects.equals(judgeParam.getOptimizationType(), ColdControlTypeDef.ADD)){
            commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, LOG_KEY + "当前操作是加机，目标冷机是：" + coldWaterMainEnginesInTarget
                    + "，已运行的冷机是：" + coldWaterMainEnginesInOperation + "，已开启的冷机不会再建议开启", null, userId);
            coldWaterMainEnginesInTarget.removeAll(coldWaterMainEnginesInOperation);
        }else {
            commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, LOG_KEY + "当前操作是减机，目标冷机是：" + coldWaterMainEnginesInTarget
                    + "，待机中的冷机是：" + coldWaterMainEnginesInStandby + "，已关闭的冷机不会再建议关闭", null, userId);
            coldWaterMainEnginesInTarget.removeAll(coldWaterMainEnginesInStandby);
        }
        judgeParam.setColdWaterMainEnginesInTarget(coldWaterMainEnginesInTarget);
    }
}

package com.cet.eem.bll.compressoroptimization.model.benefit;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * @ClassName : BenefitQueryParam
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-18 13:43
 */
@Getter
@Setter
public class BenefitQueryParam {
   private Long roomId;
   private LocalDateTime startTime;
   private LocalDateTime endTime;
   private Integer cycle;
   private String pic;
}
package com.cet.eem.fusion.transformer.core.entity.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : LoadInfoVo
 * @Description : 负载信息
 * <AUTHOR> jiangzixuan
 * @Date: 2022-03-07 15:07
 */
@Getter
@Setter
public class LoadInfoVo {
    /**
     * 实时负载
     */
    private Double loadRealTime;
    /**
     * 历史最大负载
     */
    private Double loadHis;
    /**
     * 历史最大负载率
     */
    private Double loadHisRate;
    /**
     * 经济运行区间下限
     */
    private Double sectionLowLimit;
    /**
     * 经济运行区间上限
     */
    private Double sectionUpperLimit;
    /**
     * 额定容量
     */
    private Double ratedCapacity;
    private List<SectionVo> sections;
}

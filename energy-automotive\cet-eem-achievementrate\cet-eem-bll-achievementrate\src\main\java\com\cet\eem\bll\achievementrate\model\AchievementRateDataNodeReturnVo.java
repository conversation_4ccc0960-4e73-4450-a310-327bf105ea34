package com.cet.eem.bll.achievementrate.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : AchievementRateDataNodeReturnVo
 * @Description :
 * <AUTHOR> jiang<PERSON><PERSON>uan
 * @Date: 2023-11-21 11:23
 */
@Getter
@Setter
public class AchievementRateDataNodeReturnVo {

    @ApiModelProperty("关联的模型")
    private String objectLabel;
    @ApiModelProperty("关联的模型ID")
    private Long objectId;
    @ApiModelProperty("录入的节点")
    private List<AchievementRateReturnVo> dataNodes;

}
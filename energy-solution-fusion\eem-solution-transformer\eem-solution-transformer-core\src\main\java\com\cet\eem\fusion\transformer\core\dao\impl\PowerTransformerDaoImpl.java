package com.cet.eem.fusion.transformer.core.dao.impl;

import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import com.cet.eem.toolkit.CollectionUtils;
import com.cet.eem.fusion.transformer.core.dao.PowerTransformerDao;
import com.cet.eem.fusion.transformer.core.entity.dto.PowerTransformerDto;
import com.cet.eem.fusion.transformer.core.entity.dto.ProjectDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName : PowerTransformerDaoImpl
 * <AUTHOR> yangy
 * @Date: 2022-03-15 15:46
 */
@Component
public class PowerTransformerDaoImpl implements PowerTransformerDao {

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Override
    public List<PowerTransformerDto> queryAll() {
        QueryCondition queryCondition = new QueryConditionBuilder<>(NodeLabelDef.POWER_TRANS_FORMER).build();
        return modelServiceUtils.query(queryCondition, PowerTransformerDto.class);
    }

    @Override
    public List<PowerTransformerDto> queryPowerTransformerAndProject() {
        return modelServiceUtils.queryWithChildren(null, NodeLabelDef.POWER_TRANS_FORMER, null, Collections.singletonList(NodeLabelDef.PROJECT), PowerTransformerDto.class);
    }

    @Override
    public List<PowerTransformerDto> queryByProjectId(Long projectId) {
        if (Objects.isNull(projectId)) {
            return new ArrayList<>();
        }
        List<ProjectDto> projectDtoList = modelServiceUtils.queryWithChildren(Collections.singletonList(projectId), NodeLabelDef.PROJECT, null, Collections.singletonList(NodeLabelDef.POWER_TRANS_FORMER), ProjectDto.class);
        if (CollectionUtils.isEmpty(projectDtoList) || CollectionUtils.isEmpty(projectDtoList.get(0).getPowertransformer_model())) {
            return new ArrayList<>();
        }
        return projectDtoList.get(0).getPowertransformer_model();
    }


}

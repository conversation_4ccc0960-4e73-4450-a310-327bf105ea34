package com.cet.eem.bll.energysaving.model.aioptimization;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : StrategyAdviceVo
 * @Description : 优化策略建议加字符串返回值
 * <AUTHOR> jiangzi<PERSON>uan
 * @Date: 2022-06-20 13:41
 */
@Getter
@Setter
public class StrategyAdviceVo {
    private List<StrategyAdvice> strategyAdvices;
    /**
     * 策略优化建议
     */
    private String string;
    /**
     * 策略输出时间
     */
    private Long time;
    /**
     * 策略是不是新策略（建议时间大于当前时间）
     */
    private Boolean isNew;
}
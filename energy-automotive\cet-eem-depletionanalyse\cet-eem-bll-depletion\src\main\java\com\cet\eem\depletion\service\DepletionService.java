package com.cet.eem.depletion.service;

import com.cet.eem.depletion.model.vo.*;

import java.io.IOException;
import java.util.List;

public interface DepletionService {
    //查询空耗总览
    WasteOverView depletionOverView(Long time, Long id, String modelLabel);

    //查询温度曲线
    TempTrend depletionTempTrend(Long time, Long id, String modelLabel, Integer lineBobyType) throws IOException;

    //获取各类空耗详情
    List<Detail> getDepletionDetails(Long time, Long id, String modelLabel, Integer depletionType) throws IOException;
}

package com.cet.eem.constforecast.service;


import com.cet.eem.constforecast.model.vo.CostForecastResult;
import com.cet.eem.constforecast.model.vo.FostForecast;
import com.cet.eem.constforecast.model.vo.FostForecastForm;
import com.cet.eem.model.model.BaseEntity;

import java.util.List;
import java.util.Map;

/**
 * @ClassName : CostForecastService
 * <AUTHOR> yangy
 * @Date: 2022-06-15 11:08
 */
public interface CostForecastService {

    List<FostForecast> queryDatas(FostForecastForm form, Map<Integer, Double> rates);

    CostForecastResult encapsulatedData(FostForecastForm form,Long projectId);

    List<BaseEntity> queryCostcheckplan(BaseEntity baseEntity,Long projectId);
}

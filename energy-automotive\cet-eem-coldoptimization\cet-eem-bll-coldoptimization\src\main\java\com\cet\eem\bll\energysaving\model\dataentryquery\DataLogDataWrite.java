package com.cet.eem.bll.energysaving.model.dataentryquery;

import com.cet.eem.common.model.datalog.DataLogData;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : DataLogDataWrite
 * @Description : 通用数据写入的入参
 * <AUTHOR> ji<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-11 09:00
 */
@Getter
@Setter
public class DataLogDataWrite {
    private Long projectId;
    private Long systemId;
    private List<DataLogData> dataLogData;
}
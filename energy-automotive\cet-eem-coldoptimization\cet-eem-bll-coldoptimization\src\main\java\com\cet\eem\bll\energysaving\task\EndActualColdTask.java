package com.cet.eem.bll.energysaving.task;

import com.cet.eem.bll.common.task.TaskSchedule;
import com.cet.eem.bll.energysaving.handle.EndActualColdHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2021/12/23
 */
@Component
@Slf4j
public class EndActualColdTask implements TaskSchedule {
    @Autowired
    EndActualColdHandle endActualColdHandle;

    @Scheduled(cron = "${cet.eem.task.energy-saving.end-cold-capacity.interval}")
    @Override
    public void execute() throws IOException, InstantiationException, IllegalAccessException {
        endActualColdHandle.calcColdActualData();
    }
}

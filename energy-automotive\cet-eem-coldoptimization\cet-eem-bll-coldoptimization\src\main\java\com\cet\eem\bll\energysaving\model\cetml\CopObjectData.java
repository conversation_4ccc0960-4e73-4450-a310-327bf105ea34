package com.cet.eem.bll.energysaving.model.cetml;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @ClassName : CopObjectData
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-06-08 10:50
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class CopObjectData {
    @JsonProperty("rated_refrigerating")
    private Double ratedRefrigerating;
    @JsonProperty("cooling_load")
    private Double coolingLoad;
    @JsonProperty("input_power")
    private Double inputPower;

}
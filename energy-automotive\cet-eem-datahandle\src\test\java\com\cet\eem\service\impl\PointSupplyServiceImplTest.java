package com.cet.eem.service.impl;

import com.cet.eem.def.DataHandleDef;
import com.cet.eem.model.param.RepairPointParam;
import com.cet.eem.model.vo.DataLogValue;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.reflect.Whitebox;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class PointSupplyServiceImplTest {


    private PointSupplyServiceImpl pointSupplyService = new PointSupplyServiceImpl();
    private List<RepairPointParam> repairPointParamList;

    @Test
    public void checkTimeContinuous_EmptyList_ReturnsFalse() throws Exception {
        Method method = pointSupplyService.getClass().getDeclaredMethod("checkTimeContinuous", List.class);
        method.setAccessible(true);
        assertFalse((Boolean) method.invoke(pointSupplyService, Collections.emptyList()));
    }

    @Test
    public void checkTimeContinuous_OneElement_ReturnsTrue() throws Exception {
        repairPointParamList = new ArrayList<>();
        RepairPointParam repairPointParam = new RepairPointParam();
        repairPointParam.setStartTime(1000L);
        repairPointParam.setEndTime(2000L);
        repairPointParamList.add(repairPointParam);

        Method method = pointSupplyService.getClass().getDeclaredMethod("checkTimeContinuous", List.class);
        method.setAccessible(true);
        assertTrue((Boolean) method.invoke(pointSupplyService, repairPointParamList));
    }

    @Test
    public void checkTimeContinuous_DiscontinuousTime_ReturnsFalse() throws Exception {
        repairPointParamList = new ArrayList<>();
        RepairPointParam repairPointParam1 = new RepairPointParam();
        repairPointParam1.setStartTime(1000L);
        repairPointParam1.setEndTime(2000L);
        repairPointParamList.add(repairPointParam1);

        RepairPointParam repairPointParam2 = new RepairPointParam();
        repairPointParam2.setStartTime(3000L);
        repairPointParam2.setEndTime(4000L);
        repairPointParamList.add(repairPointParam2);

        Method method = pointSupplyService.getClass().getDeclaredMethod("checkTimeContinuous", List.class);
        method.setAccessible(true);
        assertFalse((Boolean) method.invoke(pointSupplyService, repairPointParamList));
    }

    @Test
    public void checkTimeContinuous_ContinuousTime_ReturnsTrue() throws Exception {
        repairPointParamList = new ArrayList<>();
        RepairPointParam repairPointParam1 = new RepairPointParam();
        repairPointParam1.setStartTime(1000L);
        repairPointParam1.setEndTime(2000L);
        repairPointParamList.add(repairPointParam1);

        RepairPointParam repairPointParam2 = new RepairPointParam();
        repairPointParam2.setStartTime(2000L);
        repairPointParam2.setEndTime(3000L);
        repairPointParamList.add(repairPointParam2);

        Method method = pointSupplyService.getClass().getDeclaredMethod("checkTimeContinuous", List.class);
        method.setAccessible(true);
        assertTrue((Boolean) method.invoke(pointSupplyService, repairPointParamList));
    }

//    @Test
//    public void testRepairPoint_NormalOperation() throws Exception {
//        List<Long> times = new ArrayList<>(Arrays.asList(1619870400000L, 1619870700000L, 1619871000000L, 1619871300000L));
//        int startIndex = 0;
//        int endIndex = 3;
//        double startValue = 100;
//        double endValue = 1000;
//        double turnOVerEng = 1000;
//
//        List<DataLogValue> result = Whitebox.invokeMethod(pointSupplyService, "repairPoint", times, startIndex, endIndex, startValue, endValue, turnOVerEng);
//
//        assertEquals(3, result.size());
//
//        // 验证第一个元素
//        DataLogValue first = result.get(0);
//        assertEquals(1619870700000L, first.time);
//        assertEquals(200, first.value, 0.001);
//        assertEquals(DataHandleDef.STATUS_LINEAR_COMPENSATION, first.status);
//
//        // 验证第二个元素
//        DataLogValue second = result.get(1);
//        assertEquals(1619871000000L, second.time);
//        assertEquals(300, second.value, 0.001);
//        assertEquals(DataHandleDef.STATUS_LINEAR_COMPENSATION, second.status);
//
//        // 验证第三个元素
//        DataLogValue third = result.get(2);
//        assertEquals(1619871300000L, third.time);
//        assertEquals(400, third.value, 0.001);
//        assertEquals(DataHandleDef.STATUS_LINEAR_COMPENSATION, third.status);
//    }

//    @Test
//    public void testRepairPoint_WithZeroTurnOVerEng() throws Exception {
//        List<Long> times = new ArrayList<>(Arrays.asList(1619870400000L, 1619870700000L, 1619871000000L, 1619871300000L));
//        int startIndex = 0;
//        int endIndex = 3;
//        double startValue = 100;
//        double endValue = 1000;
//        double turnOVerEng = 0;
//
//        List<DataLogValue> result = Whitebox.invokeMethod(pointSupplyService, "repairPoint", times, startIndex, endIndex, startValue, endValue, turnOVerEng);
//
//        assertEquals(0, result.size());
//    }

    @Test
    public void testRepairPoint_WithNegativeEndIndex() throws Exception {
        List<Long> times = new ArrayList<>(Arrays.asList(1619870400000L, 1619870700000L, 1619871000000L, 1619871300000L));
        int startIndex = 0;
        int endIndex = -1;
        double startValue = 100;
        double endValue = 1000;
        double turnOVerEng = 1000;

        List<DataLogValue> result = Whitebox.invokeMethod(pointSupplyService, "repairPoint", times, startIndex, endIndex, startValue, endValue, turnOVerEng);

        assertEquals(0, result.size());
    }

}

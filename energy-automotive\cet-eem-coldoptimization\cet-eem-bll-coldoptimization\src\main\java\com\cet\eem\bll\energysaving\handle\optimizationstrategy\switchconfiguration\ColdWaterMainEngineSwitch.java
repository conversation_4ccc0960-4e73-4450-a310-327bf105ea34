package com.cet.eem.bll.energysaving.handle.optimizationstrategy.switchconfiguration;

import com.cet.eem.bll.energysaving.handle.optimizationstrategy.JudgeParam;
import com.cet.eem.bll.energysaving.handle.optimizationstrategy.judgeconfiguration.JudgeConfiguration;
import com.cet.eem.bll.energysaving.model.aioptimization.ColdSwitchPriorityType;
import com.cet.eem.bll.energysaving.model.config.ParameterConfig;
import com.cet.eem.bll.energysaving.model.def.ColdOptimizationLabelDef;
import com.cet.eem.bll.energysaving.model.def.ColdControlTypeDef;
import com.cet.eem.bll.energysaving.model.def.QuantityDef;
import com.cet.eem.bll.energysaving.model.weather.QueryParam;
import com.cet.eem.common.constant.EnumDataTypeId;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.DataLogData;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.model.realtime.RealTimeValue;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;
import com.cet.eem.quantity.service.QuantityManageService;
import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 冷机切换计算
 * */
@Configuration
@Slf4j
public class ColdWaterMainEngineSwitch {
    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    QuantityManageService quantityManageService;

    @Autowired
    JudgeConfiguration judgeConfiguration;

    @Value("${cet.eem.optimizationstrategy.query-interval}")
    private int interval;

    private static final String LOG_KEY = "[冷机运行优化策略计算-冷机切换计算]";

    public static final Double UP = 1.0;
    public static final Double DOWN = 0.0;

    public void calcColdWaterMainEngineSwitch(JudgeParam judgeParam, LocalDateTime time){
        if (!judgeParam.getIsOptimization()){
            return;
        }
        log.info("{}：开始执行，待操作冷机有：{}", LOG_KEY, judgeParam.getColdWaterMainEnginesInTarget());

        QueryCondition condition = new QueryConditionBuilder<>(ColdOptimizationLabelDef.PARAMETER_CONFIG)
                                    .eq(ColdOptimizationLabelDef.SYSTEM_ID, judgeParam.getRefrigeratingSystemID()).build();
        List<ParameterConfig> parameterConfigs = modelServiceUtils.query(condition, ParameterConfig.class);
        if (CollectionUtils.isEmpty(parameterConfigs)){
            log.info("{}：制冷系统ID{}没有找到对应的参数配置", LOG_KEY, judgeParam.getRefrigeratingSystemID());
            judgeParam.setColdWaterMainEnginesInTarget(null);
            return;
        }
        //一个制冷系统只有一个参数配置
        ParameterConfig parameterConfig = parameterConfigs.get(0);

        //24h内启动次数
        List<TrendDataVo> numberOfStart = quantityManageService.queryDataLogBatch(
                createQuantityDataBatchSearchVo(judgeParam.getColdWaterMainEngines(), new QueryParam(time.plusMinutes(-10), time, AggregationCycle.FIVE_MINUTES),
                        Collections.singletonList(QuantityDef.getNumberOfStart()))).get(QuantityDef.getNumberOfStart().getId());
        //冷机运行时长
        List<TrendDataVo> runningTime = quantityManageService.queryDataLogBatch(
                createQuantityDataBatchSearchVo(judgeParam.getColdWaterMainEngines(), new QueryParam(time.plusMinutes(-10), time, AggregationCycle.FIVE_MINUTES),
                        Collections.singletonList(QuantityDef.getRunningTime()))).get(QuantityDef.getRunningTime().getId());

        if (CollectionUtils.isEmpty(numberOfStart) || CollectionUtils.isEmpty(runningTime)){
            log.info("{}：制冷系统ID：{}，没有找到对应的冷机的运行情况，如启动次数、运行时长", LOG_KEY, judgeParam.getRefrigeratingSystemID());
            judgeParam.setColdWaterMainEnginesInTarget(null);
            return;
        }

        List<TrendDataVo> currentPercentageDatas = quantityManageService.queryDataLogBatch(
                createQuantityDataBatchSearchVo(judgeParam.getColdWaterMainEngines(), new QueryParam(time.plusMinutes(-(parameterConfig.getStartStopTimeInterval()/1000/60)), time, AggregationCycle.FIVE_MINUTES),
                        Collections.singletonList(QuantityDef.getMachineI()))).get(QuantityDef.getMachineI().getId());

        filterBySignal(judgeParam, time);
        filterByIntervalAndStartNum(judgeParam, parameterConfig, numberOfStart, currentPercentageDatas);
        filterByRunningTime(judgeParam, parameterConfig, runningTime);

    }

    private QuantityDataBatchSearchVo createQuantityDataBatchSearchVo(List<BaseVo> deviceNodes, QueryParam
            query, List<QuantitySearchVo> quantitySearchVo) {
        QuantityDataBatchSearchVo aggregationDataBatch = new QuantityDataBatchSearchVo();
        aggregationDataBatch.setDataTypeId(EnumDataTypeId.REALTIME.getId());
        if (Objects.nonNull(query.getStartTime())) {
            aggregationDataBatch.setStartTime(TimeUtil.localDateTime2timestamp(query.getStartTime()));
            aggregationDataBatch.setEndTime(TimeUtil.localDateTime2timestamp(query.getEndTime()));
        }
        aggregationDataBatch.setQuantitySettings((quantitySearchVo));
        aggregationDataBatch.setNodes(deviceNodes);
        aggregationDataBatch.setAggregationCycle(query.getCycle());
        return aggregationDataBatch;
    }

    /**
     * 根据冷机启停时间间隔和一天内启停次数进行过滤
     * */
    public void filterByIntervalAndStartNum(JudgeParam judgeParam, ParameterConfig parameterConfig,
                                          List<TrendDataVo> numberOfStart, List<TrendDataVo> currentPercentageDatas) {
        List<Long> coldWaterMainEnginesInTarget = judgeParam.getColdWaterMainEnginesInTarget();
        if (coldWaterMainEnginesInTarget.isEmpty()){
            log.info("{}：无可用设备供切换，退出", LOG_KEY);
            return;
        }
        log.info("{}：对待机设备进行启停时间间隔和启停次数判断，待机设备ID：{}", LOG_KEY, judgeParam.getColdWaterMainEnginesInTarget());
        judgeParam.setColdWaterMainEnginesInTarget(null);
        List<Long> res = new ArrayList<>();
        //从目标操作冷机中做进一步过滤
        for (Long target: coldWaterMainEnginesInTarget){
            List<TrendDataVo> collectForNumberOfStart = numberOfStart.stream().filter(x -> x.getMonitoredid().equals(target)).collect(Collectors.toList());
            List<TrendDataVo> collect = currentPercentageDatas.stream().filter(x -> x.getMonitoredid().equals(target)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collectForNumberOfStart) || CollectionUtils.isEmpty(collect)){
                continue;
            }
            List<DatalogValue> dataList = collectForNumberOfStart.get(0).getDataList();
            if (CollectionUtils.isEmpty(dataList)){
                continue;
            }
            DatalogValue dataLogData = dataList.get(0);
            Boolean hadStartUp = collect.get(0).getDataList().stream().anyMatch(x -> Objects.equals(x.getValue(), DOWN)) && collect.get(0).getDataList().stream().anyMatch(x ->x.getValue() > DOWN);
            /*如果24h内启停次数超过设定值，该冷机不符合条件，跳出
            如果在 （当前时间-启停时间间隔， 当前时间）的区间内，有电流百分比数据，说明该冷机在间隔时间内启动过，不符合条件，跳出*/
            if (dataLogData.getValue() >= parameterConfig.getStartStopTime() || hadStartUp){
                continue;
            }

            //将满足以上过滤的冷机id返回目标操作冷机列表中
            res.add(target);
        }
        log.info("{}：启停时间间隔和启停次数判断结束，可用设备有：{}", LOG_KEY, res);
        judgeParam.setColdWaterMainEnginesInTarget(res);
    }

    /**
     * 根据冷机运行时长进行过滤
     * */
    public void filterByRunningTime(JudgeParam judgeParam, ParameterConfig parameterConfig, List<TrendDataVo> runningTime){
        List<Long> coldWaterMainEnginesInTarget = judgeParam.getColdWaterMainEnginesInTarget();
        if (!parameterConfig.getColdSwitchPriority().equals(ColdSwitchPriorityType.runtime) || CollectionUtils.isEmpty(coldWaterMainEnginesInTarget)){
            log.info("{}：无可用设备供切换，退出", LOG_KEY);
            return;
        }
        log.info("{}：对待机设备进行运行时长判断，待机设备ID：{}", LOG_KEY, judgeParam.getColdWaterMainEnginesInTarget());
        List<TrendDataVo> collectForRunningTime = runningTime.stream().filter(x -> judgeParam.getColdWaterMainEnginesInTarget().contains(x.getMonitoredid())).collect(Collectors.toList());

        //key-运行时长，value-冷机id，如果key相同，随便取那个都可
        Map<Double, Long> timeMap = new HashMap<>();
        for (TrendDataVo item : collectForRunningTime){
            if (judgeParam.getColdWaterMainEnginesInTarget().contains(item.getMonitoredid())){
                if (CollectionUtils.isNotEmpty(item.getDataList())){
                    timeMap.put(item.getDataList().get(0).getValue(), item.getMonitoredid());
                }
            }
        }

        if (judgeParam.getOptimizationType().equals(ColdControlTypeDef.ADD)){
            //加机，选择累计运行时长最小的
            Optional<Double> optionalDouble = timeMap.keySet().stream().min(Comparator.comparingDouble(Double::doubleValue));
            if (optionalDouble.isPresent()){
                Double min = optionalDouble.get();
                judgeParam.setColdWaterMainEnginesInTarget(Collections.singletonList(timeMap.get(min)));
            }
        }else {
            //减机，选择累计运行时长最大的
            Optional<Double> optionalDouble = timeMap.keySet().stream().max(Comparator.comparingDouble(Double::doubleValue));
            if (optionalDouble.isPresent()){
                Double max = optionalDouble.get();
                judgeParam.setColdWaterMainEnginesInTarget(Collections.singletonList(timeMap.get(max)));
            }
        }
        log.info("{}：运行时长判断结束，可用设备有：{}", LOG_KEY, judgeParam.getColdWaterMainEnginesInTarget());
    }

    /**
     * 判断待开启冷机是否正常
     * */
    public void filterBySignal(JudgeParam judgeParam, LocalDateTime time){
        if (judgeParam.getIsOptimization() && judgeParam.getOptimizationType().equals(ColdControlTypeDef.ADD)){
            log.info("{}：对待机设备进行信号判断，待机设备ID：{}", LOG_KEY, judgeParam.getColdWaterMainEnginesInTarget());
            List<Long> coldWaterMainEnginesInTarget = judgeParam.getColdWaterMainEnginesInTarget();
            judgeParam.setColdWaterMainEnginesInTarget(null);
                List<Long> res = new ArrayList<>();
            for (Long coldWaterMainEnginesID : coldWaterMainEnginesInTarget){
                //查询出冷机对应连锁下的所有设备
                List<BaseVo> deviceChains = judgeConfiguration.getDeviceChain(NodeLabelDef.COLD_WATER_MAINENGINE, coldWaterMainEnginesID, judgeParam.getRoomID());
                Map<Integer, List<RealTimeValue>> signalData = quantityManageService.queryRealTimeBath(
                        new QuantityDataBatchSearchVo(Arrays.asList(QuantityDef.getFaultSignal()),
                                deviceChains, TimeUtil.localDateTime2timestamp(time.plusMinutes(-interval)), TimeUtil.localDateTime2timestamp(time),
                                AggregationCycle.FIVE_MINUTES, 5, EnumDataTypeId.REALTIME.getId())
                );
                    List<RealTimeValue> faultSignalData = signalData.get(QuantityDef.getFaultSignal().getId());
                    List<RealTimeValue> DeviceStatusData = signalData.get(QuantityDef.getDeviceStatus().getId());
                    List<RealTimeValue> collectForFaultSignal = faultSignalData.stream().filter(x -> Objects.equals(x.getValue(), UP)).collect(Collectors.toList());
                    List<RealTimeValue> collectForDeviceStatus = DeviceStatusData.stream().filter(x -> Objects.equals(x.getValue(), DOWN)).collect(Collectors.toList());
                    //过滤所有设备的故障、停止信号，只要有一个为1，说明存在异常，不可开启
                    if (!org.apache.commons.collections4.CollectionUtils.isEmpty(collectForFaultSignal) || !org.apache.commons.collections4.CollectionUtils.isEmpty(collectForDeviceStatus)){
                        break;
                    }
                    res.add(coldWaterMainEnginesID);

             }
            log.info("{}：信号判断结束，可用设备有：{}", LOG_KEY, res);
            judgeParam.setColdWaterMainEnginesInTarget(res.stream().distinct().collect(Collectors.toList()));
        }
    }

}

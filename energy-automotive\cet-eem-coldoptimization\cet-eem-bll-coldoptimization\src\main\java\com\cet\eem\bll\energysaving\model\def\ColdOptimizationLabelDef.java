package com.cet.eem.bll.energysaving.model.def;

import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : ColdOptimizationLabelDef
 * @Description : 制冷新加类抽取
 * <AUTHOR> jiangzixuan
 * @Date: 2022-07-04 16:25
 */
@Getter
@Setter
public class ColdOptimizationLabelDef {
    public static final String BOOLEAN_CONDITION = "booleancondition";
    public static final String CONTROL_SCHEME_ID = "controlschemeid";
    public static final String SIGNAL_TYPE = "signaltype";
    public static final String CONTROL_SCHEME = "controlscheme";
    public static final String CONTROL_SCHEME_NAME = "controlschemename";
    public static final String REFRIGERATING_SYSTEM_ID = "refrigeratingsystemid";
    public static final String CONTROL_TYPE = "controltype";
    public static final String NUMERICAL_CONDITION = "numericalcondition";
    public static final String TIME_VALUE = "timevalue";
    public static final String COMPARISON_OBJECT_ID = "comparisonobjectid";
    public static final String COMPARISON_LABEL = "comparisonlabel";
    public static final String DATA_TYPE = "datatype";
    public static final String OPERATION_SYMBOL = "operationsymbol";
    public static final String OPERATION_SYMBOL_OF_TIME = "operationsymboloftime";
    public static final String SELF_COMPARISON = "selfcomparison";
    public static final String EQUIPMENT_CURVE = "equipmentcurve";
    public static final String CURVE_TYPE = "curvetype";
    public static final String QUADRATIC_TERM_COEFFICIENT = "quadratictermcoefficient";
    public static final String PRIMARY_TERM_COEFFICIENT = "primarytermcoefficient";
    public static final String CONSTANT = "constant";
    public static final String PARAMETER_CONFIG = "parameterconfig";
    public static final String SYSTEM_ID = "systemid";
    public static final String COLD_SWITCH_PRIORITY = "coldswitchpriority";
    public static final String START_STOP_TIME_INTERVAL = "startstoptimeinterval";
    public static final String RUNTIME_DIFF = "runtimedifference";
    public static final String START_STOP_TIME = "startstoptime";
    public static final String SUMMER_END_DAY = "summerendday";
    public static final String SUMMER_END_MONTH = "summerendmonth";
    public static final String SUMMER_START_DAY = "summerstartday";
    public static final String SUMMER_START_MONTH = "summerstartmonth";
    public static final String WORK_PERIOD_TEMP = "workperiodtemp";
    public static final String REST_PERIOD_TEMP_MIN = "restperiodtempmin";
    public static final String REST_PERIOD_TEMP_MAX = "restperiodtempmax";
    public static final String CHILL_WATER_SUPPLY_PRESS_MAX = "chilledwatersupplypressuremax";
    public static final String CHILL_WATER_SUPPLY_PRESS_MIN = "chilledwatersupplypressuremin";
    public static final String CHILL_WATER_RETURN_PRESS_DIFF_MIN = "chilledwaterreturnpressurediffmin";
    public static final String CHILL_WATER_RETURN_PRESS_DIFF_MAX = "chilledwaterreturnpressurediffmax";
    public static final String COOL_WATER_SUPPLY_TEMP_MAX = "coolwatersupplytempmax";
    public static final String COOL_WATER_SUPPLY_TEMP_MIN = "coolwatersupplytempmin";
    public static final String SUPPLY_RETURN_WATER_DIFF = "supplyreturnwatertempdiff";
    public static final String WORK_SECTION = "worksection";
    public static final String SECTION_DATA_TYPE = "sectiondatatype";
    public static final String SECTION_TYPE = "sectiontype";
    public static final String STRATEGY_OBJECT_MAP = "strategyobjectmap";
    public static final String USEAI = "useai";
    public static final String STRATEGY_TYPE = "strategytype";
    public static final String STRATEGY_ID = "strategyid";
    public static final String PRESSURE_DIFF = "pressurediff";
    public static final String FUNCTION_TYPE = "functiontype";
    public static final String AI_START_STOP_STRATEGY = "aistartstopstrategy";
    public static final String OPERATION_TIME = "operationtime";
    public static final String METEORO_LOGICAL_MONITOR = "meteorologicalmonitor";
    public static final String COLD_LOAD_TYPE = "coldloadtype";
    public static final String PREDICT_DATA_TYPE = "predictdatatype";
    public static final String MES_SHIFT = "messhift";
    public static final String SHIFT_TYPE = "shifttype";
    public static final String CONTROL_MODE = "controlmode";
    public static final String PLC = "plc";
    public static final String PRODUCTION_DATA_DOCKING = "productiondatadocking";
    /**
     * 板换开启温度限制
     */
    public static final String OUTSIDE_TEMP_LIMIT = "outsidetemplimit";
    /**
     * 板换开启冷机制冷需求限制
     */
    public static final String COOLING_LOAD_DEMAND_LIMIT = "coolingloaddemandlimit";

}
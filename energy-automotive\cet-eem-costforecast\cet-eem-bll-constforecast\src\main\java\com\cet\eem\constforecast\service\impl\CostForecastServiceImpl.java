package com.cet.eem.constforecast.service.impl;

import com.cet.eem.bll.common.dao.energy.ProjectEnergyTypeDao;
import com.cet.eem.bll.common.model.domain.object.physicalquantity.ProjectUnitClassify;
import com.cet.eem.bll.common.model.domain.object.physicalquantity.UserDefineUnit;
import com.cet.eem.bll.common.model.domain.subject.energy.EnergyConsumption;
import com.cet.eem.bll.common.model.domain.subject.energy.ProjectEnergyType;
import com.cet.eem.bll.common.model.domain.subject.energy.TotalEnergyConsumptionPredict;
import com.cet.eem.bll.common.model.domain.subject.energy.plan.ProductionPlan;
import com.cet.eem.bll.common.model.domain.subject.generalrules.FeeScheme;
import com.cet.eem.bll.common.model.domain.subject.production.CostCheckItem;
import com.cet.eem.bll.common.model.domain.subject.production.CostCheckNodeConfig;
import com.cet.eem.bll.common.model.domain.subject.production.CostCheckPlan;
import com.cet.eem.bll.common.model.ext.objective.physicalquantity.UserDefineUnitSearchDto;
import com.cet.eem.bll.common.service.EnergyUnitService;
//import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.bll.energy.dao.consumption.EnergyConsumptionDao;
import com.cet.eem.bll.energy.dao.consumption.TotalEnergyConsumptionPredictDao;
import com.cet.eem.bll.energy.dao.cost.CostCheckItemDao;
import com.cet.eem.bll.energy.dao.cost.CostCheckNodeConfigDao;
import com.cet.eem.bll.energy.model.costvalue.ObjectCostValueVo;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.constant.EnumOperationType;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.common.exception.ValidationException;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.constforecast.dao.ConsFeeSchemeDao;
import com.cet.eem.constforecast.dao.GivenObjectCostValueDao;
import com.cet.eem.constforecast.model.CostForecast;
import com.cet.eem.constforecast.model.vo.AvgUnitPrice;
import com.cet.eem.constforecast.model.vo.CostForecastResult;
import com.cet.eem.constforecast.model.vo.FostForecast;
import com.cet.eem.constforecast.model.vo.FostForecastForm;
import com.cet.eem.constforecast.service.CostForecastService;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.model.BaseEntity;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import com.cet.eem.toolkit.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : CostForecastServiceImpl
 * <AUTHOR> yangy
 * @Date: 2022-06-15 11:08
 */
@Service
public class CostForecastServiceImpl implements CostForecastService {

    @Autowired
    TotalEnergyConsumptionPredictDao totalEnergyConsumptionPredictDao;
    @Autowired
    CostCheckNodeConfigDao costCheckNodeConfigDao;
    @Autowired
    CostCheckItemDao costCheckItemDao;
    @Autowired
    EnergyConsumptionDao energyconsumptionDao;
    @Autowired
    GivenObjectCostValueDao objectCostValueDao;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    ConsFeeSchemeDao feeSchemeDao;
    @Autowired
    ProjectEnergyTypeDao projectEnergyTypeDao;
    @Autowired
    EnergyUnitService energyUnitService;
    private final Integer RMB_UnitClassify = 1;
    private final String UNIT_STR = "元/";
    private final String PREFIX = "综合能耗/";

    @Override
    public List<FostForecast> queryDatas(FostForecastForm form, Map<Integer, Double> rateMap) {
        if (CollectionUtils.isEmpty(form.getEnergyTypes())) {
            return getEmptyList(form);
        }
        BaseVo baseVo = new BaseVo(form.getNodeId(), form.getNodelLabel());
        Integer cycel = getCycel(form.getCycle());
        List<TotalEnergyConsumptionPredict> predicts = totalEnergyConsumptionPredictDao.queryTotalEnergyConsumptionPredict(baseVo, form.getStartTime(), form.getEndTime(), cycel, form.getEnergyTypes());
        CostForecast costForecast = CostForecast.getCostForecast(form.getType());
        List<FostForecast> forecasts;
        switch (costForecast) {
            case COSTPERUNIT:
                //单台成本预测
                List<FostForecast> totalCost = getTotalCost(predicts, form, rateMap);
                forecasts = getCostPerUnit(totalCost, form, cycel);
                break;
            case UNITCONSUMPTION:
                //单耗预测
                forecasts = getUnitConsumption(predicts, form, cycel);
                break;
            case COMPREHENSIVECOST:
                //综合成本预测
                forecasts = getTotalCost(predicts, form, rateMap);
                break;
            default:
                forecasts = Collections.emptyList();
                break;
        }
        return forecasts;
    }

    /**
     * 增加平均单价和单位
     *
     * @param form
     * @return
     */
    @Override
    public CostForecastResult encapsulatedData(FostForecastForm form,Long projectId) {
        //获取平均单价
        Map<Integer, Double> rates = null;
        //获取单价和单位
        List<AvgUnitPrice> unitPrices=null;
        if (!Objects.equals(form.getType(), CostForecast.UNITCONSUMPTION.getType())) {
            rates = getRate(form);
            unitPrices = getPriceList(rates, form.getEnergyTypes(),projectId);
        }
        //获取数据
        List<FostForecast> fostForecasts = queryDatas(form, rates);
        //获取单位
        UserDefineUnit userDefineUnit = generateUnit(form, fostForecasts,projectId);
        fostForecasts.forEach(data -> data.setValue(CommonUtils.calcDouble(data.getValue(), userDefineUnit.getCoef(), EnumOperationType.DIVISION.getId())));
        return new CostForecastResult(fostForecasts, userDefineUnit, unitPrices);
    }

    /**
     * 获取单价和单位
     *
     * @param rates
     * @param energyTypes
     * @return
     */
    List<AvgUnitPrice> getPriceList(Map<Integer, Double> rates, List<Integer> energyTypes,Long projectId) {
        if (CollectionUtils.isEmpty(energyTypes)) {
            return Collections.emptyList();
        }
        List<UserDefineUnit> userDefineUnits = energyUnitService.queryBasicUserDefineUnit(energyTypes, ProjectUnitClassify.ENERGY, projectId);
        Map<Integer, String> unitMap = userDefineUnits.stream().collect(Collectors.toMap(UserDefineUnit::getType, UserDefineUnit::getBasicUnitSymbolName, (v1, v2) -> v1));
        return rates.entrySet().stream().map(val -> {
            AvgUnitPrice avgUnitPrice = new AvgUnitPrice();
            avgUnitPrice.setEneryType(val.getKey());
            avgUnitPrice.setAvgPrice(val.getValue());
            String unit = Objects.isNull(unitMap.get(val.getKey())) ? "" : unitMap.get(val.getKey());
            avgUnitPrice.setUnit(UNIT_STR + unit);
            return avgUnitPrice;
        }).collect(Collectors.toList());
    }

    /**
     * 获取单位
     *
     * @param form
     * @param fostForecasts
     * @return
     */
    private UserDefineUnit generateUnit(FostForecastForm form, List<FostForecast> fostForecasts,Long projectId) {
        UserDefineUnitSearchDto userDefineUnitSearchDto;
        //单耗查询单位转换
        if (Objects.equals(form.getType(), CostForecast.UNITCONSUMPTION.getType())) {
            userDefineUnitSearchDto = new UserDefineUnitSearchDto(projectId, form.getSingleType(), ProjectUnitClassify.PRODUCT);
        } else {
            userDefineUnitSearchDto = new UserDefineUnitSearchDto(projectId, RMB_UnitClassify, ProjectUnitClassify.OTHER);
        }
        FostForecast forecast = fostForecasts.stream().filter(data -> Objects.nonNull(data.getValue()) && !Double.isInfinite(data.getValue()))
                .max(Comparator.comparing(x -> Objects.isNull(x.getValue()) ? 0.0 : x.getValue())).orElse(new FostForecast());
        UserDefineUnit unit = energyUnitService.queryUnitCoef(userDefineUnitSearchDto, forecast.getValue(), projectId);
        return handleUnit(form, unit,projectId);
    }

    /**
     * 单耗预测的单位处理
     *
     * @param form
     * @param unit
     * @return
     */
    private UserDefineUnit handleUnit(FostForecastForm form, UserDefineUnit unit,Long projectId) {
        if (!Objects.equals(form.getType(), CostForecast.UNITCONSUMPTION.getType())||Objects.isNull(form.getSingleType())) {
            return unit;
        }
        if (form.getEnergyTypes().size() > 1) {
            unit.setUnitCn(PREFIX + unit.getUnitCn());
            unit.setUnitEn(PREFIX + unit.getUnitEn());
        } else if (form.getEnergyTypes().size() == 1) {
            UserDefineUnitSearchDto searchDto = new UserDefineUnitSearchDto(projectId, form.getEnergyTypes().get(0), ProjectUnitClassify.ENERGY);
            UserDefineUnit unitEnergy = energyUnitService.queryUnitCoef(searchDto, null,projectId);
            unit.setUnitCn(unitEnergy.getUnitCn() + "/" + unit.getUnitCn());
            unit.setUnitEn(unitEnergy.getUnitEn() + "/" + unit.getUnitEn());
        }
        return unit;
    }

    /**
     * 根据节点查询对应的能源类型
     *
     * @param baseEntity
     * @return
     */
    @Override
    public List<BaseEntity> queryCostcheckplan(BaseEntity baseEntity,Long projectId) {
        List<CostCheckNodeConfig> configs = costCheckNodeConfigDao.queryCostCheckPlan(baseEntity.getId(), baseEntity.getModelLabel());
        List<Long> costcheckplanIds = configs.stream().map(CostCheckNodeConfig::getCostcheckplan_id).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(costcheckplanIds)) {
            return Collections.emptyList();
        }
        List<CostCheckPlan> costCheckItems = costCheckItemDao.getCostCheckItems(costcheckplanIds);
        List<Long> feeschemeIds = costCheckItems.stream().map(CostCheckPlan::getCostcheckitem_model).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).map(CostCheckItem::getFeescheme_id).collect(Collectors.toList());
        List<FeeScheme> feeSchemes = feeSchemeDao.queryFeeSchemes(feeschemeIds);
        List<Integer> energytypeIds = feeSchemes.stream().map(FeeScheme::getEnergytype).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<ProjectEnergyType> energyTypes = projectEnergyTypeDao.queryProjectEnergyTypeByProjectId(projectId);
        Map<Integer, String> typeNames = energyTypes.stream().collect(Collectors.toMap(ProjectEnergyType::getEnergytype, ProjectEnergyType::getName, (v1, v2) -> v1));
        return energytypeIds.stream().map(energytypeId -> {
            BaseEntity base = new BaseEntity();
            base.setId(energytypeId.longValue());
            base.setName(typeNames.get(energytypeId));
            return base;
        }).collect(Collectors.toList());
    }

    /**
     * 单耗预测
     *
     * @param form
     * @param cycel
     * @return
     */
    List<FostForecast> getUnitConsumption(List<TotalEnergyConsumptionPredict> predicts, FostForecastForm form, Integer cycel) {
        List<Long> timeRange = TimeUtil.getTimeRange(form.getStartTime(), form.getEndTime(), getCycel(form.getCycle()));
        Map<Long, List<TotalEnergyConsumptionPredict>> preMap = predicts.stream().collect(Collectors.groupingBy(TotalEnergyConsumptionPredict::getLogtime));
        List<ProductionPlan> productionPlans = queryProductionPlan(cycel, form.getStartTime(), form.getEndTime(), form.getNodeId(), form.getNodelLabel(), Collections.singletonList(form.getSingleType()));
        Map<Long, List<ProductionPlan>> productMap = productionPlans.stream().collect(Collectors.groupingBy(item -> TimeUtil.localDateTime2timestamp(item.getLogtime())));
        List<FostForecast> datas = new ArrayList<>();
        timeRange.forEach(time -> {
            FostForecast forecast = new FostForecast();
            forecast.setLogtime(time);
            datas.add(forecast);
            List<TotalEnergyConsumptionPredict> timePredicts = preMap.get(time);
            List<ProductionPlan> planList = productMap.get(time);
            if (CollectionUtils.isEmpty(timePredicts) || CollectionUtils.isEmpty(planList)) {
                return;
            }
            double predictSum = timePredicts.stream().map(TotalEnergyConsumptionPredict::getValue).filter(Objects::nonNull).mapToDouble(value -> value).sum();
            double planSum = planList.stream().map(ProductionPlan::getValue).filter(Objects::nonNull).mapToDouble(value -> value).sum();
            forecast.setValue(CommonUtils.calcDouble(predictSum, planSum, EnumOperationType.DIVISION.getId()));
        });
        return datas;
    }


    /**
     * 单台成本预测
     *
     * @return
     */
    public List<FostForecast> getCostPerUnit(List<FostForecast> totalCost, FostForecastForm form, Integer cycel) {
        List<Integer> types = Collections.singletonList(form.getSingleType());
        List<ProductionPlan> productionPlans = queryProductionPlan(cycel, form.getStartTime(), form.getEndTime(), form.getNodeId(), form.getNodelLabel(), types);
        Map<Long, List<ProductionPlan>> productMap = productionPlans.stream().collect(Collectors.groupingBy(item -> TimeUtil.localDateTime2timestamp(item.getLogtime())));
        totalCost.forEach(cost -> {
            Long logtime = cost.getLogtime();
            List<ProductionPlan> plans = productMap.get(logtime);
            if (CollectionUtils.isEmpty(plans) || Objects.isNull(cost.getValue())) {
                cost.setValue(null);
                return;
            }
            double sum = plans.stream().map(ProductionPlan::getValue).filter(Objects::nonNull).mapToDouble(value -> value).sum();
            cost.setValue(cost.getValue() / sum);
        });
        return totalCost;
    }

    /**
     * 获取计划产量值
     *
     * @param cycle
     * @param st
     * @param end
     * @param objectId
     * @param label
     * @param types
     * @return
     */
    private List<ProductionPlan> queryProductionPlan(Integer cycle, Long st, Long end, Long objectId, String label, List<Integer> types) {
        QueryCondition queryCondition = new QueryConditionBuilder<>(ModelLabelDef.PRODUCTION_PLAN)
                .eq(ColumnDef.AGGREGATION_CYCLE, cycle)
                .ge(ColumnDef.LOGTIME, st)
                .lt(ColumnDef.LOGTIME, end)
                .eq(ColumnDef.C_OBJECT_ID, objectId)
                .eq(ColumnDef.C_OBJECT_Label, label)
                .in(ColumnDef.PRODUCT_TYPE, types)
                .build();
        return modelServiceUtils.query(queryCondition, ProductionPlan.class);
    }

    /**
     * 综合成本预测
     *
     * @param predicts
     * @return
     */
    public List<FostForecast> getTotalCost(List<TotalEnergyConsumptionPredict> predicts, FostForecastForm form, Map<Integer, Double> rateMap) {
        //核算方案
        List<CostCheckNodeConfig> nodeConfigs = costCheckNodeConfigDao.queryCostCheckPlan(form.getNodeId(), form.getNodelLabel());
        if (CollectionUtils.isEmpty(nodeConfigs)) {
            return getEmptyList(form);
        }
        Map<Long, List<TotalEnergyConsumptionPredict>> predictMap = predicts.stream().collect(Collectors.groupingBy(TotalEnergyConsumptionPredict::getLogtime));
        return getDatas(predictMap, rateMap, form);
    }

    /**
     * 综合能耗数据的处理
     *
     * @param predictMap
     * @param rateMap
     * @param form
     * @return
     */
    List<FostForecast> getDatas(Map<Long, List<TotalEnergyConsumptionPredict>> predictMap, Map<Integer, Double> rateMap, FostForecastForm form) {
        List<FostForecast> datas = new ArrayList<>();
        List<Long> timeRange = TimeUtil.getTimeRange(form.getStartTime(), form.getEndTime(), getCycel(form.getCycle()));
        timeRange.forEach(time -> {
            FostForecast forecast = new FostForecast();
            forecast.setLogtime(time);
            datas.add(forecast);
            List<TotalEnergyConsumptionPredict> predict = predictMap.get(time);
            if (CollectionUtils.isEmpty(predict)) {
                return;
            }
            Map<Integer, List<TotalEnergyConsumptionPredict>> typePredicts = predict.stream().collect(Collectors.groupingBy(TotalEnergyConsumptionPredict::getEnergytype));
            typePredicts.forEach((energytype, predictList) -> {
                Double rate = rateMap.get(energytype);
                if (Objects.isNull(rate)) {
                    return;
                }
                Double value = forecast.getValue();
                double predSum = predictList.stream().map(TotalEnergyConsumptionPredict::getValue).filter(Objects::nonNull).mapToDouble(val -> val).sum();
                if (Objects.isNull(value)) {
                    forecast.setValue(predSum * rate);
                } else {
                    forecast.setValue(predSum * rate + value);
                }
            });
        });
        return datas;
    }

    /**
     * 获取每个类别的
     *
     * @param form
     * @return
     */
    private Map<Integer, Double> getRate(FostForecastForm form) {
        if (CollectionUtils.isEmpty(form.getEnergyTypes())) {
            return Collections.emptyMap();
        }
        LocalDateTime st;
        LocalDateTime end;
        switch (form.getCycle()) {
            case AggregationCycle.ONE_YEAR:
                st = TimeUtil.timestamp2LocalDateTime(form.getStartTime()).plusYears(-1);
                end = TimeUtil.timestamp2LocalDateTime(form.getEndTime()).plusYears(-1);
                break;
            case AggregationCycle.ONE_MONTH:
                st = TimeUtil.timestamp2LocalDateTime(form.getStartTime()).plusMonths(-1);
                end = TimeUtil.timestamp2LocalDateTime(form.getEndTime()).plusMonths(-1);
                break;
            default:
                throw new ValidationException("周期参数不正确!");
        }
        BaseVo baseVo = new BaseVo(form.getNodeId(), form.getNodelLabel());
        List<EnergyConsumption> consumptions = energyconsumptionDao.queryEnergyConsumption(baseVo, st, end, form.getCycle(), form.getEnergyTypes());
        List<ObjectCostValueVo> costValueVos = objectCostValueDao.queryObjectCostValueVo(baseVo, TimeUtil.localDateTime2timestamp(st), TimeUtil.localDateTime2timestamp(end), form.getCycle(), form.getEnergyTypes());
        //节点的用量
        Map<Integer, Double> nhMap = consumptions.stream().collect(HashMap::new, (m, v) -> m.put(v.getEnergytype(), v.getUsage()), HashMap::putAll);
        //节点消耗的费用
        Map<Integer, Double> moneyMap = costValueVos.stream().collect(HashMap::new, (m, v) -> m.put(v.getEnergytype(), v.getValue()), HashMap::putAll);
        return form.getEnergyTypes().stream().collect(Collectors.toMap(val -> val, val -> {
            Double nh = nhMap.get(val);
            Double money = moneyMap.get(val);
            return Objects.isNull(CommonUtils.calcDouble(money, nh, EnumOperationType.DIVISION.getId())) ? 0 : CommonUtils.calcDouble(money, nh, EnumOperationType.DIVISION.getId());
        }));
    }

    /**
     * 获取空数组
     *
     * @param form
     * @return
     */
    List<FostForecast> getEmptyList(FostForecastForm form) {
        List<Long> timeRange = TimeUtil.getTimeRange(form.getStartTime(), form.getEndTime(), getCycel(form.getCycle()));
        return timeRange.stream().map(FostForecast::new).collect(Collectors.toList());
    }

    /**
     * 转换下周期
     *
     * @param cylce
     * @return
     */
    public Integer getCycel(Integer cylce) {
        switch (cylce) {
            case AggregationCycle.ONE_MONTH:
                cylce = AggregationCycle.ONE_DAY;
                break;
            case AggregationCycle.ONE_YEAR:
                cylce = AggregationCycle.ONE_MONTH;
                break;
            default:
                break;
        }
        return cylce;
    }
}

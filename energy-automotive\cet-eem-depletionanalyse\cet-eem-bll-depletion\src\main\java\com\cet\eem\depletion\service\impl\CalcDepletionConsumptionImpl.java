package com.cet.eem.depletion.service.impl;

import com.cet.eem.bll.common.dao.node.EnergySupplyDao;
import com.cet.eem.bll.common.model.batchnodes.PipeLine;
import com.cet.eem.bll.common.model.node.relations.EnergySupplyToPo;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.DataLogData;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.depletion.dao.DepletionDataDao;
import com.cet.eem.depletion.model.Constant.QuantityDef;
import com.cet.eem.depletion.model.DepletionConsumptionDto;
import com.cet.eem.depletion.model.DepletionPartitionDto;
import com.cet.eem.depletion.service.CalcDepletionConsumptionService;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.quantity.service.QuantityManageService;
import com.cet.electric.model.enums.EnergyTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class CalcDepletionConsumptionImpl implements CalcDepletionConsumptionService {

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    QuantityManageService quantityManageService;

    @Autowired
    DepletionDataDao depletionDataDao;

    @Autowired
    EnergySupplyDao energySupplyDao;

    List<DepletionPartitionDto> depletionPartitionDtos = new ArrayList<>();

    protected static final Logger logger = LoggerFactory.getLogger(CalcDepletionConsumptionImpl.class);

    private static final String LOG_KEY = "[空耗能耗计算]";

    @Override
    public void calcDepletionConsumption(Long startTime, Long endTime) {
        List<DepletionConsumptionDto> depletionConsumptionList = new ArrayList<>();

        depletionPartitionDtos = depletionDataDao.queryDepletionPartition(startTime, endTime);
        if (depletionPartitionDtos.isEmpty()){
            logger.info("{}：没找到{}~{}的空耗划分记录", LOG_KEY, TimeUtil.timestamp2LocalDateTime(startTime), TimeUtil.timestamp2LocalDateTime(endTime));
            return;
        }
        List<BaseVo> baseVoList = depletionPartitionDtos.stream().map(item -> new BaseVo(item.getObjectid(), item.getObjectlabel()))
                .distinct().collect(Collectors.toList());
        //查找电泳池供能关系
        List<EnergySupplyToPo> energySupplyToPos = energySupplyDao.queryEnergySupplyToByNodes(baseVoList);
        if (energySupplyToPos.isEmpty()){
            logger.info("{}：没有找到电泳池的供能关系", LOG_KEY);
            return;
        }
        List<EnergySupplyToPo> linesegmentwithswitchs = energySupplyToPos.stream().filter(x -> Objects.equals(x.getObjectlabel(), NodeLabelDef.LINE_SEGMENT_WITH_SWITCH)).collect(Collectors.toList());
        List<EnergySupplyToPo> pipelines = energySupplyToPos.stream().filter(x -> Objects.equals(x.getObjectlabel(), NodeLabelDef.PIPELINE)).collect(Collectors.toList());

        dealPowerData(linesegmentwithswitchs, startTime, endTime, depletionConsumptionList);
        dealEnergyAccumulation(pipelines, startTime, endTime, depletionConsumptionList);

        modelServiceUtils.writeData(depletionConsumptionList, DepletionConsumptionDto.class);
        logger.info("{}：空耗能耗计算完成，本次共入库{}条记录", LOG_KEY,depletionConsumptionList.size());
    }


    /**
     * 处理电的能耗
     * */
    private void dealPowerData(List<EnergySupplyToPo> linesegmentwithswitchs, Long startTime, Long endTime, List<DepletionConsumptionDto> depletionConsumptionList){
        if (linesegmentwithswitchs.isEmpty()){
            logger.info("{}：没有找到电泳池关联的一段线数据", LOG_KEY);
            return;
        }
        Map<Integer, List<TrendDataVo>> powerData = depletionDataDao.queryQuantityData(linesegmentwithswitchs.stream()
                        .map(x -> new BaseVo(x.getObjectid(), x.getObjectlabel())).collect(Collectors.toList()),
                startTime, endTime, Collections.singletonList(QuantityDef.getPower()));
        List<TrendDataVo> trendDataVos = powerData.get(QuantityDef.getPower().getId());
        for (DepletionPartitionDto item : depletionPartitionDtos){
            List<EnergySupplyToPo> anyEnergySupplyToPo = linesegmentwithswitchs.stream().filter(x -> Objects.equals(x.getSupplytoid(), item.getObjectid())
                    && Objects.equals(x.getSupplytolabel(), item.getObjectlabel())).collect(Collectors.toList());
            if (Objects.isNull(anyEnergySupplyToPo) || anyEnergySupplyToPo.isEmpty()){
                continue;
            }
            Double value = 0.0;
            for (EnergySupplyToPo energySupplyToPo : anyEnergySupplyToPo){
                List<TrendDataVo> collect = trendDataVos.stream().filter(x -> Objects.equals(x.getMonitoredid(), energySupplyToPo.getObjectid())
                        && Objects.equals(x.getMonitoredlabel(), energySupplyToPo.getObjectlabel())).collect(Collectors.toList());
                if (Objects.isNull(collect) || collect.isEmpty()){
                    continue;
                }
                for (TrendDataVo trendDataVo : collect){
                    List<DataLogData> dataLogDataList = trendDataVo.getDataList();
                    Double startValue = getAppointValue(dataLogDataList, item.getStarttime());
                    Double endValue = getAppointValue(dataLogDataList, item.getEndtime());
                    value = value + (endValue - startValue);
                }
            }

            DepletionConsumptionDto res = new DepletionConsumptionDto();
            res.setDepletionid(item.getId());
            res.setDepletiontype(item.getDepletiontype());
            res.setEnergytype(EnergyTypeEnum.ELECTRIC.getCode());
            res.setProjectid(1L);
            res.setLogtime(TimeUtil.getFirstTimeOfDay(startTime));
            res.setObjectid(item.getObjectid());
            res.setObjectlabel(item.getObjectlabel());
            res.setId(0L);
            res.setValue(value);
            depletionConsumptionList.add(res);
        }
    }

    /**
     * 电以外的能耗
     */
    private void dealEnergyAccumulation(List<EnergySupplyToPo> pipelines, Long startTime, Long endTime, List<DepletionConsumptionDto> depletionConsumptionList){
        if (pipelines.isEmpty()){
            logger.info("{}：没有找到电泳池关联的管道数据", LOG_KEY);
            return;
        }
        List<PipeLine> pipeLinesList = depletionDataDao.queryPipeLine(pipelines.stream().map(EnergySupplyToPo::getObjectid).collect(Collectors.toList()));
        Map<Integer, List<PipeLine>> collect = pipeLinesList.stream().collect(Collectors.groupingBy(PipeLine::getEnergytype));
        collect.forEach((key, value) ->{
            List<BaseVo> baseVos = value.stream().map(x -> new BaseVo(x.getId(), x.getModelLabel())).collect(Collectors.toList());
            Map<Integer, List<TrendDataVo>> energyAccumulationData = depletionDataDao.queryQuantityData(baseVos, startTime, endTime,
                    Collections.singletonList(QuantityDef.getEnergyAccumulation(key)));
            List<TrendDataVo> trendDataVos = energyAccumulationData.get(10000001);
            for (DepletionPartitionDto item : depletionPartitionDtos){
                DepletionConsumptionDto res = new DepletionConsumptionDto();
                res.setDepletionid(item.getId());
                res.setDepletiontype(item.getDepletiontype());
                res.setEnergytype(key);
                res.setProjectid(1L);
                res.setLogtime(TimeUtil.getFirstTimeOfDay(startTime));
                res.setObjectid(item.getObjectid());
                res.setObjectlabel(item.getObjectlabel());
                res.setId(0L);

                List<EnergySupplyToPo> collectForEnergySupplyToPo = pipelines.stream().filter(x -> Objects.equals(x.getSupplytoid(), item.getObjectid())
                        && Objects.equals(x.getSupplytolabel(), item.getObjectlabel())).collect(Collectors.toList());

                if (Objects.isNull(collectForEnergySupplyToPo) || collectForEnergySupplyToPo.isEmpty()){
                    continue;
                }
                Double finalValue = 0.0;
                for (EnergySupplyToPo energySupplyToPo : collectForEnergySupplyToPo ){
                    List<TrendDataVo> collectForTrendDataVo = trendDataVos.stream().filter(x -> Objects.equals(x.getMonitoredid(), energySupplyToPo.getObjectid())
                            && Objects.equals(x.getMonitoredlabel(), energySupplyToPo.getObjectlabel())).collect(Collectors.toList());
                    if (Objects.isNull(collectForTrendDataVo) || collectForTrendDataVo.isEmpty()){
                        continue;
                    }
                    for (TrendDataVo trendDataVo : collectForTrendDataVo){
                        List<DataLogData> dataLogDataList = trendDataVo.getDataList();
                        Double startValue = getAppointValue(dataLogDataList, item.getStarttime());
                        Double endValue = getAppointValue(dataLogDataList, item.getEndtime());
                        finalValue = finalValue + (endValue - startValue);
                    }
                }
                res.setValue(finalValue);

                depletionConsumptionList.add(res);
            }
        });
    }

    private Double getAppointValue(List<DataLogData> dataLogDataList, Long time){
        Optional<DataLogData> first = dataLogDataList.stream().filter(x -> Objects.equals(x.getTime(), time)).findFirst();
        if (first.isPresent()){
            return Objects.nonNull(first.get().getValue()) ? first.get().getValue() : 0.0;
        }else {
            Optional<DataLogData> next = dataLogDataList.stream().filter(x -> (x.getTime() >= time - 1800000L && x.getTime() < time + 1800000L)).findFirst();
            if(next.isPresent()){
                return Objects.nonNull(next.get().getValue()) ? next.get().getValue() : 0.0;
            }else {
                return 0.0;
            }
        }
    }
}

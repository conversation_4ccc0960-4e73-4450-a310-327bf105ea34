package com.cet.eem.bll.compressoroptimization.service.config;

import com.cet.eem.bll.compressoroptimization.model.config.CompressorConfig;
import com.cet.eem.bll.compressoroptimization.model.config.CompressorSystem;
import com.cet.eem.common.model.BaseVo;

import java.util.List;

/**
 * @ClassName : AiCompressorConfigService
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-20 15:55
 */
public interface AiCompressorConfigService {
    /**
     * 查询当前系统智控ai
     * @param systemId
     * @param projectId
     * @return
     */
    CompressorSystem querySystemUseAi( Long systemId,Long projectId);
    /**
     * 查询当前空压机智控ai
     * @param systemId
     * @param projectId
     * @return
     */
    List<CompressorSystem> queryCompressorUseAi(Long systemId, Long projectId);

    /**
     * 查询系统普通配置和高级配置
     * @param systemId
     * @return
     */
    CompressorConfig queryCompressorConfig(Long systemId, Long projectId);

    /**
     * 编辑or新增系统配置
     * @param compressorSystem
     * @param projectId
     * @return
     */
    CompressorSystem editSystemUseAi( CompressorSystem compressorSystem, Long projectId);

    /**
     * 编辑空压机配置
     * @param compressorControls
     * @param projectId
     * @return
     */
    List<CompressorSystem> editCompressorUseAi( List<CompressorSystem> compressorControls, Long projectId);

    /**
     * 编辑普通配置or高级配置
     * @param compressorConfig
     * @param isNormalConfig
     * @param projectId
     * @return
     */
    CompressorConfig editCompressorConfig( CompressorConfig compressorConfig,  Boolean isNormalConfig, Long projectId);

    /**
     * 查询房间
     * @param projectId
     * @return
     */
    List<BaseVo> getRoom(Long projectId);
}

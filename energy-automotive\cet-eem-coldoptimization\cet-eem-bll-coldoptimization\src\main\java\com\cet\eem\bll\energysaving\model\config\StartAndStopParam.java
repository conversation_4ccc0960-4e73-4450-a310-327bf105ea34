package com.cet.eem.bll.energysaving.model.config;

import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : StartAndStopParam
 * @Description : 启停顺序入参
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-24 15:43
 */
@Getter
@Setter
public class StartAndStopParam {
    private DeviceWithTimeParam firstStart;
    private DeviceWithTimeParam secondStart;
    private DeviceWithTimeParam thirdStart;
    private DeviceWithTimeParam lastStart;
    private DeviceWithTimeParam firstStop;
    private DeviceWithTimeParam secondStop;
    private DeviceWithTimeParam thirdStop;
    private DeviceWithTimeParam lastStop;
    private Long roomId;
}
package com.cet.eem.customreport.model;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/6
 * @descripition  定制报表查询参数
 */
@Getter
@Setter
public class ReportParam {
    /**
     * 房间id
     */
    private Long objectId;
    /**
     * 房间类型，1为配电室、6为管道房
     */
    private Integer roomType;
    /**
     * 查询时间点，每月1号的时间点，最多三个
     */
    private List<Long> timeList;
}

package com.cet.eem.bll.datatransport.model.mesdata;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.bll.datatransport.model.ModelDef;
import com.cet.eem.model.model.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@ModelLabel(ModelDef.PRODUCTION_DATA_DOCKING)
public class MesDataEntityVo extends BaseEntity {
    public String workshipname;
    public String shiftname;
    public Integer plannum;
    public Integer completenum;
    public Integer takttime;
    public LocalDateTime syscreatetime;
    public Long objectid;
    public String objectlabel;
    public Long logtime;
    public String code;
}

package com.cet.eem.depletion.model.vo;

import com.cet.eem.common.model.datalog.DataLogData;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class TempTrend {
    List<WasteZone> wasteZoneList;
    List<DataLogData> timeOfStart;
    List<DataLogData> timeOfStop;
    List<DataLogData> dataList;
    List<DataLogData> firstCarTime;
    List<DataLogData> latestCarTime;


    //原overview接口参数合并接口
    Long totalDuration;
    //总空耗详情
    List<WasteDetail> wasteOfAll;
    //各项空耗时长
    List<WasteDuration> wasteDurations;

}

﻿# Java Compilation Errors Report

**Generated:** 2025-08-05 16:22:30
**Module:** energy-solution-fusion\eem-solution-transformer\eem-solution-transformer-core
**Total Issues:** 53 (Compilation: 1, Import Analysis: 52)

## ISSUES FOUND

### PowerTransformerDto.java (2 issues)

**Issue 1 - Line 3:**

`import com.cet.eem.bll.common.model.domain.object.organization.Project;`

**Issue 2 - Line 4:**

`import com.cet.eem.bll.common.model.domain.object.powersystem.PowerTransformerVo;`

### TransformerAnalysisController.java (1 issues)

**Issue 1 - Line 3:**

`import com.cet.eem.bll.common.util.GlobalInfoUtils;`

### TransformerAnalysisService.java (1 issues)

**Issue 1 - Line 10:**

`import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;`

### TransformerAnalysisServiceImpl.java (17 issues)

**Issue 1 - Line 3:**

`import com.cet.eem.bll.common.dao.node.NodeDao;`

**Issue 2 - Line 4:**

`import com.cet.eem.bll.common.dao.pipenetwork.PipeNetworkConnectionModelDao;`

**Issue 3 - Line 5:**

`import com.cet.eem.bll.common.def.quantity.FrequencyDef;`

**Issue 4 - Line 6:**

`import com.cet.eem.bll.common.def.quantity.PhasorDef;`

**Issue 5 - Line 7:**

`import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;`

**Issue 6 - Line 8:**

`import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;`

**Issue 7 - Line 9:**

`import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;`

**Issue 8 - Line 10:**

`import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityAggregationData;`

**Issue 9 - Line 11:**

`import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityObject;`

**Issue 10 - Line 12:**

`import com.cet.eem.bll.common.model.domain.object.powersystem.PowerTransformerVo;`

**Issue 11 - Line 13:**

`import com.cet.eem.bll.common.model.topology.vo.LinkNode;`

**Issue 12 - Line 14:**

`import com.cet.eem.bll.common.model.topology.vo.PointNode;`

**Issue 13 - Line 35:**

`import com.cet.eem.quantity.dao.QuantityAggregationDataDao;`

**Issue 14 - Line 36:**

`import com.cet.eem.quantity.dao.QuantityObjectDao;`

**Issue 15 - Line 37:**

`import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;`

**Issue 16 - Line 38:**

`import com.cet.eem.quantity.model.quantity.QuantitySearchVo;`

**Issue 17 - Line 39:**

`import com.cet.eem.quantity.service.QuantityManageService;`

### TransformerindexDataDao.java (1 issues)

**Issue 1 - Line 3:**

`import com.cet.eem.bll.common.model.domain.perception.logicaldevice.EemPoiRecord;`

### TransformerindexDataDaoImpl.java (1 issues)

**Issue 1 - Line 3:**

`import com.cet.eem.bll.common.model.domain.perception.logicaldevice.EemPoiRecord;`

### TransformerOverviewController.java (1 issues)

**Issue 1 - Line 3:**

`import com.cet.eem.bll.common.util.GlobalInfoUtils;`

### TransformerOverviewServiceImpl.java (29 issues)

**Issue 1 - Line 1:** Compilation Error

`package com.cet.eem.fusion.transformer.core.impl;`

**Issue 2 - Line 3:**

`import com.cet.eem.bll.common.def.quantity.FrequencyDef;`

**Issue 3 - Line 4:**

`import com.cet.eem.bll.common.def.quantity.PhasorDef;`

**Issue 4 - Line 5:**

`import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;`

**Issue 5 - Line 6:**

`import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;`

**Issue 6 - Line 7:**

`import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;`

**Issue 7 - Line 8:**

`import com.cet.eem.bll.common.model.domain.object.event.SystemEvent;`

**Issue 8 - Line 9:**

`import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityAggregationData;`

**Issue 9 - Line 10:**

`import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityObject;`

**Issue 10 - Line 11:**

`import com.cet.eem.bll.common.model.domain.perception.logicaldevice.PecEventExtendVo;`

**Issue 11 - Line 12:**

`import com.cet.eem.bll.common.model.ext.objective.event.SystemEventWithText;`

**Issue 12 - Line 13:**

`import com.cet.eem.bll.common.model.topology.vo.ConnectionSearchVo;`

**Issue 13 - Line 14:**

`import com.cet.eem.bll.common.model.topology.vo.LinkNode;`

**Issue 14 - Line 15:**

`import com.cet.eem.bll.common.model.topology.vo.PointNode;`

**Issue 15 - Line 16:**

`import com.cet.eem.bll.common.util.GlobalInfoUtils;`

**Issue 16 - Line 17:**

`import com.cet.eem.bll.energy.dao.SystemEventDao;`

**Issue 17 - Line 18:**

`import com.cet.eem.bll.energy.model.event.SystemEventCountVo;`

**Issue 18 - Line 19:**

`import com.cet.eem.bll.energy.service.event.AlarmEventService;`

**Issue 19 - Line 32:**

`import com.cet.eem.event.model.analysis.ConfirmCountResult;`

**Issue 20 - Line 33:**

`import com.cet.eem.event.model.expert.EventCountSearchVo;`

**Issue 21 - Line 34:**

`import com.cet.eem.event.model.pecevent.PecEventCountVo;`

**Issue 22 - Line 35:**

`import com.cet.eem.event.service.PecEventService;`

**Issue 23 - Line 36:**

`import com.cet.eem.event.service.expert.ExpertAnalysisBffService;`

**Issue 24 - Line 37:**

`import com.cet.eem.event.service.expert.PecCoreEventBffService;`

**Issue 25 - Line 44:**

`import com.cet.eem.quantity.dao.QuantityAggregationDataDao;`

**Issue 26 - Line 45:**

`import com.cet.eem.quantity.dao.QuantityObjectDao;`

**Issue 27 - Line 46:**

`import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;`

**Issue 28 - Line 47:**

`import com.cet.eem.quantity.model.quantity.QuantitySearchVo;`

**Issue 29 - Line 48:**

`import com.cet.eem.quantity.service.QuantityManageService;`

## SUMMARY

- **Compilation Error:** 1 issues
- **:** 52 issues

---
**Generated:** 2025-08-05 16:22:30

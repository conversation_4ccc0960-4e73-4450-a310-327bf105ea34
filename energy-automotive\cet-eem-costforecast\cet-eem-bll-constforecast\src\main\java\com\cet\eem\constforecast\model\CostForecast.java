package com.cet.eem.constforecast.model;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * @ClassName : CostForecast
 * <AUTHOR> yangy
 * @Date: 2022-06-16 15:01
 */
public enum CostForecast {
    /**
     * 查询类别
     */
    COMPREHENSIVECOST(1, "综合成本预测"),
    COSTPERUNIT(2, "单台成本预测"),
    UNITCONSUMPTION(3, "单耗预测");
    Integer type;
    String name;

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    CostForecast(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

   public  static CostForecast getCostForecast(Integer type) {
        return Stream.of(CostForecast.values()).filter(item -> Objects.equals(type, item.getType())).findFirst().orElse(null);
    }
}

package com.cet.eem.bll.energysaving.dao.aioptimization;

import com.cet.eem.bll.energysaving.model.aioptimization.WorkSection;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.dao.BaseModelDao;

import java.util.List;

/**
 * @ClassName : WorkSectionDao
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-07 19:48
 */
public interface WorkSectionDao extends BaseModelDao<WorkSection> {
    /**
     *
     * @param baseVo
     * @return
     */
    List<WorkSection> queryWorkSection(BaseVo baseVo);

    /**
     *
     * @param baseVos
     * @return
     */
    List<WorkSection> queryWorkSections(List<BaseVo> baseVos);
}
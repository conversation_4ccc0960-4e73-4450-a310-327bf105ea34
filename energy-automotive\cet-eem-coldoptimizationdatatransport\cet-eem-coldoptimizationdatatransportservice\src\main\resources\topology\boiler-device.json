[{"type": 1, "relateDevice": true, "name": "锅炉", "modelLabel": "boiler", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 5}], "fieldName": "所属锅炉房"}, "fields": [{"field": "name", "required": true, "fieldName": "名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "保养周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "出厂日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "devicesize", "required": false, "fieldName": "机组尺寸", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceweight", "required": false, "fieldName": "机组总重量(吨)", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "boilertype", "required": false, "fieldName": "锅炉类型", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "fueltype", "required": false, "fieldName": "燃料类型", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}], "tips": null}]
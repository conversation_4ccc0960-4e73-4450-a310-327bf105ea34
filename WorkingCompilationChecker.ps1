# Working Compilation Error Checker - Simple and Reliable
param(
    [string]$ModulePath = "energy-solution-fusion\eem-solution-transformer\eem-solution-transformer-core",
    [string]$OutputFile = "working-compilation-errors.md"
)

Write-Host "Working Compilation Error Checker" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# Check paths
if (-not (Test-Path $ModulePath)) {
    Write-Host "ERROR: Module path not found: $ModulePath" -ForegroundColor Red
    exit 1
}

$pomPath = Join-Path $ModulePath "pom.xml"
if (-not (Test-Path $pomPath)) {
    Write-Host "ERROR: pom.xml not found: $pomPath" -ForegroundColor Red
    exit 1
}

Write-Host "Analyzing: $ModulePath" -ForegroundColor Yellow

# Get compilation errors
$compilationErrors = @()
$processedErrors = @{}

Write-Host "Running Maven compilation..." -ForegroundColor Yellow
try {
    $compileOutput = & mvn compile -f $pomPath 2>&1
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Compilation FAILED - extracting errors..." -ForegroundColor Red
        
        foreach ($line in $compileOutput) {
            $lineStr = $line.ToString()
            
            # Parse error lines
            if ($lineStr -match "\[ERROR\]\s+.*\\([^\\]+\.java):\[(\d+),(\d+)\]") {
                $fileName = $matches[1]
                $lineNum = $matches[2]
                $colNum = $matches[3]
                
                # Avoid duplicates
                $errorKey = "$fileName-$lineNum"
                if ($processedErrors.ContainsKey($errorKey)) {
                    continue
                }
                $processedErrors[$errorKey] = $true
                
                # Get code snippet with better error handling
                $codeSnippet = ""
                try {
                    $javaFilePath = Join-Path $ModulePath "src\main\java"
                    $fullJavaFiles = Get-ChildItem -Path $javaFilePath -Filter $fileName -Recurse
                    if ($fullJavaFiles.Count -gt 0) {
                        $fileContent = Get-Content $fullJavaFiles[0].FullName -Encoding UTF8 -ErrorAction Stop
                        $lineIndex = [int]$lineNum - 1
                        if ($lineIndex -ge 0 -and $lineIndex -lt $fileContent.Count) {
                            $codeSnippet = $fileContent[$lineIndex].Trim()
                            # If the line is empty or just whitespace, try to get context
                            if ([string]::IsNullOrWhiteSpace($codeSnippet)) {
                                # Try to get the previous non-empty line for context
                                for ($i = $lineIndex - 1; $i -ge 0 -and $i -ge ($lineIndex - 3); $i--) {
                                    if (-not [string]::IsNullOrWhiteSpace($fileContent[$i])) {
                                        $codeSnippet = $fileContent[$i].Trim()
                                        break
                                    }
                                }
                            }
                        }
                    }
                } catch {
                    Write-Host "Warning: Could not read line $lineNum from $fileName" -ForegroundColor Yellow
                }
                
                # Determine error type
                $errorType = "Compilation Error"
                $errorDesc = "Compilation error"
                
                if ($lineStr -match "package.*does not exist") {
                    $errorType = "Missing Package"
                    $errorDesc = "Package does not exist"
                }
                elseif ($lineStr -match "cannot find symbol") {
                    $errorType = "Missing Symbol"
                    $errorDesc = "Cannot find symbol"
                }
                
                $compilationErrors += [PSCustomObject]@{
                    File = $fileName
                    Line = [int]$lineNum
                    Type = $errorType
                    Description = $errorDesc
                    Code = $codeSnippet
                }
            }
        }
    } else {
        Write-Host "Compilation SUCCESS" -ForegroundColor Green
    }
} catch {
    Write-Host "Maven execution failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Create report
$currentTime = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'

$report = "# Java Compilation Errors Report`n`n"
$report += "**Generated:** $currentTime`n"
$report += "**Module:** $ModulePath`n"
$report += "**Total Errors:** $($compilationErrors.Count)`n`n"

if ($compilationErrors.Count -gt 0) {
    $report += "## ERRORS FOUND`n`n"

    # Group by file
    $errorsByFile = $compilationErrors | Group-Object File | Sort-Object Name
    
    foreach ($fileGroup in $errorsByFile) {
        $report += "### $($fileGroup.Name) ($($fileGroup.Count) errors)`n`n"
        
        $sortedErrors = $fileGroup.Group | Sort-Object Line
        $errorNum = 1
        foreach ($compError in $sortedErrors) {
            $report += "**Error $errorNum - Line $($compError.Line): $($compError.Type)**`n"
            
            if ($compError.Code -and -not [string]::IsNullOrWhiteSpace($compError.Code)) {
                $report += "` `n"
                $report += "$($compError.Code)`n"
                $report += " `n"
                $report += "- **Issue:** $($compError.Description)`n"
            } else {
                $report += "- **Issue:** $($compError.Description) at line $($compError.Line)`n"
                $report += "- **Location:** $($compError.File):$($compError.Line)`n"
                $report += "- **Note:** Could not retrieve source code for this line`n"
            }
            
            $report += "`n"
            $errorNum++
        }
    }
    
    $report += "## SUMMARY`n`n"
    $errorsByType = $compilationErrors | Group-Object Type
    foreach ($typeGroup in $errorsByType) {
        $report += "- **$($typeGroup.Name):** $($typeGroup.Count) errors`n"
    }
    $report += "`n"
    
} else {
    $report += "## NO ERRORS FOUND`n`n"
    $report += "All Java files compiled successfully.`n`n"
}

$report += "---`n"
$report += "**Generated:** $currentTime`n"

# Save report
try {
    $fullPath = Join-Path (Get-Location) $OutputFile
    [System.IO.File]::WriteAllText($fullPath, $report, [System.Text.Encoding]::UTF8)
    Write-Host "SUCCESS: Report saved to $OutputFile" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to save report" -ForegroundColor Red
    exit 1
}

# Console summary
Write-Host "`n=================================" -ForegroundColor Green
Write-Host "ANALYSIS COMPLETE" -ForegroundColor Green
Write-Host "Compilation errors: $($compilationErrors.Count)" -ForegroundColor $(if ($compilationErrors.Count -gt 0) { "Red" } else { "Green" })

if ($compilationErrors.Count -gt 0) {
    $errorsByFile = $compilationErrors | Group-Object File | Sort-Object Count -Descending
    Write-Host "`nFiles with errors:" -ForegroundColor Yellow
    foreach ($fileGroup in $errorsByFile) {
        Write-Host "- $($fileGroup.Name): $($fileGroup.Count) errors" -ForegroundColor Red
    }

    $errorsByType = $compilationErrors | Group-Object Type
    Write-Host "`nError types:" -ForegroundColor Yellow
    foreach ($typeGroup in $errorsByType) {
        Write-Host "- $($typeGroup.Name): $($typeGroup.Count)" -ForegroundColor Yellow
    }
}

Write-Host "`nReport: $OutputFile" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Green

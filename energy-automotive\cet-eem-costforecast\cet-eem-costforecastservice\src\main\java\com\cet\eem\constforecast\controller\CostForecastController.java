package com.cet.eem.constforecast.controller;


import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.common.model.Result;
import com.cet.eem.constforecast.model.vo.CostForecastResult;
import com.cet.eem.constforecast.model.vo.FostForecastForm;
import com.cet.eem.constforecast.service.CostForecastService;
import com.cet.eem.model.model.BaseEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName : CostForecastController
 * <AUTHOR> yangy
 * @Date: 2022-06-15 10:24
 */
@Api(value = "/eem/v1/costanalysis", tags = "成本预测查询")
@RequestMapping(value = "/eem/v1/costanalysis")
@RestController
public class CostForecastController {

    @Autowired
    CostForecastService costForecastService;

    @PostMapping("/costforecast")
    @ApiOperation(value = "成本查询")
    public Result<CostForecastResult> queryDatas(@RequestBody FostForecastForm form) {

        return Result.ok(costForecastService.encapsulatedData(form, GlobalInfoUtils.getProjectId()));
    }

    @PostMapping("/queryCostcheckplan")
    @ApiOperation(value = "根据节点查询核算方案节点")
    public Result<List<BaseEntity>> queryCostcheckplan(@RequestBody BaseEntity form) {

        return Result.ok(costForecastService.queryCostcheckplan(form,GlobalInfoUtils.getProjectId()));
    }


}

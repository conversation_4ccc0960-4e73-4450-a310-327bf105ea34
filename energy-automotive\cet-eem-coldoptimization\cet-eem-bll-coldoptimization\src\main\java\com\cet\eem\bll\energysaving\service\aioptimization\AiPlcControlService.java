package com.cet.eem.bll.energysaving.service.aioptimization;

import com.cet.eem.bll.common.model.domain.object.physicalquantity.MeasuredbyVo;
import com.cet.eem.bll.energysaving.model.aioptimization.AiStartStopStrategy;
import com.cet.eem.bll.energysaving.model.aioptimization.StrategyObjectMap;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.auth.user.UserVo;
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;
import com.cet.electric.matterhorn.devicedataservice.common.entity.station.RemoteControlPointDTO;
import com.cet.electric.matterhorn.devicedataservice.common.query.control.MultiRemoteControlPara;
import com.cet.electric.matterhorn.devicedataservice.common.query.control.RemoteControlIdParam;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName : AiPlcControlService
 * @Description : ai遥控
 * <AUTHOR> jiangzixuan
 * @Date: 2022-09-01 15:26
 */
public interface AiPlcControlService {
    /**
     * 制冷ai控制
     */
    void refrigerationAiControl();

    /**
     * 发送心跳包
     */
    void sendHeartBeat();

    /**
     * 发生遥控、调试命令
     *
     * @param systemId
     * @param roomId
     * @param strategies
     * @param plcNodes
     */
    void beforeSend(Long systemId, Long roomId, List<AiStartStopStrategy> strategies, List<BaseVo> plcNodes) ;


    /**
     * 拼接批量遥控条件
     *
     * @param strategyObjectMaps
     * @param strategies
     * @param type
     * @param measuredbyVos
     * @param data
     * @param password
     * @param userVo
     * @return
     */
    MultiRemoteControlPara assembleSendData(List<StrategyObjectMap> strategyObjectMaps, List<AiStartStopStrategy> strategies,
                                            Integer type, List<MeasuredbyVo> measuredbyVos, List<RemoteControlPointDTO> data, String password, UserVo userVo,
                                            Long systemId);

    /**
     * 提供一个接口测试
     *
     * @param systemId
     * @param queryTime
     */
    void checkAiStrategyControl(Long systemId, Long queryTime) throws InterruptedException;

    /**
     * 查询plc节点
     * @param roomId
     * @return
     */
    List<BaseVo> queryNodesByMonitor(Long roomId);

    /**
     * 拼接查询物理量的入参
     * @param deviceNodes
     * @param st
     * @param et
     * @param cycle
     * @param quantitySearchVo
     * @return
     */
    QuantityDataBatchSearchVo createQuantityDataBatchSearchVo(List<BaseVo> deviceNodes, LocalDateTime st,
                                                              LocalDateTime et, Integer cycle, List<QuantitySearchVo> quantitySearchVo);

    /**
     * 查询遥控点
     * @param plcNodes
     * @param dataId
     * @param type
     * @return
     */
    List<RemoteControlPointDTO> getControlInfo(List<BaseVo> plcNodes, Long dataId, Integer type);

    /**
     * 拼接单一遥控条件
     * @param controlPointInfos
     * @param password
     * @param userVo
     * @param value
     * @param timestamps
     * @param type
     * @return
     */
    RemoteControlIdParam createRemoteControlParam(RemoteControlPointDTO controlPointInfos, String password,
                                                  UserVo userVo, Double value, long timestamps, Integer type);

    /**
     * 发送遥控命令
     *
     * @param dataId
     * @param plcNodes
     * @param type
     * @return
     */
    Boolean sendRemoteControlByDataId(Long dataId,List<BaseVo> plcNodes,Integer type);
}
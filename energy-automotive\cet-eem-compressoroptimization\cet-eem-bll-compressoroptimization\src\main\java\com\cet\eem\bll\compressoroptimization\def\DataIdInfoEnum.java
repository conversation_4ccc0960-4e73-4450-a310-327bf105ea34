package com.cet.eem.bll.compressoroptimization.def;

import lombok.Getter;

import java.util.Objects;

/**
 * @ClassName : DataIdInfoEnum
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-25 09:55
 */
@Getter
public enum DataIdInfoEnum {



    BOV(1, "BOV开度"),
    IGV(2, "IGV开度"),
    EXHAUST_SYS_PRE(3, "排气压力系统压力"),
    EXHAUST_SYS_PRE_OUT(1, "排气压力压缩机出口"),
    MAIN_MOTOR_CUR(2, "主电机电流"),
    AIR_TEMP(3, "进气温度"),
    MAIN_MOTOR_TEMP(1, "主电机定子温度"),
    UP_TIME(2, "启动次数"),
    WORK_TEMP(3, "运行时间"),
    LOAD_TIME(1, "加载时间"),
    LOAD_TIMES(2, "加载次数"),
    CONSTANT_CONTROL_SET(3, "恒压控制设定"),
    UBLOAD_PRE_SET(1, "卸载压力设定"),
    LOAD_PRE_SET(2, "加载压力设定"),
    MAIN_MOTOR_RATED_CUR(3, "主电机额定电流"),
    LOW_LIMIT(3, "防喘振电流下限设定"),
    CONTROL_SET(1, "防喘振压力控制设定"),
    EXHAUST_FLOW_RATE(2, "排气流量"),
    POWER(3, "功率"),
    P1_PRE(2, "压力P1"),
    P2_PRE(2, "压力P2"),
    P3_PRE(2, "压力P3"),
    CURRENT(2, "电流"),
    POW(3, " 功率"),
    SUPPLY_TEMP(1, "供气总管露点温度"),
    SUPPLY_PRE(2, "供气总管压力"),
    STREAM_SPEED(3, "瞬时流速"),
    STREAM(3, "瞬时流量");



    private int id;
    private String text;
    private DataIdInfoEnum(int id, String text) {
        this.id = id;
        this.text = text;
    }
    public static String valueOf(Integer code) {
        if (Objects.isNull(code)) {
            return "";
        }
        for (DataIdInfoEnum type : DataIdInfoEnum.values()) {
            if (Objects.equals(type.getId(), code)) {
                return type.getText();
            }
        }
        return "";

    }
}
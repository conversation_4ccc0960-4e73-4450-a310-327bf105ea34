package com.cet.eem.bll.energysaving.handle.optimizationstrategy;

import com.cet.eem.bll.energysaving.model.aioptimization.ColdWaterMainEngineGroupEfficiencyVo;
import com.cet.eem.common.model.BaseVo;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class JudgeParam {
    //所有的冷机
    public List<BaseVo> coldWaterMainEngines;
    //运行中的冷机id
    public List<Long> coldWaterMainEnginesInOperation;
    //待机的冷机id
    public List<Long> coldWaterMainEnginesInStandby;
    //待操作的目标冷机
    public List<Long> coldWaterMainEnginesInTarget;
    //是否执行优化
    public Boolean isOptimization;
    //优化类型 1-加机 2-减机
    public Integer optimizationType;
    //制冷系统id
    public Long refrigeratingSystemID;
    //制冷房间id
    public Long roomID;
    //目标冷机组合
    public ColdWaterMainEngineGroupEfficiencyVo coldWaterMainEngineGroupEfficiencyVo;

    public JudgeParam(List<BaseVo> coldWaterMainEngines, List<Long> coldWaterMainEnginesInOperation,
                      List<Long> coldWaterMainEnginesInStandby, List<Long> coldWaterMainEnginesInTarget,
                      Boolean isOptimization, Integer optimizationType, Long refrigeratingSystemID,
                      ColdWaterMainEngineGroupEfficiencyVo coldWaterMainEngineGroupEfficiencyVo, Long roomID) {
        this.coldWaterMainEngines = coldWaterMainEngines;
        this.coldWaterMainEnginesInOperation = coldWaterMainEnginesInOperation;
        this.coldWaterMainEnginesInStandby = coldWaterMainEnginesInStandby;
        this.coldWaterMainEnginesInTarget = coldWaterMainEnginesInTarget;
        this.isOptimization = isOptimization;
        this.optimizationType = optimizationType;
        this.refrigeratingSystemID = refrigeratingSystemID;
        this.coldWaterMainEngineGroupEfficiencyVo = coldWaterMainEngineGroupEfficiencyVo;
        this.roomID = roomID;
    }

    public JudgeParam(List<BaseVo> coldWaterMainEngines, List<Long> coldWaterMainEnginesInOperation, List<Long> coldWaterMainEnginesInStandby, Long refrigeratingSystemID, Long roomID) {
        this.coldWaterMainEngines = coldWaterMainEngines;
        this.coldWaterMainEnginesInOperation = coldWaterMainEnginesInOperation;
        this.coldWaterMainEnginesInStandby = coldWaterMainEnginesInStandby;
        this.refrigeratingSystemID = refrigeratingSystemID;
        this.roomID = roomID;
    }
}

package com.cet.eem.bll.compressoroptimization.model.trend;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName : TrendSearchVo
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-18 10:23
 */
@Getter
@Setter
public class CompressorTrendSearchVo {
    private List<CompressorDataIdSearchVo> meterConfigs;

    private LocalDateTime endTime;

    private LocalDateTime startTime;
}
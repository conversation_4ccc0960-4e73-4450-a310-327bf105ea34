package com.cet.eem.bll.energysaving.dao.aioptimization.impl;

import com.cet.eem.bll.energysaving.dao.aioptimization.ParameterConfigDao;
import com.cet.eem.bll.energysaving.model.config.ParameterConfig;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @ClassName : ParameterConfigDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-20 19:51
 */
@Repository
public class ParameterConfigDaoImpl extends ModelDaoImpl<ParameterConfig> implements ParameterConfigDao {

    @Override
    public ParameterConfig queryConfig(Long systemId) {
        LambdaQueryWrapper<ParameterConfig> wrapper = LambdaQueryWrapper.of(ParameterConfig.class);
        wrapper.eq(ParameterConfig::getSystemId, systemId);
        List<ParameterConfig> parameterConfigs = this.selectList(wrapper);
        if (CollectionUtils.isEmpty(parameterConfigs)) {
            return new ParameterConfig();
        }
        return parameterConfigs.get(0);
    }
}
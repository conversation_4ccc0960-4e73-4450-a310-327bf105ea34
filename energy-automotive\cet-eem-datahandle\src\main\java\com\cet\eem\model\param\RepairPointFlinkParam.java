package com.cet.eem.model.param;

import com.cet.eem.model.vo.DataLogValue;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/7 19:14
 */
@Getter
@Setter
@NoArgsConstructor
public class RepairPointFlinkParam extends TimeValueParam {

    //参考数据的起始时间
    private long referenceStartTime;
    //参考数据的结束时间
    private long referenceEndTime;
    List<DataLogValue> referenceDataLogList;

}

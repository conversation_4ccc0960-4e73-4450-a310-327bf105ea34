[{"type": 1, "relateDevice": true, "name": "变压器", "modelLabel": "powertransformer", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 1}], "fieldName": "配电室"}, "fields": [{"field": "name", "required": true, "fieldName": "设备名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "设备编码", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "brand", "required": false, "fieldName": "品牌", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "coiltype", "required": false, "fieldName": "圈变压类型", "dataType": "coiltype", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "latitude", "required": false, "fieldName": "纬度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "longitude", "required": false, "fieldName": "经度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修时间", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "noloadcurrent", "required": false, "fieldName": "空载电流", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "noloadloss", "required": false, "fieldName": "空载损耗", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "phasetype", "required": false, "fieldName": "相别类型", "dataType": "phasetype", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ptratio", "required": false, "fieldName": "PT变比", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcapacity", "required": false, "fieldName": "额定容量", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcurrent", "required": false, "fieldName": "额定电流", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedvoltage", "required": false, "fieldName": "额定电压", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "shortcircuitimpedance", "required": false, "fieldName": "短路阻抗", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "shortcircuitloss", "required": false, "fieldName": "短路损耗", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "status", "required": false, "fieldName": "设备启用状态", "dataType": "deviceenablestatus", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "voltagelevel", "required": false, "fieldName": "电压等级", "dataType": "voltagelevel", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "warrantydate", "required": false, "fieldName": "质保期限", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": "deviceclassification", "sourceType": 2, "sourceField": "id"}, {"field": "asset", "required": false, "fieldName": "资产编码", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastpretestdate", "required": false, "fieldName": "上次预检日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": false, "fieldName": "负载波动损耗系数", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "保养周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "设备厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "出厂日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "materialtype", "required": false, "fieldName": "材质", "dataType": "materialtype", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "operationstatus", "required": false, "fieldName": "设备运行状态", "dataType": "deviceoperationstatus", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedloss", "required": false, "fieldName": "额定负载损耗", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "shortcircuitvoltage", "required": false, "fieldName": "短路电压", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nodetemplateid", "required": false, "fieldName": "节点模板id", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}], "tips": null}, {"type": 1, "relateDevice": true, "name": "电容柜", "modelLabel": "capacitor", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 1}], "fieldName": "配电室"}, "fields": [{"field": "name", "required": true, "fieldName": "设备名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": "deviceclassification", "sourceType": 2, "sourceField": "id"}, {"field": "brand", "required": false, "fieldName": "品牌", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "latitude", "required": false, "fieldName": "纬度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "longitude", "required": false, "fieldName": "经度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "voltagelevel", "required": false, "fieldName": "电压等级", "dataType": "voltagelevel", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "asset", "required": false, "fieldName": "资产编码", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastpretestdate", "required": false, "fieldName": "上次预检日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "保养周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "出厂日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "operationstatus", "required": false, "fieldName": "设备运行状态", "dataType": "deviceoperationstatus", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcapacity", "required": false, "fieldName": "额定功率", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcurrent", "required": false, "fieldName": "额定电流", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedvoltage", "required": false, "fieldName": "额定电压", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "vendor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "warrantydate", "required": false, "fieldName": "质保期限", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nodetemplateid", "required": false, "fieldName": "节点模板id", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}], "tips": null}, {"type": 1, "relateDevice": true, "name": "开关柜或一段线", "modelLabel": "linesegmentwithswitch", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 1}], "fieldName": "配电室"}, "fields": [{"field": "name", "required": true, "fieldName": "名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "brand", "required": false, "fieldName": "品牌", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ctpolarity", "required": false, "fieldName": "CT极性", "dataType": "ctpolarity", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": "deviceclassification", "sourceType": 2, "sourceField": "id"}, {"field": "latitude", "required": false, "fieldName": "纬度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "linefunctiontype", "required": false, "fieldName": "线的功能", "dataType": "linefunctiontype", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "linesegmentwithswitch_id", "required": false, "fieldName": "所属低压馈电柜ID", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "loadclass", "required": false, "fieldName": "供电负载类型", "dataType": "loadclass", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "longitude", "required": false, "fieldName": "经度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcapacity", "required": false, "fieldName": "额定功率", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "voltagelevel", "required": false, "fieldName": "线路电压等级", "dataType": "voltagelevel", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "asset", "required": false, "fieldName": "资产编码", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "distributionpurpose", "required": false, "fieldName": "配电用途", "dataType": "distributionpurpose", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastpretestdate", "required": false, "fieldName": "上次预检日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "保养周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "出厂日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "operationstatus", "required": false, "fieldName": "设备运行状态", "dataType": "deviceoperationstatus", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "powerproperty", "required": false, "fieldName": "电源性质", "dataType": "powerproperty", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcurrent", "required": false, "fieldName": "额定电流", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedvoltage", "required": false, "fieldName": "额定电压", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "resistance", "required": false, "fieldName": "电阻", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "vendor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "warrantydate", "required": false, "fieldName": "质保期限", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nodetemplateid", "required": false, "fieldName": "节点模板id", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}], "tips": null}, {"type": 1, "relateDevice": true, "name": "母线", "modelLabel": "busbarsection", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 1}], "fieldName": "配电室"}, "fields": [{"field": "name", "required": true, "fieldName": "设备名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "brand", "required": false, "fieldName": "品牌", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "busbarmarktype", "required": false, "fieldName": "母线标记类型", "dataType": "busbarmarktype", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "latitude", "required": false, "fieldName": "纬度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "longitude", "required": false, "fieldName": "经度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcapacity", "required": false, "fieldName": "额定功率", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "voltagelevel", "required": false, "fieldName": "电压等级", "dataType": "voltagelevel", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": "deviceclassification", "sourceType": 2, "sourceField": "id"}, {"field": "asset", "required": false, "fieldName": "资产编码", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastpretestdate", "required": false, "fieldName": "上次预检日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "保养周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "出厂日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "materialtype", "required": false, "fieldName": "材质", "dataType": "materialtype", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "operationstatus", "required": false, "fieldName": "设备运行状态", "dataType": "deviceoperationstatus", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcurrent", "required": false, "fieldName": "额定电流", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedvoltage", "required": false, "fieldName": "额定电压", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "vendor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "warrantydate", "required": false, "fieldName": "质保期限", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nodetemplateid", "required": false, "fieldName": "节点模板id", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}], "tips": null}, {"type": 1, "relateDevice": true, "name": "母联", "modelLabel": "busbarconnector", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 1}], "fieldName": "配电室"}, "fields": [{"field": "name", "required": true, "fieldName": "设备名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": "deviceclassification", "sourceType": 2, "sourceField": "id"}, {"field": "brand", "required": false, "fieldName": "品牌", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "busbarsegi", "required": false, "fieldName": "母联所关联的I段母线对象", "dataType": "int4", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "busbarsegii", "required": false, "fieldName": "母联所关联的II段母线对象", "dataType": "int4", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ctpolarity", "required": false, "fieldName": "ct极性", "dataType": "ctpolarity", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "latitude", "required": false, "fieldName": "纬度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "longitude", "required": false, "fieldName": "经度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcapacity", "required": false, "fieldName": "额定容量", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "voltagelevel", "required": false, "fieldName": "电压等级", "dataType": "voltagelevel", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "asset", "required": false, "fieldName": "资产编码", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastpretestdate", "required": false, "fieldName": "上次预检日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "保养周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "出厂日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "operationstatus", "required": false, "fieldName": "设备运行状态", "dataType": "deviceoperationstatus", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcurrent", "required": false, "fieldName": "额定电流", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedvoltage", "required": false, "fieldName": "额定电压", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "vendor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "warrantydate", "required": false, "fieldName": "质保期限", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nodetemplateid", "required": false, "fieldName": "节点模板id", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}], "tips": null}, {"type": 1, "relateDevice": true, "name": "发电机", "modelLabel": "generator", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 1}], "fieldName": "配电室"}, "fields": [{"field": "name", "required": true, "fieldName": "名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": "deviceclassification", "sourceType": 2, "sourceField": "id"}, {"field": "asset", "required": false, "fieldName": "资产编码", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "brand", "required": false, "fieldName": "品牌", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastpretestdate", "required": false, "fieldName": "上次预检日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "保养周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "出厂日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "设备型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "operationstatus", "required": false, "fieldName": "设备运行状态", "dataType": "deviceoperationstatus", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcapacity", "required": false, "fieldName": "额定容量", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcurrent", "required": false, "fieldName": "额定电流", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedvoltage", "required": false, "fieldName": "额定电压", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "warrantydate", "required": false, "fieldName": "质保期限", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nodetemplateid", "required": false, "fieldName": "节点模板id", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}], "tips": null}, {"type": 1, "relateDevice": true, "name": "高压直流设备", "modelLabel": "hvdc", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 1}], "fieldName": "配电室"}, "fields": [{"field": "name", "required": true, "fieldName": "设备名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "asset", "required": false, "fieldName": "资产编码", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "brand", "required": false, "fieldName": "品牌", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": "deviceclassification", "sourceType": 2, "sourceField": "id"}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastpretestdate", "required": false, "fieldName": "上次预检日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "latitude", "required": false, "fieldName": "纬度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "longitude", "required": false, "fieldName": "经度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "保养周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "出厂日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "operationstatus", "required": false, "fieldName": "设备运行状态", "dataType": "deviceoperationstatus", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcapacity", "required": false, "fieldName": "额定功率", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcurrent", "required": false, "fieldName": "额定电流", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedvoltage", "required": false, "fieldName": "额定电压", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "vendor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "voltagelevel", "required": false, "fieldName": "电压等级", "dataType": "voltagelevel", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "warrantydate", "required": false, "fieldName": "质保期限", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nodetemplateid", "required": false, "fieldName": "节点模板id", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}], "tips": null}, {"type": 1, "relateDevice": true, "name": "UPS", "modelLabel": "ups", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 1}], "fieldName": "配电室"}, "fields": [{"field": "name", "required": true, "fieldName": "设备名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "设备编码", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": "deviceclassification", "sourceType": 2, "sourceField": "id"}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "conversiontime", "required": false, "fieldName": "转换时间", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "inputvoltage", "required": false, "fieldName": "输入电压", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "inverterefficiency", "required": false, "fieldName": "逆变器效率", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "machineefficiency", "required": false, "fieldName": "整机效率", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "outputvoltage", "required": false, "fieldName": "输出电压", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcapacity", "required": false, "fieldName": "额定功率", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "upsstructure", "required": false, "fieldName": "逆变器结构", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "warrantydate", "required": false, "fieldName": "质保期限", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "asset", "required": false, "fieldName": "资产编码", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "brand", "required": false, "fieldName": "品牌", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastpretestdate", "required": false, "fieldName": "上次预检日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "保养周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "materialtype", "required": false, "fieldName": "材质", "dataType": "materialtype", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "operationstatus", "required": false, "fieldName": "设备运行状态", "dataType": "deviceoperationstatus", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcurrent", "required": false, "fieldName": "额定电流", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedvoltage", "required": false, "fieldName": "额定电压", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "voltagelevel", "required": false, "fieldName": "电压等级", "dataType": "voltagelevel", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nodetemplateid", "required": false, "fieldName": "节点模板id", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "生产日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}], "tips": null}, {"type": 1, "relateDevice": true, "name": "蓄电池", "modelLabel": "battery", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 1}], "fieldName": "配电室"}, "fields": [{"field": "name", "required": true, "fieldName": "名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "电池组编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": "deviceclassification", "sourceType": 2, "sourceField": "id"}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修时间", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修时间", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "warrantydate", "required": false, "fieldName": "质保期限", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "asset", "required": false, "fieldName": "资产编码", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "batterysection", "required": false, "fieldName": "电池节数", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "convergencemodulecode", "required": false, "fieldName": "收敛模块编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "convergencemodulemodel", "required": false, "fieldName": "收敛模块型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastpretestdate", "required": false, "fieldName": "上次预检日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "保养周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "电池厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "operationstatus", "required": false, "fieldName": "设备运行状态", "dataType": "deviceoperationstatus", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcapacity", "required": false, "fieldName": "额定功率", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcurrent", "required": false, "fieldName": "额定电流", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedvoltage", "required": false, "fieldName": "额定电压", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "supplytoid", "required": false, "fieldName": "供电到设备ID", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "supplytolabel", "required": false, "fieldName": "供电到设备模型标识", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "supplytoname", "required": false, "fieldName": "供电到设备名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "voltage", "required": false, "fieldName": "电池组电压", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "voltagelevel", "required": false, "fieldName": "电压等级", "dataType": "voltagelevel", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nodetemplateid", "required": false, "fieldName": "节点模板id", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "生产日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}], "tips": null}, {"type": 1, "relateDevice": true, "name": "PT柜", "modelLabel": "ptcabinet", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 1}], "fieldName": "配电室"}, "fields": [{"field": "name", "required": true, "fieldName": "设备名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "latitude", "required": false, "fieldName": "纬度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "longitude", "required": false, "fieldName": "经度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": "deviceclassification", "sourceType": 2, "sourceField": "id"}, {"field": "brand", "required": false, "fieldName": "品牌", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "voltagelevel", "required": false, "fieldName": "电压等级", "dataType": "voltagelevel", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "asset", "required": false, "fieldName": "资产编码", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastpretestdate", "required": false, "fieldName": "上次预检日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "保养周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "出厂日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "operationstatus", "required": false, "fieldName": "设备运行状态", "dataType": "deviceoperationstatus", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcapacity", "required": false, "fieldName": "额定容量", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcurrent", "required": false, "fieldName": "额定电流", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedvoltage", "required": false, "fieldName": "额定电压", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "warrantydate", "required": false, "fieldName": "质保期限", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nodetemplateid", "required": false, "fieldName": "节点模板id", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}], "tips": null}, {"type": 1, "relateDevice": true, "name": "计量柜", "modelLabel": "meteringcabinet", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 1}], "fieldName": "配电室"}, "fields": [{"field": "name", "required": true, "fieldName": "设备名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": "deviceclassification", "sourceType": 2, "sourceField": "id"}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "latitude", "required": false, "fieldName": "纬度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "longitude", "required": false, "fieldName": "经度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "voltagelevel", "required": false, "fieldName": "电压等级", "dataType": "voltagelevel", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "brand", "required": false, "fieldName": "品牌", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "出厂日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "检修周期（天）", "dataType": "int4", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nodetemplateid", "required": false, "fieldName": "节点模板id", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}], "tips": null}, {"type": 1, "relateDevice": true, "name": "列头柜", "modelLabel": "arraycabinet", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 1}], "fieldName": "配电室"}, "fields": [{"field": "name", "required": true, "fieldName": "设备名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "asset", "required": false, "fieldName": "资产编码", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "brand", "required": false, "fieldName": "品牌", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "devicetype", "required": false, "fieldName": "设备类别", "dataType": "devicetypeenum", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastpretestdate", "required": false, "fieldName": "上次预检日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "latitude", "required": false, "fieldName": "纬度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "longitude", "required": false, "fieldName": "经度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "保养周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "出厂日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "operationstatus", "required": false, "fieldName": "设备运行状态", "dataType": "deviceoperationstatus", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcapacity", "required": false, "fieldName": "额定容量", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcurrent", "required": false, "fieldName": "额定电流", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedvoltage", "required": false, "fieldName": "额定电压", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "voltagelevel", "required": false, "fieldName": "电压等级", "dataType": "voltagelevel", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "warrantydate", "required": false, "fieldName": "质保期限", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nodetemplateid", "required": false, "fieldName": "节点模板id", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": "deviceclassification", "sourceType": 2, "sourceField": "id"}], "tips": null}, {"type": 1, "relateDevice": true, "name": "配电柜", "modelLabel": "powerdiscabinet", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 1}], "fieldName": "配电室"}, "fields": [{"field": "name", "required": true, "fieldName": "设备名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "brand", "required": false, "fieldName": "品牌", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastpretestdate", "required": false, "fieldName": "上次预检日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "latitude", "required": false, "fieldName": "纬度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "longitude", "required": false, "fieldName": "经度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "保养周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "出厂日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "operationstatus", "required": false, "fieldName": "设备运行状态", "dataType": "deviceoperationstatus", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "powerdiscabinettype", "required": false, "fieldName": "配电柜类型", "dataType": "powerdiscabinettype", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "prodstandard", "required": false, "fieldName": "生产标准", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "protlevel", "required": false, "fieldName": "防护等级", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "voltagelevel", "required": false, "fieldName": "电压等级", "dataType": "voltagelevel", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "warrantydate", "required": false, "fieldName": "质保期限", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "asset", "required": false, "fieldName": "资产编码", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nodetemplateid", "required": false, "fieldName": "节点模板id", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": "deviceclassification", "sourceType": 2, "sourceField": "id"}], "tips": null}, {"type": 1, "relateDevice": true, "name": "开关柜", "modelLabel": "switchcabinet", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 1}], "fieldName": "配电室"}, "fields": [{"field": "name", "required": true, "fieldName": "名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "asset", "required": false, "fieldName": "资产编码", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "brand", "required": false, "fieldName": "品牌", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ctpolarity", "required": false, "fieldName": "CT极性", "dataType": "ctpolarity", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "distributionpurpose", "required": false, "fieldName": "配电用途", "dataType": "distributionpurpose", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastpretestdate", "required": false, "fieldName": "上次预检日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "latitude", "required": false, "fieldName": "纬度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "loadclass", "required": false, "fieldName": "供电负载类型", "dataType": "loadclass", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "longitude", "required": false, "fieldName": "经度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "保养周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "出厂日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "operationstatus", "required": false, "fieldName": "设备运行状态", "dataType": "deviceoperationstatus", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "powerproperty", "required": false, "fieldName": "电源性质", "dataType": "powerproperty", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcapacity", "required": false, "fieldName": "额定功率", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcurrent", "required": false, "fieldName": "额定电流", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedvoltage", "required": false, "fieldName": "额定电压", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "resistance", "required": false, "fieldName": "电阻", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "switchfunctiontype", "required": false, "fieldName": "开关柜的功能", "dataType": "switchfunctiontype", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "voltagelevel", "required": false, "fieldName": "线路电压等级", "dataType": "voltagelevel", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "warrantydate", "required": false, "fieldName": "质保期限", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nodetemplateid", "required": false, "fieldName": "节点模板id", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": "deviceclassification", "sourceType": 2, "sourceField": "id"}], "tips": null}, {"type": 1, "relateDevice": true, "name": "AVC", "modelLabel": "avc", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 1}], "fieldName": "配电室"}, "fields": [{"field": "name", "required": true, "fieldName": "设备名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "devicetype", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "materialtype", "required": false, "fieldName": "材质", "dataType": "materialtype", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcapacity", "required": false, "fieldName": "额定容量", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "voltagelevel", "required": false, "fieldName": "电压等级", "dataType": "voltagelevel", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "检修周期（天）", "dataType": "int4", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号规格", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nodetemplateid", "required": false, "fieldName": "节点模板id", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": "deviceclassification", "sourceType": 2, "sourceField": "id"}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "生产日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}], "tips": null}, {"type": 1, "relateDevice": true, "name": "ats", "modelLabel": "ats", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 1}], "fieldName": "配电室"}, "fields": [{"field": "name", "required": true, "fieldName": "设备名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "asset", "required": false, "fieldName": "资产编码", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "brand", "required": false, "fieldName": "品牌", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "latitude", "required": false, "fieldName": "纬度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "longitude", "required": false, "fieldName": "经度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "保养周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "出厂日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcapacity", "required": false, "fieldName": "额定功率", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcurrent", "required": false, "fieldName": "额定电流", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedvoltage", "required": false, "fieldName": "额定电压", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "voltagelevel", "required": false, "fieldName": "电压等级", "dataType": "voltagelevel", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "warrantydate", "required": false, "fieldName": "质保期限", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nodetemplateid", "required": false, "fieldName": "节点模板id", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": "deviceclassification", "sourceType": 2, "sourceField": "id"}], "tips": null}, {"type": 1, "relateDevice": true, "name": "直流屏", "modelLabel": "dcpanel", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 1}], "fieldName": "配电室"}, "fields": [{"field": "name", "required": true, "fieldName": "设备名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "asset", "required": false, "fieldName": "资产编码", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedvoltage", "required": false, "fieldName": "额定电压", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "voltagelevel", "required": false, "fieldName": "电压等级", "dataType": "voltagelevel", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "warrantydate", "required": false, "fieldName": "质保期限", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "brand", "required": false, "fieldName": "品牌", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastpretestdate", "required": false, "fieldName": "上次预检日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "latitude", "required": false, "fieldName": "纬度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "longitude", "required": false, "fieldName": "经度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "保养周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "出厂日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "operationstatus", "required": false, "fieldName": "设备运行状态", "dataType": "deviceoperationstatus", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcapacity", "required": false, "fieldName": "额定功率", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcurrent", "required": false, "fieldName": "额定电流", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nodetemplateid", "required": false, "fieldName": "节点模板id", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": "deviceclassification", "sourceType": 2, "sourceField": "id"}], "tips": null}, {"type": 1, "relateDevice": true, "name": "IT机柜", "modelLabel": "itcabinet", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 1}], "fieldName": "配电室"}, "fields": [{"field": "name", "required": true, "fieldName": "名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcapacity", "required": false, "fieldName": "额定容量", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "检修周期（天）", "dataType": "int4", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号规格", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nodetemplateid", "required": false, "fieldName": "节点模板id", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": "deviceclassification", "sourceType": 2, "sourceField": "id"}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "生产日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}], "tips": null}, {"type": 1, "relateDevice": true, "name": "一段线", "modelLabel": "linesegment", "parentNodes": [{"modelLabel": "powerdiscabinet", "fieldName": "所属配电柜"}, {"modelLabel": "arraycabinet", "fieldName": "所属列头柜"}], "fields": [{"field": "name", "required": true, "fieldName": "名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "asset", "required": false, "fieldName": "资产编码", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "brand", "required": false, "fieldName": "品牌", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ctpolarity", "required": false, "fieldName": "CT极性", "dataType": "ctpolarity", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "distributionpurpose", "required": false, "fieldName": "配电用途", "dataType": "distributionpurpose", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastpretestdate", "required": false, "fieldName": "上次预检日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "latitude", "required": false, "fieldName": "纬度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "linefunctiontype", "required": false, "fieldName": "线的功能", "dataType": "linefunctiontype", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "loadclass", "required": false, "fieldName": "供电负载类型", "dataType": "loadclass", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "longitude", "required": false, "fieldName": "经度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "保养周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "出厂日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "operationstatus", "required": false, "fieldName": "设备运行状态", "dataType": "deviceoperationstatus", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "powerproperty", "required": false, "fieldName": "电源性质", "dataType": "powerproperty", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcapacity", "required": false, "fieldName": "额定功率", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedcurrent", "required": false, "fieldName": "额定电流", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedvoltage", "required": false, "fieldName": "额定电压", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "resistance", "required": false, "fieldName": "电阻", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "voltagelevel", "required": false, "fieldName": "线路电压等级", "dataType": "voltagelevel", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "warrantydate", "required": false, "fieldName": "质保期限", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nodetemplateid", "required": false, "fieldName": "节点模板id", "dataType": "int8", "dateFormat": null, "sourceLabel": "nodetemplate", "sourceType": 2, "sourceField": "id"}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": "deviceclassification", "sourceType": 2, "sourceField": "id"}], "tips": null}, {"type": 1, "relateDevice": true, "name": "管道", "modelLabel": "pipeline", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 6}], "fieldName": "所属配电室"}, "fields": [{"field": "unitsymbol", "required": false, "fieldName": "计量单位", "dataType": "UnitSymbol", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "energytype", "required": true, "fieldName": "能源类型", "dataType": "EnergyType", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "pipefunctiontype", "required": false, "fieldName": "管道功能类型", "dataType": "Pipefunctiontype", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "name", "required": true, "fieldName": "名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}], "tips": null}]
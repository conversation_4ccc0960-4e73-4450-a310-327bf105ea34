package com.cet.eem.coldoptimizatinservice.controller;

import com.cet.eem.bll.common.model.ext.subject.energysaving.DeviceChainWithSubLayer;
import com.cet.eem.bll.energysaving.handle.CurveFitting;
import com.cet.eem.bll.energysaving.model.config.ChainWithTrendVo;
import com.cet.eem.bll.energysaving.model.weather.QueryParam;
import com.cet.eem.common.model.Result;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.utils.TimeUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/22
 */
@Api(value = "CurveFittingController", tags = {"设备运行曲线拟合接口"})
@RestController
@RequestMapping(value = "/eem/v1/curveFitting")
public class CurveFittingController {
    @Autowired
    CurveFitting curveFitting;

    @ApiOperation(value = "手动执行曲线拟合")
    @PostMapping(value = "/calCurveFitting", produces = "application/json")
    public Result<Object> setCurveFitting(@RequestParam Long time){
         curveFitting.dataFitting(TimeUtil.timestamp2LocalDateTime(time));
        return Result.ok();
    }
}

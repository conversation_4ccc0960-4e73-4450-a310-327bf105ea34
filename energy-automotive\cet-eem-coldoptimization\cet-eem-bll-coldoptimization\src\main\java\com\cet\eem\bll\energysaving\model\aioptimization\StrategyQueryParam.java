package com.cet.eem.bll.energysaving.model.aioptimization;

import com.cet.eem.common.model.Page;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * @ClassName : StrategyQueryParam
 * @Description : 策略查询参数
 * <AUTHOR> ji<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-15 14:19
 */
@Getter
@Setter
public class StrategyQueryParam {
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Integer cycle;
    private Integer strategyType;
    private Long systemId;
    private Page page;
}
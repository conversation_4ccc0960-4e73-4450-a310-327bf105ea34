package com.cet.eem.compressoroptimizationservice.controller;

import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.bll.compressoroptimization.model.benefit.BenefitQueryParam;
import com.cet.eem.bll.compressoroptimization.model.benefit.ElectricalMonthData;
import com.cet.eem.bll.compressoroptimization.model.benefit.ElectricalYearData;
import com.cet.eem.bll.compressoroptimization.model.trend.BaseVoWithDataId;
import com.cet.eem.bll.compressoroptimization.model.trend.CompressorTrendSearchVo;
import com.cet.eem.bll.compressoroptimization.model.trend.TrendDataVoWithUnit;
import com.cet.eem.bll.compressoroptimization.service.config.BenefitAnalysisService;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName : BenefitAnalysisController
 * @Description :效益分析
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-17 09:59
 */
@Api(value = "BenefitAnalysisController", tags = {"效益分析"})
@RestController
@RequestMapping(value = "/eem/v1/compressorOptimization/benefit")
public class BenefitAnalysisController {
    @Autowired
    BenefitAnalysisService benefitAnalysisService;
    @ApiOperation("查询年度电气相关")
    @PostMapping(value = "/electrical/year")
    public Result<ElectricalYearData> queryElectricalYearData(@RequestParam(required = false) Long roomId) throws IOException {
        return Result.ok(benefitAnalysisService.queryElectricalYearData(roomId, GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation("查询月度电气比")
    @PostMapping(value = "/electrical/month")
    public Result<List<ElectricalMonthData>> queryElectricalMonthData(@RequestBody BenefitQueryParam queryParam) throws IOException {
        return Result.ok(benefitAnalysisService.queryElectricalMonthData(queryParam,GlobalInfoUtils.getProjectId()));

    }

    @ApiOperation("导出数据")
    @PostMapping(value = "/export")
    public Result<Object> exportMonthData(@RequestBody BenefitQueryParam queryParam, HttpServletResponse response) throws IOException {
        benefitAnalysisService.exportMonthData(queryParam,response,GlobalInfoUtils.getProjectId());
        return Result.ok();

    }
}
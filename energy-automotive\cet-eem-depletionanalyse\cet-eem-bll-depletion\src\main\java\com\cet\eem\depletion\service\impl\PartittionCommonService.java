package com.cet.eem.depletion.service.impl;

import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.constant.EnumOperationType;
import com.cet.eem.common.feign.DeviceDataService;
import com.cet.eem.common.model.event.EventCondition;
import com.cet.eem.common.model.event.EventLogVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.depletion.config.DepletionCfg;
import com.cet.eem.depletion.model.CataphoresisDto;
import com.cet.eem.depletion.model.Constant.Constant;
import com.cet.eem.depletion.model.DepletionPartitionDto;
import com.cet.eem.depletion.model.vo.DepletionCfgVo;
import com.cet.eem.model.tool.ModelServiceUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PartittionCommonService {
    @Autowired
    private DepletionCfg depletionCfg;
    @Autowired
    private DeviceDataService deviceDataService;
    @Autowired
    private ModelServiceUtils modelServiceUtils;
    @Value("${cet.eem.depletion.beforeDay:30}")
    private Integer beforeDay;

    private List<Long> startTimeList;
    private List<Long> endTimeList;
    private CataphoresisDto item;
    private Long logTime;

    /**
     * 赋值 是否进行执行.
     */
    public Boolean getCheck(List<Long> startTimeList, List<Long> endTimeList, CataphoresisDto item, Long logTime) {
        Boolean check = check(startTimeList, endTimeList, item, logTime);
        this.startTimeList = startTimeList;
        this.endTimeList = endTimeList;
        this.item = item;
        this.logTime = logTime;
        return check;
    }

    private Boolean check(List<Long> startTimeList, List<Long> endTimeList, CataphoresisDto item, Long logTime) {
        //如果过车信息不为空 则直接为true
        if (CollectionUtils.isNotEmpty(startTimeList) || CollectionUtils.isNotEmpty(endTimeList)) {
            return true;
        }
        return getLastDayIsOpen(item, logTime);
    }

    /**
     * 当前产线无开关机 往前查30天 判断最后是开关机
     *
     * @param item
     * @param logTime
     * @return
     */
    public Boolean getLastDayIsOpen(CataphoresisDto item, Long logTime) {
        Long st = TimeUtil.addDateTimeByCycle(logTime, AggregationCycle.ONE_DAY, -beforeDay);
        List<EventLogVo> eventLogVos = queryEventLogVos(item, st, logTime, Arrays.asList(Constant.EVENTTYPE_OPEN, Constant.EVENTTYPE_CLOSE));
        EventLogVo last = eventLogVos.stream().max(Comparator.comparing(EventLogVo::getEventTime)).orElse(null);
        //如果全部都为空 则不执行 返回为false
        return !Objects.isNull(last) && !Objects.equals(last.getEventType(), Constant.EVENTTYPE_CLOSE);
    }

    /**
     * 没有数据的时候 向前查询三天 作为辅助判断
     * 必须先执行getCHeck方法
     */
    public void noDataExcuet() {
        if (CollectionUtils.isNotEmpty(startTimeList) || CollectionUtils.isNotEmpty(endTimeList)) {
            return;
        }
        writeData(item, logTime);
    }

    /**
     * 检查昨天或者前天的事件
     * 当天没有开关机记录且没有过车记录
     */
    private void writeData(CataphoresisDto item, Long logTime) {
        DepletionPartitionDto partitionDto = getDepletionPartitionDto(item, logTime, getDepletiontype(item));
        List<DepletionPartitionDto> dtos = modelServiceUtils.writeData(partitionDto, DepletionPartitionDto.class);
        log.info("当前节点入库空耗事件数据为:{}", JsonTransferUtils.toJSONString(dtos));
    }

    /**
     * 获取空调的温度达标时间
     *
     * @param item
     * @param st
     * @param end
     * @return
     */
    public List<Long> queryAirReachedTimes(CataphoresisDto item, Long st, Long end) {
        DepletionCfgVo cfgVo = getDepletionCfgVo(item.getId(), item.getModelLabel());
        if (Objects.isNull(cfgVo)) {
            log.error("未获取到当前产线的配置信息!请排查问题!");
            return new ArrayList<>();
        }
        EventCondition condition = new EventCondition();
        condition.setChannelId(cfgVo.getChannelID());
        condition.setStationId(cfgVo.getStationID());
        condition.setEventClasses(Collections.singletonList(Constant.EVENTCLASSES_COMMON));
        condition.setEventTypes(Collections.singletonList(Constant.EVENTTYPE_OPEN));
        condition.setKeyWord(cfgVo.getKeyWord());
        List<EventLogVo> eventLogVos = getEventLogVos(st, end, condition);
        return eventLogVos.stream().map(EventLogVo::getEventTime).collect(Collectors.toList());
    }

    public Long getAirReachedTime(List<Long> timeList, Long time, Long end) {
        Long reachTime = getNextTime(timeList, time, 1);
        if (Objects.isNull(reachTime)){
            return end;
        }
        if (Objects.nonNull(end)&&reachTime>end){
            return end;
        }
        return reachTime;
    }

    /**
     * 查询开关事件
     *
     * @param item
     * @param st
     * @param end
     * @return
     */
    public List<EventLogVo> queryEventLogVos(CataphoresisDto item, Long st, Long end, List<Integer> eventTypes) {
        DepletionCfgVo cfgVo = getDepletionCfgVo(item.getId(), item.getModelLabel());
        if (Objects.isNull(cfgVo)) {
            log.error("未获取到当前产线的配置信息!请排查问题!");
            return new ArrayList<>();
        }
        EventCondition condition = createEventCondition(cfgVo, eventTypes);
        return getEventLogVos(st, end, condition);
    }

    public List<EventLogVo> queryEventLogVos(DepletionCfgVo cfgVo, Long st, Long end, List<Integer> eventTypes, List<Integer> eventClass) {
        EventCondition condition = createEventCondition(cfgVo, eventTypes, eventClass);
        return getEventLogVos(st, end, condition);
    }

    private Integer getDepletiontype(CataphoresisDto item) {
        if (Objects.equals(Constant.LINEBODYTYPE_CATA, item.getLinebodytype())) {
            return Constant.DEPLTIONETYPE_HEATING;
        } else {
            return Constant.DEPLTIONETYPE_ENTER;
        }

    }

    public Long getNextTime(List<Long> timeList, Long time, int offset) {
        if (CollectionUtils.isEmpty(timeList)) {
            return time;
        }
        List<Long> collect = new ArrayList<>(timeList);
        collect.add(time);
        collect = collect.stream().sorted().collect(Collectors.toList());
        for (int i = 0; i < collect.size(); i++) {
            if (Objects.equals(time, collect.get(i))) {
                if (i + offset <= 0) {
                    return collect.get(0);
                } else if (i + offset >= collect.size()) {
                    return collect.get(collect.size() - 1);
                } else {
                    return collect.get(i + offset);
                }
            }
        }
        return time;
    }

    /**
     * 获取一个事件数据
     *
     * @param item
     * @param logTime
     * @return
     */
    private DepletionPartitionDto getDepletionPartitionDto(CataphoresisDto item, Long logTime, Integer depletiontype) {
        DepletionPartitionDto partitionDto = new DepletionPartitionDto();
        partitionDto.setLogtime(logTime);
        partitionDto.setStarttime(logTime);
        partitionDto.setObjectid(item.getId());
        partitionDto.setProjectid(1L);
        partitionDto.setObjectlabel(item.getModelLabel());
        partitionDto.setDepletiontype(depletiontype);
        partitionDto.setEndtime(logTime + Constant.ONE_DAY);
        Long duration = CommonUtils.calcLong(partitionDto.getEndtime() - partitionDto.getStarttime(), Constant.ONE_S, EnumOperationType.DIVISION.getId());
        partitionDto.setDuration(duration);
        return partitionDto;
    }

    /**
     * 获取点泳池配置
     *
     * @return
     */
    private DepletionCfgVo getDepletionCfgVo(Long id, String label) {
        List<DepletionCfgVo> depletionCfgs = null;
        try {
            depletionCfgs = depletionCfg.getDepletionCfg();
        } catch (IOException e) {
            log.error(e.getMessage());
            log.error("获取电泳池配置数据失败!");
            return null;
        }
        return depletionCfgs.stream().filter(x -> Objects.equals(label, x.getObjectLabel()) && Objects.equals(id, x.getObjectID())).findFirst().orElse(null);
    }

    /**
     * 获取事件数据
     *
     * @param startTime
     * @param endTime
     * @param condition
     * @return
     */
    private List<EventLogVo> getEventLogVos(Long startTime, Long endTime, EventCondition condition) {
        List<EventLogVo> data = deviceDataService.queryEventData(0, 2000, startTime, endTime, condition).getData();
        if (Objects.isNull(data) || data.isEmpty()) {
            return new ArrayList<>();
        }
        return data;
    }

    private EventCondition createEventCondition(DepletionCfgVo item, List<Integer> eventTypes) {
        EventCondition eventCondition = new EventCondition();
        eventCondition.setChannelId(item.getChannelID());
        eventCondition.setStationId(item.getStationID());
        eventCondition.setEventClasses(Collections.singletonList(Constant.EVENTCLASSES));
        eventCondition.setEventTypes(eventTypes);
        eventCondition.setKeyWord(item.getKeyWord());
        return eventCondition;
    }

    private EventCondition createEventCondition(DepletionCfgVo item, List<Integer> eventTypes, List<Integer> eventClass) {
        EventCondition eventCondition = new EventCondition();
        eventCondition.setChannelId(item.getChannelID());
        eventCondition.setStationId(item.getStationID());
        eventCondition.setEventClasses(eventClass);
        eventCondition.setEventTypes(eventTypes);
        eventCondition.setKeyWord(item.getKeyWord());
        return eventCondition;
    }
}

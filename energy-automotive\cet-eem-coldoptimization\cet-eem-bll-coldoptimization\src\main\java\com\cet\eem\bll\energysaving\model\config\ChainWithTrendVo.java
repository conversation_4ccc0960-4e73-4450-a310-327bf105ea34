package com.cet.eem.bll.energysaving.model.config;

import com.cet.eem.common.model.datalog.TrendDataVo;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ChainWithTrendVo {
    private Long chainId;
    private Long time;
    private Long coldWaterMainEngineId;
    private Double coldWaterMainEnginePower = 0D;
    private Long coolingTowerId;
    private Double coolingTowerPower = 0D;
    private Long coolingPumpId;
    private Double coolingPumpPower = 0D;
    private Long refrigeratingPumpId;
    private Double refrigeratingPumpPower = 0D;
    private Double cop;
    private Double coolingLoadRate;
}

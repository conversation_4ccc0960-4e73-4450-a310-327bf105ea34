package com.cet.eem.depletionservice.task;

import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.depletion.service.CalcDepletionConsumptionService;
import com.cet.eem.depletion.service.DepletionDivideForCataphoresisService;
import com.cet.eem.depletion.service.DepletionDivideForLineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Configuration
@EnableScheduling
public class DepletionTask {

    @Autowired
    DepletionDivideForCataphoresisService depletionDivideForCataphoresisService;

    @Autowired
    DepletionDivideForLineService depletionDivideForLineService;

    @Autowired
    CalcDepletionConsumptionService calcDepletionConsumption;

    public static final Long ONE_DAY = 86400000L;

    @Scheduled(cron = "${cet.eem.depletion.depletiondivide-cron:'-'}")
    public void depletionDivide() throws Exception {
        depletionDivideForCataphoresisService.depletionDivide(TimeUtil.getFirstTimeOfDay(System.currentTimeMillis() - ONE_DAY), TimeUtil.getFirstTimeOfDay(System.currentTimeMillis()));
        depletionDivideForLineService.depletionDivide(TimeUtil.getFirstTimeOfDay(System.currentTimeMillis() - ONE_DAY), TimeUtil.getFirstTimeOfDay(System.currentTimeMillis()));
    }

    @Scheduled(cron = "${cet.eem.depletion.calcdepletionconsumption-cron:'-'}")
    public void calcDepletionConsumption() throws Exception {
        calcDepletionConsumption.calcDepletionConsumption(TimeUtil.getFirstTimeOfDay(System.currentTimeMillis() - ONE_DAY), TimeUtil.getFirstTimeOfDay(System.currentTimeMillis()));
    }
}

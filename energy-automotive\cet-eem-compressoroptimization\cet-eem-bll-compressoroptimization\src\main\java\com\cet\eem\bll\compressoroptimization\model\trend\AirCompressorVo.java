package com.cet.eem.bll.compressoroptimization.model.trend;

import com.cet.eem.common.model.BaseVo;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : AirCompressorVo
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-25 09:40
 */
@Getter
@Setter
public class AirCompressorVo extends BaseEntity {
    @JsonProperty("aircompressorattr")
    private Integer airCompressorAttr;
    @ApiModelProperty("子节点")
    private List<AirCompressorVo> children;
}
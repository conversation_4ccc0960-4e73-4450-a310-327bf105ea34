package com.cet.eem.bll.energysaving.service.aioptimization.impl;

import com.cet.eem.auth.service.AuthUtils;
import com.cet.eem.bll.common.dao.node.MeasureByDao;
import com.cet.eem.bll.common.dao.node.NodeDao;
import com.cet.eem.bll.common.log.service.CommonUtilsService;
import com.cet.eem.bll.common.model.domain.object.physicalquantity.MeasuredbyVo;
import com.cet.eem.bll.common.model.domain.subject.energysaving.DeviceChainDetail;

import com.cet.eem.bll.common.model.ext.subject.energysaving.DeviceChainWithSubLayer;
import com.cet.eem.bll.common.service.impl.PecCoreService;
import com.cet.eem.bll.energysaving.dao.aioptimization.AiStartStopStrategyDao;
import com.cet.eem.bll.energysaving.dao.aioptimization.ControlModeDao;
import com.cet.eem.bll.energysaving.dao.aioptimization.StrategyObjectMapDao;
import com.cet.eem.bll.energysaving.dao.weather.DeviceChainDao;
import com.cet.eem.bll.energysaving.dao.weather.RefrigeratingSystemDao;
import com.cet.eem.bll.energysaving.handle.AiScheduleConfig;
import com.cet.eem.bll.energysaving.model.aioptimization.AiStartStopStrategy;
import com.cet.eem.bll.energysaving.model.aioptimization.StrategyObjectMap;
import com.cet.eem.bll.energysaving.model.aioptimization.StrategyTypeDef;
import com.cet.eem.bll.energysaving.model.aioptimization.plc.ControlMode;
import com.cet.eem.bll.energysaving.model.aioptimization.plc.ControlParamInfo;
import com.cet.eem.bll.energysaving.model.config.PumpFunctionType;
import com.cet.eem.bll.energysaving.model.config.system.RefrigeratingSystem;
import com.cet.eem.bll.energysaving.model.def.ColdOptimizationLabelDef;
import com.cet.eem.bll.energysaving.model.def.ModelDef;
import com.cet.eem.bll.energysaving.model.def.QuantityDef;
import com.cet.eem.bll.energysaving.model.weather.PumpVo;
import com.cet.eem.bll.energysaving.service.aioptimization.AiPlcControlService;
import com.cet.eem.bll.energysaving.service.trend.OperationTrendService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.constant.EnumDataTypeId;
import com.cet.eem.common.definition.LoginDef;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.file.Md5Utils;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.auth.user.QueryUserPassword;
import com.cet.eem.common.model.auth.user.UserVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.model.realtime.RealTimeValue;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;
import com.cet.eem.quantity.service.QuantityManageService;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.matterhorn.devicedataservice.api.DataServiceRestApi;
import com.cet.electric.matterhorn.devicedataservice.api.PecCoreConfigRestApi;
import com.cet.electric.matterhorn.devicedataservice.common.entity.station.MeasureNodeWithDeviceIdDTO;
import com.cet.electric.matterhorn.devicedataservice.common.entity.station.RemoteControlPointDTO;
import com.cet.electric.matterhorn.devicedataservice.common.query.auth.UserPasswordParam;
import com.cet.electric.matterhorn.devicedataservice.common.query.control.ControlIdParam;
import com.cet.electric.matterhorn.devicedataservice.common.query.control.MultiRemoteControlPara;
import com.cet.electric.matterhorn.devicedataservice.common.query.control.RemoteControlIdParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : AiPlcControlServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-09-01 15:27
 */
@Service
@Slf4j
public class AiPlcControlServiceImpl implements AiPlcControlService {

    @Autowired
    AiScheduleConfig aiScheduleConfig;
    @Autowired
    protected AuthUtils authUtils;
    @Autowired
    RefrigeratingSystemDao refrigeratingSystemDao;
    @Autowired
    OperationTrendService operationTrendService;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    ControlModeDao controlModeDao;
    @Autowired
    QuantityManageService quantityManageService;
    @Autowired
    AiStartStopStrategyDao aiStartStopStrategyDao;
    @Autowired
    StrategyObjectMapDao strategyObjectMapDao;
    @Autowired
    DataServiceRestApi dataServiceRestApi;
    @Autowired
    MeasureByDao measureByDao;
    @Autowired
    DeviceChainDao deviceChainDao;
    @Autowired
    NodeDao nodeDao;
    @Autowired
    CommonUtilsService commonUtilsService;
    @Autowired
    PecCoreConfigRestApi coreConfigRestApi;
    @Autowired
    PecCoreService pecCoreService;
    public static final Double TRUE_DATA = 1.0;
    public static final Double FALSE_DATA = 0.0;
    public static final Integer START = 1;
    public static final Integer STOP = 0;
    public static final Integer TYPE = 6;
    public static final Integer REMOTE_TYPE = 0;
    public static final String WEB = "web";
    public static final String OPEN = "开";
    public static final String CLOSE = "关";
    public static final Integer OPERATION_TYPE = 65;
    public static final String SUCCESS = "遥控/调成功";
    public static final String WRONG = "遥控/调失败";
    public static final String LOG_START = "[制冷ai控制]";
    public static final String LOG_START_HEART = "[发送心跳包]";
    public static final String LOG_MIDDLE = "调整id为";
    public static final String NO_SYSTEM = "没有配置制冷系统！";
    public static final String NO_PLC = "没有创建制冷总设备！";
    public static final String CATCH_ERROR = "线程等待出现问题！";
    public static final String SYSTEM_ID = "系统id为";

    public static final Long HEART_DATA_ID = 9014186L;
    public static final Long PLC_AUTO_DATA_ID = 9014189L;
    public static final Long PLC_MANUAL_DATA_ID = 9014191L;

    public static final Long START_OR_STOP_DATA_ID = 9014137L;
    public static final Long OUT_WATER_DATA_ID = 6000695L;


    @Override
    public void refrigerationAiControl() {
        log.error(LOG_START + "开始运行制冷AI控制。");
        long beginTime = System.currentTimeMillis();

        try {
            plcControl();
        } catch (Exception e) {
            log.error(LOG_START + "运行制冷AI控制异常", e);
            Thread.currentThread().interrupt();
        }

        long overTime = System.currentTimeMillis();
        log.error(LOG_START + "本次运行制冷AI控制，耗时(s)：{}", (overTime - beginTime) * CommonUtils.DOUBLE_CONVERSION_COEFFICIENT / 1000);
        log.error(LOG_START + "结束运行制冷AI控制。");
    }

    @Override
    public void sendHeartBeat() {
        log.error(LOG_START_HEART + "开始发送心跳包");
        long beginTime = System.currentTimeMillis();

        try {
            sendHeartBeatMessage();
        } catch (Exception e) {
            log.error(LOG_START_HEART + "发送心跳包异常", e);
            Thread.currentThread().interrupt();
        }

        long overTime = System.currentTimeMillis();
        log.error(LOG_START_HEART + "本次发送心跳包，耗时(s)：{}", (overTime - beginTime) * CommonUtils.DOUBLE_CONVERSION_COEFFICIENT / 1000);
        log.error(LOG_START_HEART + "结束发送心跳包。");
    }

    private void sendHeartBeatMessage() {
        List<RefrigeratingSystem> refrigeratingSystems = refrigeratingSystemDao.selectAll();
        if (CollectionUtils.isEmpty(refrigeratingSystems)) {
            log.error(LOG_START_HEART + NO_SYSTEM);
            return;
        }
        refrigeratingSystems = refrigeratingSystems.stream().filter(it ->
                Boolean.TRUE.equals(it.getUseAi())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(refrigeratingSystems)) {
            log.error(LOG_START_HEART + NO_SYSTEM);
            return;
        }
        for (RefrigeratingSystem system : refrigeratingSystems) {
            //查询plc节点
            List<BaseVo> plcNodes = queryNodesByMonitor(system.getRoomId());
            if (CollectionUtils.isEmpty(plcNodes)) {
                log.error(LOG_START_HEART + SYSTEM_ID + "{}" + NO_PLC, system.getId());
                continue;
            }
            List<RemoteControlPointDTO> controlInfo = getControlInfo(plcNodes, HEART_DATA_ID, null);
//            List<RemoteControlPointInfo> controlInfo = controlInfo;

            if (CollectionUtils.isEmpty(controlInfo)) {
                log.error(LOG_START_HEART + SYSTEM_ID + "{}" + "没有配置心跳包遥控点！", system.getId());
            } else {
                //查询用户
                UserVo userVo = authUtils.queryUser(LoginDef.USER_ROOT);
                //获得明文密码
                String password = getPassword(userVo);
                //遥调一条条发，遥控开机关机分开发
                RemoteControlPointDTO startInfo = controlInfo.stream().filter(remoteControlPointInfo ->
                        Objects.equals(remoteControlPointInfo.getControlType(), START))
                        .findAny().orElse(null);
                RemoteControlPointDTO stopInfo = controlInfo.stream().filter(remoteControlPointInfo ->
                        Objects.equals(remoteControlPointInfo.getControlType(), STOP))
                        .findAny().orElse(null);
                if (Objects.isNull(stopInfo) || Objects.isNull(startInfo)) {
                    log.error(LOG_START_HEART + "系统id为{}没有配置一对心跳包遥控点！", system.getId());
                    continue;
                }
                RemoteControlIdParam remoteControlParam = createRemoteControlParam(startInfo, password,
                        userVo, TRUE_DATA, System.currentTimeMillis(), REMOTE_TYPE);
                dataServiceRestApi.remoteControlByParamId(remoteControlParam, true, null);
                //心跳包没有必要重试,发1信号，再发0信号,通过一组命令，上升或者下降沿记录1次
                try {
                    Thread.sleep(aiScheduleConfig.getWait() * TimeUtil.SECOND);
                } catch (Exception e) {
                    log.error(LOG_START_HEART + CATCH_ERROR);
                }
                RemoteControlIdParam remoteControlParam1 = createRemoteControlParam(startInfo, password,
                        userVo, FALSE_DATA, System.currentTimeMillis(), REMOTE_TYPE);
                dataServiceRestApi.remoteControlByParamId(remoteControlParam1,true, null);
            }
        }

    }

    @Override
    public Boolean sendRemoteControlByDataId(Long dataId, List<BaseVo> plcNodes, Integer type) {
        List<RemoteControlPointDTO> controlInfo = getControlInfo(plcNodes, dataId, type);
        if (CollectionUtils.isEmpty(controlInfo)) {
            return Boolean.FALSE;
        }
        //查询用户
        UserVo userVo = authUtils.queryUser(LoginDef.USER_ROOT);
        //获得明文密码
        String password = getPassword(userVo);
        //遥调一条条发，遥控开机关机分开发
        RemoteControlIdParam remoteControlParam = createRemoteControlParam(controlInfo.get(0), password,
                userVo, TRUE_DATA, System.currentTimeMillis(), REMOTE_TYPE);
        ApiResult<Boolean> booleanApiResult = dataServiceRestApi.remoteControlByParamId(remoteControlParam, true, null);
        return booleanApiResult.getData();
    }

    /**
     * 查询制冷总设备
     *
     * @param roomId
     * @return
     */
    @Override
    public List<BaseVo> queryNodesByMonitor(Long roomId) {
        QueryCondition condition = new QueryConditionBuilder<>(NodeLabelDef.ROOM, roomId)
                .leftJoin(ColdOptimizationLabelDef.PLC).queryAsTree().build();
        List<BaseVo> query = modelServiceUtils.query(condition, BaseVo.class);
        List<BaseVo> result = new ArrayList<>();
        for (BaseVo baseVo : query) {
            if (CollectionUtils.isNotEmpty(baseVo.getChildren())) {
                result.addAll(baseVo.getChildren());
            }
        }
        return result;
    }


    private void plcControl() {
        List<RefrigeratingSystem> refrigeratingSystems = refrigeratingSystemDao.selectAll();
        if (CollectionUtils.isEmpty(refrigeratingSystems)) {
            return;
        }
        refrigeratingSystems = refrigeratingSystems.stream().filter(it ->
                Boolean.TRUE.equals(it.getUseAi())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(refrigeratingSystems)) {
            log.error(LOG_START + NO_SYSTEM);
            return;
        }
        for (RefrigeratingSystem system : refrigeratingSystems) {
            //查询plc节点
            List<BaseVo> plcNodes = queryNodesByMonitor(system.getRoomId());
            if (CollectionUtils.isEmpty(plcNodes)) {
                log.error(LOG_START + "系统id为{}" + NO_PLC, system.getId());
                continue;
            }
            //首先判断制冷系统是否处于“AI程序控制”状态、PLC是否处于AI模式
            Boolean needAi = ifNeedAi(system.getId(), plcNodes);
            if (Boolean.TRUE.equals(needAi)) {
                LocalDateTime now = LocalDateTime.now();
                //最新的策略
                List<AiStartStopStrategy> strategies = aiStartStopStrategyDao.queryAiStartStopStrategy
                        (system.getId(), Collections.emptyList(), now, TimeUtil.addDateTimeByCycle(now, AggregationCycle.ONE_HOUR, 1));
                if (CollectionUtils.isEmpty(strategies)) {
                    log.error(LOG_START + "系统id为{}" + "没有新策略，退出此次控制任务", system.getId());
                } else {
                    beforeSend(system.getId(), system.getRoomId(), strategies, plcNodes);
                }
            }
        }

    }


    /**
     * 发送控制信息
     *
     * @param systemId
     * @param roomId
     * @param strategies
     * @param plcNodes
     */
    @Override
    public void beforeSend(Long systemId, Long roomId, List<AiStartStopStrategy> strategies, List<BaseVo> plcNodes) {
        //获取策略内容    策略执行时间与当前时间戳作判断，当时间相差过大时，认为冷机策略任务有故障或者无需调节，退出此次控制任务，并记录系统日志
        List<Long> ids = strategies.stream().map(AiStartStopStrategy::getId).distinct().collect(Collectors.toList());
        List<StrategyObjectMap> strategyObjectMaps = strategyObjectMapDao.queryStrategyObjectMap(ids);
        List<BaseVo> baseVos = strategyObjectMaps.stream().map(strategyObjectMap -> new BaseVo(strategyObjectMap.getObjectId(), strategyObjectMap.getObjectLabel()))
                .distinct().collect(Collectors.toList());
        //查询deviceid
        List<MeasuredbyVo> measuredbyVos = measureByDao.queryMeasureBy(baseVos);
        List<Long> deviceIds = measuredbyVos.stream().map(MeasuredbyVo::getMeasuredby).distinct().collect(Collectors.toList());
        //根据deviceid查询遥控点信息
        List<Integer> deviceIdList = deviceIds.stream().map(Long::intValue).collect(Collectors.toList());
        //根据deviceid查询遥控点信息
        ApiResult<List<RemoteControlPointDTO>> remoteControlPoints = coreConfigRestApi.getRemoteControlPoints(deviceIdList);
        List<RemoteControlPointDTO> data = remoteControlPoints.getData();
        //查询用户
        UserVo userVo = authUtils.queryUser(LoginDef.USER_ROOT);
        //获得明文密码
        String password = getPassword(userVo);
        //遥调一条条发，遥控开机关机分开发
        MultiRemoteControlPara controlParam = assembleSendData(strategyObjectMaps, strategies,
                StrategyTypeDef.start, measuredbyVos, data, password, userVo, systemId);
        ControlParamInfo controlParamInfo = new ControlParamInfo(controlParam, systemId, roomId, userVo, password, plcNodes, strategyObjectMaps, strategies, StrategyTypeDef.start);
        Boolean checkSendMessage = checkSendMessage(controlParamInfo);
        if (Boolean.FALSE.equals(checkSendMessage)) {
            return;
        }
        //发送关机遥控命令
        //遥调一条条发，遥控开机关机分开发
        MultiRemoteControlPara controlParam1 = assembleSendData(strategyObjectMaps, strategies,
                StrategyTypeDef.stop, measuredbyVos, data, password, userVo, systemId);
        ControlParamInfo controlParamInfo1 = new ControlParamInfo(controlParam1, systemId, roomId, userVo, password, plcNodes, strategyObjectMaps, strategies, StrategyTypeDef.stop);
        //有可能没有关机策略
        Boolean checkSendMessage1 = checkSendMessage(controlParamInfo1);
        if (Boolean.FALSE.equals(checkSendMessage1)) {
            return;
        }

        //遥调信息
        Boolean opti = sendRemoteAdjustmentMessage(strategyObjectMaps, strategies,
                measuredbyVos, data, password, userVo, systemId);
        if (Boolean.FALSE.equals(opti)) {
            failedToExecute(systemId, userVo, password, plcNodes);
        }
    }

    private Boolean checkSendMessage(ControlParamInfo controlParamInfo) {
        if (CollectionUtils.isEmpty(controlParamInfo.getControlParam().getIds())) {
            log.error(LOG_START + "id为{}的制冷系统，批量" + assembleString(controlParamInfo.getStrategyType()) + "机遥控没有符合的参数号", controlParamInfo.getSystemId());
        } else {
            log.error(LOG_START + "id为{}的制冷系统，批量" + assembleString(controlParamInfo.getStrategyType()) + "机遥控发送的命令是{}", controlParamInfo.getSystemId(), JsonTransferUtils.toJSONString(removePassword(controlParamInfo.getControlParam())));
            commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, LOG_START + "id为" + controlParamInfo.getSystemId() + "的制冷系统，批量" + assembleString(controlParamInfo.getStrategyType()) + "机遥控发送的命令是" +
                    JsonTransferUtils.toJSONString(removePassword(controlParamInfo.getControlParam())), null, controlParamInfo.getUserVo().getId());
            //发送开机遥控命令
            Boolean start = sendMessageStartOrStop(controlParamInfo);
            if (Boolean.FALSE.equals(start)) {
                failedToExecute(controlParamInfo.getSystemId(), controlParamInfo.getUserVo(), controlParamInfo.getPassword(), controlParamInfo.getPlcNodes());
                return false;
            }
        }
        return true;
    }

    /**
     * 获得最新的策略
     *
     * @param strategies
     * @param type
     * @return
     */
    private Set<Long> getNewStrategy(List<AiStartStopStrategy> strategies, Integer type) {

        List<AiStartStopStrategy> strategyList = strategies.stream().filter(aiStartStopStrategy -> Objects.equals(type, aiStartStopStrategy.getStrategyType()))
                .sorted(Comparator.comparing(AiStartStopStrategy::getOperationTime).reversed()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(strategyList)) {
            return Collections.emptySet();
        }
        Long operationTime = strategyList.get(0).getOperationTime();
        return strategyList.stream().filter(aiStartStopStrategy -> Objects.equals(aiStartStopStrategy.getOperationTime(), operationTime))
                .map(AiStartStopStrategy::getId).collect(Collectors.toSet());
    }

    private Boolean sendRemoteAdjustmentMessage(List<StrategyObjectMap> strategyObjectMaps, List<AiStartStopStrategy> strategies,
                                                List<MeasuredbyVo> measuredbyVos, List<RemoteControlPointDTO> data, String password, UserVo userVo, Long systemId) {
        //遥调是一个个
        Set<Long> ids = getNewStrategy(strategies, StrategyTypeDef.optimization);
        if (CollectionUtils.isEmpty(ids)) {
            log.error(LOG_START + SYSTEM_ID + "{}遥调的策略信息是空", systemId);
            return true;
        }
        List<StrategyObjectMap> maps = strategyObjectMaps.stream().filter(strategyObjectMap -> ids.contains(strategyObjectMap.getStrategyId())).collect(Collectors.toList());
        Set<BaseVo> baseVos = maps.stream()
                .map(strategyObjectMap -> new BaseVo(strategyObjectMap.getObjectId(), strategyObjectMap.getObjectLabel()))
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(baseVos)) {
            log.error(LOG_START + SYSTEM_ID + "{}遥调的节点信息是空", systemId);
            return true;
        }
        List<BaseVo> baseVos1 = nodeDao.queryNodeName(baseVos);
        for (BaseVo baseVo : baseVos1) {
            RemoteControlIdParam param = assembleParam(baseVo, measuredbyVos, data, password, userVo, maps.get(0).getTemp());
            if (Objects.isNull(param)) {
                continue;
            }
            log.error(LOG_START + "id为{}名称为{}的冷机，单一遥调发送的命令是{}", baseVo.getId(), baseVo.getName(), JsonTransferUtils.toJSONString(removePassword(param)));
            commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, LOG_START + "id为" + baseVo.getId() + "名称为" + baseVo.getName() + "的冷机，单一遥调发送的命令是" +
                    JsonTransferUtils.toJSONString(removePassword(param)), null, userVo.getId());
            Boolean aBoolean = sendRemoteAdjustmentMessageSingle(param, baseVo, userVo.getId(), password);
            log.error(LOG_START + "id为{}名称为{}的冷机，单一遥调发送的命令是的结果是{}", baseVo.getId(), baseVo.getName(), checkRemoteResult(aBoolean));
            if (Boolean.FALSE.equals(aBoolean)) {
                return false;
            }
        }
        return true;
    }

    private RemoteControlIdParam removePassword(RemoteControlIdParam controlParam) {
        RemoteControlIdParam controlParam1 = new RemoteControlIdParam();
        BeanUtils.copyProperties(controlParam, controlParam1);
        UserPasswordParam param = new UserPasswordParam();
        BeanUtils.copyProperties(controlParam.getUserInfo(), param);
        param.setPassword(null);
        controlParam1.setUserInfo(param);
        return controlParam1;
    }

    private String checkRemoteResult(Boolean aBoolean) {
        if (Boolean.TRUE.equals(aBoolean)) {
            return SUCCESS;
        }
        return WRONG;
    }

    private Boolean sendRemoteAdjustmentMessageSingle(RemoteControlIdParam param, BaseVo baseVo, Long userId, String password) {
        Integer retryCount = aiScheduleConfig.getRetryCount();
        Integer wait = aiScheduleConfig.getWait();
        for (int i = 0; i < retryCount; i++) {
            log.error(LOG_START + "id为{}名称为{}的冷机，开始执行第{}次遥调的命令", baseVo.getId(), baseVo.getName(), i + 1);
            Boolean aBoolean;
            reCreateControlParamWithTime(param, password);
            if (i < retryCount - 1) {
                aBoolean = ifNormalExecute(param, baseVo, userId, false);
            } else {
                aBoolean = ifNormalExecute(param, baseVo, userId, true);
            }
            if (Boolean.TRUE.equals(aBoolean)) {
                log.error(LOG_START + "id为{}名称为{}的冷机，第{}次遥调的命令成功执行", baseVo.getId(), baseVo.getName(), i + 1);
                commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE,
                        LOG_START + "id为" + baseVo.getId() + "名称为" + baseVo.getName() + "的冷机，第" + (i + 1) + "次遥调的命令成功执行", null, userId);
                return true;
            }
            try {
                Thread.sleep(wait * TimeUtil.SECOND);
            } catch (Exception e) {
                log.error(LOG_START + CATCH_ERROR);
            }

        }
        return false;
    }

    private void reCreateControlParamWithTime(RemoteControlIdParam param, String passWord) {
        long l = System.currentTimeMillis();
        String s = Md5Utils.md5EncryptionForPassword(passWord, l);
        UserPasswordParam userInfo = param.getUserInfo();
        userInfo.setTimestamps(l);
        userInfo.setPassword(s);

    }

    private Boolean ifNormalExecute(RemoteControlIdParam param, BaseVo baseVo, Long userId, Boolean last) {
        ApiResult<Boolean> stringResultWithTotal = new ApiResult<>();
        try {

            stringResultWithTotal = dataServiceRestApi.remoteControlByParamId(param, true, null);
        } catch (Exception e) {
            log.error(LOG_START + "遥调命令超时", e);
        }

        Boolean data = stringResultWithTotal.getData();
        if (Objects.equals(data, Boolean.TRUE)) {
            return true;
        }
        //歇5s  遥调很快，核心平台下发到反馈结果不超过5s
        try {
            Thread.sleep(5 * TimeUtil.SECOND);
        } catch (Exception e) {
            log.error(LOG_START + CATCH_ERROR);
        }
        Map<Integer, List<RealTimeValue>> integerListMap = quantityManageService.queryRealTimeBath(createQuantityDataBatchSearchVo(Collections.singletonList(baseVo), TimeUtil.getFirstTimeOfHour(LocalDateTime.now()), LocalDateTime.now(),
                AggregationCycle.FIVE_MINUTES, Collections.singletonList(QuantityDef.getFreezingWaterForSupplyStandardQuantitySetting())));
        List<RealTimeValue> realTimeValues = integerListMap.get(QuantityDef.getFreezingWaterForSupplyStandardQuantitySetting().getId());
        if (CollectionUtils.isEmpty(realTimeValues)) {
            if (Boolean.TRUE.equals(last)) {
                log.error(LOG_START + "id为{}名称为{}的冷机，单一遥调校验结果查询出水温度设定值的实时值为空", baseVo.getId(), baseVo.getName());
            }
            return false;
        }
        if (Boolean.TRUE.equals(last)) {
            log.error(LOG_START + "id为{}名称为{}的冷机，单一遥调校验结果查询出水温度设定值的实时值为{}", baseVo.getId(), baseVo.getName(), realTimeValues.get(0).getValue());
            commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, LOG_START + "id为" + baseVo.getId() + "名称为" + baseVo.getName() + "的冷机，单一遥调校验结果查询出水温度设定值的实时值为" +
                    realTimeValues.get(0).getValue(), null, userId);
        }

        RealTimeValue timeValue = realTimeValues.stream().filter(realTimeValue -> Objects.equals(baseVo.getModelLabel(), realTimeValue.getMonitoredLabel())
                && Objects.equals(baseVo.getId(), realTimeValue.getMonitoredId()) && Objects.equals(realTimeValue.getValue(), param.getParamIdValues().get(0).getParamValue())).findAny().orElse(null);
        return !Objects.isNull(timeValue);
    }

    private RemoteControlIdParam assembleParam(BaseVo baseVo, List<MeasuredbyVo> measuredbyVos, List<RemoteControlPointDTO> data, String password,
                                             UserVo userVo, Double value) {
        Set<Long> measures = measuredbyVos.stream().filter(measuredbyVo -> Objects.equals(baseVo.getId(), measuredbyVo.getMonitoredid())
                && Objects.equals(measuredbyVo.getMonitoredlabel(), baseVo.getModelLabel())).map(MeasuredbyVo::getMeasuredby).collect(Collectors.toSet());
        List<Integer> deviceIdList = measures.stream().map(Long::intValue).collect(Collectors.toList());
        List<MeasureNodeWithDeviceIdDTO> pointsInfos = pecCoreService.queryMeasureNodeWithDeviceId(deviceIdList);
//        List<MeasureNodeWithDeviceIdDTO> pointsInfos = deviceListMeasureInfo.getData();
        if (CollectionUtils.isEmpty(pointsInfos)) {
            log.error(LOG_START + "该制冷设备关联的表计没有配测点");
            return null;
        }
        Set<Integer> integers = pointsInfos.stream().filter(measurePointsInfo -> Objects.equals(measurePointsInfo.getDataId(), OUT_WATER_DATA_ID))
                .map(MeasureNodeWithDeviceIdDTO::getParaId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(integers)) {
            log.error(LOG_START + "该制冷设备没有配置冷机出水温度需要的模拟量");
            return null;
        }
        RemoteControlPointDTO controlPointInfos = data.stream().filter(remoteControlPointInfo -> measures.contains((remoteControlPointInfo.getDeviceId()))
                && integers.contains(remoteControlPointInfo.getPara())).findAny().orElse(null);
        if (Objects.isNull(controlPointInfos)) {
            log.error(LOG_START + "该制冷设备没有配置冷机出水温度需要的遥调点");
            return null;
        }

        return createRemoteControlParam(controlPointInfos, password,
                userVo, value, System.currentTimeMillis(), TYPE);
    }

    @Override
    public RemoteControlIdParam createRemoteControlParam(RemoteControlPointDTO controlPointInfos, String password,
                                                       UserVo userVo, Double value, long timestamps, Integer type) {
        RemoteControlIdParam param = new RemoteControlIdParam();
        ControlIdParam paramData = new ControlIdParam();
        paramData.setParamId(controlPointInfos.getPara());
        paramData.setParamValue(value);
        param.setParamIdValues(Collections.singletonList(paramData));
        param.setChannelId(controlPointInfos.getChannelId());
        param.setDeviceId(controlPointInfos.getDeviceId());
        param.setType(type);
        param.setStationId(controlPointInfos.getStationId());
        UserPasswordParam controlParam=new UserPasswordParam();
        controlParam.setUserName(userVo.getName());
        controlParam.setClient(WEB);
        controlParam.setTimestamps(timestamps);
        controlParam.setPassword(Md5Utils.md5EncryptionForPassword(password, timestamps));
        param.setUserInfo(controlParam);
        return param;
    }

    private void failedToExecute(Long systemId, UserVo userVo, String password, List<BaseVo> plcNodes) {
        ControlMode controlMode = controlModeDao.queryControlModeBySystemId(systemId);
        List<RemoteControlPointDTO> controlInfo = getControlInfo(plcNodes, PLC_AUTO_DATA_ID, START);
        if (CollectionUtils.isEmpty(controlInfo)) {
            log.error("id为{}的制冷系统,没有PLC自动的遥控点配置", systemId);
            return;
        }
        List<Integer> nodeIds = controlInfo.stream().map(RemoteControlPointDTO::getNodeId).distinct().collect(Collectors.toList());
        MultiRemoteControlPara controlParam = createControlParam(nodeIds, password, userVo, System.currentTimeMillis());
        dataServiceRestApi.batchRemoteControlByPoints(controlParam,true,null);
        controlMode.setMode(ModelDef.PLC_AUTO);
        modelServiceUtils.writeData(Collections.singletonList(controlMode));
        log.error(LOG_START + "系统id为{}的控制模式调整为PLC自动！", systemId);
        commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, LOG_MIDDLE + systemId + "制冷系统的控制模式为plc自动", null, userVo.getId());
        log.error(LOG_START + "系统id为{}遥控PLC运行模式,发送的遥控信息为{}", systemId, JsonTransferUtils.toJSONString(removePassword(controlParam)));
        commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, SYSTEM_ID + systemId + "遥控PLC运行模式,发送的遥控信息为" + JsonTransferUtils.toJSONString(removePassword(controlParam)),
                null, userVo.getId());

    }

    @Override
    public List<RemoteControlPointDTO> getControlInfo(List<BaseVo> plcNodes, Long dataId, Integer type) {
        List<MeasuredbyVo> measuredbyVos = measureByDao.queryMeasureBy(plcNodes);
        if (CollectionUtils.isEmpty(measuredbyVos)) {
            log.error(LOG_START + "节点信息为{}没有关联表计！", JsonTransferUtils.toJSONString(plcNodes));
            return Collections.emptyList();
        }
        List<Long> deviceIds = measuredbyVos.stream().map(MeasuredbyVo::getMeasuredby).distinct().collect(Collectors.toList());
        List<Integer> deviceIdList = deviceIds.stream().map(Long::intValue).collect(Collectors.toList());
        //根据deviceid查询遥控点信息
        ApiResult<List<RemoteControlPointDTO>> remoteControlPoints = coreConfigRestApi.getRemoteControlPoints(deviceIdList);
        List<RemoteControlPointDTO> data = remoteControlPoints.getData();
        if (CollectionUtils.isEmpty(data)) {
            log.error(LOG_START + "节点信息为{}没有遥控点信息！", JsonTransferUtils.toJSONString(plcNodes));
            return Collections.emptyList();
        }
        List<MeasureNodeWithDeviceIdDTO> pointsInfos = pecCoreService.queryMeasureNodeWithDeviceId(deviceIdList);
//        List<MeasureNodeWithDeviceIdDTO> pointsInfos = deviceListMeasureInfo.getData();
        if (CollectionUtils.isEmpty(pointsInfos)) {
            log.error(LOG_START + "节点信息为{}关联的表计没有配置测点！", JsonTransferUtils.toJSONString(plcNodes));
            return Collections.emptyList();
        }
        Set<Integer> integers = pointsInfos.stream().filter(measurePointsInfo -> Objects.equals(measurePointsInfo.getDataId(), dataId.intValue()))
                .map(MeasureNodeWithDeviceIdDTO::getParaId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(integers)) {
            log.error(LOG_START + "没有符合该测点{}的物理量配置", dataId);
            return Collections.emptyList();
        }
        if (Objects.isNull(type)) {
            return data.stream().filter(remoteControlPointInfo -> integers.contains(remoteControlPointInfo.getPara()))
                    .collect(Collectors.toList());
        }
        return data.stream().filter(remoteControlPointInfo -> integers.contains(remoteControlPointInfo.getPara()) &&
                Objects.equals(remoteControlPointInfo.getControlType(), type))
                .collect(Collectors.toList());
    }

    private MultiRemoteControlPara removePassword(MultiRemoteControlPara controlParam) {
        MultiRemoteControlPara controlParam1 = new MultiRemoteControlPara();
        BeanUtils.copyProperties(controlParam, controlParam1);
        controlParam1.setPassword(null);
        return controlParam1;
    }

    /**
     * 计算延迟时间
     *
     * @param roomId
     * @param type
     * @return
     */
    private Long calculateWaitTime(Long roomId, Integer type, List<BaseVo> plcNodes) {
        List<DeviceChainWithSubLayer> deviceChainWithSubLayers = deviceChainDao.queryDeviceChainDetail(Collections.singletonList(roomId));
        List<BaseVo> nodes = new ArrayList<>();
        for (DeviceChainWithSubLayer deviceChainWithSubLayer : deviceChainWithSubLayers) {
            if (CollectionUtils.isNotEmpty(deviceChainWithSubLayer.getDeviceChainDetails())) {
                List<DeviceChainDetail> deviceChainDetails = deviceChainWithSubLayer.getDeviceChainDetails();
                nodes = deviceChainDetails.stream().map(deviceChainDetail -> new BaseVo(deviceChainDetail.getObjectId(), deviceChainDetail.getObjectLabel()))
                        .collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isEmpty(nodes)) {
            return addDelayAndOperation();
        }
        LocalDateTime now = LocalDateTime.now();
        Map<Integer, List<RealTimeValue>> integerListMap = quantityManageService.queryRealTimeBath(createQuantityDataBatchSearchVo(plcNodes, TimeUtil.getFirstTimeOfHour(now), LocalDateTime.now(), AggregationCycle.FIVE_MINUTES,
                Arrays.asList(QuantityDef.getMachineDelayStart(), QuantityDef.getFreezingPumpDelayStart(), QuantityDef.getCoolingPumpDelayStart()
                        , QuantityDef.getMachineDelayStop(), QuantityDef.getCoolingPumpDelayStop(), QuantityDef.getFreezingPumpDelayStop())));
        Long machine;
        Long coolingPump;
        Long freezingPump;
        if (Objects.equals(type, StrategyTypeDef.start)) {
            machine = getMachineDelay(integerListMap.get(QuantityDef.getMachineDelayStart().getId()));
            coolingPump = getMachineDelay(integerListMap.get(QuantityDef.getCoolingPumpDelayStart().getId()));
            freezingPump = getMachineDelay(integerListMap.get(QuantityDef.getFreezingPumpDelayStart().getId()));
        } else {
            machine = getMachineDelay(integerListMap.get(QuantityDef.getMachineDelayStop().getId()));
            coolingPump = getMachineDelay(integerListMap.get(QuantityDef.getCoolingPumpDelayStop().getId()));
            freezingPump = getMachineDelay(integerListMap.get(QuantityDef.getFreezingPumpDelayStop().getId()));
        }
        if (Objects.equals(machine, 0L) || Objects.equals(coolingPump, 0L) || Objects.equals(freezingPump, 0L)) {
            return addDelayAndOperation();
        }
        return calculateWithNodes(nodes, machine, coolingPump, freezingPump);
    }

    private Long addDelayAndOperation() {
        long delay = 0;
        long operate = 0;
        if (Objects.nonNull(aiScheduleConfig.getDelayed())) {
            delay = aiScheduleConfig.getDelayed().longValue();
        } else {
            log.error(LOG_START + "没有获取到延时信息以及延时配置");
        }
        if (Objects.nonNull(aiScheduleConfig.getOperation())) {
            operate = aiScheduleConfig.getOperation().longValue();
        } else {
            log.error(LOG_START + "没有获取操作时间配置");
        }
        return delay + operate;
    }

    /**
     * 计算整条连锁的延时
     *
     * @param nodes
     * @param machine
     * @param coolingPump
     * @param freezingPump
     * @return
     */
    private Long calculateWithNodes(List<BaseVo> nodes, Long machine, Long coolingPump, Long freezingPump) {
        List<PumpVo> pumpVos = nodeDao.queryNodes(nodes, PumpVo.class);
        long result = 0L;
        for (PumpVo baseVo : pumpVos) {
            if (Objects.equals(baseVo.getModelLabel(), NodeLabelDef.COLD_WATER_MAINENGINE)) {
                result += machine;
            } else if (Objects.equals(baseVo.getModelLabel(), NodeLabelDef.PUMP) &&
                    Objects.equals(baseVo.getFunctionType(), PumpFunctionType.COOLING_PUMP)) {
                result += coolingPump;
            } else if (Objects.equals(baseVo.getModelLabel(), NodeLabelDef.PUMP) &&
                    Objects.equals(baseVo.getFunctionType(), PumpFunctionType.REFRIGERATING_PUMP)) {
                result += freezingPump;
            }
        }
        long operate = 0;
        if (Objects.nonNull(aiScheduleConfig.getOperation())) {
            operate = aiScheduleConfig.getOperation().longValue();
        } else {
            log.error(LOG_START + "没有获取操作时间配置");
        }
        return result + operate;
    }

    /**
     * 获得一条实时数据
     *
     * @param realTimeValues
     * @return
     */
    private Long getMachineDelay(List<RealTimeValue> realTimeValues) {
        if (CollectionUtils.isEmpty(realTimeValues)) {
            return 0L;
        }
        Double value = realTimeValues.get(0).getValue();
        if (Objects.isNull(value)) {
            return 0L;
        }
        return value.longValue();
    }

    /**
     * 拼接批量遥控信息
     *
     * @param strategyObjectMaps
     * @param strategies
     * @param type
     * @param measuredbyVos
     * @param data
     * @param password
     * @param userVo
     * @return
     */
    @Override
    public MultiRemoteControlPara  assembleSendData(List<StrategyObjectMap> strategyObjectMaps, List<AiStartStopStrategy> strategies,
                                         Integer type, List<MeasuredbyVo> measuredbyVos, List<RemoteControlPointDTO> data, String password, UserVo userVo,
                                         Long systemId) {
        List<Integer> ids = filterDeviceIds(strategyObjectMaps, strategies, type, measuredbyVos,
                data, systemId);
        return createControlParam(ids, password, userVo, System.currentTimeMillis());
    }

    @Override
    public void checkAiStrategyControl(Long systemId, Long queryTime) {
        RefrigeratingSystem system = refrigeratingSystemDao.selectById(systemId);

        //查询plc节点
        List<BaseVo> plcNodes = queryNodesByMonitor(system.getRoomId());
        if (CollectionUtils.isEmpty(plcNodes)) {
            log.error(LOG_START_HEART + NO_PLC);
            return;
        }
        //首先判断制冷系统是否处于“AI程序控制”状态、PLC是否处于AI模式
        LocalDateTime time = TimeUtil.timestamp2LocalDateTime(queryTime);
        Boolean needAi = ifNeedAi(system.getId(), plcNodes);
        if (Boolean.TRUE.equals(needAi)) {
            //最新的策略
            List<AiStartStopStrategy> strategies = aiStartStopStrategyDao.queryAiStartStopStrategy
                    (system.getId(), Collections.emptyList(), time, TimeUtil.addDateTimeByCycle(time, AggregationCycle.ONE_HOUR, 1));
            if (CollectionUtils.isEmpty(strategies)) {
                log.error(LOG_START + "没有新策略，退出此次控制任务");
            } else {
                beforeSend(system.getId(), system.getRoomId(), strategies, plcNodes);
            }


        }
    }


    private MultiRemoteControlPara createControlParam(List<Integer> ids, String password, UserVo userVo, Long time) {
        MultiRemoteControlPara controlParam=new MultiRemoteControlPara();
        controlParam.setClient(WEB);
        controlParam.setIds(ids);
        controlParam.setPassword(Md5Utils.md5EncryptionForPassword(password, time));
        controlParam.setTimestamps(time);
        controlParam.setUserName(userVo.getName());
        return controlParam;
    }

    /**
     * 获得控制的nodeid
     *
     * @param strategyObjectMaps
     * @param strategies
     * @param type
     * @param measuredbyVos
     * @param data
     * @return
     */
    private List<Integer> filterDeviceIds(List<StrategyObjectMap> strategyObjectMaps, List<AiStartStopStrategy> strategies, Integer
            type, List<MeasuredbyVo> measuredbyVos, List<RemoteControlPointDTO> data, Long systemId) {
        Set<Long> ids = getNewStrategy(strategies, type);
        if (CollectionUtils.isEmpty(ids)) {
            log.error(LOG_START + "id为{}的系统没有新的" + assembleString(type) + "机策略", systemId);
            return Collections.emptyList();
        }
        Set<BaseVo> baseVos = strategyObjectMaps.stream().filter(strategyObjectMap -> ids.contains(strategyObjectMap.getStrategyId()))
                .map(strategyObjectMap -> new BaseVo(strategyObjectMap.getObjectId(), strategyObjectMap.getObjectLabel()))
                .collect(Collectors.toSet());
        Set<Long> measures = measuredbyVos.stream().filter(measuredbyVo -> baseVos.contains(new BaseVo(measuredbyVo.getMonitoredid(), measuredbyVo.getMonitoredlabel())))
                .map(MeasuredbyVo::getMeasuredby).collect(Collectors.toSet());
        List<Integer> deviceIdList = measures.stream().map(Long::intValue).distinct().collect(Collectors.toList());
        List<MeasureNodeWithDeviceIdDTO> pointsInfos = pecCoreService.queryMeasureNodeWithDeviceId(deviceIdList);
//        List<MeasureNodeWithDeviceIdDTO> pointsInfos = deviceListMeasureInfo.getData();
//        Result<List<MeasurePointsInfo>> measurePoints = deviceDataService.queryMeasurePoints(null, new ArrayList<>(measures));
//        List<MeasurePointsInfo> pointsInfos = measurePoints.getData();
        if (CollectionUtils.isEmpty(pointsInfos)) {
            log.error(LOG_START + "节点信息为{}关联的表计没有配置测点！", JsonTransferUtils.toJSONString(baseVos));
            return Collections.emptyList();
        }
        Set<Integer> integers = pointsInfos.stream().filter(measurePointsInfo -> Objects.equals(measurePointsInfo.getDataId(), START_OR_STOP_DATA_ID))
                .map(MeasureNodeWithDeviceIdDTO::getParaId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(integers)) {
            log.error(LOG_START + SYSTEM_ID + "{}下制冷设备没有配置冷机启停需要的开关量", systemId);
            return Collections.emptyList();
        }
        List<RemoteControlPointDTO> controlPointInfos = data.stream().filter(remoteControlPointInfo -> measures.contains(Long.valueOf(remoteControlPointInfo.getDeviceId().toString()))).collect(Collectors.toList());
        //controlType 为1指打开开关，关闭设备
        if (Objects.equals(type, StrategyTypeDef.start)) {
            return controlPointInfos.stream().filter(remoteControlPointInfo -> Objects.equals(remoteControlPointInfo.getControlType(), START)
                    && integers.contains(remoteControlPointInfo.getPara()))
                    .map(RemoteControlPointDTO::getNodeId).collect(Collectors.toList());
        } else if (Objects.equals(type, StrategyTypeDef.stop)) {
            return controlPointInfos.stream().filter(remoteControlPointInfo -> Objects.equals(remoteControlPointInfo.getControlType(), STOP)
                    && integers.contains(remoteControlPointInfo.getPara()))
                    .map(RemoteControlPointDTO::getNodeId).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 发送信息
     *
     * @param controlParamInfo
     * @return
     */
    private Boolean sendMessageStartOrStop(ControlParamInfo controlParamInfo) {

        Long time = calculateWaitTime(controlParamInfo.getRoomId(), controlParamInfo.getStrategyType(), controlParamInfo.getPlcNodes());
        Set<Long> ids = getNewStrategy(controlParamInfo.getStrategies(), controlParamInfo.getStrategyType());
        List<BaseVo> machines = controlParamInfo.getStrategyObjectMaps().stream().filter(strategyObjectMap -> ids.contains(strategyObjectMap.getStrategyId())
                && Objects.equals(strategyObjectMap.getObjectLabel(), NodeLabelDef.COLD_WATER_MAINENGINE))
                .map(strategyObjectMap -> new BaseVo(strategyObjectMap.getObjectId(), strategyObjectMap.getObjectLabel()))
                .distinct().collect(Collectors.toList());
        List<BaseVo> baseVos = nodeDao.queryNodeName(machines);
        Integer retryCount = aiScheduleConfig.getRetryCount();
        Integer wait = aiScheduleConfig.getWait();
        for (int i = 0; i < retryCount; i++) {
            log.error(LOG_START + "id为{}的制冷系统，开始执行第{}次" + assembleString(controlParamInfo.getStrategyType()) + "机遥控命令发送", controlParamInfo.getSystemId(), i + 1);
            reCreateControlParamWithTime(controlParamInfo.getControlParam(), controlParamInfo.getPassword());
            Boolean aBoolean;
            if (i < retryCount - 1) {
                aBoolean = ifNormalExecution(controlParamInfo.getControlParam(), time, controlParamInfo.getStrategyType(), baseVos, controlParamInfo.getUserVo().getId(), false, controlParamInfo.getSystemId());
            } else {
                aBoolean = ifNormalExecution(controlParamInfo.getControlParam(), time, controlParamInfo.getStrategyType(), baseVos, controlParamInfo.getUserVo().getId(), true, controlParamInfo.getSystemId());
            }

            if (Boolean.TRUE.equals(aBoolean)) {
                log.error(LOG_START + "id为{}的制冷系统，第{}次批量" + assembleString(controlParamInfo.getStrategyType()) + "机遥控发送的命令成功执行", controlParamInfo.getSystemId(), i + 1);
                commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, LOG_START + "id为" + controlParamInfo.getSystemId() + "的制冷系统，第" + (i + 1) + "次批量" + assembleString(controlParamInfo.getStrategyType()) + "机遥控发送的命令成功执行", null, controlParamInfo.getUserVo().getId());
                return true;
            }
            try {
                Thread.sleep(wait * TimeUtil.SECOND);
            } catch (Exception e) {
                log.error(LOG_START + CATCH_ERROR);
            }

        }
        return false;
    }

    private void reCreateControlParamWithTime(MultiRemoteControlPara  param, String passWord) {
        long l = System.currentTimeMillis();
        String s = Md5Utils.md5EncryptionForPassword(passWord, l);
        param.setTimestamps(l);
        param.setPassword(s);
    }

    private String assembleString(Integer type) {
        if (Objects.equals(type, StrategyTypeDef.start)) {
            return OPEN;
        } else {
            return CLOSE;
        }
    }

    /**
     * 消息发送和校验是否正常执行
     *
     * @param controlParam
     * @param time
     * @param type
     * @param baseVos
     * @param userId
     * @param last
     * @return
     */
    private Boolean ifNormalExecution(MultiRemoteControlPara controlParam, Long time, Integer type, List<BaseVo> baseVos, Long userId
            , Boolean last, Long systemId) {
        dataServiceRestApi.batchRemoteControlByPoints(controlParam,  true, null);

        log.error(LOG_START + SYSTEM_ID + "{}制冷系统执行遥控后开始等待设备响应，预计等待{}秒", systemId, time);
        try {
            Thread.sleep(time * TimeUtil.SECOND);
        } catch (Exception e) {
            log.error(LOG_START + CATCH_ERROR);
        }
        log.error(LOG_START + SYSTEM_ID + "{}制冷系统执行遥控后结束等待设备响应", systemId);
        //过滤出策略中的冷机
        Map<Integer, List<RealTimeValue>> integerListMap = quantityManageService.queryRealTimeBath(createQuantityDataBatchSearchVo(baseVos, TimeUtil.getFirstTimeOfHour(LocalDateTime.now()), LocalDateTime.now(),
                AggregationCycle.FIVE_MINUTES, Collections.singletonList(QuantityDef.getDeviceStatus())));
        List<RealTimeValue> realTimeValues = integerListMap.get(QuantityDef.getDeviceStatus().getId());
        if (CollectionUtils.isEmpty(realTimeValues)) {
            if (Boolean.TRUE.equals(last)) {
                log.error(LOG_START + SYSTEM_ID + "{}制冷系统查询到的冷机状态数据为空", systemId);
            }
            return false;
        }
        for (BaseVo baseVo : baseVos) {
            List<RealTimeValue> timeValues = realTimeValues.stream().filter(realTimeValue -> Objects.equals(baseVo.getModelLabel(), realTimeValue.getMonitoredLabel())
                    && Objects.equals(baseVo.getId(), realTimeValue.getMonitoredId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(timeValues)) {
                if (Boolean.TRUE.equals(last)) {
                    log.error(LOG_START + "id为{}的名称为{}冷机查询到的冷机状态数据为空", baseVo.getId(), baseVo.getName());
                }
                return false;
            }
            Boolean result = judgeMachineRemoteResult(type, timeValues, userId, last, baseVo);
            if (Boolean.FALSE.equals(result)) {
                return false;
            }
        }
        return true;
    }

    private Boolean judgeMachineRemoteResult(Integer type, List<RealTimeValue> timeValues, Long userId, Boolean
            last, BaseVo baseVo) {
        RealTimeValue timeValue;
        if (Objects.equals(type, StrategyTypeDef.start)) {
            timeValue = timeValues.stream().filter(realTimeValue -> Objects.equals(realTimeValue.getValue(), TRUE_DATA)).findAny().orElse(null);
        } else {
            timeValue = timeValues.stream().filter(realTimeValue -> Objects.equals(realTimeValue.getValue(), FALSE_DATA)).findAny().orElse(null);
        }
        if (Objects.isNull(timeValue)) {
            if (Boolean.TRUE.equals(last)) {
                log.error(LOG_START + "id为{}的名称为{}的冷机查询到的冷机状态数据和遥控命令不符合！冷机状态为{}", baseVo.getId(), baseVo.getName(), timeValues.get(0).getValue());
                commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, LOG_START + "id为" + baseVo.getId() + "名称为" + baseVo.getName() + "的冷机查询到的冷机状态数据和遥控命令不符合！冷机状态为" + timeValues.get(0).getValue(), null, userId);
            }
            return false;
        }
        return true;
    }

    /**
     * 获得明文密码
     *
     * @param userVo
     * @return
     */
    private String getPassword(UserVo userVo) {
        long l = System.currentTimeMillis();
        String password = authUtils.getPassword(userVo.getId());
        QueryUserPassword queryUserPassword = new QueryUserPassword();
        String s = Md5Utils.md5EncryptionForPassword(password, l);
        queryUserPassword.setPassword(password);
        queryUserPassword.setMd5(s);
        queryUserPassword.setTimestamps(l);
        return authUtils.getDecryptPassword(queryUserPassword);
    }

    /**
     * 是否需要ai--需要：controlmodel是ai，plc的ai反馈是1
     *
     * @param systemId
     * @return
     */
    private Boolean ifNeedAi(Long systemId, List<BaseVo> plcNodes) {
        ControlMode controlMode = controlModeDao.queryControlModeBySystemId(systemId);
        if (Objects.isNull(controlMode)) {
            log.error(LOG_START + "系统id为{}没有查到控制模式的内容！", systemId);
            return false;
        }
        if (Objects.equals(controlMode.getMode(), ModelDef.AI)) {
            LocalDateTime now = LocalDateTime.now();

            Map<Integer, List<RealTimeValue>> integerListMap = quantityManageService.queryRealTimeBath(
                    createQuantityDataBatchSearchVo(plcNodes, TimeUtil.getFirstTimeOfHour(now), LocalDateTime.now(), AggregationCycle.FIVE_MINUTES,
                            Arrays.asList(QuantityDef.getPlcAi(), QuantityDef.getPlcAuto(), QuantityDef.getPlcManual())));
            //如果plc不是ai，controlmode修改赋值
            //是ai 发送接着处理
            List<RealTimeValue> realTimeValues = integerListMap.get(QuantityDef.getPlcAi().getId());
            if (CollectionUtils.isNotEmpty(realTimeValues)) {
                Double value = realTimeValues.get(0).getValue();
                if (Objects.equals(value, TRUE_DATA)) {
                    return true;
                }
            }
            List<RealTimeValue> realTimeValue1 = integerListMap.get(QuantityDef.getPlcAuto().getId());
            if (CollectionUtils.isNotEmpty(realTimeValue1) && Objects.equals(realTimeValue1.get(0).getValue(), TRUE_DATA)) {
                controlMode.setMode(ModelDef.PLC_AUTO);
                modelServiceUtils.writeData(Collections.singletonList(controlMode));
                log.error(LOG_START + "系统id为{}的控制模式调整为PLC自动！", systemId);
                commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, LOG_MIDDLE + systemId + "制冷系统的控制模式为plc自动", null, LoginDef.USER_ROOT);
                return false;
            }
            List<RealTimeValue> realTimeValue2 = integerListMap.get(QuantityDef.getPlcManual().getId());
            if (CollectionUtils.isNotEmpty(realTimeValue2) && Objects.equals(realTimeValue2.get(0).getValue(), TRUE_DATA)) {
                controlMode.setMode(ModelDef.PLC_MANUAL);
                modelServiceUtils.writeData(Collections.singletonList(controlMode));
                log.error(LOG_START + "系统id为{}的控制模式调整为PLC手动！", systemId);
                commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, LOG_MIDDLE + systemId + "制冷系统的控制模式为plc手动", null, LoginDef.USER_ROOT);
                return false;
            }
        }
        log.error(LOG_START + "id为{}的系统控制策略不是ai！", systemId);
        return false;
    }

    @Override
    public QuantityDataBatchSearchVo createQuantityDataBatchSearchVo(List<BaseVo> deviceNodes, LocalDateTime st,
                                                                     LocalDateTime et, Integer cycle, List<QuantitySearchVo> quantitySearchVo) {
        QuantityDataBatchSearchVo aggregationDataBatch = new QuantityDataBatchSearchVo();
        aggregationDataBatch.setDataTypeId(EnumDataTypeId.REALTIME.getId());
        aggregationDataBatch.setStartTime(TimeUtil.localDateTime2timestamp(st));
        aggregationDataBatch.setEndTime(TimeUtil.localDateTime2timestamp(et));
        aggregationDataBatch.setQuantitySettings((quantitySearchVo));
        aggregationDataBatch.setNodes(deviceNodes);
        aggregationDataBatch.setAggregationCycle(cycle);
        return aggregationDataBatch;
    }
}
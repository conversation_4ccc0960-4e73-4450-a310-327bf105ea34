package com.cet.eem.achievementrateservice.controller;

import com.cet.eem.bll.achievementrate.model.*;
import com.cet.eem.bll.achievementrate.service.AchievementRateDataService;
import com.cet.eem.bll.common.model.domain.subject.energy.ProjectEnergyType;
import com.cet.eem.bll.common.model.domain.subject.energyefficiency.EnergyEfficiencySetVo;
import com.cet.eem.bll.common.model.ext.modelentity.EemQueryCondition;
import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.ParamUtils;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.Result;
import com.cet.eem.common.parse.JsonTransferUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @ClassName : AchievementRateController
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-11-20 14:19
 */
@Api(value = "AchievementRateController", tags = {"达成率相关数据录入管理接口"})
@RestController
@RequestMapping(value = "/eem/v1/achievementrate/data")
public class AchievementRateController {
    @Autowired
    AchievementRateDataService achievementRateDataService;

    @ApiOperation("系统数据录入，返回格式根据计量参数类型分组")
    @PostMapping("/input")
    public Result<List<AchievementRateReturnVo>> inputAchievementRateData(@RequestBody @Validated AchievementRateInputDataVo inputDataVo) {
        return Result.ok(achievementRateDataService.inputAchievementRateData(inputDataVo));
    }

    @ApiOperation("查询录入数据，返回格式根据计量参数类型分组")
    @PostMapping("/input/query")
    public Result<List<AchievementRateDataNodeReturnVo>> queryAchievementRateData(@RequestBody @Validated AchievementRateQueryVo queryVo) {
        return Result.ok(achievementRateDataService.queryAchievementRateData(queryVo));
    }

    @ApiOperation("批量导出录入数据")
    @PostMapping("/export/multiNodes")
    public Result<Object> exportMultiNodesData(@RequestBody @Valid AchievementRateQueryVo queryVo) {
        achievementRateDataService.exportAchievementRateData(queryVo);
        return null;
    }

    @ApiOperation("导入系统数据录入")
    @PostMapping("/import/multiNodes")
    public Result<Object> createSystemData(MultipartFile multipartFile,
                                           @RequestParam @ApiParam(value = "数据类型", name = "dataEntryType", required = true) int dataEntryType) throws Exception {
        achievementRateDataService.importAchievementRateData(multipartFile, dataEntryType);
        return Result.ok();
    }

    @ApiOperation("达成率计算公式查询")
    @PostMapping("/formula")
    public Result<String> queryFormula() {
        return Result.ok(achievementRateDataService.queryFormula());
    }

    @ApiOperation("达成率查询")
    @PostMapping("/compare")
    public Result<AchievementRateReturnDataWithUnit> queryAchievementRateCompareData(@RequestBody AchievementRateQueryVo queryVo) {
        return Result.ok(achievementRateDataService.queryAchievementRateCompareData(queryVo));
    }

    @ApiOperation("达成率数据导出")
    @PostMapping("/export")
    public Result<Object> exportAchievementRateCompareData(@RequestBody AchievementRateQueryVo queryVo) {
        achievementRateDataService.exportAchievementRateCompareData(queryVo);
        return null;
    }

    @ApiOperation("查询当前节点关联的指标信息数据")
    @PostMapping("/effSetList")
    public Result<List<EnergyEfficiencySetVo>> queryEffSetList(@RequestBody EffQueryVo effQueryVo) {

        return Result.ok(achievementRateDataService.queryEffSetList(effQueryVo));
    }

    @ApiOperation("查询当天节点相关的核算方案的能源类型")
    @PostMapping("/energyType")
    public Result<List<FeeTypesReturnVo>> queryEnergyType(@RequestBody BaseVo baseVo) {

        return Result.ok(achievementRateDataService.queryEnergyType(baseVo));
    }

    @ApiOperation(value = "获取项目的树节点结构")
    @PostMapping(value = "/nodeTree", produces = "application/json")
    public Result<List<BaseVo>> queryNodeTree(
            @RequestBody @ApiParam(name = "condition", value = "节点树参数", required = true) EemQueryCondition condition) {

        return Result.ok(achievementRateDataService.queryNodeTree(condition, GlobalInfoUtils.getUserId(), GlobalInfoUtils.getProjectId()));

    }

    @ApiOperation("查询当前项目和成本项相关的能源类型")
    @PostMapping("/energyTypeList")
    public Result<List<ProjectEnergyType>> queryEnergyTypeList() {

        return Result.ok(achievementRateDataService.queryEnergyTypeByProjectId(GlobalInfoUtils.getProjectId()));
    }
    @ApiOperation(value = "获取项目的树节点结构--根据指标类型过滤")
    @PostMapping(value = "/nodeTree/eff", produces = "application/json")
    public Result<List<BaseVo>> queryNodeTreeFilterByEff(
            @RequestBody @ApiParam(name = "condition", value = "节点树参数", required = true) EemQueryCondition condition,
            @RequestParam @ApiParam(name = "effSetId", value = "指标ID", required = true) Long effSetId) {

        return Result.ok(achievementRateDataService.queryNodeTree(condition, GlobalInfoUtils.getUserId(), GlobalInfoUtils.getProjectId(),effSetId));

    }
}
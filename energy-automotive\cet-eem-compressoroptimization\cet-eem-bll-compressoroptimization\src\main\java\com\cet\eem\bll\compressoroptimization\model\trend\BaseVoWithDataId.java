package com.cet.eem.bll.compressoroptimization.model.trend;

import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.TrendSearchVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : BaseVoWithDataId
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-18 09:49
 */
@Getter
@Setter
public class BaseVoWithDataId extends BaseVo {
    @ApiModelProperty("测点信息")
    private List<CompressorDataIdSearchVo> meterConfigs;

}
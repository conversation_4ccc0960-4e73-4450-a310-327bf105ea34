<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cet.eem</groupId>
        <artifactId>eem-base-service-parent</artifactId>
        <version>4.0.4.42</version>
        <relativePath/>
    </parent>
    <artifactId>cet-eem-customreport</artifactId>
    <packaging>pom</packaging>
    <version>1.0.12</version>

    <modules>
        <module>cet-eem-bll-customreport</module>
        <module>cet-eem-customreportservice</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.cet.eem</groupId>
            <artifactId>cet-eem-dal-starter</artifactId>
        </dependency>

        <!-- eem基础业务层 -->
        <dependency>
            <groupId>com.cet.eem</groupId>
            <artifactId>cet-eem-bll-base-starter</artifactId>
        </dependency>

        <!-- eem核心业务层 -->
        <dependency>
            <groupId>com.cet.eem</groupId>
            <artifactId>cet-eem-bll-core-starter</artifactId>
        </dependency>

    </dependencies>

    <!--发布配置管理-->
    <distributionManagement>
        <repository>
            <id>10.12.135.233_9086</id>
            <name>releases</name>
            <url>http://10.12.135.233:9086/repository/cet/</url>
        </repository>
        <snapshotRepository>
            <id>10.12.135.233_9086</id>
            <name>snapshots</name>
            <url>http://10.12.135.233:9086/repository/cet/</url>
        </snapshotRepository>
    </distributionManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
            </plugin>
        </plugins>
    </build>
</project>
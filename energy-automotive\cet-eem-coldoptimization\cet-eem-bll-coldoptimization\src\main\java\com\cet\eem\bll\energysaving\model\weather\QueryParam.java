package com.cet.eem.bll.energysaving.model.weather;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * @ClassName : QueryParam
 * @Description : 查询参数
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-15 17:04
 */
@Getter
@Setter
public class QueryParam {
    /**
     * 在这里是作为房间id使用
     */
    private Long objectId;
    private String objectLabel;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Integer cycle;
    /**
     * 冷冻水供回水那块筛选框传的设备id，为0是查总管
     */
    private Long deviceId;
    private String deviceLabel;
    public QueryParam() {

    }

    public QueryParam(LocalDateTime startTime, LocalDateTime endTime, Integer cycle) {
        this.startTime = startTime;
        this.endTime = endTime;
        this.cycle = cycle;
    }
    public QueryParam(LocalDateTime startTime, LocalDateTime endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
    }

    public QueryParam(Long objectId, String objectLabel, LocalDateTime startTime, LocalDateTime endTime, Integer cycle) {
        this.objectId = objectId;
        this.objectLabel = objectLabel;
        this.startTime = startTime;
        this.endTime = endTime;
        this.cycle = cycle;
    }

    public QueryParam(Long objectId, String objectLabel) {
        this.objectId=objectId;
        this.objectLabel=objectLabel;
    }
}
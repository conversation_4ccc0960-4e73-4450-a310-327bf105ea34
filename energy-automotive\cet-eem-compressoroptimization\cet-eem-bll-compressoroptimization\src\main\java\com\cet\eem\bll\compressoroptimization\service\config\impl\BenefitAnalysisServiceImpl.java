package com.cet.eem.bll.compressoroptimization.service.config.impl;

import com.cet.eem.bll.common.model.domain.subject.energy.EnergyConsumption;
import com.cet.eem.bll.compressoroptimization.model.benefit.BenefitQueryParam;
import com.cet.eem.bll.compressoroptimization.model.benefit.ElectricalMonthData;
import com.cet.eem.bll.compressoroptimization.model.benefit.ElectricalYearData;
import com.cet.eem.bll.compressoroptimization.service.config.AiCompressorConfigService;
import com.cet.eem.bll.compressoroptimization.service.config.BenefitAnalysisService;
import com.cet.eem.bll.energy.dao.consumption.EnergyConsumptionDao;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.constant.EnergyTypeDef;
import com.cet.eem.common.constant.EnumOperationType;
import com.cet.eem.common.constant.ExcelType;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.exception.ValidationException;
import com.cet.eem.common.file.FileUtils;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.DataLogData;
import com.cet.eem.common.model.excel.ExcelAnchor;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.utils.PoiExcelUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.ibm.icu.text.DecimalFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @ClassName : BenefitAnalysisServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-25 16:18
 */
@Service
@Slf4j
public class BenefitAnalysisServiceImpl implements BenefitAnalysisService {
    @Value("${cet.eem.compressor.filepath}")
    private String filePath;
    private static final String LOG_KEY = "[空压ai效益分析]";
    private static final Integer SIZE = 12;
    private static final Double PERCENT = 100.0;
    @Autowired
    EnergyConsumptionDao consumptionDao;
    @Autowired
    AiCompressorConfigService compressorConfigService;
    public static final String SPILT = "_";
    public static final Integer START = 1;
    public static final Integer FIRST = 0;
    public static final Integer SECOND = 1;

    /**
     * 年度电气比=年度用电量/年度用气量，kwh/Nm3；
     * 累计节省电量=（优化前年度电气比-年度电气比）*当年度用气量，kwh--累计值
     * 年度节省比例=（优化前年度电气比-年度电气比）/优化前电气比，%
     * 累计节省费用=累计节省电量*当地电价，元）
     *
     * @param roomId
     * @param projectId
     * @return
     */
    @Override
    public ElectricalYearData queryElectricalYearData(Long roomId, Long projectId) throws IOException {

        List<DataLogData> data = new ArrayList<>();
        //因为需要读取的信息有当地电价，ai优化开始年份，和优化前年度电气比，返回值是第二个，其他2个字段存入datalogdata信息中
        DataLogData airCompressorBenefitConfig = new DataLogData();
        Double beforeRatioOfYear = getAirCompressorBenefitConfig(data, airCompressorBenefitConfig);
        Long startTime = airCompressorBenefitConfig.getTime();
        Double price = airCompressorBenefitConfig.getValue();
        BaseVo room = getRoom(roomId, projectId);
        ElectricalYearData electricalYearData = new ElectricalYearData();
        if (Objects.isNull(room)) {
            return electricalYearData;
        }
        Long queryStartTime = TimeUtil.getFirstDayOfThisYear(System.currentTimeMillis());
        if (Objects.nonNull(startTime)) {
            queryStartTime = startTime;
        }
        Long nowYear = TimeUtil.getFirstDayOfThisYear(System.currentTimeMillis());
        Long queryEndTime = TimeUtil.addDateTimeByCycle(nowYear, AggregationCycle.ONE_YEAR, 1);
        //查询能耗，ai开始年份，到今年的数据--压缩空气和电能
        List<EnergyConsumption> consumptions = consumptionDao.queryEnergyConsumption(room, queryStartTime, queryEndTime, AggregationCycle.ONE_YEAR,
                Arrays.asList(EnergyTypeDef.ELECTRIC, EnergyTypeDef.COMPRESSEDAIR));
        //计算年度电气比和节省比例
        calculateElectricalRatio(consumptions, nowYear, electricalYearData, beforeRatioOfYear);
        //计算累计节省电量和费用
        calculate(startTime, consumptions, queryEndTime, electricalYearData, beforeRatioOfYear, price);
        return electricalYearData;
    }

    @Override
    public List<ElectricalMonthData> queryElectricalMonthData(BenefitQueryParam queryParam, Long projectId) throws IOException {
        //查询月度用电量，月底用气量，使用优化前电气比，计算优化后电气比，计算节省比例
        List<DataLogData> data = new ArrayList<>();
        DataLogData airCompressorBenefitConfig = new DataLogData();
        getAirCompressorBenefitConfig(data, airCompressorBenefitConfig);
        Map<Integer, Double> map = data.stream().collect(
                Collectors.toMap(dataLogData -> TimeUtil.timestamp2LocalDateTime(dataLogData.getTime()).getMonthValue(), DataLogData::getValue));

        BaseVo room = getRoom(queryParam.getRoomId(), projectId);
        List<LocalDateTime> timeRange = TimeUtil.getTimeRange(queryParam.getStartTime(), queryParam.getEndTime(), AggregationCycle.ONE_MONTH);
        if (Objects.isNull(room)) {
            return assembleEmptyData(timeRange);
        }
        List<EnergyConsumption> consumptions = consumptionDao.queryEnergyConsumption(room, queryParam.getStartTime(), queryParam.getEndTime(),
                AggregationCycle.ONE_MONTH,
                Arrays.asList(EnergyTypeDef.ELECTRIC, EnergyTypeDef.COMPRESSEDAIR));

        List<ElectricalMonthData> monthDataList = new ArrayList<>();
        for (LocalDateTime dateTime : timeRange) {
            ElectricalMonthData monthData = new ElectricalMonthData();
            EnergyConsumption electric = consumptions.stream().filter(energyConsumption -> Objects.equals(energyConsumption.getEnergytype(),
                    EnergyTypeDef.ELECTRIC)
                    && Objects.equals(energyConsumption.getLogtime(), TimeUtil.localDateTime2timestamp(dateTime)))
                    .findAny().orElse(new EnergyConsumption());
            EnergyConsumption air = consumptions.stream().filter(energyConsumption -> Objects.equals(energyConsumption.getEnergytype(),
                    EnergyTypeDef.COMPRESSEDAIR)
                    && Objects.equals(energyConsumption.getLogtime(), TimeUtil.localDateTime2timestamp(dateTime)))
                    .findAny().orElse(new EnergyConsumption());
            //优化后
            Double ratio = CommonUtils.calcDouble(electric.getUsage(), air.getUsage(), EnumOperationType.DIVISION.getId());
            monthData.setLogTime(TimeUtil.localDateTime2timestamp(dateTime));
            monthData.setPowerConsumption(electric.getUsage());
            monthData.setGasConsumption(air.getUsage());
            Double beforeRatio = map.get(dateTime.getMonthValue());
            monthData.setBeforeAiElectricalRatio(beforeRatio);
            monthData.setAfterAiElectricalRatio(ratio);
            //（当月优化前电气比-当月优化后电气比）/当月优化前电气比
            monthData.setSavingsRatio(CommonUtils.calcDouble(CommonUtils.calcDouble(CommonUtils.calcDouble(beforeRatio, ratio, EnumOperationType.SUBTRACT.getId()), beforeRatio, EnumOperationType.DIVISION.getId()), PERCENT, EnumOperationType.MULTIPLICATION.getId()));
            monthDataList.add(monthData);
        }
        return monthDataList;
    }

    List<ElectricalMonthData> assembleEmptyData(List<LocalDateTime> timeRange) {
        List<ElectricalMonthData> monthDataList = new ArrayList<>();
        for (LocalDateTime dateTime : timeRange) {
            ElectricalMonthData monthData = new ElectricalMonthData();
            monthData.setLogTime(TimeUtil.localDateTime2timestamp(dateTime));
            monthDataList.add(monthData);
        }
        return monthDataList;
    }

    @Override
    public void exportMonthData(BenefitQueryParam queryParam, HttpServletResponse response, Long projectId) throws IOException {
        ExcelType excelType = ExcelType.XLS_X;

        try(Workbook wb = PoiExcelUtils.createWorkBook(excelType);) {
            List<Integer> colsWidth = Stream.of(15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15).collect(Collectors.toList());
            String sheetName = "节能效果";
            //名字 从xx-xx
            String title = sheetName + "(" + TimeUtil.format(queryParam.getStartTime(), TimeUtil.DATE_TIME_FORMAT) +
                    "至" + TimeUtil.format(TimeUtil.addDateTimeByCycle(queryParam.getEndTime(), AggregationCycle.ONE_MONTH, -1),
                    TimeUtil.DATE_TIME_FORMAT) + ")";
            List<ElectricalMonthData> monthDataList = queryElectricalMonthData(queryParam, projectId);
            String pic = queryParam.getPic();
            PoiExcelUtils.createSheet(wb, sheetName, (sheet, baseCellStyle, rowIndex) -> {
                //写表格
                Row row1 = sheet.createRow(rowIndex++);
                Row row2 = sheet.createRow(rowIndex++);
                Row row3 = sheet.createRow(rowIndex++);
                Row row4 = sheet.createRow(rowIndex++);
                int col = 0;
                PoiExcelUtils.createCell(row1, col, baseCellStyle, "统计量");
                PoiExcelUtils.createCell(row2, col, baseCellStyle, "用电量（kWh）");
                PoiExcelUtils.createCell(row3, col, baseCellStyle, "用气量（Nm³）");
                PoiExcelUtils.createCell(row4, col, baseCellStyle, "节省比例（%）");
                col++;
                for (ElectricalMonthData monthData : monthDataList) {
                    //有4行

                    PoiExcelUtils.createCell(row1, col, baseCellStyle, TimeUtil.format(monthData.getLogTime(), TimeUtil.YYYY_MM));
                    PoiExcelUtils.createCell(row2, col, baseCellStyle, handleDouble(monthData.getPowerConsumption()));
                    PoiExcelUtils.createCell(row3, col, baseCellStyle, handleDouble(monthData.getGasConsumption()));
                    PoiExcelUtils.createCell(row4, col, baseCellStyle, handleDouble(monthData.getSavingsRatio()));

                    col++;

                }
                // 写图片
                if (Objects.nonNull(pic)) {
                    ExcelAnchor anchor = new ExcelAnchor(0, colsWidth.size(), rowIndex, rowIndex + 16);
                    PoiExcelUtils.insertImages(wb, sheet, pic, excelType, anchor);
                }
            }, colsWidth);
            FileUtils.downloadExcel(response, wb, title, CommonUtils.APPLICATION_MSEXCEL);
        } catch (Exception e) {
            log.error("导出数据异常", e);
            throw new ValidationException(e.getMessage());
        }

    }

    private String handleDouble(Double value) {
        if (Objects.isNull(value)) {
            return CommonUtils.BLANK_STR;
        }
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(value);
    }

    /**
     * 计算年度电气比
     */
    private void calculateElectricalRatio(List<EnergyConsumption> consumptions, Long nowTime, ElectricalYearData electricalYearData, Double beforeRatioOfYear) {
        Double ratio = calculateElectricalRatio(consumptions, nowTime);
        //年度电气比
        if (Objects.nonNull(ratio)) {
            electricalYearData.setElectricalRatio(ratio );
        }

        //年度节省比例=（优化前年度电气比-年度电气比）/优化前电气比，%
        Double powerSaveRatio = CommonUtils.calcDouble(CommonUtils.calcDouble(beforeRatioOfYear, ratio, EnumOperationType.SUBTRACT.getId()),
                beforeRatioOfYear, EnumOperationType.DIVISION.getId());
        if (Objects.nonNull(powerSaveRatio)) {
            electricalYearData.setPowerSaveRatio(powerSaveRatio * PERCENT);
        }

    }

    private void calculate(Long startTime, List<EnergyConsumption> consumptions, Long nowTime, ElectricalYearData electricalYearData, Double beforeRatioOfYear, Double price) {
        if (Objects.isNull(startTime)) {
            return;
        }
        //累计节省电量=（优化前年度电气比-年度电气比）*当年度用气量，kwh--累计值
        // 累计节省费用=累计节省电量*当地电价，元）
        List<Long> timeRange = TimeUtil.getTimeRange(startTime, nowTime, AggregationCycle.ONE_YEAR);
        Double saveElectric = null;
        for (Long time : timeRange) {
            EnergyConsumption electric = consumptions.stream().filter(energyConsumption -> Objects.equals(energyConsumption.getEnergytype(),
                    EnergyTypeDef.ELECTRIC)
                    && Objects.equals(energyConsumption.getLogtime(), time))
                    .findAny().orElse(new EnergyConsumption());
            EnergyConsumption air = consumptions.stream().filter(energyConsumption -> Objects.equals(energyConsumption.getEnergytype(),
                    EnergyTypeDef.COMPRESSEDAIR)
                    && Objects.equals(energyConsumption.getLogtime(), time))
                    .findAny().orElse(new EnergyConsumption());
            //优化后
            Double after = CommonUtils.calcDouble(electric.getUsage(), air.getUsage(), EnumOperationType.DIVISION.getId());
            //（优化前-优化后）/优化前  rate是（v1-v2)/v2
            Double saveSingle = CommonUtils.calcDouble(
                    CommonUtils.calcDouble(beforeRatioOfYear, after, EnumOperationType.SUBTRACT.getId()),
                    air.getUsage(), EnumOperationType.MULTIPLICATION.getId());
            if (Objects.nonNull(saveSingle) && Objects.nonNull(saveElectric)) {
                saveElectric += saveSingle;
            } else if (Objects.nonNull(saveSingle)){
                saveElectric = saveSingle;
            }
        }
        electricalYearData.setPowerSave(saveElectric);
        electricalYearData.setCostSave(CommonUtils.calcDouble(saveElectric, price, EnumOperationType.MULTIPLICATION.getId()));
    }

    /**
     * 计算电气比
     *
     * @param consumptions
     * @param time
     * @return
     */
    private Double calculateElectricalRatio(List<EnergyConsumption> consumptions, Long time) {
        EnergyConsumption electric = consumptions.stream().filter(energyConsumption -> Objects.equals(energyConsumption.getEnergytype(),
                EnergyTypeDef.ELECTRIC)
                && Objects.equals(energyConsumption.getLogtime(), time))
                .findAny().orElse(new EnergyConsumption());
        EnergyConsumption air = consumptions.stream().filter(energyConsumption -> Objects.equals(energyConsumption.getEnergytype(),
                EnergyTypeDef.COMPRESSEDAIR)
                && Objects.equals(energyConsumption.getLogtime(), time))
                .findAny().orElse(new EnergyConsumption());
        //优化后
        return CommonUtils.calcDouble(electric.getUsage(), air.getUsage(), EnumOperationType.DIVISION.getId());
    }

    private BaseVo getRoom(Long roomId, Long projectId) {
        if (Objects.isNull(roomId)) {
            List<BaseVo> room = compressorConfigService.getRoom(projectId);
            if (CollectionUtils.isNotEmpty(room)) {
                return room.get(0);
            }
            return null;
        } else {
            return new BaseVo(roomId, NodeLabelDef.ROOM);
        }
    }

    private String getPath() throws UnsupportedEncodingException, FileNotFoundException {
        String parentPath;
        File curve;
        // 处理父节点路径
        if (StringUtils.isNotBlank(filePath)) {
            parentPath = filePath;
        } else {
            curve = ResourceUtils.getFile("classpath:config");
            parentPath = curve.getPath() + "/AirCompressorBenefitConfig.txt";
            parentPath = URLDecoder.decode(parentPath, "utf-8");
        }

        return parentPath;
    }

    /**
     * 获取车间名称与编号对应关系的txt
     * key-车间名称 value-车间编号
     */
    private Double getAirCompressorBenefitConfig(List<DataLogData> data, DataLogData dataLogData) throws IOException {
        log.info("{}：读取空压ai的效益分析配置文件", LOG_KEY);
        String path = getPath();
        if (Objects.isNull(path)) {
            log.info("{}：没有找到空压ai的效益分析配置文件，路径：{}", LOG_KEY, filePath);
            return null;
        }
        List<String> allLines = Files.readAllLines(Paths.get(path));
        if (CollectionUtils.isEmpty(allLines)) {
            log.info("{}：没有找到空压ai的效益分析配置文件，路径：{}", LOG_KEY, filePath);
            return null;
        }
        //读取的实际数据从第二行开始，第一列是时间，第二列是优化前电气比，用逗号分割，到13行，是当地电价，14行是优化前年度电气比，所以size设置值为12，i从1开始，读取的
        //每一行分割的结果，2到12行，第0项是时间（年-月份）,第1项是优化前电气比，13行和14行读取的是第1项
        for (int i = START; i < allLines.size(); i++) {
            String[] split = allLines.get(i).split(",");
            if (i <= SIZE) {
                String time = split[FIRST];
                String[] timeData = time.split("-");
                //读取月份的年和月，然后拼接第一天，0时0分构建一个时间
                LocalDateTime of = LocalDateTime.of(Integer.parseInt(timeData[FIRST]), Integer.parseInt(timeData[SECOND]), 1, 0, 0);
                long cur = TimeUtil.localDateTime2timestamp(of);
                if (Objects.equals(i, SECOND)) {
                    dataLogData.setTime(cur);
                }
                data.add(new DataLogData(cur, Double.valueOf(split[SECOND])));
            } else if (i == SIZE + START) {
                dataLogData.setValue(Double.valueOf(split[SECOND]));
            } else {
                return Double.valueOf(split[SECOND]);
            }

        }
        return null;
    }
}
package com.cet.eem.compressorservice.model.enums;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * @ClassName : QueryTypes
 * <AUTHOR> yangy
 * @Date: 2022-04-06 15:04
 */
public enum QueryTypes {
    /**
     * 查询type
     */
    YEARONYEAR(1, "同比"),
    MONTHONMONTH(2, "环比"),
    SAMEMONTHONMONTHRATIO(3, "同环比"),
    THISDATAS(0, "当前数据");
    public Integer type;
    public String desc;

    QueryTypes(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static QueryTypes getQueryTypes(Integer type) {
        return Stream.of(QueryTypes.values()).filter(item -> Objects.equals(item.type, type)).findFirst().orElse(null);
    }
}

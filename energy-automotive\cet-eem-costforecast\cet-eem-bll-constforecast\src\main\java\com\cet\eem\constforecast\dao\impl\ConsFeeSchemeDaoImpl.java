package com.cet.eem.constforecast.dao.impl;

import com.cet.eem.constforecast.dao.ConsFeeSchemeDao;
import com.cet.eem.bll.common.model.domain.subject.generalrules.FeeScheme;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.model.base.ConditionBlock;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import com.cet.eem.toolkit.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/3
 */
@Repository
public class ConsFeeSchemeDaoImpl implements ConsFeeSchemeDao {
    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Override
    public List<FeeScheme> queryFeeRecord(Collection<Long> schemeIds, String feeRecordType) {
        List<ConditionBlock> filters = new ArrayList<>();
        filters.add(new ConditionBlock(ColumnDef.ID, ConditionBlock.OPERATOR_EQ, schemeIds));

        List<String> subNodes = null;
        if (StringUtils.isNotBlank(feeRecordType)) {
            subNodes = new ArrayList<>();
            subNodes.add(feeRecordType);
            switch (feeRecordType) {
                case ModelLabelDef.STAGE_FEE_RECORD:
                    // 阶梯费率
                    subNodes.add(ModelLabelDef.STAGE_FEE_SET);
                    break;
                case ModelLabelDef.POWER_TARIFF:
                    // 力调费率
                    subNodes.add(ModelLabelDef.POWER_TARIFF_FACTOR);
                    break;
                case ModelLabelDef.TIME_SHARE_FEE_RECORD:
                    // 分时费率
                    subNodes.add(ModelLabelDef.TIME_SHARE_PERIOD);
                    subNodes.add(ModelLabelDef.DAY_SET);
                    subNodes.add(ModelLabelDef.DAY_SHARE_SET);
                    subNodes.add(ModelLabelDef.TIME_SHARE_FEE_SINGLE_RATE);
                    break;
                default:
                    break;
            }
        }

        return modelServiceUtils.queryWithChildren(null, ModelLabelDef.FEE_SCHEME, filters,
                subNodes, FeeScheme.class);
    }

    @Override
    public List<FeeScheme> queryFeeSchemes(Collection<Long> schemeIds) {
        if (CollectionUtils.isEmpty(schemeIds)) {
            return Collections.emptyList();
        }
        QueryCondition condition = QueryConditionBuilder.of(ModelLabelDef.FEE_SCHEME, schemeIds).leftJoin(ModelLabelDef.STAGE_FEE_RECORD).leftJoin(ModelLabelDef.STAGE_FEE_SET).build();
        return modelServiceUtils.query(condition, FeeScheme.class);
    }
}

package com.cet.eem.customreport.model;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 定制报表数据结构
 * <AUTHOR>
 * @date 2023/11/14
 */
@Getter
@Setter
public class ReportData {
    //房间名称
    private String roomName;
    private String deviceName;
    private List<ValueAndTime> indicateValueList;
    private List<ValueAndTime> usedValueList;
    private List<ValueAndTime> daySumList;
    private Long objectId;
    private String objectLabel;
}

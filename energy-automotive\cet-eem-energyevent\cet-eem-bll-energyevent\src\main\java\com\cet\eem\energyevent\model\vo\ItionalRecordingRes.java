package com.cet.eem.energyevent.model.vo;

import com.cet.eem.energyevent.model.Constant;
import com.cet.eem.energyevent.model.enums.EnergyNameEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/7/24 14:21
 */
@Getter
@Setter
public class ItionalRecordingRes {
    private Integer type;
    private Long logtime;
    private String paraName;
    private String unit;
    private Long dataId;
    private List<AutomDataLogData> datas;

    public String getUnit() {
        if (Objects.equals(type,Constant.DSJL_TYPE)){
            return null;
        }
        if (Objects.equals(paraName,Constant.ENERGY_NAME)){
            if (Objects.equals(dataId, Constant.FLOW)){
                return null;
            }
            return Constant.KWH;
        }else {
            return EnergyNameEnum.duration.getUnit();
        }
    }
}

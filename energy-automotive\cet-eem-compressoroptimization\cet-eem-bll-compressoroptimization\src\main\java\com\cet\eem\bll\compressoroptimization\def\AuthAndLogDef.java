package com.cet.eem.bll.compressoroptimization.def;

import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : AuthAndLogDef
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-08-04 15:31
 */
@Getter
@Setter
public class AuthAndLogDef {
    public static final int AUTH_COMPRESSOR = 69;
    public static final String SYSTEM_USE_AI_UPDATE = "compressor_systemuseaiupdate";
    public static final String COMPRESSOR_USE_AI_UPDATE = "compressor_useaiupdate";
    public static final String SYSTEM_NORMAL_CONFIG_UPDATE = "compressor_systemnormalconfigupdate";
    public static final String SYSTEM_HIGH_CONFIG_UPDATE = "compressor_systemhighconfigupdate";

}
package com.cet.eem.bll.energysaving.model.cetml;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName : PumpFitData
 * @Description :
 * <AUTHOR> jiang<PERSON><PERSON>uan
 * @Date: 2023-06-29 17:07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PumpFitData {
    /**
     * 冷冻总管瞬时流量,Double，立方米/小时
     */
    @JsonProperty("total_flow")
    private Double totalFlow;
    /**
     * 对应冷机id的冷冻泵功率
     */
    @JsonProperty("pump_power")
    private Double pumpPower;

}
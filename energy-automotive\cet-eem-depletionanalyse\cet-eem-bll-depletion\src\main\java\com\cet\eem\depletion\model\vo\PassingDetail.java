package com.cet.eem.depletion.model.vo;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PassingDetail extends Detail {
    //过车时间
    private Long timeOfPassing;
    //vin码
    private String VIN;
    //生产节拍
    private Double taktTime;
    //空耗开始时间
    private Long startOfWaste;
    //空耗类型
    private Integer wasteType;


    //上一台车过车
    private Long lastPasstime;
    //实际过车节拍
    private Long realTaktTime;

}

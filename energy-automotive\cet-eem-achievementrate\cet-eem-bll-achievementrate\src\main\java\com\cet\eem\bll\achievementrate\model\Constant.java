package com.cet.eem.bll.achievementrate.model;

import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/12 14:31
 */
public interface Constant {
    /**
     * 折标煤(kgce)
     * 蒸汽(m³)
     * CO2(kgCO₂e)
     */
    List<Integer> FOLDED_LABEL_ENERGTYPE = Arrays.asList(13, 8, 18);

    String PROJECTENERGYTYPE = "projectenergytype";
    String ZHCB = "综合成本";
    Integer TWO = 2;
    Integer ONE = 1;
    Integer ZERO_INT = 0;
    Long ZERO_LONG = 0L;
    Double ZERO_DOUBLE = 0D;
    Double ONE_HUNDRED_DOUBLE = 100D;
    String RATE = "rate";
    String ACHIEVEMENTRATE = "achievementrate";
    String M_UPDATETIME = "m_updatetime";
    List<Integer> CYCLES = Arrays.asList(AggregationCycle.ONE_DAY, AggregationCycle.ONE_MONTH, AggregationCycle.ONE_YEAR);
    String RECALCULATE_KEY = "RECALCULATE";
    String INDICATORTYPE = "indicatortype";
    String LOG="[达成率数据转存]";
}

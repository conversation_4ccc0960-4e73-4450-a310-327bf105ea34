package com.cet.eem.model.param;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/7 19:26
 */
@Getter
@Setter
@NoArgsConstructor
public class RepairPointWebParam extends TimeValueParam {

    //web典型日与缺失时间的关系
    private List<RepairPointParam> params;

    public double genReferenceSumValue(){
        if(CollectionUtils.isEmpty(params)){
            return 0d;
        }
        return params.stream().map(RepairPointParam::genReferenceValue).reduce(Double::sum).orElse(0d);
    }

}

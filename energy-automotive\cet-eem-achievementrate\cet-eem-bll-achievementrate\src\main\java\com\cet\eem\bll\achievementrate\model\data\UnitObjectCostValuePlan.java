package com.cet.eem.bll.achievementrate.model.data;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * @ClassName : UnitObjectCostValuePlan
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-11-21 14:34
 */
@Getter
@Setter
@ModelLabel("unitobjectcostvalueplan")
public class UnitObjectCostValuePlan extends BaseEntity {
    @JsonProperty(ColumnDef.AGGREGATION_CYCLE)
    private Integer aggregationCycle;
    @JsonProperty(ColumnDef.ENERGY_TYPE)
    private Integer energyType;
    @JsonProperty(ColumnDef.LOG_TIME)
    private Long logTime;
    @JsonProperty(ColumnDef.OBJECTID)
    private Long objectId;
    @JsonProperty(ColumnDef.C_OBJECT_Label)
    private String objectLabel;
    @JsonProperty(ColumnDef.PRODUCT_TYPE)
    private Integer productType;
    private Double value;
    @JsonProperty("m_updatetime")
    private Long mUpdateTime;
    @JsonProperty(ColumnDef.PROJECT_ID)
    private Long projectId;

    public UnitObjectCostValuePlan() {
        this.modelLabel = "unitobjectcostvalueplan";
    }
}
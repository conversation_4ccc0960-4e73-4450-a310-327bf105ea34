package com.cet.eem.bll.energysaving.model.aiconsumption;

import com.cet.eem.bll.common.model.energy.EnergyResult;
import com.cet.eem.common.model.datalog.DataLogData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName : AiEnergyResult
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-05-31 14:22
 */
@Getter
@Setter
public class AiEnergyResult extends EnergyResult {
    @ApiModelProperty(value = "ai运行数据")
    private List<DataLogData> aidata = new ArrayList<>();
    public AiEnergyResult(){
        super();
    }
}
package com.cet.eem.bll.energysaving.model.config.system;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.bll.energysaving.model.def.ColdOptimizationLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : BooleanCondition
 * @Description : 布尔条件
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-28 15:01
 */
@Getter
@Setter
@ModelLabel(ColdOptimizationLabelDef.BOOLEAN_CONDITION)
public class BooleanCondition extends BaseEntity {
    @JsonProperty(ColdOptimizationLabelDef.CONTROL_SCHEME_ID)
    private Long controlSchemeId;
    @JsonProperty(ColumnDef.C_OBJECT_ID)
    private Long objectId;
    @JsonProperty(ColdOptimizationLabelDef.SIGNAL_TYPE)
    private Integer signalType;
    @JsonProperty(ColumnDef.C_OBJECT_Label)
    private String objectLabel;
    private Double value;
    public BooleanCondition() {
        this.modelLabel = ColdOptimizationLabelDef.BOOLEAN_CONDITION;
    }
}
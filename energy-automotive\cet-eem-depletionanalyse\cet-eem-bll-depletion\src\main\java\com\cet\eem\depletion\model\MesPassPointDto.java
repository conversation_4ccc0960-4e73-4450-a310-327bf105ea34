package com.cet.eem.depletion.model;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.depletion.model.Constant.Constant;
import com.cet.eem.model.model.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class MesPassPointDto extends BaseEntity {
    public LocalDateTime time;
    public String skidno;
    public String stationcode;
    public String vin;
    public Long passtime;

    public MesPassPointDto(){
        super.modelLabel = Constant.PASS_POINT_INFO;
    }
}

package com.cet.eem.eemservice;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.Arrays;
import java.util.List;

/**
 * swagger配置类
 *
 * <AUTHOR>
 */
@Configuration
@EnableSwagger2
public class Swagger {
    @Value("${cet.eem.swagger.enabled}")
    private boolean enabled;

    @Bean
    public Docket createColdoptimizatinRestApi() {
        List<Parameter> parameters = createParameters();

        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("制冷mes对接")
                .apiInfo(apiInfo())
                .enable(enabled)
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.cet.eem.datatransportservice"))
                .paths(PathSelectors.any())
                .build()
                .globalOperationParameters(parameters);
    }


    private List<Parameter> createParameters() {
        ParameterBuilder parameterBuilder1 = new ParameterBuilder();
        parameterBuilder1
                .name("User-ID")
                .description("用户id，测试使用")
                .modelRef(new ModelRef("long"))
                .parameterType("header")
                .required(false)
                .build();

        ParameterBuilder parameterBuilder2 = new ParameterBuilder();
        parameterBuilder2
                .name("projectId")
                .description("项目id")
                .modelRef(new ModelRef("long"))
                .parameterType("header")
                .required(false)
                .build();
        return Arrays.asList(parameterBuilder1.build(), parameterBuilder2.build());
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("能源管理服务")
                .description("能源管理服务接口定义说明")
                .version("1.0")
                .build();
    }
}

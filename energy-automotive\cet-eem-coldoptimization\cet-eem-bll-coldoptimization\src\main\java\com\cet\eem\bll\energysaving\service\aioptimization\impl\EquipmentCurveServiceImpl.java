package com.cet.eem.bll.energysaving.service.aioptimization.impl;

import com.cet.eem.bll.common.dao.node.NodeDao;
import com.cet.eem.bll.common.model.domain.subject.energysaving.DeviceChainDetail;
import com.cet.eem.bll.common.model.ext.subject.energysaving.DeviceChainWithSubLayer;
import com.cet.eem.bll.energysaving.dao.aioptimization.EquipmentCurveDao;
import com.cet.eem.bll.energysaving.dao.weather.DeviceChainDao;
import com.cet.eem.bll.energysaving.dao.weather.DeviceChainDetailDao;
import com.cet.eem.bll.energysaving.dao.weather.RefrigeratingSystemDao;
import com.cet.eem.bll.energysaving.model.config.EquipmentCurve;
import com.cet.eem.bll.energysaving.model.config.PumpFunctionType;
import com.cet.eem.bll.energysaving.model.def.CurveTypeDef;
import com.cet.eem.bll.energysaving.model.equipmentoperation.BasicData;
import com.cet.eem.bll.energysaving.model.equipmentoperation.TrendListVo;
import com.cet.eem.bll.energysaving.model.weather.PumpVo;
import com.cet.eem.bll.energysaving.service.aioptimization.AiOptimizationService;
import com.cet.eem.bll.energysaving.service.aioptimization.EquipmentCurveService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.constant.EnumOperationType;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : EquipmentCurveServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-30 15:08
 */
@Repository
public class EquipmentCurveServiceImpl implements EquipmentCurveService {
    @Autowired
    AiOptimizationService aiOptimizationService;
    @Autowired
    RefrigeratingSystemDao refrigeratingSystemDao;
    @Autowired
    DeviceChainDao deviceChainDao;
    @Autowired
    DeviceChainDetailDao deviceChainDetailDao;
    @Autowired
    EquipmentCurveDao equipmentCurveDao;
    @Autowired
    NodeDao nodeDao;
    private static final ArrayList<Integer> X_LIST = new ArrayList<>(Arrays.asList(0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100));

    @Override
    public List<TrendListVo> queryMainsOperatingEfficiency(Long chainId, Long projectId) {
        return queryEquipmentCurve(chainId, NodeLabelDef.COLD_WATER_MAINENGINE, null, Arrays.asList(CurveTypeDef.COP_PERCENT, CurveTypeDef.COP_PERCENT_ACTUAL), projectId);
    }

    @Override
    public List<TrendListVo> queryMainsOperatingPower(Long chainId, Long projectId) {
        return queryEquipmentCurve(chainId, NodeLabelDef.COLD_WATER_MAINENGINE, null, Arrays.asList(CurveTypeDef.POWER_PERCENT, CurveTypeDef.POWER_PERCENT_ACTUAL), projectId);
    }

    @Override
    public List<TrendListVo> queryMatchingPowerOfRefrigerationPump(Long chainId, Long projectId) {
        return queryEquipmentCurve(chainId, NodeLabelDef.PUMP, PumpFunctionType.REFRIGERATING_PUMP, Collections.singletonList(CurveTypeDef.POWER_PERCENT_ACTUAL), projectId);
    }

    @Override
    public List<TrendListVo> queryMatchingPowerOfCoolingPump(Long chainId, Long projectId) {
        return queryEquipmentCurve(chainId, NodeLabelDef.PUMP, PumpFunctionType.COOLING_PUMP, Collections.singletonList(CurveTypeDef.POWER_PERCENT_ACTUAL), projectId);

    }

    @Override
    public List<TrendListVo> queryMatchingPowerOfCoolTower(Long chainId, Long projectId) {
        return queryEquipmentCurve(chainId, NodeLabelDef.COOLING_TOWER, null, Collections.singletonList(CurveTypeDef.POWER_PERCENT_ACTUAL), projectId);

    }

    @Override
    public List<TrendListVo> queryFrequencyAndPowerOfRefrigerationPump(Long chainId, Long projectId) {
        return queryEquipmentCurve(chainId, NodeLabelDef.PUMP, PumpFunctionType.REFRIGERATING_PUMP, Collections.singletonList(CurveTypeDef.FREQUENCY_POWER), projectId);
    }

    @Override
    public List<TrendListVo> queryFrequencyAndPowerOfCoolPump(Long chainId, Long projectId) {
        return queryEquipmentCurve(chainId, NodeLabelDef.PUMP, PumpFunctionType.COOLING_PUMP, Collections.singletonList(CurveTypeDef.FREQUENCY_POWER), projectId);
    }

    @Override
    public List<TrendListVo> queryFrequencyAndPowerOfCoolTower(Long chainId, Long projectId) {
        return queryEquipmentCurve(chainId, NodeLabelDef.COOLING_TOWER, null, Collections.singletonList(CurveTypeDef.FREQUENCY_POWER), projectId);
    }

    @Override
    public List<TrendListVo> queryTempAndPowerOfSupplyAndReturnWater(Long chainId, Long projectId) {
        return queryEquipmentCurve(chainId, NodeLabelDef.COLD_WATER_MAINENGINE, null, Arrays.asList(CurveTypeDef.SUPPLY_TEMP_POWER, CurveTypeDef.RETURN_TEMP_POWER), projectId);
    }

    private List<TrendListVo> queryEquipmentCurve(Long chainId, String objectLabel, Integer functionType, List<Integer> curveTypes, Long projectId) {
        //连锁
        List<DeviceChainWithSubLayer> deviceChainWithSubLayers = deviceChainDao.queryDeviceChainWithDetail(Collections.singletonList(chainId), projectId);
        if (CollectionUtils.isEmpty(deviceChainWithSubLayers)) {
            return Collections.emptyList();
        }
        List<DeviceChainDetail> deviceChainDetails = deviceChainWithSubLayers.get(0).getDeviceChainDetails();
        if (CollectionUtils.isEmpty(deviceChainDetails)) {
            return Collections.emptyList();
        }
        //连锁的设备
        List<BaseVo> chainDetails = deviceChainDetails.stream().filter(deviceChainDetail -> Objects.equals(deviceChainDetail.getObjectLabel(), objectLabel))
                .map(deviceChainDetail -> new BaseVo(deviceChainDetail.getObjectId(), deviceChainDetail.getObjectLabel())).collect(Collectors.toList());
        List<BaseVo> nodes;
        if (Objects.nonNull(functionType)) {
            List<PumpVo> baseVos = nodeDao.queryNodes(chainDetails, PumpVo.class);
            //冷冻泵
            nodes = baseVos.stream().filter(pumpVo -> Objects.equals(pumpVo.getFunctionType(), functionType))
                    .map(pumpVo -> new BaseVo(pumpVo.getId(), pumpVo.getModelLabel(), pumpVo.getName())).collect(Collectors.toList());
        } else {
            nodes = nodeDao.queryNodeName(chainDetails);
        }
        List<EquipmentCurve> equipmentCurves = equipmentCurveDao.queryCurveList(nodes, curveTypes);
        Map<BaseVo, Map<Integer, List<EquipmentCurve>>> map = equipmentCurves.stream().collect(Collectors.groupingBy(equipmentCurve -> new BaseVo(equipmentCurve.getObjectId(), equipmentCurve.getObjectLabel()),
                Collectors.groupingBy(EquipmentCurve::getCurveType)));
        return assembleData(map, nodes, curveTypes);
    }

    private List<TrendListVo> assembleData(Map<BaseVo, Map<Integer, List<EquipmentCurve>>> map, List<BaseVo> baseVos, List<Integer> curveTypes) {
        List<TrendListVo> result = new ArrayList<>();
        List<Integer> types = curveTypes.stream().sorted(Comparator.comparing(Integer::intValue)).collect(Collectors.toList());
        for (Map.Entry<BaseVo, Map<Integer, List<EquipmentCurve>>> entry : map.entrySet()) {
            BaseVo key = entry.getKey();
            BaseVo nodeWithName = baseVos.stream().filter(baseVo -> Objects.equals(key.getId(), baseVo.getId()) && Objects.equals(key.getModelLabel(), baseVo.getModelLabel()))
                    .findAny().orElse(new BaseVo());
            Map<Integer, List<EquipmentCurve>> value = entry.getValue();
            TrendListVo trendListVo = new TrendListVo();
            trendListVo.setObjectId(key.getId());
            trendListVo.setObjectName(nodeWithName.getName());
            trendListVo.setObjectLabel(key.getModelLabel());
            if (types.size() > 1) {
                //理论值
                List<EquipmentCurve> value1 = value.get(types.get(0));
                List<BasicData> basicData = assembleSingleData(value1, new ArrayList<>());
                List<Double> xData = basicData.stream().map(BasicData::getXData).collect(Collectors.toList());
                //实际值,根据理论值的xdata整合出实际值的列表
                List<EquipmentCurve> value2 = value.get(types.get(1));
                List<BasicData> basicDataList = assembleSingleData(value2, xData);
                trendListVo.setBasicDataList(basicData);
                trendListVo.setBasicData(basicDataList);
            } else {
                List<EquipmentCurve> value1 = value.get(types.get(0));
                List<BasicData> basicData = assembleSingleData(value1, new ArrayList<>());
                assembleTrendListVoByType(types.get(0), basicData, trendListVo);
            }

            result.add(trendListVo);
        }
        return result;
    }


    private List<BasicData> assembleRealityXData(List<BasicData> basicData, List<Double> xData) {
        if (CollectionUtils.isEmpty(basicData)) {
            basicData = new ArrayList<>();
        }
        for (Double d : xData) {
            BasicData data = basicData.stream().filter(basicData1 -> Objects.equals(basicData1.getXData(), d)).findAny().orElse(null);
            if (Objects.isNull(data)) {
                basicData.add(new BasicData(d, null));
            }
        }
        return basicData.stream().sorted(Comparator.comparing(BasicData::getXData)).collect(Collectors.toList());
    }

    private void assembleTrendListVoByType(Integer type, List<BasicData> basicData, TrendListVo trendListVo) {
        if (Objects.equals(type, CurveTypeDef.COP_PERCENT) || Objects.equals(type, CurveTypeDef.POWER_PERCENT) || Objects.equals(type, CurveTypeDef.RETURN_TEMP_POWER)) {
            trendListVo.setBasicDataList(basicData);
        } else {
            trendListVo.setBasicData(basicData);
        }
    }

    private List<BasicData> assembleSingleData(List<EquipmentCurve> value, List<Double> xData) {
        if (CollectionUtils.isEmpty(value)) {
            return Collections.emptyList();
        }
        //cop和冷负荷理论
        //找是否赋值方程系数
        EquipmentCurve curve = value.stream().filter(equipmentCurve -> Objects.nonNull(equipmentCurve.getQuadraticTermCoefficient())
                || Objects.nonNull(equipmentCurve.getPrimaryTermCoefficient())
                || Objects.nonNull(equipmentCurve.getConstant())).findAny().orElse(new EquipmentCurve());
        //没有就取录入的坐标值
        if (Objects.isNull(curve.getCurveType())) {
            return assembleCoordinateData(value.get(0), xData);
        } else {
            return assembleOtherList(curve, xData);
        }

    }

    private List<BasicData> assembleCoordinateData(EquipmentCurve value, List<Double> xData) {
        List<BasicData> coordinate = value.getCoordinate();
        if (CollectionUtils.isEmpty(coordinate)){
            coordinate=new ArrayList<>();
        }
        for (BasicData basicData : coordinate) {
            basicData.setXData(basicData.getXData() * 100);
        }
        List<BasicData> data = assembleRealityXData(coordinate, xData);
        return data.stream().sorted(Comparator.comparing(BasicData::getXData)).collect(Collectors.toList());
    }

    /**
     * 合并拟合数据和录入的数据（考虑最大值不是整点的情况），排序后过滤掉x一样的结果
     *
     * @param value
     * @return
     */
    private List<BasicData> assembleOtherList(EquipmentCurve value, List<Double> xData) {
        List<BasicData> result = new ArrayList<>();
        List<BasicData> dataList = new ArrayList<>();
        if (CollectionUtils.isEmpty(xData)){
            for (Integer x : X_LIST) {
                BasicData data = new BasicData();
                Double calculate = calculate(value.getQuadraticTermCoefficient(), value.getPrimaryTermCoefficient(), value.getConstant(), x.doubleValue() / 100);
                data.setXData(x.doubleValue());
                data.setYData(calculate);
                result.add(data);
            }
        }
        for (Double x : xData) {
            BasicData data = new BasicData();
            Double calculate = calculate(value.getQuadraticTermCoefficient(), value.getPrimaryTermCoefficient(), value.getConstant(), x / 100);
            data.setXData(x);
            data.setYData(calculate);
            result.add(data);
        }
        List<BasicData> data = assembleCoordinateData(value, xData);
        result.addAll(data);
        Map<Double, List<BasicData>> map = result.stream().collect(Collectors.groupingBy(BasicData::getXData));
        for (Map.Entry<Double, List<BasicData>> entry : map.entrySet()) {
            dataList.add(entry.getValue().get(0));
        }
        return dataList.stream().sorted(Comparator.comparing(BasicData::getXData)).collect(Collectors.toList());
    }



    /**
     * y=ax2+bx+c;
     *
     * @param a
     * @param b
     * @param c
     * @param x
     * @return
     */
    private Double calculate(Double a, Double b, Double c, Double x) {
        if (Objects.isNull(a)) {
            a = 0.0;
        }
        if (Objects.isNull(b)) {
            b = 0.0;
        }
        if (Objects.isNull(c)) {
            c = 0.0;
        }

        return CommonUtils.calcDouble(a * x * x + b * x, c, EnumOperationType.ADD.getId());
    }
}
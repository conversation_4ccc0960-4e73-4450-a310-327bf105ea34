package com.cet.eem.bll.energysaving.dao.weather.impl;

import com.cet.eem.bll.common.model.domain.subject.energysaving.ColdPredict;
import com.cet.eem.bll.energysaving.dao.weather.ColdPredictDao;
import com.cet.eem.bll.energysaving.model.def.ColdOptimizationLabelDef;
import com.cet.eem.common.ParamUtils;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import com.cet.eem.model.base.ModelSingeWriteVo;
import com.cet.eem.model.tool.ModelServiceUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.*;

/**
 * @ClassName : ColdPredictDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-16 16:36
 */
@Repository
public class ColdPredictDaoImpl extends ModelDaoImpl<ColdPredict> implements ColdPredictDao {
    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Override
    public List<ColdPredict> querySingleDeviceData(LocalDateTime st, LocalDateTime et, Integer type, Integer predictType, Long roomId,Long projectId) {
        LambdaQueryWrapper<ColdPredict> wrapper = LambdaQueryWrapper.of(ColdPredict.class);
        wrapper.ge(ColdPredict::getLogTime, st)
                .lt(ColdPredict::getLogTime, et)
                .eq(ColdPredict::getColdLoadType, type)
                .eq(ColdPredict::getProjectId, projectId)
                .eq(ColdPredict::getRoomId, roomId);
        if (Objects.nonNull(predictType)) {
            wrapper.eq(ColdPredict::getPredictDataType, predictType);
        }
        return this.selectList(wrapper);
    }

    @Override
    public List<ColdPredict> queryColdPredictData(Collection<Integer> coldLoadTypes, Collection<Integer> dataTypes, Collection<LocalDateTime> logTimes, Long roomId) {
        LambdaQueryWrapper<ColdPredict> wrapper = LambdaQueryWrapper.of(ColdPredict.class)
                .eq(ColdPredict::getRoomId, roomId)
                .in(ColdPredict::getColdLoadType, coldLoadTypes)
                .in(ColdPredict::getPredictDataType, dataTypes)
                .in(ColdPredict::getLogTime, logTimes);
        List<ColdPredict> predicts = this.selectList(wrapper);
        if (CollectionUtils.isEmpty(predicts)) {
            return Collections.emptyList();
        }
        return predicts;
    }

    @Override
    public List<ColdPredict> queryColdPredictData(LocalDateTime st, LocalDateTime et, Integer type, List<Integer> predictType, Long roomId,Long projectId) {
        LambdaQueryWrapper<ColdPredict> wrapper = LambdaQueryWrapper.of(ColdPredict.class);
        wrapper.ge(ColdPredict::getLogTime, st)
                .lt(ColdPredict::getLogTime, et)
                .eq(ColdPredict::getColdLoadType, type)
                .eq(ColdPredict::getProjectId, projectId)
                .eq(ColdPredict::getRoomId, roomId);
        if (CollectionUtils.isNotEmpty(predictType)) {
            wrapper.in(ColdPredict::getPredictDataType, predictType);
        }
        List<ColdPredict> predicts = this.selectList(wrapper);
        if (CollectionUtils.isEmpty(predicts)) {
            return Collections.emptyList();
        }
        return predicts;
    }

    @Override
    public List<ColdPredict> queryColdPredictData(Collection<Integer> coldLoadTypes, Collection<Integer> dataTypes, LocalDateTime st, LocalDateTime et, Long roomId) {
        LambdaQueryWrapper<ColdPredict> wrapper = LambdaQueryWrapper.of(ColdPredict.class)
                .eq(ColdPredict::getRoomId, roomId)

                .ge(ColdPredict::getLogTime, st)
                .lt(ColdPredict::getLogTime, et);
        if (CollectionUtils.isNotEmpty(coldLoadTypes)) {
            wrapper.in(ColdPredict::getColdLoadType, coldLoadTypes);
        }
        if (CollectionUtils.isNotEmpty(dataTypes)) {
            wrapper.in(ColdPredict::getPredictDataType, dataTypes);
        }
        List<ColdPredict> predicts = this.selectList(wrapper);
        if (CollectionUtils.isEmpty(predicts)) {
            return Collections.emptyList();
        }
        return predicts;
    }

    @Override
    public List<ColdPredict> queryColdPredictData(Collection<Integer> coldLoadTypes, Collection<Integer> dataTypes, LocalDateTime st, LocalDateTime et, List<Long> roomId) {
        LambdaQueryWrapper<ColdPredict> wrapper = LambdaQueryWrapper.of(ColdPredict.class)
                .in(ColdPredict::getRoomId, roomId)
                .in(ColdPredict::getColdLoadType, coldLoadTypes)
                .in(ColdPredict::getPredictDataType, dataTypes)
                .ge(ColdPredict::getLogTime, st)
                .lt(ColdPredict::getLogTime, et);
        List<ColdPredict> predicts = this.selectList(wrapper);
        if (CollectionUtils.isEmpty(predicts)) {
            return Collections.emptyList();
        }
        return predicts;
    }

    @Override
    public List<ColdPredict> write(List<ColdPredict> predicts) {
        if (CollectionUtils.isEmpty(predicts)) {
            return Collections.emptyList();
        }
        List<List<Object>> addDataList = new ArrayList<>();
        List<List<Object>> updateDataList = new ArrayList<>();
        List<List<Object>> updateIds = new ArrayList<>();
        for (ColdPredict connection : predicts) {
            List<Object> dataList = new ArrayList<>();
            if (ParamUtils.checkPrimaryKeyValid(connection.getId())) {
                updateIds.add(Collections.singletonList(connection.getId()));
                updateDataList.add(dataList);
            } else {
                addDataList.add(dataList);
            }

            dataList.add(connection.getRoomId());
            dataList.add(connection.getProjectId());
            dataList.add(connection.getValue());
            dataList.add(connection.getLogTime());
            dataList.add(connection.getColdLoadType());
            dataList.add(connection.getPredictDataType());
        }

        List<Map<String, Object>> result = new ArrayList<>();
        ModelSingeWriteVo writeResult;
        if (com.cet.eem.toolkit.CollectionUtils.isNotEmpty(addDataList)) {
            writeResult = modelServiceUtils.writeDataBatch(ModelLabelDef.COLD_PREDICT, true, getQuantityObjectFields(), addDataList, null);
            result.addAll(modelServiceUtils.convertModelSingeWrite(writeResult));
        }

        if (com.cet.eem.toolkit.CollectionUtils.isNotEmpty(updateDataList)) {
            writeResult = modelServiceUtils.writeDataBatch(ModelLabelDef.COLD_PREDICT, false, getQuantityObjectFields(), updateDataList, updateIds);
            result.addAll(modelServiceUtils.convertModelSingeWrite(writeResult));
        }

        return JsonTransferUtils.transferList(result, ColdPredict.class);
    }

    private List<String> getQuantityObjectFields() {
        List<String> result = new ArrayList<>();

        result.add(ColumnDef.ROOM_ID);
        result.add(ColumnDef.PROJECT_ID);
        result.add(ColumnDef.VALUE);
        result.add(ColumnDef.LOGTIME);
        result.add(ColdOptimizationLabelDef.COLD_LOAD_TYPE);
        result.add(ColdOptimizationLabelDef.PREDICT_DATA_TYPE);

        return result;
    }
}
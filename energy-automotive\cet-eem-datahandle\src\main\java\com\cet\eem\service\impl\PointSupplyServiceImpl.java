package com.cet.eem.service.impl;

import com.cet.eem.def.AggregationCycle;
import com.cet.eem.def.DataHandleDef;
import com.cet.eem.model.param.DataHandleParam;
import com.cet.eem.model.param.RepairPointFlinkParam;
import com.cet.eem.model.param.RepairPointLineParam;
import com.cet.eem.model.param.RepairPointWebParam;
import com.cet.eem.model.param.RepairPointParam;
import com.cet.eem.model.param.TimeValueParam;
import com.cet.eem.model.vo.DataLogValue;
import com.cet.eem.service.PointSupplyService;
import com.cet.eem.util.NumberUtil;
import com.cet.eem.util.TimeUtil;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 增补点处理类
 *
 * <AUTHOR>
 * @date 2024/6/18 11:06
 */
public class PointSupplyServiceImpl implements PointSupplyService {

    //一周的毫秒数
    public static final Long WEEK_TIME_MILLS = 7 * 24 * 60 * 60 * 1000L;

    /**
     * 根据参考数据和指定时间范围，修复指定时间范围内的数据点。
     * 本方法主要用于处理数据缺失或需要插值的情况，通过参考相邻的完整数据来推算缺失数据的值。
     *
     * @return 返回修复后的数据点列表。
     */
    @Override
    public List<DataLogValue> repairPointForFlink(RepairPointFlinkParam flinkParam,
                                                  DataHandleParam handleParam) {
//        handleTurnOverOrChangeMeter(flinkParam, handleParam);
        //是否需要补15分钟的点
        boolean needRepair = judgeIfNeedRepairFifteenPoints(flinkParam.getStartTime(), flinkParam.getEndTime());
        if (!needRepair) {
            return Collections.emptyList();
        }
        //处理翻转换表的情况
        List<DataLogValue> datalogValues = handleTurnOverOrChangeMeter(flinkParam.getReferenceDataLogList());
        flinkParam.setReferenceDataLogList(datalogValues);
        Map<Long, Double> referenceMap = datalogValues.stream().collect(HashMap::new, (m, v) -> m.put(v.getTime(), v.getValue()), HashMap::putAll);
        boolean canRepairByLastWeek = judgeCanRepairByLastWeek(referenceMap, flinkParam.getReferenceStartTime(), flinkParam.getReferenceEndTime());
        //参考值缺失 采用线性补的方式
        if (!canRepairByLastWeek) {
            return repairPointByLine(genRepairPointByLineParam(flinkParam), handleParam);
        }
        List<Long> times = TimeUtil.getTimeRange(flinkParam.getStartTime(), flinkParam.getEndTime() + 1, AggregationCycle.FIVE_MINUTES);
        //计算缺失数据
        return repairPointByHandleOverWeek(referenceMap, handleParam.getTurnOverEng(), times, flinkParam.getStartTime(), flinkParam.getEndTime(), flinkParam.getStartValue(), flinkParam.getEndValue(), flinkParam.getReferenceStartTime(), flinkParam.getReferenceEndTime());
    }

    private RepairPointLineParam genRepairPointByLineParam(RepairPointFlinkParam flinkParam) {
        RepairPointLineParam param = new RepairPointLineParam();
        param.setStartTime(flinkParam.getStartTime());
        param.setStartValue(flinkParam.getStartValue());
        param.setEndTime(flinkParam.getEndTime());
        param.setEndValue(flinkParam.getEndValue());
        return param;
    }

    private RepairPointLineParam genRepairPointByLineParam(RepairPointWebParam webParam) {
        RepairPointLineParam param = new RepairPointLineParam();
        param.setStartTime(webParam.getStartTime());
        param.setStartValue(webParam.getStartValue());
        param.setEndTime(webParam.getEndTime());
        param.setEndValue(webParam.getEndValue());
        return param;
    }

    /**
     * 判断是否可以采用典型日的形式
     * todo 需要判断从参考起始时间开始的一周 是否能够用于补录 若能 后续所有周 都使用这一周的数据进行补录 否则进行线性补录
     *
     * @return 是否可以采用典型日的形式
     */
    private boolean judgeCanRepairByLastWeek(Map<Long, Double> referenceMap,
                                             Long referenceStartTime,
                                             Long referenceEndTime) {
        Double startValue = referenceMap.get(referenceStartTime);
        //起始值为空采用线性修正
        if (Objects.isNull(startValue)) {
            return false;
        }
        Long nextWeek = TimeUtil.addDateTimeByCycle(referenceStartTime, AggregationCycle.SEVEN_DAYS, 1);
        //如果结束值为空 需要判断是否超出7天
        //如果没有超出7天 则采用线性修正的方式
        //如果超出7天则采用 则按照7天为1一个周期 用上一个周期的补的点 修下一个周期的点
        long differenceTime = referenceEndTime - referenceStartTime;
        long sevenDaysTime = 7 * 24 * 60 * 60 * 1000L;
        //超出7天 且后值大于前值
        if (differenceTime >= sevenDaysTime) {
            return Objects.nonNull(referenceMap.get(nextWeek)) &&
                    Objects.equals(NumberUtil.compare(referenceMap.get(nextWeek), startValue, DataHandleDef.DATA_HANDLE_SCALE), 1);
        }
        //没超出7天 且后值大于前值
        return Objects.nonNull(referenceMap.get(referenceEndTime)) &&
                Objects.equals(NumberUtil.compare(referenceMap.get(referenceEndTime), startValue, DataHandleDef.DATA_HANDLE_SCALE), 1);
    }

    /**
     * 处理换表或者翻转的情况
     * <p>
     * todo 需要考虑连续多次换表的情况
     *
     * @param datalogValues 定时记录
     */
    @Override
    public List<DataLogValue> handleTurnOverOrChangeMeter(List<DataLogValue> datalogValues) {
        if (CollectionUtils.isEmpty(datalogValues)) {
            return Collections.emptyList();
        }
        datalogValues.sort(Comparator.comparing(DataLogValue::getTime));
        List<DataLogValue> results = new ArrayList<>();
        results.add(datalogValues.get(0));
        //换表值 出现换表的情况时  需要加上该值
        double oldBeforeValue = datalogValues.get(0).getValue();
        //存在翻转或者换表的情况
        for (int i = 1; i <= datalogValues.size() - 1; i++) {
            //前值
            double beforeValue = datalogValues.get(i - 1).getValue();
            //后值
            double nextValue = datalogValues.get(i).getValue();
            //出现换表
            if (nextValue < beforeValue) {
                DataLogValue newDatalogValue = new DataLogValue();
                newDatalogValue.setTime(datalogValues.get(i).getTime());
                //换表默认能耗为0
                newDatalogValue.setValue(oldBeforeValue);
                newDatalogValue.setStatus(datalogValues.get(i).getStatus());
                results.add(newDatalogValue);
                continue;
            }
            //正常值
            //得到新值 新值等于换表后的差值+换表前值
            double newNextValue = nextValue - beforeValue + oldBeforeValue;
            DataLogValue newDatalogValue = new DataLogValue();
            newDatalogValue.setTime(datalogValues.get(i).getTime());
            newDatalogValue.setValue(newNextValue);
            newDatalogValue.setStatus(datalogValues.get(i).getStatus());
            results.add(newDatalogValue);
            //更新换表前值
            oldBeforeValue = newNextValue;
        }
        return results;
    }

    @Override
    public List<DataLogValue> repairPointForWeb(RepairPointWebParam webParam,
                                                DataHandleParam handleParam) {
//        //处理换表
//        handleTurnOverOrChangeMeter(webParam, handleParam);
        //处理起始时间和结束时间
        handleStartAndEndTimeForParam(webParam);
        //校验是否可以进行典型日的补录
        boolean canRepair = checkCanRepairByLastWeek(webParam);
        if (!canRepair) {
            return repairPointByLine(genRepairPointByLineParam(webParam), handleParam);
        }
        //设置真实的起始时间和结束时间
        handleRepairPointWebParamBySetStartEndValue(webParam);
        List<DataLogValue> result = new ArrayList<>();
        webParam.getParams().forEach(param -> {
            RepairPointFlinkParam flinkParam = new RepairPointFlinkParam();
            flinkParam.setReferenceDataLogList(param.genReferenceDataLog());
            flinkParam.setStartTime(param.getStartTime());
            flinkParam.setEndTime(param.getEndTime());
            flinkParam.setStartValue(param.getStartValue());
            flinkParam.setEndValue(param.getEndValue());
            flinkParam.setReferenceStartTime(param.genReferenceStartTime());
            flinkParam.setReferenceEndTime(param.genReferenceEndTime());
            List<DataLogValue> dataLogValues = repairPointForFlink(flinkParam, handleParam);
            result.addAll(dataLogValues);
        });
        return result;
    }

    private void handleRepairPointWebParamBySetStartEndValue(RepairPointWebParam webParam) {
        Map<Long, Double> realValueMap = new HashMap<>();
        realValueMap.put(webParam.getStartTime(), webParam.getStartValue());
        realValueMap.put(webParam.getEndTime(), webParam.getEndValue());
        double finalStartValue = webParam.getStartValue();
        double sumValue = webParam.genReferenceSumValue();
        webParam.getParams().forEach(param -> {
            double paramStartValue = realValueMap.get(param.getStartTime());
            double paramEndValue = paramStartValue + (param.genReferenceValue() / sumValue) * (webParam.getEndValue() - finalStartValue);
            realValueMap.put(param.getEndTime(), paramEndValue);
            param.setStartValue(paramStartValue);
            param.setEndValue(paramEndValue);
        });
    }

    private boolean checkCanRepairByLastWeek(RepairPointWebParam webParam) {
        boolean success = checkTimeContinuous(webParam.getParams());
        double sumValue = webParam.genReferenceSumValue();
        //时间连续 参考数据递增 且缺点数据递增 才允许走典型日
        return success && sumValue > 0d && Objects.equals(NumberUtil.compare(webParam.getEndValue(), webParam.getStartValue(), DataHandleDef.DATA_HANDLE_SCALE), 1);
    }

    private void handleTurnOverOrChangeMeter(TimeValueParam timeValueParam,
                                             DataHandleParam handleParam) {
        //数据是递增的  不需要处理
        if (timeValueParam.getStartValue() <= timeValueParam.getEndValue()) {
            return;
        }
        //否则进行判断 是换表  还是翻转
        long minute = (timeValueParam.getEndTime() - timeValueParam.getStartTime()) / (60 * 1000L);
        //按照翻转处理
        double differenceValue = (timeValueParam.getEndValue() - timeValueParam.getStartValue() + handleParam.getTurnOverEng()) % handleParam.getTurnOverEng();
        double validDifference = minute * handleParam.getMaxDifEng();
        //实际差值大于合理差值 判断为换表 换表起始值设置为0
        if (differenceValue > validDifference) {
            timeValueParam.setStartValue(0d);
        }
    }

    /**
     * 是否是换表
     *
     * @param timeValueParam 补点参数
     * @param handleParam    异常处理参数
     */
    private boolean isChangeMeter(TimeValueParam timeValueParam,
                                  DataHandleParam handleParam) {
        //数据是递增的  不需要处理
        if (timeValueParam.getStartValue() <= timeValueParam.getEndValue()) {
            return false;
        }
        //否则进行判断 是换表  还是翻转
        long minute = (timeValueParam.getEndTime() - timeValueParam.getStartTime()) / (60 * 1000L);
        //按照翻转处理
        double differenceValue = (timeValueParam.getEndValue() - timeValueParam.getStartValue() + handleParam.getTurnOverEng()) % handleParam.getTurnOverEng();
        double validDifference = minute * handleParam.getMaxDifEng();
        //实际差值大于合理差值 判断为换表 换表起始值设置为0
        if (differenceValue > validDifference) {
            return true;
        }
        return false;
    }

    private void handleStartAndEndTimeForParam(RepairPointWebParam webParam) {
        //按照启动时间排序
        List<RepairPointParam> collect = webParam.getParams().stream()
                .sorted(Comparator.comparing(RepairPointParam::getStartTime))
                .collect(Collectors.toList());
        webParam.setParams(collect);
        collect.forEach(param -> {
            param.getDataLogValues().sort(Comparator.comparing(DataLogValue::getTime));
            if (param.getStartTime() < webParam.getStartTime() && param.getEndTime() > webParam.getStartTime()) {
                param.setStartTime(webParam.getStartTime());
            }
            if (param.getEndTime() > webParam.getEndTime() && param.getStartTime() < webParam.getEndTime()) {
                param.setEndTime(webParam.getEndTime());
            }
        });
    }

    /**
     * 检查维修点时间是否连续。
     * 通过比较列表中相邻维修点的结束时间和开始时间，确保时间上没有间隔，即连续。
     *
     * @param collect 维修点参数列表，包含每个维修点的开始和结束时间。
     * @return 如果所有维修点的时间连续，则返回true；否则返回false。
     */
    private boolean checkTimeContinuous(List<RepairPointParam> collect) {
        // 检查列表是否为空，为空则时间不可能连续，直接返回false。
        if (CollectionUtils.isEmpty(collect)) {
            return false;
        }

        // 对维修点参数列表按开始时间进行排序，确保比较的时间顺序正确。
        collect = collect.stream().sorted(Comparator.comparing(RepairPointParam::getStartTime)).collect(Collectors.toList());

        // 遍历排序后的列表，检查相邻维修点的时间是否连续。
        for (int i = 0; i < collect.size() - 1; i++) {
            Long lastEndTime = collect.get(i).getEndTime();
            Long nextStartTime = collect.get(i + 1).getStartTime();

            // 如果相邻维修点的结束时间和开始时间不相等，则时间不连续，返回false。
            // 不连续 缺失参考数据
            if (!Objects.equals(lastEndTime, nextStartTime)) {
                return false;
            }
        }

        // 如果所有相邻维修点的时间都连续，则返回true。
        return true;
    }


    /**
     * 判断中间是否缺失15分钟的点 因为增补点只补15分钟的点
     *
     * @param startTime 起始时间
     * @param endTime   结束时间
     * @return 是否需要补15分钟的点
     */
    public boolean judgeIfNeedRepairFifteenPoints(Long startTime, Long endTime) {
        long fifteenMinutes = 15 * 60 * 1000L;
        //正好是两个15分钟  不需要补点
        if (endTime % fifteenMinutes == 0 && startTime % fifteenMinutes == 0 && endTime - startTime == fifteenMinutes) {
            return false;
        }

        long startFifteenMinutes = startTime / fifteenMinutes;
        long endFifteenMinutes = endTime / fifteenMinutes;
        return !Objects.equals(startFifteenMinutes, endFifteenMinutes);
    }

    /**
     * 环比上周失败 采用线性平均的方式增补点
     * 后值比前值小 将缺点 全都设置成前值
     *
     * @return 增补点
     */
    @Override
    public List<DataLogValue> repairPointByLine(RepairPointLineParam lineParam,
                                                DataHandleParam handleParam) {
        //是否是换表
        boolean changeMeter = isChangeMeter(lineParam, handleParam);
        //考虑翻转的情况
        double difference = (lineParam.getEndValue() - lineParam.getStartValue() + handleParam.getTurnOverEng()) % handleParam.getTurnOverEng();
        List<Long> times = TimeUtil.getTimeRange(lineParam.getStartTime(), lineParam.getEndTime() + 1, AggregationCycle.FIVE_MINUTES);
        int size = times.size();
        //差值除以5分钟的个数 5分钟的个数要减1 如果是换表 后续缺点都设置成第一个点
        double perValueFiveMinutes = changeMeter ? 0D : (difference) / (size - 1);
        List<DataLogValue> result = new ArrayList<>();
        Map<Long, Double> valueMap = new HashMap<>();
        valueMap.put(lineParam.getStartTime(), lineParam.getStartValue());
        valueMap.put(lineParam.getEndTime(), lineParam.getEndValue());
        //i是后值 j是前值
        for (int i = 1, j = 0; i <= size - 1 && j <= size - 2; ) {
            Long iTime = times.get(i);
            Long jTime = times.get(j);
            boolean isFifteenMinutes = isFifteenMinutes(iTime);
            //如果不是15分钟 继续往后找
            if (!isFifteenMinutes) {
                i++;
                continue;
            }
            //是15分钟
            double jValue = valueMap.get(jTime);
            //考虑翻转的情况 取余
            double iValue = (jValue + perValueFiveMinutes * (i - j)) % handleParam.getTurnOverEng();
            valueMap.put(iTime, iValue);
            DataLogValue DataLogValue = new DataLogValue();
            DataLogValue.setTime(iTime);
            DataLogValue.setValue(iValue);
            DataLogValue.setStatus(DataHandleDef.STATUS_LINEAR_COMPENSATION);
            result.add(DataLogValue);
            //前值更新至后值
            j = i;
            //后值继续向前推进
            i++;
        }
        return result;
    }

    /**
     * 判断时间是否是15分钟的整点
     *
     * @param logTime 时间
     * @return 是否是整15分钟
     */
    private boolean isFifteenMinutes(long logTime) {
        long left = logTime % (15 * 60 * 1000L);
        return Objects.equals(left, 0L);
    }


    /**
     * 修正点  增加超出7天的逻辑  超出7天用第一个7天的参考数据 补后续所有7天的参考数据
     *
     * @param referenceMap       参考值列表
     * @param turnOVerEng        翻转值
     * @param times              实际时间
     * @param startTime          实际起始时间
     * @param endTime            实际结束时间
     * @param startValue         实际起始值
     * @param endValue           实际结束值
     * @param referenceStartTime 参考起始时间
     * @param referenceEndTime   参考结束时间
     * @return 修正点
     */
    private List<DataLogValue> repairPointByHandleOverWeek(Map<Long, Double> referenceMap, double turnOVerEng,
                                                           List<Long> times,
                                                           long startTime, long endTime,
                                                           double startValue, double endValue,
                                                           long referenceStartTime, long referenceEndTime) {
        //参考起始时间下一周时间
        Long nextWeek = TimeUtil.addDateTimeByCycle(referenceStartTime, AggregationCycle.SEVEN_DAYS, 1);
        //超出7天按照 7天的趋势进行修正
        long newReferenceEndTime = (referenceEndTime - referenceStartTime) > 7 * 24 * 60 * 60 * 1000L ? nextWeek : referenceEndTime;
        List<Long> newReferenceTimes = TimeUtil.getTimeRange(referenceStartTime, newReferenceEndTime + 1000, AggregationCycle.FIVE_MINUTES);
        //参考起始值
        Double newReferenceStartValue = referenceMap.get(referenceStartTime);
        //参考结束值
        Double newReferenceEndValue = referenceMap.get(newReferenceEndTime);
        return repairPointByLastWeek(referenceMap, turnOVerEng, times, newReferenceTimes, startTime, endTime,
                startValue, endValue, referenceStartTime, newReferenceEndTime, newReferenceStartValue, newReferenceEndValue);
    }

    //需要处理中间跨周的情况
    //realDifference可能超出一周了

    /**
     * 对于中间跨周的情况  计算一周的真实值差 不能简单的相减  需要按照比例计算
     *
     * @param referenceMap       参考值列表
     * @param referenceStartTime 参考起始时间
     * @param referenceEndTime   参考结束时间
     * @param startTime          实际起始时间
     * @param endTime            实际结束时间
     * @param realDifference     实际差值
     * @return 相较于参考时间的实际差值
     */
    private double calcRealDifference(Map<Long, Double> referenceMap,
                                      Long referenceStartTime, Long referenceEndTime,
                                      Long startTime, Long endTime,
                                      Double realDifference, Double turnOVerEng) {
        Double referenceStartValue = referenceMap.get(referenceStartTime);
        Double referenceEndValue = referenceMap.get(referenceEndTime);
        //参考值对应的差值
        double referenceTotalDifference = (referenceEndValue - referenceStartValue + turnOVerEng) % turnOVerEng;
        //计算中间跨了几周
        int week = (int) ((endTime - startTime) / WEEK_TIME_MILLS);
        //真实的结束时间按周平移到参考时间对应的时间
        Long realEndCompareReference = TimeUtil.addDateTimeByCycle(endTime, AggregationCycle.SEVEN_DAYS, -week - 1);
        //平移之后对应的值
        Double realEndCompareReferenceValue = referenceMap.get(realEndCompareReference);
        double difference = (realEndCompareReferenceValue - referenceStartValue + turnOVerEng) % turnOVerEng;
        return realDifference * (referenceTotalDifference) / (week * (referenceTotalDifference) + difference);
    }

    /**
     * 根据典型日计算缺失数据
     * <p>
     * todo 需要处理参考值是修补点的场景  参考时间和参考值  需要特殊处理一下
     *
     * @param referenceMap        参考数据
     * @param turnOVerEng         翻转值
     * @param times               缺失时间
     * @param referenceTimes      参考时间
     * @param startTime           缺失起始时间
     * @param endTime             缺失结束时间
     * @param startValue          缺失起始值
     * @param endValue            缺失结束值
     * @param referenceStartTime  参考起始时间
     * @param referenceEndTime    参考结束时间
     * @param referenceStartValue 参考起始值
     * @param referenceEndValue   参考结束值
     * @return 缺失数据
     */
    private List<DataLogValue> repairPointByLastWeek(Map<Long, Double> referenceMap, double turnOVerEng,
                                                     List<Long> times,
                                                     List<Long> referenceTimes,
                                                     long startTime, long endTime,
                                                     double startValue, double endValue,
                                                     long referenceStartTime, long referenceEndTime,
                                                     double referenceStartValue, double referenceEndValue) {
        //当前值差值 需要考虑翻转值的情况
        double realTotalDifference = (endValue - startValue + turnOVerEng) % turnOVerEng;
        if (endTime - startTime > WEEK_TIME_MILLS) {
            realTotalDifference = calcRealDifference(referenceMap, referenceStartTime, referenceEndTime,
                    startTime, endTime, realTotalDifference, turnOVerEng);
        }
        //参考值对应的差值
        double referenceTotalDifference = (referenceEndValue - referenceStartValue + turnOVerEng) % turnOVerEng;
        //当前值数据
        Map<Long, Double> realMap = new HashMap<>();
        realMap.put(startTime, startValue);
        realMap.put(endTime, endValue);
        List<DataLogValue> result = new ArrayList<>();

        //i是后值 j是前值
        for (int i = 1, j = 0; i <= times.size() - 1 && j <= times.size() - 2; ) {
            Long realEt = times.get(i);
            Long realSt = times.get(j);
            boolean isFifteenMinutes = isFifteenMinutes(realEt);
            //如果不是15分钟 继续往后找
            if (!isFifteenMinutes) {
                i++;
                continue;
            }
            //当取余为0时 不能设置为0 因为i比j要大  取余为0时 需要设置为MAX
            int newI = getIIndex(i, referenceTimes.size());
            int newJ = getJIndex(j, referenceTimes.size());
            Long referenceEt = referenceTimes.get(newI);
            Long referenceSt = referenceTimes.get(newJ);
            Double eValue = referenceMap.get(referenceEt);
            //后值为空 向后继续推进
            if (Objects.isNull(eValue)) {
                i++;
                continue;
            }
            //后值不为空,计算经验值
            Double sValue = referenceMap.get(referenceSt);
            //参考值差值 需要考虑翻转的情况
            double referenceDifference = (eValue - sValue + turnOVerEng) % turnOVerEng;
            //真实值差值
            double realDifference = (referenceDifference / referenceTotalDifference) * realTotalDifference;
            //j对应的真实值
            double realJValue = realMap.get(realSt);
            //i对应的真实值数据 考虑翻转的情况
            double realIValue = (realJValue + realDifference) % turnOVerEng;
            //计算缺失的点
            List<DataLogValue> repairPoints = repairPoint(times, j, i, realJValue, realIValue, turnOVerEng);
            result.addAll(repairPoints);
            Map<Long, Double> repairMap = repairPoints.stream().collect(Collectors.toMap(DataLogValue::getTime, DataLogValue::getValue));
            realMap.putAll(repairMap);

            //将前值推进至后值位置
            j = i;
            //后值继续向后推进
            i++;
        }
        return result;
    }

    /**
     * 获取后值的索引
     *
     * @param i    后值索引
     * @param size 参考数据数量
     * @return 后值索引
     */
    private int getIIndex(int i, int size) {
        return (i - 1) % (size - 1) + 1;
    }

    /**
     * 获取前值的索引
     *
     * @return 前值索引
     */
    private int getJIndex(int j, int size) {
        if (j % (size - 1) == 0) {
            return 0;
        }
        return (j - 1) % (size - 1) + 1;
    }

    /**
     * 差值增补点
     *
     * @param times      时间列表
     * @param startIndex 起始索引
     * @param endIndex   结束索引
     * @param startValue 起始值
     * @param endValue   结束值
     * @return 增补点
     */
    private List<DataLogValue> repairPoint(List<Long> times, int startIndex, int endIndex, double startValue, double endValue, double turnOVerEng) {
        double difference = (endValue - startValue + turnOVerEng) % turnOVerEng;
        double differencePerIndex = (difference) / (endIndex - startIndex);
        List<DataLogValue> result = new ArrayList<>();
        for (int i = startIndex + 1; i <= endIndex; i++) {
            double value = (startValue + (i - startIndex) * differencePerIndex) % turnOVerEng;

            long logTime = times.get(i);

            boolean isFifteenMinutes = isFifteenMinutes(logTime);
            //不是15分钟不需要进行处理
            if (!isFifteenMinutes) {
                continue;
            }

            DataLogValue DataLogValue = new DataLogValue();
            DataLogValue.setTime(logTime);
            DataLogValue.setValue(value);
            DataLogValue.setStatus(DataHandleDef.STATUS_LINEAR_COMPENSATION);
            result.add(DataLogValue);
        }
        return result;
    }

}

package com.cet.eem.depletion.dao.impl;

import com.cet.eem.depletion.dao.DepletionpartitionDao;
import com.cet.eem.depletion.model.Constant.Constant;
import com.cet.eem.depletion.model.DepletionPartitionDto;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class DepletionpartitionDaoImpl implements DepletionpartitionDao {

    @Autowired
    ModelServiceUtils modelServiceUtils;


    @Override
    public List<DepletionPartitionDto> queryByParam(Long st, Long end, Long objId, String label) {
        QueryCondition condition = new QueryConditionBuilder<>(Constant.DEPLETIONPARTITION)
                .eq(Constant.OBJECTID, objId)
                .eq(Constant.OBJECTLABEL, label)
                .ge(Constant.LOGTIME, st)
                .lt(Constant.LOGTIME, end)
                .build();
        return modelServiceUtils.query(condition, DepletionPartitionDto.class);
    }

    @Override
    public List<DepletionPartitionDto> queryByParam(Long st, Long end, Long objId, String label, List<Integer> depletiontype) {
        QueryCondition condition = new QueryConditionBuilder<>(Constant.DEPLETIONPARTITION)
                .eq(Constant.OBJECTID, objId)
                .eq(Constant.OBJECTLABEL, label)
                .ge(Constant.LOGTIME, st)
                .lt(Constant.LOGTIME, end)
                .in(Constant.DEPLETIONTYPE, depletiontype)
                .build();
        return modelServiceUtils.query(condition, DepletionPartitionDto.class);
    }
}

package com.cet.eem.bll.energysaving.model.config.system;

import com.cet.eem.bll.energysaving.model.config.ParameterConfig;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : ParameterConfigVo
 * @Description : 参数配置入参
 * <AUTHOR> jiang<PERSON><PERSON><PERSON>
 * @Date: 2022-06-30 18:27
 */
@Getter
@Setter
public class ParameterConfigVo extends ParameterConfig {


    @JsonProperty("startstoptimeinterval")
    private Double interval;

    @JsonProperty("runtimedifference")
    private Double difference;

}
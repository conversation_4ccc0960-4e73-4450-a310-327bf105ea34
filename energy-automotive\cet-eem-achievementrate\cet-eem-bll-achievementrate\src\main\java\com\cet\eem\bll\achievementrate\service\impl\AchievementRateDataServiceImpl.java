package com.cet.eem.bll.achievementrate.service.impl;

import com.cet.eem.auth.service.NodeCorrelationCheckService;
import com.cet.eem.auth.service.NodeManageWithAuthService;
import com.cet.eem.bll.achievementrate.dao.*;
import com.cet.eem.bll.achievementrate.model.*;
import com.cet.eem.bll.achievementrate.model.data.EnergyEfficiencyDataPlan;
import com.cet.eem.bll.achievementrate.model.data.ObjectCostValuePlan;
import com.cet.eem.bll.achievementrate.model.data.UnitObjectCostValuePlan;
import com.cet.eem.bll.achievementrate.model.def.AchievementRateDataDef;
import com.cet.eem.bll.achievementrate.model.def.AchievementRateType;
import com.cet.eem.bll.achievementrate.model.pojo.AchievementRate;
import com.cet.eem.bll.achievementrate.service.AchievementRateDataHandleService;
import com.cet.eem.bll.achievementrate.service.AchievementRateDataService;
import com.cet.eem.bll.common.dao.energy.EnergyConvertDao;
import com.cet.eem.bll.common.dao.node.NodeDao;
import com.cet.eem.bll.energy.dao.cost.ObjectCostValueDao;
import com.cet.eem.bll.common.dao.product.ProductionDataDao;
import com.cet.eem.bll.common.dao.product.UnitCostDao;
import com.cet.eem.bll.common.dao.project.ProductDao;
import com.cet.eem.bll.common.def.DataEntryTypeDef;
import com.cet.eem.bll.common.def.ProductLabel;
import com.cet.eem.bll.common.log.constant.EEMOperationLogType;
import com.cet.eem.bll.common.log.service.CommonUtilsService;
import com.cet.eem.bll.common.model.domain.object.physicalquantity.ProjectUnitClassify;
import com.cet.eem.bll.common.model.domain.object.physicalquantity.UserDefineUnit;
import com.cet.eem.bll.common.model.domain.subject.energy.ConvertedStandardCoalCoef;
import com.cet.eem.bll.common.model.domain.subject.energy.EnergyConsumption;
import com.cet.eem.bll.common.model.domain.subject.energy.EnergyConsumptionPlan;
import com.cet.eem.bll.common.model.domain.subject.energy.ProjectEnergyType;
import com.cet.eem.bll.common.model.domain.subject.energy.plan.ProductionPlan;
import com.cet.eem.bll.common.model.domain.subject.energyefficiency.EnergyEfficiencyData;
import com.cet.eem.bll.common.model.domain.subject.energyefficiency.EnergyEfficiencySetVo;
import com.cet.eem.bll.common.model.domain.subject.energyefficiency.KpiSetVo;
import com.cet.eem.bll.common.model.domain.subject.generalrules.FeeScheme;
import com.cet.eem.bll.common.model.domain.subject.production.*;
import com.cet.eem.bll.common.model.energy.QueryEnergyEfficiencyDataCriterial;
import com.cet.eem.bll.common.model.ext.modelentity.EemQueryCondition;
import com.cet.eem.bll.common.model.ext.objective.physicalquantity.UserDefineUnitSearchDto;
import com.cet.eem.bll.common.model.input.TypeInDataDetailVo;
import com.cet.eem.bll.common.model.product.ProductVo;
import com.cet.eem.bll.common.service.EnergyConvertService;
import com.cet.eem.bll.common.service.EnergyUnitService;
import com.cet.eem.bll.common.service.ProjectEnergyTypeService;
import com.cet.eem.bll.common.service.product.ProductService;
import com.cet.eem.bll.common.util.ExcelValidationUtils;
import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.bll.common.util.NodeFormatUtils;
import com.cet.eem.bll.energy.config.DataEntryConfig;
import com.cet.eem.bll.energy.dao.EnergyEfficiencyDataDao;
import com.cet.eem.bll.energy.dao.EnergyEfficiencySetDao;
import com.cet.eem.bll.energy.dao.KpiSetDao;
import com.cet.eem.bll.energy.dao.consumption.EnergyConsumptionDao;
import com.cet.eem.bll.energy.dao.consumption.EnergyConsumptionPlanDao;
import com.cet.eem.bll.energy.dao.cost.CostCheckNodeConfigDao;
import com.cet.eem.bll.energy.dao.cost.FeeSchemeDao;
import com.cet.eem.bll.energy.dao.impl.ProductionPlanDao;
import com.cet.eem.bll.energy.service.setting.SchemeConfigService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.constant.EnergyTypeDef;
import com.cet.eem.common.constant.EnumOperationType;
import com.cet.eem.common.constant.ExcelType;
import com.cet.eem.common.constant.OtherUnitTypeDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.definition.SplitCharDef;
import com.cet.eem.common.exception.ValidationException;
import com.cet.eem.common.file.FileUtils;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.ResultWithTotal;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.common.time.DateUtils;
import com.cet.eem.common.utils.PoiExcelUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.model.base.ConditionBlock;
import com.cet.eem.model.model.BaseEntity;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName : AchievementRateDataServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-11-22 13:48
 */
@Slf4j
@Service
public class AchievementRateDataServiceImpl implements AchievementRateDataService {
    @Autowired
    EnergyEfficiencyDataPlanDao energyEfficiencyDataPlanDao;
    @Autowired
    ObjectCostValuePlanDao objectCostValuePlanDao;
    @Autowired
    UnitObjectCostValuePlanDao unitObjectCostValuePlanDao;
    @Autowired
    EnergyEfficiencySetDao energyEfficiencySetDao;
    @Autowired
    KpiSetDao kpiSetDao;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    ProjectEnergyTypeService projectEnergyTypeService;
    @Autowired
    CostCheckNodeConfigDao costCheckNodeConfigDao;
    @Autowired
    FeeSchemeDao feeSchemeDao;
    @Resource
    DataEntryConfig dataEntryConfig;
    @Autowired
    NodeDao nodeDao;
    @Autowired
    EnergyConvertService energyConvertService;
    @Autowired
    ProductDao productDao;
    @Autowired
    EnergyUnitService energyUnitService;
    @Autowired
    EnergyConsumptionDao energyConsumptionDao;
    @Autowired
    EnergyConsumptionPlanDao energyConsumptionPlanDao;
    @Autowired
    ObjectCostValueDao objectCostValueDao;
    @Autowired
    UnitCostDao unitCostDao;
    @Autowired
    ProductionDataDao productionDataDao;
    @Autowired
    ProductionPlanDao productionPlanDao;
    @Autowired
    EnergyEfficiencyDataDao energyEfficiencyDataDao;
    @Autowired
    CommonUtilsService commonUtilsService;
    @Autowired
    EnergyConvertDao energyConvertDao;
    @Autowired
    GivenEnergyConsumptionPlanDao givenEnergyConsumptionPlanDao;
    @Autowired
    AchievementRateDataHandleService achievementRateDataHandleService;
    @Autowired
    NodeManageWithAuthService nodeManageBffService;
    @Autowired
    SchemeConfigService schemeConfigService;
    @Autowired
    private ProductService productService;
    @Autowired
    private AchievementRateDao achievementRateDao;

    @Autowired
    NodeCorrelationCheckService nodeCorrelationCheckService;

    public static final Integer UNIT_TYPE = 1;
    private static final String NODE_SHEET_NAME = "node_sheet";
    public static final Integer ACHIEVEMENT_TYPE_GS = 1;
    public static final Integer ACHIEVEMENT_TYPE_GZRC = 2;
    public static final Double CALCULATE = 2.0;
    public static final Double PERCENT = 100.0;
    @Value("${cet.eem.achievement.type}")
    private Integer type;
    public static final String NAN_VALUE = "--";
    public static final String PERCENT_SYMBOL = "%";
    public static final String DIAGONAL_SYMBOL = "/";
    public static final Integer ALLOWED_INPUT = 0;
    public static final Integer NOT_ALLOWED_INPUT = 1;
    public static final Integer CHOOSE = 1;
    public static final Integer YEAR_SIZE = 1;
    public static final Integer MONTH_SIZE = 2;
    public static final Integer DAY_SIZE = 3;
    public static final String SPILT_SYMBOL = "，";
    public static final String DATE_SPILT_SYMBOL = "~";

    @Override
    public List<AchievementRateDataNodeReturnVo> queryAchievementRateData(AchievementRateQueryVo queryVo) {
        List<AchievementRateDataNodeReturnVo> typeInDataVos;
        //单耗用能效的单位，费用类型用钱的基本单位，单位成本用钱基本单位/产品基本单位
        assembleQueryParam(queryVo, GlobalInfoUtils.getProjectId());
        switch (queryVo.getDataEntryType()) {
            case AchievementRateDataDef.ENERGY_CONSUMPTION_PLAN:
                typeInDataVos = queryEnergyConsumptionPlan(queryVo);
                break;
            case AchievementRateDataDef.EFF_PLAN:
                typeInDataVos = queryEnergyEfficiencyDataPlanData(queryVo);
                break;
            case AchievementRateDataDef.PRODUCT_PLAN:
                typeInDataVos = queryObjectCostValuePlanData(queryVo);
                break;
            case AchievementRateDataDef.UNIT_OBJECT_COST_PLAN:
                typeInDataVos = queryUnitObjectCostValuePlanData(queryVo);
                break;
            default:
                return Collections.emptyList();
        }

        return typeInDataVos;
    }

    private void assembleQueryParam(AchievementRateQueryVo queryVo, Long projectId) {
        switch (queryVo.getDataEntryType()) {
            case AchievementRateDataDef.ENERGY_CONSUMPTION_PLAN:
                List<ProjectEnergyType> projectEnergyTypes = projectEnergyTypeService.queryEnergyTypes(projectId, queryVo.getEnergyTypes());
                if (CollectionUtils.isNotEmpty(projectEnergyTypes)) {
                    List<Integer> energyTypes = projectEnergyTypes.stream().map(ProjectEnergyType::getEnergytype).collect(Collectors.toList());
                    queryVo.setEnergyTypes(energyTypes);
                }
                break;
            case AchievementRateDataDef.EFF_PLAN:
                List<KpiSetVo> kpiSetList = kpiSetDao.getKpiSetList(queryVo.getNodes());
                if (CollectionUtils.isEmpty(kpiSetList)) {
                    return;
                }
                List<Long> setIds = kpiSetList.stream().map(KpiSetVo::getEnergyefficiencyset_id).distinct().collect(Collectors.toList());

                List<EnergyEfficiencySetVo> energyEfficiencySetVos = energyEfficiencySetDao.queryEnergyEfficiencySetByIds(setIds);
                List<EnergyEfficiencySetVo> efficiencySetVos = energyEfficiencySetVos.stream()
                        .filter(energyEfficiencySetVo -> Objects.equals(energyEfficiencySetVo.getUnittype(), UNIT_TYPE)
                                && Objects.equals(energyEfficiencySetVo.getAggregationcycle(), queryVo.getAggregationCycle()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(efficiencySetVos)) {
                    return;
                }
                List<Long> setIdList = efficiencySetVos.stream().map(EnergyEfficiencySetVo::getId).distinct().collect(Collectors.toList());
                List<Integer> productTypes = efficiencySetVos.stream().map(EnergyEfficiencySetVo::getProducttype).distinct().collect(Collectors.toList());
                List<Integer> energyTypes = efficiencySetVos.stream().map(EnergyEfficiencySetVo::getEnergytype).distinct().collect(Collectors.toList());
                queryVo.setEffSetIds(setIdList);
                queryVo.setProductTypes(productTypes);
                queryVo.setEnergyTypes(energyTypes);
                break;
            case AchievementRateDataDef.PRODUCT_PLAN:
//                Set<Integer> integers = getEnergyTypeList(queryVo.getNodes());
//                queryVo.setEnergyTypes(new ArrayList<>(integers));
                break;
            case AchievementRateDataDef.UNIT_OBJECT_COST_PLAN:
//                Set<Integer> energyType = getEnergyTypeList(queryVo.getNodes());
//                Set<Integer> energyType = queryEnergyType(queryVo.getNodes());
//                queryVo.setEnergyTypes(new ArrayList<>(energyType));
                List<Product> products = productDao.queryProducts(projectId);
                List<Integer> product = products.stream().map(Product::getProductType).collect(Collectors.toList());
                queryVo.setProductTypes(product);
                break;
            default:
                return;
        }
        // 查询项目能源类型

    }

    @Override
    public List<AchievementRateReturnVo> inputAchievementRateData(AchievementRateInputDataVo inputDataVo) {
        List<AchievementRateReturnVo> typeInDataVos;

        switch (inputDataVo.getDataEntryType()) {
            case AchievementRateDataDef.ENERGY_CONSUMPTION_PLAN:
                typeInDataVos = inputEnergyConsumptionPlan(inputDataVo);
                break;
            case AchievementRateDataDef.EFF_PLAN:
                typeInDataVos = inputEnergyEfficiencyDataPlan(inputDataVo);
                break;
            case AchievementRateDataDef.PRODUCT_PLAN:
                typeInDataVos = inputObjectCostValuePlan(inputDataVo);
                break;
            case AchievementRateDataDef.UNIT_OBJECT_COST_PLAN:
                typeInDataVos = inputUnitObjectCostValuePlan(inputDataVo);
                break;
            default:
                return Collections.emptyList();
        }
        List<BaseVo> baseVos = nodeDao.queryNodeName(Collections.singletonList(new BaseVo(inputDataVo.getObjectId(), inputDataVo.getObjectLabel())));
        commonUtilsService.writeDeleteOperationLogs(EEMOperationLogType.DATA_ENTRY, assembleLogs(inputDataVo.getDataEntryType(), baseVos), new Object[]{}, GlobalInfoUtils.getUserId());
        return typeInDataVos;
    }

    private String assembleLogs(Integer dataEntryType, List<BaseVo> nodes) {
        StringBuilder builder = new StringBuilder();
        List<String> names = nodes.stream().map(BaseVo::getName).collect(Collectors.toList());
        String join = StringUtils.join(names, SPILT_SYMBOL);
        switch (dataEntryType) {
            case AchievementRateDataDef.ENERGY_CONSUMPTION_PLAN:
                builder.append("录入计划能耗数据，相关节点为").append(join);
                break;
            case AchievementRateDataDef.EFF_PLAN:
                builder.append("录入计划单耗数据，相关节点为").append(join);
                break;
            case AchievementRateDataDef.PRODUCT_PLAN:
                builder.append("录入计划成本数据，相关节点为").append(join);
                break;
            case AchievementRateDataDef.UNIT_OBJECT_COST_PLAN:
                builder.append("录入计划单台成本数据，相关节点为").append(join);
                break;
            default:
                return builder.toString();
        }
        return builder.toString();
    }

    private String assembleImportLogs(Integer dataEntryType, List<BaseVo> nodes, String fileName) {
        StringBuilder builder = new StringBuilder();
        List<String> names = nodes.stream().map(BaseVo::getName).collect(Collectors.toList());
        String join = StringUtils.join(names, SPILT_SYMBOL);
        builder.append("导入").append(getAchievementExportName(dataEntryType)).append(SPILT_SYMBOL).append("文件名为").append(fileName)
                .append(SPILT_SYMBOL).append("相关节点为").append(join);
        return builder.toString();
    }

    @Override
    public void exportAchievementRateData(AchievementRateQueryVo inputDataVo) {

        Assert.isTrue(inputDataVo.getNodes().size() <= dataEntryConfig.getMaxExportNodeNumber(),
                "导出的节点数量超出允许的最大数量！");
//        assembleQueryParam(inputDataVo, GlobalInfoUtils.getProjectId());
        exportEnergyEffPlan(inputDataVo);


    }

    @Override
    public void importAchievementRateData(MultipartFile multipartFile, int dataEntryType) {
        //多写校验
        switch (dataEntryType) {
            case AchievementRateDataDef.ENERGY_CONSUMPTION_PLAN:
                importEnergyConsumptionPlanData(multipartFile, dataEntryType);
                break;
            case AchievementRateDataDef.EFF_PLAN:
                importEnergyEffPlanData(multipartFile, dataEntryType);
                break;
            case AchievementRateDataDef.PRODUCT_PLAN:
                importObjectCostValuePlanData(multipartFile, dataEntryType);
                break;
            case AchievementRateDataDef.UNIT_OBJECT_COST_PLAN:
                importUnitObjectCostValuePlan(multipartFile, dataEntryType);
                break;
            default:
                return;
        }
    }

    @Override
    public String queryFormula() {
        String formula;
        if (Objects.equals(type, ACHIEVEMENT_TYPE_GS)) {
            formula = "达成率=(2-实际值/计划值)*100% ，即数值越大越好";
        } else {
            formula = "达成率=实际值/计划值*100% ，即数值越小越好";
        }
        return formula;
    }

    @Override
    public AchievementRateReturnDataWithUnit queryAchievementRateCompareData(AchievementRateQueryVo queryVo) {
        //4种情况查询数据，实际值和计划值
        AchievementRateReturnDataWithUnit result = new AchievementRateReturnDataWithUnit();
        switch (queryVo.getDataEntryType()) {
            case AchievementRateDataDef.ENERGY_CONSUMPTION_PLAN:
                result = queryCompareDataWithEnergyConsumptionPlan(queryVo);
                break;
            case AchievementRateDataDef.EFF_PLAN:
                result = queryCompareDataWithEffData(queryVo);
                break;
            case AchievementRateDataDef.PRODUCT_PLAN:
                result = queryCompareDataWithObjectCostValue(queryVo);
                break;
            case AchievementRateDataDef.UNIT_OBJECT_COST_PLAN:
                result = queryCompareDataWithUnitObjectCostValue(queryVo);
                break;
            default:
                return result;
        }
        setDatas(result, queryVo);
        return result;
    }

    /**
     * 页面达成率数据改为
     *
     * @param result
     */
    private void setDatas(AchievementRateReturnDataWithUnit result, AchievementRateQueryVo queryVo) {
        List<AchievementRateReturnDataVo> rateReturnDataVos = result.getRateReturnDataVos();
        if (CollectionUtils.isEmpty(rateReturnDataVos)) {
            return;
        }
        Integer type = getType(queryVo.getDataEntryType());
        if (Objects.equals(queryVo.getDataEntryType(), AchievementRateDataDef.ENERGY_CONSUMPTION_PLAN) ||
                Objects.equals(queryVo.getDataEntryType(), AchievementRateDataDef.PRODUCT_PLAN)) {
            queryVo.setEffSetIds(Collections.emptyList());
            queryVo.setProductTypes(Collections.emptyList());
        }
        if (Objects.equals(AchievementRateDataDef.UNIT_OBJECT_COST_PLAN, queryVo.getDataEntryType())) {
            queryVo.setEffSetIds(Collections.emptyList());
        }
        List<AchievementRate> rates = achievementRateDao.querAchievementRates(queryVo.getStartTime(), queryVo.getEndTime(), queryVo.getAggregationCycle(),
                queryVo.getEnergyTypes(), queryVo.getEffSetIds(), type, queryVo.getProductTypes(), queryVo.getNodes());
        Map<Long, Double> timeRate = rates.stream().filter(item -> Objects.nonNull(item.getRate())).collect(Collectors.toMap(AchievementRate::getLogtime,
                AchievementRate::getRate, (v1, v2) -> v1));

        rateReturnDataVos.forEach(rate -> {
            Double rateVal = CommonUtils.calcDouble(timeRate.get(rate.getLogTime()), Constant.ONE_HUNDRED_DOUBLE, EnumOperationType.MULTIPLICATION.getId());
            rate.setAchievementRate(rateVal);
        });
    }

    /**
     * 查询达成率 类别的转换
     *
     * @param type
     * @return
     */
    private Integer getType(Integer type) {
        switch (type) {
            case AchievementRateDataDef.ENERGY_CONSUMPTION_PLAN:
                return AchievementRateType.TOTAL_ENERGY_CONSUMPTION_ACHIEVEMENT_RATE;
            case AchievementRateDataDef.EFF_PLAN:
                return AchievementRateType.ACHIEVEMENT_RATE_OF_ENERGY_CONSUMPTION_PER_UNIT;
            case AchievementRateDataDef.PRODUCT_PLAN:
                return AchievementRateType.TOTAL_COST_ACHIEVEMENT_RATE;
            case AchievementRateDataDef.UNIT_OBJECT_COST_PLAN:
                return AchievementRateType.UNIT_COST_ACHIEVEMENT_RATIO;
            default:
                return type;
        }
    }

    @Override
    public void exportAchievementRateCompareData(AchievementRateQueryVo queryVo) {

        exportCompareData(queryVo);
    }

    @Override
    public List<EnergyEfficiencySetVo> queryEffSetList(EffQueryVo effQueryVo) {
        List<BaseVo> nodes = new ArrayList<>();
        if (Objects.nonNull(effQueryVo.getBaseVo())) {
            nodes = Collections.singletonList(effQueryVo.getBaseVo());
        }
        List<EnergyEfficiencySetVo> energyEfficiencySetVos = queryEnergyEffSetList(nodes, effQueryVo.getCycle(),
                effQueryVo.getEnergyType());
        Map<Integer, String> productTypeMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.PRODUCT_TYPE);
        List<Integer> energyTypes = energyEfficiencySetVos.stream().map(EnergyEfficiencySetVo::getEnergytype).distinct().collect(Collectors.toList());
        // 查询项目能源类型
        List<ProjectEnergyType> projectEnergyTypes = projectEnergyTypeService.queryEnergyTypes(GlobalInfoUtils.getProjectId(), energyTypes);
        Map<Integer, ProjectEnergyType> energyTypeMap = projectEnergyTypes.stream().collect(Collectors.toMap(ProjectEnergyType::getEnergytype, item -> item, (v1, v2) -> v1));
        for (EnergyEfficiencySetVo setVo : energyEfficiencySetVos) {
            setVo.setEnergyTypeName(energyTypeMap.get(setVo.getEnergytype()).getName());
            setVo.setProductTypeName(productTypeMap.get(setVo.getProducttype()));
        }
        return energyEfficiencySetVos;
    }

    @Override
    public List<FeeTypesReturnVo> queryEnergyType(BaseVo baseVo) {
        Set<Integer> energyTypeList = getEnergyTypeList(Collections.singletonList(baseVo));
        List<ProjectEnergyType> projectEnergyTypes = projectEnergyTypeService.queryEnergyTypes(GlobalInfoUtils.getProjectId(), new ArrayList<>(energyTypeList));
        Map<Integer, ProjectEnergyType> energyTypeMap = projectEnergyTypes.stream().collect(Collectors.toMap(ProjectEnergyType::getEnergytype, item -> item, (v1, v2) -> v1));
        //查钱的单位
        UserDefineUnitSearchDto dto = new UserDefineUnitSearchDto(GlobalInfoUtils.getProjectId(), OtherUnitTypeDef.RMB, ProjectUnitClassify.OTHER);
        ResultWithTotal<List<UserDefineUnit>> resultWithTotal = energyUnitService.queryUserDefineUnit(dto);
        String unit = null;
        if (CollectionUtils.isNotEmpty(resultWithTotal.getData())) {
            unit = resultWithTotal.getData().get(0).getBasicUnitSymbolName();
        }
        List<FeeTypesReturnVo> result = new ArrayList<>();
        for (Integer energyType : energyTypeList) {
            FeeTypesReturnVo returnVo = new FeeTypesReturnVo();
            returnVo.setId(energyType);
            returnVo.setUnit(unit);
            returnVo.setText(energyTypeMap.get(energyType).getName());
            result.add(returnVo);
        }
        return result;
    }

    @Override
    public List<BaseVo> queryNodeTree(EemQueryCondition queryCondition, Long userId, Long projectId) {
        Integer energyType = queryCondition.getEnergyType();
        //nodeCorrelationCheckService.filterNodeTree();
        List<Map<String, Object>> nodeTree = nodeManageBffService.queryNodeTree(queryCondition, userId);
        //List<BaseVoChoose> baseVos = JsonTransferUtils.transferList(nodeTree, BaseVoChoose.class);
        List<BaseVo> baseVos = JsonTransferUtils.transferList(nodeTree, BaseVo.class);
        //查询和核算方案关联的节点信息
        /*List<CostCheckNodeConfig> costCheckNodeConfigs = schemeConfigService.queryCostCheckNodeConfigByProject(GlobalInfoUtils.getProjectId());
        if (CollectionUtils.isEmpty(costCheckNodeConfigs)) {
            return Collections.emptyList();
        }
        Set<Long> costCheckId = costCheckNodeConfigs.stream().map(CostCheckNodeConfig::getCostcheckplan_id).collect(Collectors.toSet());
        Map<Long, List<BaseVoChoose>> map = new HashMap<>();
        for (CostCheckNodeConfig costCheckNodeConfig : costCheckNodeConfigs) {
            List<BaseVoChoose> node = costCheckNodeConfigs.stream().filter(costCheckNodeConfig1 -> Objects.equals(costCheckNodeConfig.getCostcheckplan_id(), costCheckNodeConfig1.getCostcheckplan_id()))
                    .map(costCheckNodeConfig1 -> new BaseVoChoose(costCheckNodeConfig1.getObjectid(), costCheckNodeConfig1.getObjectlabel())).collect(Collectors.toList());
            map.put(costCheckNodeConfig.getCostcheckplan_id(), node);
        }*/
        List<BaseVo> filterList = filterNodes(energyType);
        if (CollectionUtils.isEmpty(filterList)) {
            return Collections.emptyList();
        }
        //return filterNodeTree(baseVos, filterList);
        Set<BaseVo> baseVoChooseSet = filterList.stream().collect(Collectors.toSet());
        nodeCorrelationCheckService.filterNodeTree(baseVos, baseVoChooseSet);
        return baseVos;
    }

    /**
     * 获取能源类型对应的节点
     *
     * @param type
     * @return
     */
    private List<BaseVo> filterNodes(Integer type) {
        if (Objects.equals(type, EnergyTypeDef.STANDARD_COAL)) {
            type = null;
        }
        List<FeeScheme> feeSchemes = objectCostValuePlanDao.queryFeeSchemesByType(type);
        List<Long> feeIds = feeSchemes.stream().map(FeeScheme::getId).collect(Collectors.toList());
        List<CostcheckitemVO> costcheckitemVOS = objectCostValuePlanDao.queryCostCheckPlanByFeeSchemeIds(feeIds);
        List<Long> costPlanIds = costcheckitemVOS.stream().map(CostcheckitemVO::getCostcheckplan_model).flatMap(Collection::stream)
                .map(CostCheckPlan::getId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(costPlanIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CostCheckNodeConfig> wrapper = LambdaQueryWrapper.of(CostCheckNodeConfig.class).in(CostCheckNodeConfig::getCostcheckplan_id, costPlanIds);
        List<CostCheckNodeConfig> checkNodeConfigs = costCheckNodeConfigDao.selectList(wrapper);
        return checkNodeConfigs.stream().map(item -> new BaseVo(item.getObjectid(), item.getObjectlabel())).distinct().collect(Collectors.toList());
    }


    @Override
    public List<ProjectEnergyType> queryEnergyTypeByProjectId(Long projectId) {
        Set<Integer> energyType = queryEnergyType(projectId);
        if (CollectionUtils.isEmpty(energyType)) {
            return Collections.emptyList();
        }
        List<ProjectEnergyType> energyTypes = projectEnergyTypeService.queryEnergyTypes(projectId, new ArrayList<>(energyType));
        ProjectEnergyType type = new ProjectEnergyType();
        type.setEnergytype(EnergyTypeDef.STANDARD_COAL);
        type.setModelLabel(Constant.PROJECTENERGYTYPE);
        type.setName(Constant.ZHCB);
        energyTypes.add(0, type);
        return energyTypes;
    }

    @Override
    public List<BaseVo> queryNodeTree(EemQueryCondition queryCondition, Long userId, Long projectId, Long effSetId) {
        List<Map<String, Object>> nodeTree = nodeManageBffService.queryNodeTree(queryCondition, userId);
        List<BaseVo> baseVos = JsonTransferUtils.transferList(nodeTree, BaseVo.class);
        //查询指标id关联的对象
        List<KpiSetVo> kpiSetVo = kpiSetDao.getKpiSetList(effSetId);
        Set<BaseVo> filterNodes = kpiSetVo.stream().map(it -> new BaseVo(it.getObjectid(), it.getObjectLabel()))
                .collect(Collectors.toSet());
        //return filterNodeTree(baseVos, new ArrayList<>(filterNodes));
        nodeCorrelationCheckService.filterNodeTree(baseVos, filterNodes);
        return baseVos;
    }

    private List<BaseVoChoose> filterNodeTree(List<BaseVoChoose> nodeTree, List<BaseVoChoose> filterList) {
        List<BaseVoChoose> result = new ArrayList<>();
        for (BaseVoChoose baseVo : nodeTree) {
            baseVo.setChooseState(null);
            if (CollectionUtils.isNotEmpty(baseVo.getChildren())) {
                List<BaseVoChoose> childFilterResult = filterNodeTree(baseVo.getChildren(), filterList);
                baseVo.setChildren(childFilterResult);
                handleNodeTree(childFilterResult, baseVo, filterList, result);
            } else {
                if (filterBaseVoNotInList(filterList, baseVo)) {
                    baseVo.setChooseState(CHOOSE);
                    result.add(baseVo);
                }
            }
        }
        return result;
    }

    private void handleNodeTree(List<BaseVoChoose> childFilterResult, BaseVoChoose baseVo, List<BaseVoChoose> filterList, List<BaseVoChoose> result) {
        if (CollectionUtils.isNotEmpty(childFilterResult)) {
            if (filterBaseVoNotInList(filterList, baseVo)) {
                baseVo.setChooseState(CHOOSE);
            }
            result.add(baseVo);
        } else {
            if (filterBaseVoNotInList(filterList, baseVo)) {
                baseVo.setChooseState(CHOOSE);
                result.add(baseVo);
            }
        }
    }

    private boolean filterBaseVoNotInList(List<BaseVoChoose> filterList, BaseVoChoose baseVo) {
        return filterList.contains(baseVo);
    }

    private List<BaseVoChoose> query(List<Long> costCheckPlanId, Map<Long, List<BaseVoChoose>> map, Integer energyType) {
        List<String> subNodes = new ArrayList<>();
        subNodes.add(ModelLabelDef.COST_CHECK_ITEM);
        List<BaseVoChoose> result = new ArrayList<>();
        List<CostCheckPlan> costCheckPlans = modelServiceUtils.queryWithChildren(costCheckPlanId, ModelLabelDef.COST_CHECK_PLAN,
                null, subNodes, CostCheckPlan.class);
        //根据能耗类型和项目id查询费率方案，然后根据核算方案详情中费率方案的名称筛选出对应的费率方案信息
        List<FeeScheme> feeSchemes = schemeConfigService.queryFeeScheme(GlobalInfoUtils.getProjectId(), energyType, null);
        List<Long> feeSchemeIds = feeSchemes.stream().map(FeeScheme::getId).collect(Collectors.toList());
        for (CostCheckPlan costCheckPlan : costCheckPlans) {
            if (CollectionUtils.isNotEmpty(costCheckPlan.getCostcheckitem_model())) {
                List<Long> schemeIds = costCheckPlan.getCostcheckitem_model().stream().map(CostCheckItem::getFeescheme_id).collect(Collectors.toList());
                schemeIds.retainAll(feeSchemeIds);
                if (CollectionUtils.isNotEmpty(feeSchemeIds)) {
                    result.addAll(map.get(costCheckPlan.getId()));
                }
            }
        }
        return result;
    }

    private String getDateName(Integer cycle, Long st, Long et) {
        Long time = TimeUtil.addDateTimeByCycle(et, cycle, -1);
        if (Objects.equals(st, time)) {
            return getCycleName(cycle, st);
        }
        if (Objects.equals(AggregationCycle.ONE_DAY, cycle)) {
            return getCycleName(cycle, st)
                    + DATE_SPILT_SYMBOL + getCycleName(cycle, et);
        } else {
            return getCycleName(cycle, st)
                    + DATE_SPILT_SYMBOL + getCycleName(cycle, time);

        }
    }

    private void exportCompareData(AchievementRateQueryVo queryVo) {
        //序号	时间	能耗计划值	能耗实际值	达成率	（产品类型）产量	（产品类型）计划产量
        // 查询节点数据
        List<BaseVo> nodes = nodeDao.queryNodeName(queryVo.getNodes());


        try(Workbook workbook = PoiExcelUtils.createWorkBook(ExcelType.BIG_DATA);) {
            String name = getCompareExportName(queryVo.getDataEntryType());
            String colExportName = getColExportName(queryVo.getDataEntryType());
            List<ProductVo> productVos = productService.queryProducts(GlobalInfoUtils.getProjectId());
            Map<Integer, String> productTypeName = productVos.stream().collect(Collectors.toMap(ProductVo::getProductType, ProductVo::getName));
            //Map<Integer, String> productTypeMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.PRODUCT_TYPE);
            String productName = productTypeName.get(queryVo.getProductTypes().get(0));
            // 查询能耗/产量数据
            AchievementRateReturnDataWithUnit data = queryAchievementRateCompareData(queryVo);
            String typeName = getDataEntryTypeName(queryVo);
            String workbookName = nodes.get(0).getName() + SplitCharDef.UNDERLINE + productName + SplitCharDef.UNDERLINE + typeName + SplitCharDef.UNDERLINE +
                    name + "(" + getCycleName(queryVo.getAggregationCycle(), TimeUtil.localDateTime2timestamp(queryVo.getStartTime()))
                    + "~" + getCycleName(queryVo.getAggregationCycle(), TimeUtil.localDateTime2timestamp(queryVo.getEndTime())) + ")";

//        List<String> headers = createCompareHeader(queryVo.getDataEntryType(), colExportName, typeName, productName, data.getUnit(), data.getProductUnit());
            List<String> headers = createExportHeader(name, data.getUnit(), data.getProductUnit());
            PoiExcelUtils.createSheet(workbook, name, (sheet, baseCellStyle, rowIndex) -> {
                int rowNum = 0;
                writeHeaderAll(sheet, baseCellStyle, rowNum++, queryVo.getDataEntryType(), colExportName, typeName, productName, data.getUnit(), data.getProductUnit());

                int count = 1;
                writeExcelData(sheet, rowNum, baseCellStyle, count, data.getRateReturnDataVos(), queryVo.getAggregationCycle());
            }, createColWidth(headers.size()));

            FileUtils.downloadExcel(GlobalInfoUtils.getHttpResponse(), workbook, workbookName, CommonUtils.APPLICATION_MSEXCEL);
        } catch (Exception e) {
            log.error("导出异常", e);
            throw new ValidationException(e.getMessage());
        }
    }

    private void writeExcelData(Sheet sheet, int rowNum, CellStyle baseCellStyle, int count,
                                List<AchievementRateReturnDataVo> rateReturnDataVos, Integer cycle) {
        int col;
        for (AchievementRateReturnDataVo dataVo : rateReturnDataVos) {
            col = 0;
            Row row = PoiExcelUtils.createRow(sheet, rowNum);
            PoiExcelUtils.createCell(row, col++, baseCellStyle, count++);
            PoiExcelUtils.createCell(row, col++, baseCellStyle, getCycleName(cycle, dataVo.getLogTime()));

            PoiExcelUtils.createCell(row, col++, baseCellStyle, handleDoubleData(dataVo.getActualData()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, handleDoubleData(dataVo.getPlanData()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, handlePercentData(dataVo.getAchievementRate()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, handleDoubleData(dataVo.getProductData()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, handleDoubleData(dataVo.getProductDataPlan()));
            rowNum++;
        }

    }

    private String getCycleName(Integer cycle, Long logTime) {
        if (Objects.equals(AggregationCycle.ONE_DAY, cycle)) {
            return TimeUtil.format(logTime, TimeUtil.DATE_TIME_FORMAT);
        } else if (Objects.equals(AggregationCycle.ONE_MONTH, cycle)) {
            return TimeUtil.format(logTime, TimeUtil.YYYY_MM);
        } else {
            return TimeUtil.format(logTime, TimeUtil.YEAR_TIME_FORMAT);
        }
    }


    private String handleDoubleData(Double value) {
        if (Objects.isNull(value)) {
            return NAN_VALUE;
        }
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(value);
    }

    private String handlePercentData(Double value) {
        if (Objects.isNull(value)) {
            return NAN_VALUE;
        }
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(value) + PERCENT_SYMBOL;
    }

    private void writeHeaderAll(Sheet sheet, CellStyle baseCellStyle, int startRow, Integer type, String name, String typeName, String productName, String unit, String productUnit) {
        LinkedHashMap<String, CellStyle> headerMap = new LinkedHashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        headerMap.put("序号", baseCellStyle);
        headerMap.put("时间", baseCellStyle);
        if (Objects.equals(type, AchievementRateDataDef.UNIT_OBJECT_COST_PLAN)) {
            headerMap.put(name + "实际值" + "(" + unit + ")", baseCellStyle);
            headerMap.put(name + "目标值" + "(" + unit + ")", baseCellStyle);
        } else {
            headerMap.put(name + "实际值" + "(" + unit + ")", baseCellStyle);
            headerMap.put(name + "目标值" + "(" + unit + ")", baseCellStyle);
        }

        headerMap.put("达成率", baseCellStyle);
        headerMap.put("产量" + "(" + productUnit + ")", baseCellStyle);
        headerMap.put("计划产量" + "(" + productUnit + ")", baseCellStyle);

        PoiExcelUtils.createHeaderName(sheet, startRow, headerMap);
    }

    private String getDataEntryTypeName(AchievementRateQueryVo queryVo) {

        if (Objects.equals(queryVo.getDataEntryType(), AchievementRateDataDef.ENERGY_CONSUMPTION_PLAN)) {
            List<ProjectEnergyType> projectEnergyTypes = projectEnergyTypeService.queryEnergyTypes(GlobalInfoUtils.getProjectId(), queryVo.getEnergyTypes());
            Map<Integer, ProjectEnergyType> energyTypeMap = projectEnergyTypes.stream().collect(Collectors.toMap(ProjectEnergyType::getEnergytype, item -> item, (v1, v2) -> v1));
            return energyTypeMap.get(queryVo.getEnergyTypes().get(0)).getName();
        } else if (Objects.equals(queryVo.getDataEntryType(), AchievementRateDataDef.EFF_PLAN)) {
            List<EnergyEfficiencySetVo> energyEfficiencySetVos = energyEfficiencySetDao.queryEnergyEfficiencySetByIds(queryVo.getEffSetIds());
            return energyEfficiencySetVos.get(0).getName();
        } else if (Objects.equals(queryVo.getDataEntryType(), AchievementRateDataDef.PRODUCT_PLAN)) {
            //费用需要和
            List<ProjectEnergyType> projectEnergyTypes = projectEnergyTypeService.queryEnergyTypes(GlobalInfoUtils.getProjectId(), queryVo.getEnergyTypes());
            Map<Integer, ProjectEnergyType> energyTypeMap = projectEnergyTypes.stream().collect(Collectors.toMap(ProjectEnergyType::getEnergytype, item -> item, (v1, v2) -> v1));
            if (Objects.equals(queryVo.getEnergyTypes().get(0), EnergyTypeDef.STANDARD_COAL)) {
                return "综合成本";
            } else {
                return energyTypeMap.get(queryVo.getEnergyTypes().get(0)).getName() + "费";
            }
        } else {

            // 查询项目能源类型
            List<ProjectEnergyType> projectEnergyTypes = projectEnergyTypeService.queryEnergyTypes(GlobalInfoUtils.getProjectId(), queryVo.getEnergyTypes());
            Map<Integer, ProjectEnergyType> energyTypeMap = projectEnergyTypes.stream().collect(Collectors.toMap(ProjectEnergyType::getEnergytype, item -> item, (v1, v2) -> v1));
            return energyTypeMap.get(queryVo.getEnergyTypes().get(0)).getName();
        }
    }

    private List<String> createCompareHeader(Integer type, String name, String typeName, String productName, String unit, String productUnit) {
        List<String> headers = new ArrayList<>();
        headers.add("序号");
        headers.add("时间");
        if (Objects.equals(type, AchievementRateDataDef.UNIT_OBJECT_COST_PLAN)) {
            headers.add("(" + typeName + ")" + "(" + productName + ")" + name + "实际值" + "(" + unit + ")");
            headers.add("(" + typeName + ")" + "(" + productName + ")" + name + "目标值" + "(" + unit + ")");
        } else {
            headers.add("(" + typeName + ")" + name + "实际值" + "(" + unit + ")");
            headers.add("(" + typeName + ")" + name + "目标值" + "(" + unit + ")");
        }

        headers.add("达成率");
        headers.add("(" + productName + ")产量" + "(" + productUnit + ")");
        headers.add("(" + productName + ")计划产量" + "(" + productName + ")");
        return headers;
    }

    private List<String> createExportHeader(String name, String unit, String productUnit) {
        List<String> headers = new ArrayList<>();
        headers.add("序号");
        headers.add("时间");
        headers.add(name + "实际值" + "(" + unit + ")");
        headers.add(name + "目标值" + "(" + unit + ")");
        headers.add("达成率");
        headers.add("产量" + "(" + productUnit + ")");
        headers.add("计划产量" + "(" + productUnit + ")");
        return headers;
    }

    private AchievementRateReturnDataWithUnit queryCompareDataWithEnergyConsumptionPlan(AchievementRateQueryVo queryVo) {
        //查询计划值和实际值  产量的计划和实际值  达成率根据类型进行计算  其中查询的节点，产品类型，能源类型等都是一个
        //产量抽个方法查询
        List<AchievementRateReturnDataVo> rateReturnDataVos = new ArrayList<>();
        List<EnergyConsumption> consumptions = energyConsumptionDao.queryEnergyConsumption(queryVo.getNodes(), queryVo.getStartTime(), queryVo.getEndTime(), queryVo.getAggregationCycle(),
                queryVo.getEnergyTypes());
        List<EnergyConsumptionPlan> energyConsumptionPlans = energyConsumptionPlanDao.queryEnergyConsumption(queryVo.getNodes().get(0), TimeUtil.localDateTime2timestamp(queryVo.getStartTime()),
                TimeUtil.localDateTime2timestamp(queryVo.getEndTime()), queryVo.getAggregationCycle(), queryVo.getEnergyTypes());
        UserDefineUnit unit = generateUnit(consumptions, energyConsumptionPlans, queryVo);
        List<Long> timeRange = TimeUtil.getTimeRange(TimeUtil.localDateTime2timestamp(queryVo.getStartTime()), TimeUtil.localDateTime2timestamp(queryVo.getEndTime()), queryVo.getAggregationCycle());
        Map<Long, EnergyConsumption> energyConsumptionMap = consumptions.stream().collect(Collectors.toMap(EnergyConsumption::getLogtime, Function.identity()));
        Map<Long, EnergyConsumptionPlan> energyConsumptionPlanMap = energyConsumptionPlans.stream().collect(Collectors.toMap(EnergyConsumptionPlan::getLogtime, Function.identity()));
        AchievementRateReturnDataWithUnit data = new AchievementRateReturnDataWithUnit();
        data.setRateReturnDataVos(rateReturnDataVos);
        data.setUnit(unit.getUnitCn());
        for (Long time : timeRange) {
            AchievementRateReturnDataVo rateReturnDataVo = new AchievementRateReturnDataVo();
            rateReturnDataVo.setLogTime(time);
            rateReturnDataVos.add(rateReturnDataVo);
            EnergyConsumptionPlan productionPlan = energyConsumptionPlanMap.get(time);
            if (Objects.nonNull(productionPlan)) {
                rateReturnDataVo.setPlanData(CommonUtils.calcDouble(productionPlan.getValue(), unit.getCoef(), EnumOperationType.DIVISION.getId()));
            }
            EnergyConsumption production = energyConsumptionMap.get(time);
            if (Objects.nonNull(production)) {
                rateReturnDataVo.setActualData(CommonUtils.calcDouble(production.getUsage(), unit.getCoef(), EnumOperationType.DIVISION.getId()));
            }
            calculateRate(rateReturnDataVo);
        }
        assembleProductActualAndPlanData(queryVo, rateReturnDataVos, data);
        return data;
    }


    /**
     * 生成单位转换需要的内容，避免处理数据时一直查询
     *
     * @param energyConsumptionList
     * @param energyConsumptionPlans
     * @param queryVo
     * @return
     */
    private UserDefineUnit generateUnit(List<EnergyConsumption> energyConsumptionList,
                                        List<EnergyConsumptionPlan> energyConsumptionPlans, AchievementRateQueryVo queryVo) {
        UserDefineUnitSearchDto userDefineUnitSearchDto = new UserDefineUnitSearchDto(GlobalInfoUtils.getProjectId(), queryVo.getEnergyTypes().get(0),
                ProjectUnitClassify.ENERGY);
        List<EnergyConsumption> consumptions = energyConsumptionList.stream().filter(energyConsumption -> Objects.nonNull(energyConsumption.getUsage())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(consumptions)) {

            EnergyConsumption maxConsumption = energyConsumptionList.stream().max(Comparator.comparing(x -> Objects.isNull(x.getUsage()) ? 0.0 : x.getUsage())).orElse(new EnergyConsumption());

            return energyUnitService.queryUnitCoef(userDefineUnitSearchDto, maxConsumption.getUsage(), GlobalInfoUtils.getProjectId());
        }
        List<EnergyConsumptionPlan> consumptionPlan = energyConsumptionPlans.stream().filter(energyConsumptionPlan -> Objects.nonNull(energyConsumptionPlan.getValue())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(consumptionPlan)) {
            EnergyConsumptionPlan energyConsumptionPlan1 = energyConsumptionPlans.stream().max(Comparator.comparing(x -> Objects.isNull(x.getValue()) ? 0.0 : x.getValue())).orElse(new EnergyConsumptionPlan());
            return energyUnitService.queryUnitCoef(userDefineUnitSearchDto, energyConsumptionPlan1.getValue(), GlobalInfoUtils.getProjectId());
        }

        return energyUnitService.queryUnitCoef(userDefineUnitSearchDto, null, GlobalInfoUtils.getProjectId());
    }

    /**
     * 生成单位转换需要的内容，避免处理数据时一直查询
     *
     * @param energyConsumptionList
     * @param energyConsumptionPlans
     * @return
     */
    private UserDefineUnit generateUnitWithObjectCostValue(List<ObjectCosValue> energyConsumptionList,
                                                           List<ObjectCostValuePlan> energyConsumptionPlans) {

        UserDefineUnitSearchDto userDefineUnitSearchDto = new UserDefineUnitSearchDto(GlobalInfoUtils.getProjectId(), OtherUnitTypeDef.RMB, ProjectUnitClassify.OTHER);
        List<ObjectCosValue> consumptions = energyConsumptionList.stream().
                filter(energyConsumption -> Objects.nonNull(energyConsumption.getValue())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(consumptions)) {

            ObjectCosValue maxConsumption = energyConsumptionList.stream().max(Comparator.comparing(x -> Objects.isNull(x.getValue())
                    ? 0.0 : x.getValue())).orElse(new ObjectCosValue());

            return energyUnitService.queryUnitCoef(userDefineUnitSearchDto, maxConsumption.getValue(), GlobalInfoUtils.getProjectId());
        }
        List<ObjectCostValuePlan> consumptionPlan = energyConsumptionPlans.stream().filter(energyConsumptionPlan -> Objects.nonNull(energyConsumptionPlan.getValue())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(consumptionPlan)) {
            ObjectCostValuePlan energyConsumptionPlan1 = energyConsumptionPlans.stream().max(Comparator.comparing(x -> Objects.isNull(x.getValue()) ? 0.0 : x.getValue())).orElse(new ObjectCostValuePlan());
            return energyUnitService.queryUnitCoef(userDefineUnitSearchDto, energyConsumptionPlan1.getValue(), GlobalInfoUtils.getProjectId());
        }

        return energyUnitService.queryUnitCoef(userDefineUnitSearchDto, null, GlobalInfoUtils.getProjectId());
    }

    /**
     * 生成单位转换需要的内容，避免处理数据时一直查询
     *
     * @param energyConsumptionList
     * @param energyConsumptionPlans
     * @return
     */
    private UserDefineUnit generateUnitWithUnitObjectCostValue(List<UnitObjectCostValue> energyConsumptionList,
                                                               List<UnitObjectCostValuePlan> energyConsumptionPlans) {

        UserDefineUnitSearchDto userDefineUnitSearchDto = new UserDefineUnitSearchDto(GlobalInfoUtils.getProjectId(), OtherUnitTypeDef.RMB, ProjectUnitClassify.OTHER);
        List<UnitObjectCostValue> consumptions = energyConsumptionList.stream().
                filter(energyConsumption -> Objects.nonNull(energyConsumption.getValue())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(consumptions)) {

            UnitObjectCostValue maxConsumption = energyConsumptionList.stream().max(Comparator.comparing(x -> Objects.isNull(x.getValue())
                    ? 0.0 : x.getValue())).orElse(new UnitObjectCostValue());

            return energyUnitService.queryUnitCoef(userDefineUnitSearchDto, maxConsumption.getValue(), GlobalInfoUtils.getProjectId());
        }
        List<UnitObjectCostValuePlan> consumptionPlan = energyConsumptionPlans.stream().filter(energyConsumptionPlan -> Objects.nonNull(energyConsumptionPlan.getValue())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(consumptionPlan)) {
            UnitObjectCostValuePlan energyConsumptionPlan1 = energyConsumptionPlans.stream().max(Comparator.comparing(x -> Objects.isNull(x.getValue()) ? 0.0 : x.getValue())).orElse(new UnitObjectCostValuePlan());
            return energyUnitService.queryUnitCoef(userDefineUnitSearchDto, energyConsumptionPlan1.getValue(), GlobalInfoUtils.getProjectId());
        }

        return energyUnitService.queryUnitCoef(userDefineUnitSearchDto, null, GlobalInfoUtils.getProjectId());
    }

    private QueryEnergyEfficiencyDataCriterial createQueryParam(AchievementRateQueryVo queryVo) {
        QueryEnergyEfficiencyDataCriterial queryEnergyEfficiencyDataCriterial = new QueryEnergyEfficiencyDataCriterial();
        queryEnergyEfficiencyDataCriterial.setEnergyEfficiencySetId(queryVo.getEffSetIds().get(0));
        queryEnergyEfficiencyDataCriterial.setEnergyType(queryVo.getEnergyTypes().get(0));
        queryEnergyEfficiencyDataCriterial.setProductType(queryVo.getProductTypes().get(0));
        queryEnergyEfficiencyDataCriterial.setEndTime(queryVo.getEndTime());
        queryEnergyEfficiencyDataCriterial.setStartTime(queryVo.getStartTime());
        queryEnergyEfficiencyDataCriterial.setAggregationcycle(queryVo.getAggregationCycle());
        queryEnergyEfficiencyDataCriterial.setObjectId(queryVo.getNodes().get(0).getId());
        queryEnergyEfficiencyDataCriterial.setObjectLabel(queryVo.getNodes().get(0).getModelLabel());
        return queryEnergyEfficiencyDataCriterial;
    }

    private AchievementRateReturnDataWithUnit queryCompareDataWithEffData(AchievementRateQueryVo queryVo) {
        //查询计划值和实际值  产量的计划和实际值  达成率根据类型进行计算  其中查询的节点，产品类型，能源类型等都是一个
        //产量抽个方法查询
        List<AchievementRateReturnDataVo> rateReturnDataVos = new ArrayList<>();
        List<EnergyEfficiencyData> efficiencyData = energyEfficiencyDataDao.queryEnergyEfficiencyDataWithCondition(createQueryParam(queryVo));
        List<EnergyEfficiencyDataPlan> efficiencyDataPlans = energyEfficiencyDataPlanDao.query(TimeUtil.localDateTime2timestamp(queryVo.getStartTime()), TimeUtil.localDateTime2timestamp(queryVo.getEndTime()),
                queryVo.getEffSetIds(), queryVo.getNodes(), queryVo.getAggregationCycle());
        List<EnergyEfficiencySetVo> energyEfficiencySetVos = energyEfficiencySetDao.queryEnergyEfficiencySetByIds(queryVo.getEffSetIds());
        AchievementRateReturnDataWithUnit data = new AchievementRateReturnDataWithUnit();
        data.setRateReturnDataVos(rateReturnDataVos);
        if (CollectionUtils.isNotEmpty(energyEfficiencySetVos)) {
            data.setUnit(energyEfficiencySetVos.get(0).getSymbol());
        }
        List<Long> timeRange = TimeUtil.getTimeRange(TimeUtil.localDateTime2timestamp(queryVo.getStartTime()), TimeUtil.localDateTime2timestamp(queryVo.getEndTime()), queryVo.getAggregationCycle());
        Map<LocalDateTime, EnergyEfficiencyData> energyConsumptionMap = efficiencyData.stream().collect(Collectors.toMap(EnergyEfficiencyData::getLogTime, Function.identity()));
        Map<Long, EnergyEfficiencyDataPlan> energyConsumptionPlanMap = efficiencyDataPlans.stream().collect(Collectors.toMap(EnergyEfficiencyDataPlan::getLogTime, Function.identity(), (v1, v2) -> v1));
        for (Long time : timeRange) {
            AchievementRateReturnDataVo rateReturnDataVo = new AchievementRateReturnDataVo();
            rateReturnDataVo.setLogTime(time);
            rateReturnDataVos.add(rateReturnDataVo);
            EnergyEfficiencyDataPlan productionPlan = energyConsumptionPlanMap.get(time);
            if (Objects.nonNull(productionPlan)) {
                rateReturnDataVo.setPlanData(productionPlan.getValue());
            }
            EnergyEfficiencyData production = energyConsumptionMap.get(TimeUtil.timestamp2LocalDateTime(time));
            if (Objects.nonNull(production)) {
                rateReturnDataVo.setActualData(production.getValue());
            }
            calculateRate(rateReturnDataVo);
        }
        assembleProductActualAndPlanData(queryVo, rateReturnDataVos, data);
        return data;
    }

    private AchievementRateReturnDataWithUnit queryCompareDataWithObjectCostValue(AchievementRateQueryVo queryVo) {
        //查询计划值和实际值  产量的计划和实际值  达成率根据类型进行计算  其中查询的节点，产品类型，能源类型等都是一个
        //产量抽个方法查询
        List<AchievementRateReturnDataVo> rateReturnDataVos = new ArrayList<>();
        List<ObjectCosValue> objectCosValues = objectCostValueDao.queryObjectCostValues(queryVo.getNodes().get(0).getModelLabel(), Collections.singletonList(queryVo.getNodes().get(0).getId()),
                queryVo.getAggregationCycle(), queryVo.getEnergyTypes(), TimeUtil.localDateTime2timestamp(queryVo.getStartTime()),
                TimeUtil.localDateTime2timestamp(queryVo.getEndTime()), null);
        List<ObjectCostValuePlan> query = objectCostValuePlanDao.query(TimeUtil.localDateTime2timestamp(queryVo.getStartTime()),
                TimeUtil.localDateTime2timestamp(queryVo.getEndTime()), queryVo.getEnergyTypes(), queryVo.getNodes(), queryVo.getAggregationCycle());
        AchievementRateReturnDataWithUnit data = new AchievementRateReturnDataWithUnit();
        data.setRateReturnDataVos(rateReturnDataVos);
        //查钱的单位

        UserDefineUnit unit = generateUnitWithObjectCostValue(objectCosValues, query);
        data.setUnit(unit.getUnitCn());
        List<Long> timeRange = TimeUtil.getTimeRange(TimeUtil.localDateTime2timestamp(queryVo.getStartTime()), TimeUtil.localDateTime2timestamp(queryVo.getEndTime()), queryVo.getAggregationCycle());
        Map<Long, ObjectCosValue> energyConsumptionMap = objectCosValues.stream().collect(Collectors.toMap(ObjectCosValue::getLogtime, Function.identity()));
        Map<Long, ObjectCostValuePlan> energyConsumptionPlanMap = query.stream().collect(Collectors.toMap(ObjectCostValuePlan::getLogTime, Function.identity()));
        for (Long time : timeRange) {
            AchievementRateReturnDataVo rateReturnDataVo = new AchievementRateReturnDataVo();
            rateReturnDataVo.setLogTime(time);
            rateReturnDataVos.add(rateReturnDataVo);
            ObjectCostValuePlan productionPlan = energyConsumptionPlanMap.get(time);
            if (Objects.nonNull(productionPlan)) {
                rateReturnDataVo.setPlanData(CommonUtils.calcDouble(productionPlan.getValue(), unit.getCoef(), EnumOperationType.DIVISION.getId()));
            }
            ObjectCosValue production = energyConsumptionMap.get((time));
            if (Objects.nonNull(production)) {
                rateReturnDataVo.setActualData(CommonUtils.calcDouble(production.getValue(), unit.getCoef(), EnumOperationType.DIVISION.getId()));
            }
            calculateRate(rateReturnDataVo);
        }
        assembleProductActualAndPlanData(queryVo, rateReturnDataVos, data);
        return data;
    }

    private AchievementRateReturnDataWithUnit queryCompareDataWithUnitObjectCostValue(AchievementRateQueryVo queryVo) {
        //查询计划值和实际值  产量的计划和实际值  达成率根据类型进行计算  其中查询的节点，产品类型，能源类型等都是一个
        //产量抽个方法查询
        List<AchievementRateReturnDataVo> rateReturnDataVos = new ArrayList<>();
        List<UnitObjectCostValue> unitObjectCostValues = unitCostDao.queryUnitObjectValue(queryVo.getNodes().get(0), queryVo.getStartTime(), queryVo.getEndTime(), queryVo.getAggregationCycle(),
                queryVo.getEnergyTypes().get(0), queryVo.getProductTypes().get(0));
        List<UnitObjectCostValuePlan> objectCostValuePlans = unitObjectCostValuePlanDao.query(TimeUtil.localDateTime2timestamp(queryVo.getStartTime()),
                TimeUtil.localDateTime2timestamp(queryVo.getEndTime()), queryVo.getEnergyTypes(), queryVo.getNodes(), queryVo.getAggregationCycle(), queryVo.getProductTypes());
        AchievementRateReturnDataWithUnit data = new AchievementRateReturnDataWithUnit();
        data.setRateReturnDataVos(rateReturnDataVos);
        //查钱的单位
        UserDefineUnit unit = generateUnitWithUnitObjectCostValue(unitObjectCostValues, objectCostValuePlans);
        List<Long> timeRange = TimeUtil.getTimeRange(TimeUtil.localDateTime2timestamp(queryVo.getStartTime()), TimeUtil.localDateTime2timestamp(queryVo.getEndTime()), queryVo.getAggregationCycle());
        Map<Long, UnitObjectCostValue> energyConsumptionMap = unitObjectCostValues.stream().collect(Collectors.toMap(UnitObjectCostValue::getLogTime, Function.identity()));
        Map<Long, UnitObjectCostValuePlan> energyConsumptionPlanMap = objectCostValuePlans.stream().collect(Collectors.toMap(UnitObjectCostValuePlan::getLogTime, Function.identity()));
        for (Long time : timeRange) {
            AchievementRateReturnDataVo rateReturnDataVo = new AchievementRateReturnDataVo();
            rateReturnDataVo.setLogTime(time);
            rateReturnDataVos.add(rateReturnDataVo);
            UnitObjectCostValuePlan productionPlan = energyConsumptionPlanMap.get(time);
            if (Objects.nonNull(productionPlan)) {
                rateReturnDataVo.setPlanData(CommonUtils.calcDouble(productionPlan.getValue(), unit.getCoef(), EnumOperationType.DIVISION.getId()));
            }
            UnitObjectCostValue production = energyConsumptionMap.get((time));
            if (Objects.nonNull(production)) {
                rateReturnDataVo.setActualData(CommonUtils.calcDouble(production.getValue(), unit.getCoef(), EnumOperationType.DIVISION.getId()));
            }
            calculateRate(rateReturnDataVo);
        }
        assembleProductActualAndPlanData(queryVo, rateReturnDataVos, data);
        data.setUnit(unit.getUnitCn() + "/" + data.getProductUnit());
        return data;
    }

    private void calculateRate(AchievementRateReturnDataVo rateReturnDataVo) {
        //"达成率=(2-实际值/计划值)*100% ，即数值越大越好" 光束
        Double calcDouble = CommonUtils.calcDouble(rateReturnDataVo.getActualData(), rateReturnDataVo.getPlanData(), EnumOperationType.DIVISION.getId());
        if (Objects.equals(type, ACHIEVEMENT_TYPE_GS)) {

            rateReturnDataVo.setAchievementRate(CommonUtils.calcDouble(CommonUtils.calcDouble(CALCULATE, calcDouble, EnumOperationType.SUBTRACT.getId()),
                    PERCENT, EnumOperationType.MULTIPLICATION.getId()));
        } else {
            //达成率=实际值/计划值*100%
            rateReturnDataVo.setAchievementRate(CommonUtils.calcDouble(calcDouble,
                    PERCENT, EnumOperationType.MULTIPLICATION.getId()));
        }

    }

    private void assembleProductActualAndPlanData(AchievementRateQueryVo queryVo, List<AchievementRateReturnDataVo> rateReturnDataVos,
                                                  AchievementRateReturnDataWithUnit data) {
        List<ProductionData> productionData = productionDataDao.queryProductionData(queryVo.getStartTime(), queryVo.getEndTime(), queryVo.getNodes(),
                queryVo.getAggregationCycle(), queryVo.getProductTypes());
        if (CollectionUtils.isEmpty(queryVo.getProductTypes())) {
            return;
        }
        List<ProductionPlan> productionPlans = productionPlanDao.queryProductionPlan(queryVo.getStartTime(), queryVo.getEndTime(), queryVo.getNodes(),
                queryVo.getAggregationCycle(), queryVo.getProductTypes());
        UserDefineUnit unit = generateUnitProduction(productionData,
                productionPlans, queryVo);
        data.setProductUnit(unit.getUnitCn());
        //实际查询只有一种产品类型
        Map<LocalDateTime, ProductionData> productionDataMap = productionData.stream().collect(Collectors.toMap(ProductionData::getLogTime, Function.identity()));
        Map<LocalDateTime, ProductionPlan> productionPlanMap = productionPlans.stream().collect(Collectors.toMap(ProductionPlan::getLogtime, Function.identity()));
        for (AchievementRateReturnDataVo dataVo : rateReturnDataVos) {
            ProductionPlan productionPlan = productionPlanMap.get(TimeUtil.timestamp2LocalDateTime(dataVo.getLogTime()));
            if (Objects.nonNull(productionPlan)) {
                dataVo.setProductDataPlan(CommonUtils.calcDouble(productionPlan.getValue(), unit.getCoef(), EnumOperationType.DIVISION.getId()));
            }
            ProductionData production = productionDataMap.get(TimeUtil.timestamp2LocalDateTime(dataVo.getLogTime()));
            if (Objects.nonNull(production)) {
                dataVo.setProductData(CommonUtils.calcDouble(production.getValue(), unit.getCoef(), EnumOperationType.DIVISION.getId()));
            }
        }
    }

    /**
     * 生成单位转换需要的内容，避免处理数据时一直查询
     *
     * @param productionData
     * @param productionPlans
     * @param queryVo
     * @return
     */
    private UserDefineUnit generateUnitProduction(List<ProductionData> productionData,
                                                  List<ProductionPlan> productionPlans, AchievementRateQueryVo queryVo) {
        UserDefineUnitSearchDto userDefineUnitSearchDto = new UserDefineUnitSearchDto(GlobalInfoUtils.getProjectId(), queryVo.getProductTypes().get(0),
                ProjectUnitClassify.PRODUCT);
        List<ProductionData> consumptions = productionData.stream().filter(energyConsumption -> Objects.nonNull(energyConsumption.getValue())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(consumptions)) {

            ProductionData maxConsumption = consumptions.stream().max(Comparator.comparing(x -> Objects.isNull(x.getValue()) ? 0.0 : x.getValue())).
                    orElse(new ProductionData());

            return energyUnitService.queryUnitCoef(userDefineUnitSearchDto, maxConsumption.getValue(), GlobalInfoUtils.getProjectId());
        }
        List<ProductionPlan> consumptionPlan = productionPlans.stream().filter(energyConsumptionPlan -> Objects.nonNull(energyConsumptionPlan.getValue())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(consumptionPlan)) {
            ProductionPlan energyConsumptionPlan1 = consumptionPlan.stream().max(Comparator.comparing(x -> Objects.isNull(x.getValue()) ? 0.0 : x.getValue())).orElse(new ProductionPlan());
            return energyUnitService.queryUnitCoef(userDefineUnitSearchDto, energyConsumptionPlan1.getValue(), GlobalInfoUtils.getProjectId());
        }
        return energyUnitService.queryUnitCoef(userDefineUnitSearchDto, null, GlobalInfoUtils.getProjectId());
    }

    private void importEnergyEffPlanData(MultipartFile multipartFile, int dataEntryType) {

        try (Workbook wb = PoiExcelUtils.createWorkbook(multipartFile.getInputStream(), multipartFile.getOriginalFilename())) {
            Sheet sheet = wb.getSheetAt(0);

            int endRow = sheet.getLastRowNum() + 1;

            Row row;

            int rowNum = 1;
            List<EnergyEfficiencySetVo> energyEfficiencySetVos = energyEfficiencySetDao.queryEfSets(GlobalInfoUtils.getProjectId());
            Map<String, Integer> productMap = getProductByProjectId();
            Map<String, Integer> energyTypeMap = getEnergyByProjectId();
            Map<String, Integer> cycleMap = modelServiceUtils.queryEnumTextAndId(ModelLabelDef.AGGREGATION_CYCLE);

            int headerRowNum = 0;
            Row headerRow = sheet.getRow(headerRowNum);
            short endCol = headerRow.getLastCellNum();
            //需要校验填的能效配置是不是对的--能源类型和产品类型
            checkHeaders(headerRow, dataEntryType);
            Map<Integer, List<AchievementRateDataNode>> integerListMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_4);
            Set<BaseVo> nodes = new HashSet<>();
            for (int i = rowNum; i < endRow; i++) {
                row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }

                int col = 1;
                // 解析节点
                String s = PoiExcelUtils.readValueAsString(row.getCell(col++));
                String indicatorName = PoiExcelUtils.readValueAsString(row.getCell(2));
                BaseVo node = NodeFormatUtils.parseNode(s);
                nodes.add(node);
                //指标类型--直接根据名字过滤
                String value = PoiExcelUtils.readValueAsString(row.getCell(col++));
                if (StringUtils.isEmpty(value)) {
                    throw new ValidationException("第" + i + "行" + "指标类型不存在,请调整后再导入!");
                }
                //指标后面 为单位
                col++;
                // 能源类型
                Integer product = PoiExcelUtils.readValueAsEnum(row.getCell(col++), productMap);
                if (Objects.isNull(product)) {
                    throw new ValidationException("第" + i + "行" + "产品类型不存在,请调整后再导入!");
                }
                // 能源类型
                Cell energy = row.getCell(col++);
                Integer energyType = PoiExcelUtils.readValueAsEnum(energy, energyTypeMap);
                if (Objects.isNull(energyType)) {
                    throw new ValidationException("第" + i + "行" + "能源类型不存在,请调整后再导入!");
                }
//                Optional<EnergyEfficiencySetVo> anyNode = energyEfficiencySetVos.stream().filter(it -> Objects.equals(it.getObjectid(), node.getId())
//                        && Objects.equals(it.getObjectlabel(), node.getModelLabel()))
//                        .findAny();


                // 周期
                Integer cycle = PoiExcelUtils.readValueAsEnum(row.getCell(col), cycleMap);
                if (Objects.isNull(cycle)) {
                    throw new ValidationException("第" + i + "行周期类型不存在,请调整后再导入!");
                }
                Optional<EnergyEfficiencySetVo> any = energyEfficiencySetVos.stream().filter(it -> Objects.equals(it.getName(), value)
                                && Objects.equals(it.getUnittype(), UNIT_TYPE)
                                && Objects.equals(it.getEnergytype(), energyType)
                                && Objects.equals(it.getProducttype(), product)
                                && Objects.equals(it.getAggregationcycle(), cycle))
                        .findAny();
                if (!any.isPresent()) {
                    throw new ValidationException("第" + i + "行数据的指标类型不存在,请调整后再导入!");
                }
                List<AchievementRateDataNode> systemDataNodes = integerListMap.computeIfAbsent(cycle, k -> new ArrayList<>());

                getEffPlanDataExcelRow(row, headerRow, getStartColMultiNodes(dataEntryType), endCol, energyType, systemDataNodes,
                        node, product, any.get().getId(), cycle);

            }

            Assert.isTrue(nodes.size() <= dataEntryConfig.getMaxExportNodeNumber(),
                    "导入的节点数量超出允许的最大数量！");

            handleEffPlanData(integerListMap);
            commonUtilsService.writeDeleteOperationLogs(EEMOperationLogType.DATA_ENTRY, assembleImportLogs(dataEntryType, new ArrayList<>(nodes), multipartFile.getOriginalFilename()), new Object[]{}, GlobalInfoUtils.getUserId());

        } catch (ValidationException v) {
            log.error("导入数据错误：", v);
            throw new ValidationException(v.getMessage());
        } catch (Exception e) {
            log.error("导入数据错误：", e);
            throw new ValidationException("导入数据错误");
        }
    }

    private void importObjectCostValuePlanData(MultipartFile multipartFile, int dataEntryType) {

        try (Workbook wb = PoiExcelUtils.createWorkbook(multipartFile.getInputStream(), multipartFile.getOriginalFilename())) {
            Sheet sheet = wb.getSheetAt(0);

            int endRow = sheet.getLastRowNum() + 1;

            Row row;

            int rowNum = 1;
            //查询全部节点的费用类型，配置有问题提醒
            Map<String, Integer> feeTypeByProjectId = getFeeTypeByProjectId();
            Map<String, Integer> cycleMap = modelServiceUtils.queryEnumTextAndId(ModelLabelDef.AGGREGATION_CYCLE);
            Map<BaseVo, Set<Integer>> checkConfigByNodes = createCheckConfigByNodes();
            int headerRowNum = 0;
            Row headerRow = sheet.getRow(headerRowNum);
            short endCol = headerRow.getLastCellNum();
            //需要校验填的能效配置是不是对的--能源类型和产品类型
            checkHeaders(headerRow, dataEntryType);
            Map<Integer, List<AchievementRateDataNode>> integerListMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_4);
            Set<BaseVo> nodes = new HashSet<>();
            for (int i = rowNum; i < endRow; i++) {
                row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }

                int col = 1;
                // 解析节点
                String s = PoiExcelUtils.readValueAsString(row.getCell(col++));
                BaseVo node = NodeFormatUtils.parseNode(s);
                nodes.add(node);
                Set<Integer> integers = checkConfigByNodes.get(node);
                if (CollectionUtils.isEmpty(integers)) {
                    throw new ValidationException("第" + i + "行" + "节点没有关联核算方案或者核算方案没有成本构成！");
                }
                // 费用类型
                Integer energyType = PoiExcelUtils.readValueAsEnum(row.getCell(col++), feeTypeByProjectId);
                if (Objects.isNull(energyType) || !integers.contains(energyType)) {
                    throw new ValidationException("第" + i + "行" + "节点没有配置该能源类型的成本构成！");
                }
                col++;
                // 周期
                Integer cycle = PoiExcelUtils.readValueAsEnum(row.getCell(col), cycleMap);
                if (Objects.isNull(cycle)) {
                    throw new ValidationException("第" + i + "行周期不存在,请调整后再导入!");
                }
                List<AchievementRateDataNode> systemDataNodes = integerListMap.computeIfAbsent(cycle, k -> new ArrayList<>());

                getObjectCostValuePlanDataExcelRow(row, headerRow, getStartColMultiNodes(dataEntryType), endCol, energyType, systemDataNodes, node, cycle);

            }

            Assert.isTrue(nodes.size() <= dataEntryConfig.getMaxExportNodeNumber(),
                    "导入的节点数量超出允许的最大数量！");
            handleObjectCostValuePlan(integerListMap);
            commonUtilsService.writeDeleteOperationLogs(EEMOperationLogType.DATA_ENTRY, assembleImportLogs(dataEntryType, new ArrayList<>(nodes), multipartFile.getOriginalFilename()), new Object[]{}, GlobalInfoUtils.getUserId());
        } catch (ValidationException v) {
            log.error("导入数据错误：", v);
            throw new ValidationException(v.getMessage());
        } catch (Exception e) {
            log.error("导入数据错误：", e);
            throw new ValidationException("导入数据错误");
        }
    }

    private void importUnitObjectCostValuePlan(MultipartFile multipartFile, int dataEntryType) {

        try (Workbook wb = PoiExcelUtils.createWorkbook(multipartFile.getInputStream(), multipartFile.getOriginalFilename())) {
            Sheet sheet = wb.getSheetAt(0);

            int endRow = sheet.getLastRowNum() + 1;

            Row row;

            int rowNum = 1;
            Map<String, Integer> productMap = getProductByProjectId();
//            Map<String, Integer> energyTypeMap = getEnergyByProjectId();
            Map<String, Integer> feeTypeByProjectId = getFeeTypeByProjectId();
            Map<String, Integer> cycleMap = modelServiceUtils.queryEnumTextAndId(ModelLabelDef.AGGREGATION_CYCLE);
            Map<BaseVo, Set<Integer>> checkConfigByNodes = createCheckConfigByNodes();
            int headerRowNum = 0;
            Row headerRow = sheet.getRow(headerRowNum);
            short endCol = headerRow.getLastCellNum();
            //需要校验填的能效配置是不是对的--能源类型和产品类型
            checkHeaders(headerRow, dataEntryType);
            Map<Integer, List<AchievementRateDataNode>> integerListMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_4);
            Set<BaseVo> nodes = new HashSet<>();
            for (int i = rowNum; i < endRow; i++) {
                row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }

                int col = 1;
                // 解析节点
                String s = PoiExcelUtils.readValueAsString(row.getCell(col++));
                BaseVo node = NodeFormatUtils.parseNode(s);
                nodes.add(node);
                Set<Integer> integers = checkConfigByNodes.get(node);
                if (CollectionUtils.isEmpty(integers)) {
                    throw new ValidationException("第" + i + "行" + "节点没有关联核算方案或者核算方案没有成本构成！");
                }
                // 产品类型
                Integer product = PoiExcelUtils.readValueAsEnum(row.getCell(col++), productMap);
                if (Objects.isNull(product)) {
                    throw new ValidationException("第" + i + "行" + "产品类型不存在,请调整后再导入!");
                }
                // 能源类型
                Cell energy = row.getCell(col++);
                Integer energyType = PoiExcelUtils.readValueAsEnum(energy, feeTypeByProjectId);
                if (Objects.isNull(energyType)||!integers.contains(energyType)) {
                    throw new ValidationException("第" + i + "行" + "费用类型不存在,请调整后再导入!");
                }
                col++;

                // 周期
                Integer cycle = PoiExcelUtils.readValueAsEnum(row.getCell(col), cycleMap);
                if (Objects.isNull(cycle)) {
                    throw new ValidationException("第" + i + "行周期类型不存在,请调整后再导入!");
                }
                List<AchievementRateDataNode> systemDataNodes = integerListMap.computeIfAbsent(cycle, k -> new ArrayList<>());

                getUnitObjectCostValuePlanDataExcelRow(row, headerRow, getStartColMultiNodes(dataEntryType), endCol, energyType, systemDataNodes,
                        node, product, cycle);

            }

            Assert.isTrue(nodes.size() <= dataEntryConfig.getMaxExportNodeNumber(),
                    "导入的节点数量超出允许的最大数量！");

            handleUnitObjectCostValuePlan(integerListMap);
            commonUtilsService.writeDeleteOperationLogs(EEMOperationLogType.DATA_ENTRY, assembleImportLogs(dataEntryType, new ArrayList<>(nodes), multipartFile.getOriginalFilename()), new Object[]{}, GlobalInfoUtils.getUserId());

        } catch (ValidationException v) {
            log.error("导入数据错误：", v);
            throw new ValidationException(v.getMessage());
        } catch (Exception e) {
            log.error("导入数据错误：", e);
            throw new ValidationException("导入数据错误");
        }
    }

    private void importEnergyConsumptionPlanData(MultipartFile multipartFile, int dataEntryType) {

        try (Workbook wb = PoiExcelUtils.createWorkbook(multipartFile.getInputStream(), multipartFile.getOriginalFilename())) {
            Sheet sheet = wb.getSheetAt(0);

            int endRow = sheet.getLastRowNum() + 1;

            Row row;

            int rowNum = 1;

            Map<String, Integer> energyTypeMap = getEnergyByProjectId();
            Map<String, Integer> cycleMap = modelServiceUtils.queryEnumTextAndId(ModelLabelDef.AGGREGATION_CYCLE);

            int headerRowNum = 0;
            Row headerRow = sheet.getRow(headerRowNum);
            short endCol = headerRow.getLastCellNum();
            //需要校验填的能效配置是不是对的--能源类型和产品类型
            checkHeaders(headerRow, dataEntryType);
            Map<Integer, List<AchievementRateDataNode>> integerListMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_4);
            Set<BaseVo> nodes = new HashSet<>();
            List<ConvertedStandardCoalCoef> coefs = energyConvertDao.queryCoefByProjectId(GlobalInfoUtils.getProjectId());
            List<Integer> collect = new ArrayList<>();
            if (Objects.isNull(coefs) || coefs.isEmpty()){
                collect = Constant.FOLDED_LABEL_ENERGTYPE;
            }else {
                collect = coefs.stream().map(ConvertedStandardCoalCoef::getTargetenergytype).collect(Collectors.toList());
            }

            for (int i = rowNum; i < endRow; i++) {
                row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }

                int col = 1;
                // 解析节点
                String s = PoiExcelUtils.readValueAsString(row.getCell(col++));
                BaseVo node = NodeFormatUtils.parseNode(s);
                nodes.add(node);
                // 能源类型
                Cell energy = row.getCell(col++);
                Integer energyType = PoiExcelUtils.readValueAsEnum(energy, energyTypeMap);
                if (collect.contains(energyType)) {
                    continue;
                }
                if (Objects.isNull(energyType)) {
                    throw new ValidationException("第" + i + "行" + "能源类型不存在||折标,请调整后再导入!");
                }
                col++;
                // 周期
                Integer cycle = PoiExcelUtils.readValueAsEnum(row.getCell(col), cycleMap);
                if (Objects.isNull(cycle)) {
                    throw new ValidationException("第" + i + "行周期类型不存在,请调整后再导入!");
                }
                List<AchievementRateDataNode> systemDataNodes = integerListMap.computeIfAbsent(cycle, k -> new ArrayList<>());

                getEffPlanDataExcelRow(row, headerRow, getStartColMultiNodes(dataEntryType), endCol, energyType, systemDataNodes,
                        node, null, null, cycle);

            }
            Assert.isTrue(nodes.size() <= dataEntryConfig.getMaxExportNodeNumber(),
                    "导入的节点数量超出允许的最大数量！");
            handleEnergyConsumptionPlanData(integerListMap);
            commonUtilsService.writeDeleteOperationLogs(EEMOperationLogType.DATA_ENTRY, assembleImportLogs(dataEntryType, new ArrayList<>(nodes), multipartFile.getOriginalFilename()), new Object[]{}, GlobalInfoUtils.getUserId());

        } catch (Exception e) {
            log.error("导入数据错误：", e);
            throw new ValidationException(e.getMessage());
        }
    }

    private void handleEnergyConsumptionPlanData(Map<Integer, List<AchievementRateDataNode>> cycleAndNodeMap) {
        if (MapUtils.isEmpty(cycleAndNodeMap)) {
            return;
        }

        List<EnergyConsumptionPlan> dataList = new ArrayList<>();
        cycleAndNodeMap.forEach((cycle, val) -> {
            dataList.addAll(handleEnergyConsumptionPlanData(val, cycle));
        });
        //查历史数据对比
//        achievementRateDataHandleService.writeEnergyConsumptionPlanByCycle(dataList,GlobalInfoUtils.getProjectId());
        achievementRateDataHandleService.writeEnergyConsumptionPlanDataByCycle(dataList, GlobalInfoUtils.getProjectId());
//        givenEnergyConsumptionPlanDao.writeEnergyConsumptionPlan(dataList);

    }

    private List<EnergyConsumptionPlan> handleEnergyConsumptionPlanData(List<AchievementRateDataNode> dataList, Integer cycle) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }

        List<EnergyConsumptionPlan> productionDataList = new ArrayList<>();
        for (AchievementRateDataNode systemDataNode : dataList) {
            EnergyConsumptionPlan data = new EnergyConsumptionPlan();
            productionDataList.add(data);
            data.setLogtime(TimeUtil.localDateTime2timestamp(systemDataNode.getLogTime()));
            data.setAggregationCycle(cycle);
            data.setObjectId(systemDataNode.getObjectId());
            data.setObjectLabel(systemDataNode.getObjectLabel());
            data.setValue(systemDataNode.getValue());

            data.setEnergyType(systemDataNode.getEnergyType());

        }

        return productionDataList;
    }

    private void handleEffPlanData(Map<Integer, List<AchievementRateDataNode>> cycleAndNodeMap) {
        if (MapUtils.isEmpty(cycleAndNodeMap)) {
            return;
        }

        List<EnergyEfficiencyDataPlan> dataList = new ArrayList<>();
        cycleAndNodeMap.forEach((cycle, val) -> {
            dataList.addAll(handleEffPlanData(val, cycle));
        });
        dataList.forEach(data -> data.setProjectId(GlobalInfoUtils.getProjectId()));
        //查历史数据对比
        achievementRateDataHandleService.writeEffDataByCycle(dataList, GlobalInfoUtils.getProjectId());
//        energyEfficiencyDataPlanDao.writeEnergyEffData(dataList);
    }

    private List<EnergyEfficiencyDataPlan> handleEffPlanData(List<AchievementRateDataNode> dataList, Integer cycle) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }

        List<EnergyEfficiencyDataPlan> productionDataList = new ArrayList<>();
        for (AchievementRateDataNode systemDataNode : dataList) {
            EnergyEfficiencyDataPlan data = new EnergyEfficiencyDataPlan();
            productionDataList.add(data);
            data.setLogTime(TimeUtil.localDateTime2timestamp(systemDataNode.getLogTime()));
            data.setAggregationCycle(cycle);
            data.setObjectId(systemDataNode.getObjectId());
            data.setObjectLabel(systemDataNode.getObjectLabel());
            data.setValue(systemDataNode.getValue());
            data.setProductType(systemDataNode.getProductType());
            data.setEnergyType(systemDataNode.getEnergyType());
            data.setEnergyEfficiencySetId(systemDataNode.getEffSetId());
        }

        return productionDataList;
    }

    private void handleObjectCostValuePlan(Map<Integer, List<AchievementRateDataNode>> cycleAndNodeMap) {
        if (MapUtils.isEmpty(cycleAndNodeMap)) {
            return;
        }

        List<ObjectCostValuePlan> dataList = new ArrayList<>();
        cycleAndNodeMap.forEach((cycle, val) -> {
            dataList.addAll(handleObjectCostValuePlan(val, cycle));
        });
        //加上项目id
        dataList.forEach(data -> data.setProjectId(GlobalInfoUtils.getProjectId()));
        achievementRateDataHandleService.writeObjectCostValuePlanByCycle(dataList, GlobalInfoUtils.getProjectId());
    }

    /**
     * 校验多余的能耗类别
     *
     * @param dataList
     */
    private void checkEnergyType(List<ObjectCostValuePlan> dataList) {
        List<BaseVo> voList = dataList.stream().map(item -> new BaseVo(item.getObjectId(), item.getObjectLabel())).distinct().collect(Collectors.toList());
        Set<Integer> energys = dataList.stream().map(ObjectCostValuePlan::getEnergyType).collect(Collectors.toSet());
        List<CostCheckNodeConfig> costCheckNodeConfigs = getCostCheckNodeConfigList(voList);
        List<CostCheckPlan> costCheckPlans = queryCheckPlan(costCheckNodeConfigs);
        List<Long> feeSchemeIds = new ArrayList<>();
        costCheckPlans.forEach(it -> {
            if (CollectionUtils.isEmpty(it.getCostcheckitem_model())) {
                return;
            }
            feeSchemeIds.addAll(
                    it.getCostcheckitem_model()
                            .stream()
                            .map(CostCheckItem::getFeescheme_id)
                            .collect(Collectors.toList()));
        });
        List<FeeScheme> feeSchemes = feeSchemeDao.queryFeeRecord(feeSchemeIds, StringUtils.EMPTY);
        Set<Integer> energyTypes = feeSchemes.stream().map(FeeScheme::getEnergytype).collect(Collectors.toSet());
        //综合成本
        energyTypes.add(EnergyTypeDef.STANDARD_COAL);
        boolean containsAll = energyTypes.containsAll(energys);
        if (!containsAll) {
            throw new ValidationException("当前存在多余错误的成本费用类型,请去除后重新导入!");
        }
    }

    private List<ObjectCostValuePlan> handleObjectCostValuePlan(List<AchievementRateDataNode> dataList, Integer cycle) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }

        List<ObjectCostValuePlan> productionDataList = new ArrayList<>();
        for (AchievementRateDataNode systemDataNode : dataList) {
            ObjectCostValuePlan data = new ObjectCostValuePlan();
            productionDataList.add(data);
            data.setLogTime(TimeUtil.localDateTime2timestamp(systemDataNode.getLogTime()));
            data.setAggregationCycle(cycle);
            data.setObjectId(systemDataNode.getObjectId());
            data.setObjectLabel(systemDataNode.getObjectLabel());
            data.setValue(systemDataNode.getValue());
            data.setEnergyType(systemDataNode.getEnergyType());
        }

        return productionDataList;
    }

    private void handleUnitObjectCostValuePlan(Map<Integer, List<AchievementRateDataNode>> cycleAndNodeMap) {
        if (MapUtils.isEmpty(cycleAndNodeMap)) {
            return;
        }

        List<UnitObjectCostValuePlan> dataList = new ArrayList<>();
        cycleAndNodeMap.forEach((cycle, val) -> {
            dataList.addAll(handleUnitObjectCostValuePlan(val, cycle));
        });
        dataList.forEach(data -> data.setProjectId(GlobalInfoUtils.getProjectId()));
        achievementRateDataHandleService.writeUnitObjectCostValuePlanByCycle(dataList, GlobalInfoUtils.getProjectId());
    }

    private List<UnitObjectCostValuePlan> handleUnitObjectCostValuePlan(List<AchievementRateDataNode> dataList, Integer cycle) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }

        List<UnitObjectCostValuePlan> productionDataList = new ArrayList<>();
        for (AchievementRateDataNode systemDataNode : dataList) {
            UnitObjectCostValuePlan data = new UnitObjectCostValuePlan();
            productionDataList.add(data);
            data.setLogTime(TimeUtil.localDateTime2timestamp(systemDataNode.getLogTime()));
            data.setAggregationCycle(cycle);
            data.setObjectId(systemDataNode.getObjectId());
            data.setObjectLabel(systemDataNode.getObjectLabel());
            data.setValue(systemDataNode.getValue());
            data.setEnergyType(systemDataNode.getEnergyType());
            data.setProductType(systemDataNode.getProductType());
        }

        return productionDataList;
    }

    private LocalDateTime getLocalDateTime(String[] split, Integer cycle) {
        //长度判断，年只有1，月有2，日有3
        Integer year = Integer.parseInt(split[0]);
        Integer month = 1;
        Integer day = 1;
        if (Objects.equals(cycle, AggregationCycle.ONE_YEAR) && Objects.equals(split.length, YEAR_SIZE)) {
            return LocalDateTime.of(year, month, day, 0, 0);
        } else if (Objects.equals(cycle, AggregationCycle.ONE_MONTH) && Objects.equals(split.length, MONTH_SIZE)) {
            month = Integer.parseInt(split[1]);
            return LocalDateTime.of(year, month, day, 0, 0);
        } else if (Objects.equals(cycle, AggregationCycle.ONE_DAY) && Objects.equals(split.length, DAY_SIZE)) {
            month = Integer.parseInt(split[1]);
            day = Integer.parseInt(split[2]);
            return LocalDateTime.of(year, month, day, 0, 0);
        }
        return null;
    }

    private Long readDate(Cell cell, Integer cycle) {
        if (cell == null) {
            return null;
        }
        Long time = null;
        try {
            String str = cell.getStringCellValue();
            String[] split = StringUtils.split(str, SplitCharDef.STRIKE);
            LocalDateTime of = getLocalDateTime(split, cycle);
//            LocalDateTime of = LocalDateTime.of(Integer.parseInt(split[0]), Integer.parseInt(split[1]), Integer.parseInt(split[2]), 0, 0);
            if (Objects.nonNull(of)) {
                time = TimeUtil.localDateTime2timestamp(of);
            }

        } catch (Exception e) {
            try {
                String str = cell.getStringCellValue();
                String[] split = StringUtils.split(str, DIAGONAL_SYMBOL);
                LocalDateTime of = getLocalDateTime(split, cycle);
//                LocalDateTime of = LocalDateTime.of(Integer.parseInt(split[0]), Integer.parseInt(split[1]), Integer.parseInt(split[2]), 0, 0);
                if (Objects.nonNull(of)) {
                    time = TimeUtil.localDateTime2timestamp(of);
                }
            } catch (Exception ne) {
                try {
                    double num = cell.getNumericCellValue();
                    Calendar calendar = new GregorianCalendar(1900, Calendar.JANUARY, 1);
                    Date d = calendar.getTime();
                    Date dd = org.apache.commons.lang3.time.DateUtils.addDays(d, (int) num - 2);
                    time = DateUtils.dateToTimeStamp(dd);
//                    time = dd.getTime();
                } catch (Exception ignored) {

                }
            }
        }
        if (time == null) {
            throw new ValidationException("时间格式异常或者上传文件表格头与模板不匹配");
        }
        return time;
    }

//    private LocalDateTime readCellTransDate(Cell cell) {
//        if (Objects.isNull(cell)) {
//            return null;
//        }
//        String stringCellValue = cell.getStringCellValue();
//        String[] split = StringUtils.split(stringCellValue, SplitCharDef.STRIKE);
//        return LocalDateTime.of(Integer.parseInt(split[0]), Integer.parseInt(split[1]), Integer.parseInt(split[2]), 0, 0);
//    }

    public short getStartColMultiNodes(Integer dataEntryType) {
        switch (dataEntryType) {
            case AchievementRateDataDef.ENERGY_CONSUMPTION_PLAN:
                return 5;
            case AchievementRateDataDef.EFF_PLAN:
                return 7;
            case AchievementRateDataDef.PRODUCT_PLAN:
                return 5;
            case AchievementRateDataDef.UNIT_OBJECT_COST_PLAN:
                return 6;
            default:
                break;
        }

        return 0;
    }

    private void getEffPlanDataExcelRow(Row row, Row headerRow, short startCol, short endCol, Integer energyType,
                                        List<AchievementRateDataNode> systemDataNodes, BaseVo node, Integer product, Long effId, Integer cycle) {
        LocalDateTime now = LocalDateTime.now();
        for (int j = startCol; j < endCol; j++) {
            AchievementRateDataNode bill = new AchievementRateDataNode();
            bill.setEnergyType(energyType);
            LocalDateTime time = TimeUtil.timestamp2LocalDateTime(readDate(headerRow.getCell(j), cycle));

            bill.setLogTime(time);
            // 能耗值
            Double value = parseDouble(row.getCell(j));
            if (value != null && Double.compare(value, 1000000000000D) >= 0) {
                throw new ValidationException("导入数据超出12位");
            }
            bill.setProductType(product);
            bill.setEffSetId(effId);
            bill.setValue(value);
            if (Objects.nonNull(node)) {
                bill.setObjectId(node.getId());
                bill.setObjectLabel(node.getModelLabel());
            }
            systemDataNodes.add(bill);
        }
    }

    private void getObjectCostValuePlanDataExcelRow(Row row, Row headerRow, short startCol, short endCol, Integer energyType,
                                                    List<AchievementRateDataNode> systemDataNodes, BaseVo node, Integer cycle) {
        LocalDateTime now = LocalDateTime.now();

        for (int j = startCol; j < endCol; j++) {
            AchievementRateDataNode bill = new AchievementRateDataNode();
            bill.setEnergyType(energyType);
            LocalDateTime time = TimeUtil.timestamp2LocalDateTime(readDate(headerRow.getCell(j), cycle));
            bill.setLogTime(time);
            // 能耗值
            Double value = parseDouble(row.getCell(j));
            if (value != null && Double.compare(value, 1000000000000D) >= 0) {
                throw new ValidationException("导入数据超出12位");
            }
            bill.setValue(value);
            if (Objects.nonNull(node)) {
                bill.setObjectId(node.getId());
                bill.setObjectLabel(node.getModelLabel());
            }
            systemDataNodes.add(bill);
        }
    }

    private void getUnitObjectCostValuePlanDataExcelRow(Row row, Row headerRow, short startCol, short endCol, Integer energyType,
                                                        List<AchievementRateDataNode> systemDataNodes, BaseVo node, Integer product, Integer cycle) {
        LocalDateTime now = LocalDateTime.now();

        for (int j = startCol; j < endCol; j++) {
            AchievementRateDataNode bill = new AchievementRateDataNode();
            bill.setEnergyType(energyType);
            LocalDateTime time = TimeUtil.timestamp2LocalDateTime(readDate(headerRow.getCell(j), cycle));

            bill.setLogTime(time);
            // 能耗值
            Double value = parseDouble(row.getCell(j));
            if (value != null && Double.compare(value, 1000000000000D) >= 0) {
                throw new ValidationException("导入数据超出12位");
            }
            bill.setProductType(product);
            bill.setValue(value);
            if (Objects.nonNull(node)) {
                bill.setObjectId(node.getId());
                bill.setObjectLabel(node.getModelLabel());
            }
            systemDataNodes.add(bill);
        }
    }

    private Double parseDouble(Cell cell) {
        String s = PoiExcelUtils.readValueAsString(cell);
        if (Objects.equals(s, CommonUtils.BLANK_STR) || Objects.isNull(s)) {
            return null;
        }
        if (!NumberUtils.isParsable(s)) {
            throw new ValidationException("当前导入数据有非数字,请重新填写!");
        }
        Double value = PoiExcelUtils.readValueAsDouble(cell);
        if (value < 0) {
            throw new ValidationException("当前导入数据有负数,请重新填写!");
        }
        return value;
    }

    private void checkHeaders(Row headerRow, Integer dataEntryType) {
        List<String> headers = createHeader(dataEntryType);
        try {
            for (int i = 0; i < headers.size(); i++) {
                if (!Objects.equals(headerRow.getCell(i).getStringCellValue(), headers.get(i))) {
                    throw new ValidationException("上传文件表格头与模板不匹配");
                }
            }
        } catch (Exception e) {
            throw new ValidationException("上传文件表格头与模板不匹配");
        }

    }

    private Map<String, Integer> queryMeasureByProjectId(int dataEntryType) {
        if (dataEntryType == DataEntryTypeDef.ENERGY) {
            return getEnergyByProjectId();
        } else {
            return getProductByProjectId();
        }
    }

    /**
     * 查询项目的能源类型
     *
     * @return
     */
    private Map<String, Integer> getEnergyByProjectId() {
        Map<Integer, String> energyTypeMap = energyConvertService.getProjectEnergyTypeName(GlobalInfoUtils.getProjectId());
        Map<String, Integer> result = new HashMap<>(energyTypeMap.size());
        energyTypeMap.forEach((key, val) -> {
            result.put(val, key);
        });

        if (energyTypeMap.size() != result.size()) {
            throw new ValidationException("项目配置的能源类型名称有重复，请修改！");
        }

        return result;
    }

    /**
     * 查询项目的费用类型
     *
     * @return
     */
    private Map<String, Integer> getFeeTypeByProjectId() {
        Map<Integer, String> energyTypeMap = energyConvertService.getProjectEnergyTypeName(GlobalInfoUtils.getProjectId());
        Map<String, Integer> result = new HashMap<>(energyTypeMap.size());
        energyTypeMap.forEach((key, val) -> {
            if (Objects.equals(key, EnergyTypeDef.STANDARD_COAL)) {
                result.put("综合成本", key);
            } else {
                result.put(val + "费", key);
            }
        });
        if (!result.containsKey("综合成本")) {
            result.put("综合成本", EnergyTypeDef.STANDARD_COAL);
        }
        if (energyTypeMap.size() != result.size()) {
            throw new ValidationException("项目配置的能源类型名称有重复，请修改！");
        }

        return result;
    }

    private Map<BaseVo, Set<Integer>> createCheckConfigByNodes() {
        List<CostCheckNodeConfig> costCheckNodeConfigs = costCheckNodeConfigDao.selectAll();

        if (CollectionUtils.isEmpty(costCheckNodeConfigs)) {
            return Collections.emptyMap();
        }
        List<CostCheckPlan> costCheckPlans = queryCheckPlan(costCheckNodeConfigs);
        if (CollectionUtils.isEmpty(costCheckNodeConfigs)) {
            return Collections.emptyMap();
        }
        List<Long> feeSchemeIds = new ArrayList<>();
        costCheckPlans.forEach(it -> {
            if (CollectionUtils.isEmpty(it.getCostcheckitem_model())) {
                return;
            }
            feeSchemeIds.addAll(
                    it.getCostcheckitem_model()
                            .stream()
                            .map(CostCheckItem::getFeescheme_id)
                            .collect(Collectors.toList()));
        });
        List<FeeScheme> feeSchemes = feeSchemeDao.queryFeeRecord(feeSchemeIds, StringUtils.EMPTY);
        List<BaseVo> nodes = costCheckNodeConfigs.stream().map(it -> new BaseVo(it.getObjectid(), it.getObjectlabel())).distinct().collect(Collectors.toList());
        return createNodeWithEnergyType(nodes, costCheckNodeConfigs, costCheckPlans,
                feeSchemes);
    }

    /**
     * 查询项目的能源类型
     *
     * @return
     */
    private Map<String, Integer> getProductByProjectId() {
        Map<Integer, String> energyTypeMap = productDao.queryProjectProductType(GlobalInfoUtils.getProjectId());
        Map<String, Integer> result = new HashMap<>(energyTypeMap.size());
        energyTypeMap.forEach((key, val) -> {
            result.put(val, key);
        });

        if (energyTypeMap.size() != result.size()) {
            throw new ValidationException("项目配置的产品类型名称有重复，请修改！");
        }

        return result;
    }

    private String getDateNameByCycle(LocalDateTime time, Integer cycle) {
        if (Objects.equals(cycle, AggregationCycle.ONE_YEAR)) {
            return TimeUtil.format(time, TimeUtil.YEAR_TIME_FORMAT);
        } else if (Objects.equals(cycle, AggregationCycle.ONE_MONTH)) {
            return TimeUtil.format(time, TimeUtil.YYYY_MM);
        } else {
            return TimeUtil.format(time, TimeUtil.DATE_TIME_FORMAT);
        }
    }

    private void exportEnergyEffPlan(AchievementRateQueryVo queryVo) {
        // 查询能耗/产量数据
        List<AchievementRateDataNodeReturnVo> achievementRateDataNodeReturnVos = queryAchievementRateData(queryVo);
      /*  if (CollectionUtils.isEmpty(achievementRateDataNodeReturnVos)) {
            throw new ValidationException("数据查询异常！");
        }*/

        // 查询节点数据
        List<BaseVo> nodes = nodeDao.queryNodes(queryVo.getNodes(), BaseVo.class);


        try(Workbook workbook = PoiExcelUtils.createWorkBook(ExcelType.BIG_DATA);) {
            String name = getAchievementExportName(queryVo.getDataEntryType());
            String workbookName = name + "(" + getDateName(queryVo.getAggregationCycle(), TimeUtil.localDateTime2timestamp(queryVo.getStartTime()),
                    TimeUtil.localDateTime2timestamp(queryVo.getEndTime())) + ")";
            List<String> headers = createAchievementHeadersWithNode(queryVo.getStartTime(), queryVo.getEndTime(),
                    queryVo.getAggregationCycle(), queryVo.getDataEntryType());
            List<EnergyEfficiencySetVo> energyEfficiencySetVos = new ArrayList<>();
            if (Objects.equals(queryVo.getDataEntryType(), AchievementRateDataDef.PRODUCT_PLAN)) {
                energyEfficiencySetVos = energyEfficiencySetDao.queryEfSets(GlobalInfoUtils.getProjectId());
                energyEfficiencySetVos = energyEfficiencySetVos.stream().filter(energyEfficiencySetVo -> Objects.equals(UNIT_TYPE, energyEfficiencySetVo.getUnittype()))
                        .collect(Collectors.toList());
            }
            List<EnergyEfficiencySetVo> finalEnergyEfficiencySetVos = energyEfficiencySetVos;
            Map<BaseVo, AchievementRateDataNodeReturnVo> rateDataNodeReturnVoMap = achievementRateDataNodeReturnVos.stream().collect(Collectors.toMap(it -> new BaseVo(it.getObjectId(), it.getObjectLabel()), Function.identity()));
            PoiExcelUtils.createSheet(workbook, name, (sheet, baseCellStyle, rowIndex) -> {
                Integer rowNum = 0;
                PoiExcelUtils.createHeaderName(sheet, rowNum++, headers, baseCellStyle);
//            createAchievementHeadersWithNode(workbook, sheet, baseCellStyle, rowNum++, headers, queryVo.getDataEntryType(), nodes, finalEnergyEfficiencySetVos);

                Integer count = 1;
                assembleExcelData(sheet, rowNum, baseCellStyle, count,
                        nodes, rateDataNodeReturnVoMap, queryVo);
            }, createColWidth(headers.size()));

            FileUtils.downloadExcel(GlobalInfoUtils.getHttpResponse(), workbook, workbookName, CommonUtils.APPLICATION_MSEXCEL);
        } catch (IOException e) {
            log.error("导出异常", e);
            throw new ValidationException(e.getMessage());
        }

    }

    private void assembleExcelData(Sheet sheet, Integer rowNum, CellStyle baseCellStyle, Integer count,
                                   List<BaseVo> nodes, Map<BaseVo, AchievementRateDataNodeReturnVo> rateDataNodeReturnVoMap, AchievementRateQueryVo queryVo) {
        for (BaseVo node : nodes) {
            //分组来着
            AchievementRateDataNodeReturnVo rateDataNodeReturnVo = rateDataNodeReturnVoMap.get(node);
            if (Objects.isNull(rateDataNodeReturnVo)) {
                continue;
            }
            List<AchievementRateReturnVo> dataNodes = rateDataNodeReturnVo.getDataNodes();
            Map<String, List<AchievementRateReturnVo>> map;
            if (Objects.equals(queryVo.getDataEntryType(), AchievementRateDataDef.ENERGY_CONSUMPTION_PLAN)) {

                map = dataNodes.stream().collect(Collectors.groupingBy(AchievementRateReturnVo::getEnergyTypeName, LinkedHashMap::new, Collectors.toList()));

//            map = dataNodes.stream().collect(Collectors.groupingBy(AchievementRateReturnVo::getEnergyTypeName));
            } else if (Objects.equals(queryVo.getDataEntryType(), AchievementRateDataDef.EFF_PLAN)) {
                map = dataNodes.stream()
                        .collect(Collectors.groupingBy(it -> it.getEffSetName() + SplitCharDef.UNDERLINE + it.getProductTypeName() +
                                SplitCharDef.UNDERLINE + it.getEnergyTypeName(), LinkedHashMap::new, Collectors.toList()));

            } else if (Objects.equals(queryVo.getDataEntryType(), AchievementRateDataDef.PRODUCT_PLAN)) {
                //todo 这块拼接数据注意
                map = dataNodes.stream().collect(Collectors.groupingBy(AchievementRateReturnVo::getEnergyTypeName, LinkedHashMap::new, Collectors.toList()));
            } else {
                map = dataNodes.stream().collect(Collectors.groupingBy(it -> it.getProductTypeName() + SplitCharDef.UNDERLINE + it.getEnergyTypeName(), LinkedHashMap::new, Collectors.toList()));
            }
            String nodeName = NodeFormatUtils.getFormatNode(node);
            //分组来着
            for (Map.Entry<String, List<AchievementRateReturnVo>> entry : map.entrySet()) {
                //也要分几种情况
                Row row = PoiExcelUtils.createRow(sheet, rowNum++);
                int col = 0;
                PoiExcelUtils.createCell(row, col++, baseCellStyle, count++);
                PoiExcelUtils.createCell(row, col++, baseCellStyle, nodeName);
                //todo
                assembleExcel(row, queryVo.getDataEntryType(), col, baseCellStyle,
                        queryVo, entry.getKey(), entry.getValue());
            }
        }

    }

    private void assembleExcel(Row row, Integer dataEntryType, int col, CellStyle baseCellStyle
            , AchievementRateQueryVo queryVo, String key, List<AchievementRateReturnVo> values) {
        for (AchievementRateReturnVo returnVo : values) {
            if (Objects.equals(dataEntryType, AchievementRateDataDef.ENERGY_CONSUMPTION_PLAN)) {
                PoiExcelUtils.createCell(row, col++, baseCellStyle, key);
                PoiExcelUtils.createCell(row, col++, baseCellStyle, returnVo.getUnit());
            } else if (Objects.equals(dataEntryType, AchievementRateDataDef.EFF_PLAN)) {
                String[] split = StringUtils.split(key, SplitCharDef.UNDERLINE);
                PoiExcelUtils.createCell(row, col++, baseCellStyle, split[0]);
                PoiExcelUtils.createCell(row, col++, baseCellStyle, returnVo.getUnit());
                PoiExcelUtils.createCell(row, col++, baseCellStyle, split[1]);
                PoiExcelUtils.createCell(row, col++, baseCellStyle, split[2]);

            } else if (Objects.equals(dataEntryType, AchievementRateDataDef.PRODUCT_PLAN)) {
                //todo 这块拼接数据注意
                PoiExcelUtils.createCell(row, col++, baseCellStyle, key);
                PoiExcelUtils.createCell(row, col++, baseCellStyle, returnVo.getUnit());
            } else {
                String[] split = StringUtils.split(key, SplitCharDef.UNDERLINE);

                PoiExcelUtils.createCell(row, col++, baseCellStyle, split[0]);
                PoiExcelUtils.createCell(row, col++, baseCellStyle, split[1]);
                PoiExcelUtils.createCell(row, col++, baseCellStyle, returnVo.getUnit());
            }
            PoiExcelUtils.createCell(row, col++, baseCellStyle, returnVo.getAggregationCycleName());
            for (LocalDateTime time = queryVo.getStartTime(); time.isBefore(queryVo.getEndTime()); ) {
                LocalDateTime finalTime = time;
                List<TypeInDataDetailVo> tmpList = returnVo.getDetails().stream().filter(it -> it.getLogTime().equals(finalTime)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(tmpList)) {
                    PoiExcelUtils.createCell(row, col++, baseCellStyle, CommonUtils.formatDouble(tmpList.get(0).getValue(), CommonUtils.PRECISION, CommonUtils.BLANK_STR));
                } else {
                    PoiExcelUtils.createCell(row, col++, baseCellStyle, CommonUtils.BLANK_STR);
                }
                time = TimeUtil.addDateTimeByCycle(time, queryVo.getAggregationCycle(), 1);
            }
        }

    }

    private List<Integer> createColWidth(int size) {
        List<Integer> result = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            result.add(18);
        }

        return result;
    }

    private void createAchievementHeadersWithNode(Workbook workbook, Sheet sheet, CellStyle baseCellStyle, int rowNum, List<String> headers,
                                                  int dataEntryType, List<BaseVo> nodes, List<EnergyEfficiencySetVo> energyEfficiencySetVos) {
        PoiExcelUtils.createHeaderName(sheet, rowNum++, headers, baseCellStyle);

        for (int i = 0; i < headers.size(); i++) {
            sheet.setDefaultColumnStyle(i, baseCellStyle);
        }

        int col = 1;
        // 设置节点
        ExcelValidationUtils.addValidationData(workbook, NODE_SHEET_NAME, sheet, NodeFormatUtils.getFormatNodes(nodes), rowNum, col++);
        if (Objects.equals(dataEntryType, AchievementRateDataDef.ENERGY_CONSUMPTION_PLAN)) {
            addEnergyValidation(ModelLabelDef.ENERGY_TYPE, workbook, sheet, rowNum, col++);
        } else if (Objects.equals(dataEntryType, AchievementRateDataDef.EFF_PLAN)) {
            addEffSetTypeValidation(workbook, sheet, rowNum, col++, energyEfficiencySetVos);
        } else if (Objects.equals(dataEntryType, AchievementRateDataDef.PRODUCT_PLAN)) {

            addFeeValidation(ModelLabelDef.ENERGY_TYPE, workbook, sheet, rowNum, col++);
        } else {
            addProductTypeValidation(workbook, sheet, rowNum, col++);
            addFeeValidation(ModelLabelDef.ENERGY_TYPE, workbook, sheet, rowNum, col++);

        }


    }

    private void addEnergyValidation(String label, Workbook workbook, Sheet sheet, int rowNum, int col) {
        Map<Integer, String> energyTypeMap = energyConvertService.getProjectEnergyTypeName(GlobalInfoUtils.getProjectId());
        ExcelValidationUtils.addEnumValidationData(workbook, sheet, rowNum, col, label, energyTypeMap);
    }

    private void addFeeValidation(String label, Workbook workbook, Sheet sheet, int rowNum, int col) {
        Map<Integer, String> energyTypeMap = energyConvertService.getProjectEnergyTypeName(GlobalInfoUtils.getProjectId());
        Map<Integer, String> result = new HashMap<>();
        if (!energyTypeMap.containsKey(EnergyTypeDef.STANDARD_COAL)) {
            result.put(EnergyTypeDef.STANDARD_COAL, "综合成本");
        }
        for (Map.Entry<Integer, String> entry : energyTypeMap.entrySet()) {
            if (Objects.equals(entry.getKey(), EnergyTypeDef.STANDARD_COAL)) {
                result.put(entry.getKey(), "综合成本");
            } else {
                result.put(entry.getKey(), entry.getValue() + "费");
            }


        }
        ExcelValidationUtils.addEnumValidationData(workbook, sheet, rowNum, col, label, result);
    }

    private void addProductTypeValidation(Workbook workbook, Sheet sheet, int rowNum, int col) {
        Map<Integer, String> energyTypeMap = productDao.queryProjectProductType(GlobalInfoUtils.getProjectId());
        ExcelValidationUtils.addEnumValidationData(workbook, sheet, rowNum, col, ProductLabel.LABEL_NAME, energyTypeMap);
    }

    private void addEffSetTypeValidation(Workbook workbook, Sheet sheet, int rowNum, int col, List<EnergyEfficiencySetVo> energyEfficiencySetVos) {
        List<String> nodeValidationList = new ArrayList<>();
        for (EnergyEfficiencySetVo baseVo : energyEfficiencySetVos) {
            nodeValidationList.add(CommonUtils.setDeviceNameAndIdAndModelLabel(baseVo.getName(), baseVo.getModelLabel(), baseVo.getId()));
        }
        String[] nodeValidation = nodeValidationList.toArray(new String[energyEfficiencySetVos.size()]);
        ExcelValidationUtils.addValidationData(workbook, "设备信息", sheet, nodeValidation, rowNum, col);

    }

    //    private void writeHeaderByType(Workbook workbook, Sheet sheet, CellStyle baseCellStyle, int startRow, List<BaseVo> baseVos) {
//        LinkedHashMap<String, CellStyle> headerMap = new LinkedHashMap<>(CommonUtils.MAP_INIT_SIZE_16);
//        int col = 0;
//        List<String> nodeValidationList = new ArrayList<>();
//        for (BaseVo baseVo : baseVos) {
//            nodeValidationList.add(CommonUtils.setDeviceNameAndIdAndModelLabel(baseVo.getName(), baseVo.getModelLabel(), baseVo.getId()));
//        }
//        String[] nodeValidation = nodeValidationList.toArray(new String[nodeValidationList.size()]);
////        CellStyle requiredStyle = createRequiredStyle(workbook, baseCellStyle);
//        headerMap.put("设备节点", requiredStyle);
//        sheet.setDefaultColumnStyle(col, baseCellStyle);
//        ExcelValidationUtils.addValidationData(workbook, "设备信息", sheet, nodeValidation, startRow + 1, col);
//        headerMap.put("零件名称", requiredStyle);
//        headerMap.put("型号", requiredStyle);
//        headerMap.put("厂家", requiredStyle);
//        headerMap.put("数量", requiredStyle);
//        headerMap.put("单位", requiredStyle);
//        PoiExcelUtils.createHeaderName(sheet, startRow, headerMap);
//    }
    private List<String> createAchievementHeadersWithNode(LocalDateTime st, LocalDateTime et, Integer cycle, int dataEntryType) {
        List<String> headers = createHeader(dataEntryType);
        for (LocalDateTime time = st; time.isBefore(et); ) {
            headers.add(getDateNameByCycle(time, cycle));
            time = TimeUtil.addDateTimeByCycle(time, cycle, 1);
        }
        return headers;
    }

    private List<String> createHeader(int dataEntryType) {
        List<String> headers = new ArrayList<>();
        headers.add("序号");
        headers.add("节点");
        if (Objects.equals(dataEntryType, AchievementRateDataDef.ENERGY_CONSUMPTION_PLAN)) {
            headers.add("能源类型");
            headers.add("单位");
        } else if (Objects.equals(dataEntryType, AchievementRateDataDef.EFF_PLAN)) {
            headers.add("指标类型");
            headers.add("单位");
            headers.add("产品类型");
            headers.add("能源类型");
        } else if (Objects.equals(dataEntryType, AchievementRateDataDef.PRODUCT_PLAN)) {
            headers.add("费用类型");
            headers.add("单位");
        } else {
            headers.add("产品类型");
            headers.add("费用类型");
            headers.add("单位");
        }
        headers.add("周期");
        return headers;
    }

    public String getAchievementExportName(Integer dataEntryType) {
        switch (dataEntryType) {
            case AchievementRateDataDef.ENERGY_CONSUMPTION_PLAN:
                return "计划能耗";
            case AchievementRateDataDef.EFF_PLAN:
                return "计划单耗";
            case AchievementRateDataDef.PRODUCT_PLAN:
                return "计划成本";
            case AchievementRateDataDef.UNIT_OBJECT_COST_PLAN:
                return "计划单台成本";
            default:
                break;
        }

        return null;
    }

    public String getCompareExportName(Integer dataEntryType) {
        switch (dataEntryType) {
            case AchievementRateDataDef.ENERGY_CONSUMPTION_PLAN:
                return "总目标能耗";
            case AchievementRateDataDef.EFF_PLAN:
                return "单台目标能耗";
            case AchievementRateDataDef.PRODUCT_PLAN:
                return "总目标成本";
            case AchievementRateDataDef.UNIT_OBJECT_COST_PLAN:
                return "单台目标成本";
            default:
                break;
        }

        return null;
    }

    public String getColExportName(Integer dataEntryType) {
        switch (dataEntryType) {
            case AchievementRateDataDef.ENERGY_CONSUMPTION_PLAN:
                return "能耗";
            case AchievementRateDataDef.EFF_PLAN:
                return "单台能耗";
            case AchievementRateDataDef.PRODUCT_PLAN:
                return "成本";
            case AchievementRateDataDef.UNIT_OBJECT_COST_PLAN:
                return "单台成本";
            default:
                break;
        }

        return null;
    }

    private List<AchievementRateReturnVo> inputUnitObjectCostValuePlan(AchievementRateInputDataVo inputDataVo) {
        //输入数据
        List<UnitObjectCostValuePlan> unitObjectCostValuePlans = inputDataVo.generateUnitObjectCostValuePlan();
        //历史数据
        List<Integer> energTypes = inputDataVo.getDataNodes().stream().map(AchievementRateDataNode::getEnergyType).distinct().collect(Collectors.toList());
        List<Integer> productTypes = inputDataVo.getDataNodes().stream().map(AchievementRateDataNode::getProductType).distinct().collect(Collectors.toList());
        List<UnitObjectCostValuePlan> query = unitObjectCostValuePlanDao.query(TimeUtil.localDateTime2timestamp(inputDataVo.getStartTime()),
                TimeUtil.localDateTime2timestamp(inputDataVo.getEndTime()), energTypes,
                Collections.singletonList(new BaseVo(inputDataVo.getObjectId(), inputDataVo.getObjectLabel())), inputDataVo.getAggregationCycle()
                , productTypes);
        //筛选出需要更新和插入的能耗数据
        for (UnitObjectCostValuePlan plan : unitObjectCostValuePlans) {
            Optional<UnitObjectCostValuePlan> any = query.stream()
                    .filter(s -> Objects.equals(s.getLogTime(), plan.getLogTime())
                            && Objects.equals(s.getEnergyType(), plan.getEnergyType())
                            && Objects.equals(s.getProductType(), plan.getProductType()))
                    .findFirst();
            if (any.isPresent()) {
                UnitObjectCostValuePlan energyConsumptionInDb = any.get();
                plan.setId(energyConsumptionInDb.getId());

            }
            plan.setProjectId(GlobalInfoUtils.getProjectId());
        }
        achievementRateDataHandleService.writeUnitObjectCostValuePlanByCycle(unitObjectCostValuePlans, GlobalInfoUtils.getProjectId());
//        unitObjectCostValuePlanDao.insertData(unitObjectCostValuePlans);
        List<AchievementRateReturnVo> result = new ArrayList<>();
        assemblyUnitObjectCostValuePlan(unitObjectCostValuePlans, result);
        return result;
    }

    /**
     * 组装需要录入的数据格式
     *
     * @param energyEfficiencyDataPlans
     * @param result
     */
    private void assemblyUnitObjectCostValuePlan(List<UnitObjectCostValuePlan> energyEfficiencyDataPlans, List<AchievementRateReturnVo> result) {
        Map<Integer, List<UnitObjectCostValuePlan>> planMap = energyEfficiencyDataPlans.stream().collect(Collectors.groupingBy(UnitObjectCostValuePlan::getEnergyType));
        for (Map.Entry<Integer, List<UnitObjectCostValuePlan>> entry : planMap.entrySet()) {
            Map<Integer, List<UnitObjectCostValuePlan>> map = entry.getValue().stream().collect(Collectors.groupingBy(UnitObjectCostValuePlan::getProductType));
            for (Map.Entry<Integer, List<UnitObjectCostValuePlan>> listEntry : map.entrySet()) {
                AchievementRateReturnVo typeInDataVo = new AchievementRateReturnVo();
                typeInDataVo.setEnergyType(entry.getKey());
                typeInDataVo.setProductType(listEntry.getKey());
                typeInDataVo.setDetails(listEntry.getValue().stream().map(s -> {
                    TypeInDataDetailVo detail = new TypeInDataDetailVo();
                    detail.setId(s.getId());
                    detail.setValue(s.getValue());
                    detail.setLogTime(TimeUtil.timestamp2LocalDateTime(s.getLogTime()));
                    return detail;
                }).collect(Collectors.toList()));
                result.add(typeInDataVo);
            }
        }

    }


    private List<AchievementRateReturnVo> inputEnergyEfficiencyDataPlan(AchievementRateInputDataVo inputDataVo) {
        //输入数据
        List<EnergyEfficiencyDataPlan> energyEfficiencyDataPlans = inputDataVo.generateEnergyConsumption();

        //历史数据
        List<EnergyEfficiencyDataPlan> efficiencyDataPlans = queryPlan(Collections.singletonList(new BaseVo(inputDataVo.getObjectId(), inputDataVo.getObjectLabel())),
                inputDataVo.getStartTime(), inputDataVo.getEndTime(), inputDataVo.getAggregationCycle());
        //4.筛选出需要更新和插入的能耗数据
        for (EnergyEfficiencyDataPlan plan : energyEfficiencyDataPlans) {
            Optional<EnergyEfficiencyDataPlan> any = efficiencyDataPlans.stream()
                    .filter(s -> Objects.equals(s.getLogTime(), plan.getLogTime()))
                    .filter(s -> Objects.equals(s.getEnergyEfficiencySetId(), plan.getEnergyEfficiencySetId()))
                    .findFirst();
            if (any.isPresent()) {
                EnergyEfficiencyDataPlan energyConsumptionInDb = any.get();
                plan.setId(energyConsumptionInDb.getId());

            }
            plan.setProjectId(GlobalInfoUtils.getProjectId());
        }
        achievementRateDataHandleService.writeEffDataByCycle(energyEfficiencyDataPlans, GlobalInfoUtils.getProjectId());
        List<AchievementRateReturnVo> result = new ArrayList<>();
        assemblyEnergyEfficiencyDataPlan(energyEfficiencyDataPlans, result);
        return result;
    }

    private List<AchievementRateReturnVo> inputEnergyConsumptionPlan(AchievementRateInputDataVo inputDataVo) {
        //输入数据
        List<EnergyConsumptionPlan> energyConsumptionPlans = inputDataVo.generateEnergyConsumptionPlan();
        Set<Integer> energyTypes = inputDataVo.getDataNodes().stream().map(AchievementRateDataNode::getEnergyType).collect(Collectors.toSet());

        //历史数据
        List<EnergyConsumptionPlan> consumptionPlans = givenEnergyConsumptionPlanDao.queryEnergyConsumptionBatch(Collections.singletonList(new BaseVo(inputDataVo.getObjectId(), inputDataVo.getObjectLabel())),
                TimeUtil.localDateTime2timestamp(inputDataVo.getStartTime()), TimeUtil.localDateTime2timestamp(inputDataVo.getEndTime()), inputDataVo.getAggregationCycle(), energyTypes);


        //4.筛选出需要更新和插入的能耗数据
        for (EnergyConsumptionPlan plan : energyConsumptionPlans) {
            Optional<EnergyConsumptionPlan> any = consumptionPlans.stream()
                    .filter(s -> Objects.equals(s.getLogtime(), plan.getLogtime()))
                    .filter(s -> Objects.equals(s.getEnergyType(), plan.getEnergyType()))
                    .findFirst();
            if (any.isPresent()) {
                EnergyConsumptionPlan energyConsumptionInDb = any.get();
                plan.setId(energyConsumptionInDb.getId());

            }
        }
        achievementRateDataHandleService.writeEnergyConsumptionPlanDataByCycle(energyConsumptionPlans, GlobalInfoUtils.getProjectId());
//        givenEnergyConsumptionPlanDao.insertData(energyConsumptionPlans);
//        summarizedByCoef(TimeUtil.localDateTime2timestamp(inputDataVo.getStartTime()), TimeUtil.localDateTime2timestamp(inputDataVo.getEndTime())
//                , GlobalInfoUtils.getProjectId(), inputDataVo.getObjectId(), inputDataVo.getObjectLabel(), inputDataVo.getAggregationCycle());
        List<AchievementRateReturnVo> result = new ArrayList<>();
        assemblyEnergyConsumptionPlan(energyConsumptionPlans, result);
        return result;
    }

    /**
     * 组装需要录入的数据格式
     *
     * @param energyEfficiencyDataPlans
     * @param result
     */
    private void assemblyEnergyConsumptionPlan(List<EnergyConsumptionPlan> energyEfficiencyDataPlans, List<AchievementRateReturnVo> result) {
        Map<Integer, List<EnergyConsumptionPlan>> planMap = energyEfficiencyDataPlans.stream().collect(Collectors.groupingBy(EnergyConsumptionPlan::getEnergyType));
        planMap.forEach((key, val) -> {
            AchievementRateReturnVo typeInDataVo = new AchievementRateReturnVo();
            typeInDataVo.setEnergyType(key);
            typeInDataVo.setDetails(val.stream().map(s -> {
                TypeInDataDetailVo detail = new TypeInDataDetailVo();
                detail.setId(s.getId());
                detail.setValue(s.getValue());
                detail.setLogTime(TimeUtil.timestamp2LocalDateTime(s.getLogtime()));
                return detail;
            }).collect(Collectors.toList()));
            result.add(typeInDataVo);
        });
    }

    /**
     * 统计折标能耗
     *
     * @param st
     * @param et
     * @param projectId
     */

    public void summarizedByCoef(Long st, Long et, Long projectId, Long objectId, String objectLabel, Integer aggregationCycle) {
        List<EnergyConsumptionPlan> oldDataList = energyConsumptionPlanDao.queryEnergyConsumption(new BaseVo(objectId, objectLabel), st, et, aggregationCycle, null);
        List<ConvertedStandardCoalCoef> coefs = energyConvertDao.queryConvertedStandardCoef(projectId, EnergyTypeDef.STANDARD_COAL);
        Map<Integer, Double> coefMap = coefs.stream().collect(Collectors.toMap(ConvertedStandardCoalCoef::getSourceenergytype, ConvertedStandardCoalCoef::getCoef, (v1, v2) -> v2));

        // 过滤出非折标的能耗
        Map<Long, List<EnergyConsumptionPlan>> oldTimeDataMap = oldDataList.stream().filter(it -> it.getEnergyType() != EnergyTypeDef.STANDARD_COAL)
                .collect(Collectors.groupingBy(EnergyConsumptionPlan::getLogtime));
        List<EnergyConsumptionPlan> newDataList = new ArrayList<>();
        oldTimeDataMap.forEach((time, val) -> {
            double total = val.stream().mapToDouble(it -> {
                Double coef = coefMap.get(it.getEnergyType());
                if (coef == null) {
                    return 0;
                }

                return coef * (it.getValue() == null ? 0D : it.getValue());
            }).sum();

            Optional<EnergyConsumptionPlan> any = oldDataList.stream()
                    .filter(it -> it.getLogtime().equals(time) && it.getEnergyType() == EnergyTypeDef.STANDARD_COAL).findFirst();
            EnergyConsumptionPlan obj = any.orElseGet(EnergyConsumptionPlan::new);

            obj.setObjectId(objectId);
            obj.setObjectLabel(objectLabel);
            obj.setValue(total);
            obj.setEnergyType(EnergyTypeDef.STANDARD_COAL);
            obj.setAggregationCycle(aggregationCycle);
            obj.setLogtime(time);


            newDataList.add(obj);
        });

        givenEnergyConsumptionPlanDao.insertData(newDataList);
        commonUtilsService.writeUpdateOperationLogs(EEMOperationLogType.BILL, "保存折标账单数据", newDataList);
    }

    private List<AchievementRateReturnVo> inputObjectCostValuePlan(AchievementRateInputDataVo inputDataVo) {
        //输入数据
        List<ObjectCostValuePlan> objectCostValuePlans = inputDataVo.generateObjectCostValuePlan();
        //历史数据
        List<ObjectCostValuePlan> costValuePlans = queryObjectCostValuePlan(Collections.singletonList(new BaseVo(inputDataVo.getObjectId(), inputDataVo.getObjectLabel())),
                inputDataVo.getStartTime(), inputDataVo.getEndTime(), inputDataVo.getAggregationCycle());
        //4.筛选出需要更新和插入的能耗数据
        for (ObjectCostValuePlan plan : objectCostValuePlans) {
            Optional<ObjectCostValuePlan> any = costValuePlans.stream()
                    .filter(s -> Objects.equals(s.getLogTime(), plan.getLogTime()))
                    .filter(s -> Objects.equals(s.getEnergyType(), plan.getEnergyType()))
                    .findFirst();
            if (any.isPresent()) {
                ObjectCostValuePlan energyConsumptionInDb = any.get();
                plan.setId(energyConsumptionInDb.getId());

            }
            plan.setProjectId(GlobalInfoUtils.getProjectId());
        }
        achievementRateDataHandleService.writeObjectCostValuePlanByCycle(objectCostValuePlans, GlobalInfoUtils.getProjectId());
//        objectCostValuePlanDao.insertObjectCostValuePlan(objectCostValuePlans);
        List<AchievementRateReturnVo> result = new ArrayList<>();
        assemblyObjectCostValuePlan(objectCostValuePlans, result);
        return result;
    }

    /**
     * 组装需要录入的数据格式
     *
     * @param energyEfficiencyDataPlans
     * @param result
     */
    private void assemblyObjectCostValuePlan(List<ObjectCostValuePlan> energyEfficiencyDataPlans, List<AchievementRateReturnVo> result) {
        Map<Integer, List<ObjectCostValuePlan>> planMap = energyEfficiencyDataPlans.stream().collect(Collectors.groupingBy(ObjectCostValuePlan::getEnergyType));
        planMap.forEach((key, val) -> {
            AchievementRateReturnVo typeInDataVo = new AchievementRateReturnVo();
            typeInDataVo.setEnergyType(key);
            typeInDataVo.setDetails(val.stream().map(s -> {
                TypeInDataDetailVo detail = new TypeInDataDetailVo();
                detail.setId(s.getId());
                detail.setValue(s.getValue());
                detail.setLogTime(TimeUtil.timestamp2LocalDateTime(s.getLogTime()));
                return detail;
            }).collect(Collectors.toList()));
            result.add(typeInDataVo);
        });
    }

    private List<ObjectCostValuePlan> queryObjectCostValuePlan(List<BaseVo> nodes, LocalDateTime st, LocalDateTime et, Integer cycle) {
        Set<Integer> energyType = getEnergyTypeList(nodes);
        if (CollectionUtils.isEmpty(energyType)) {
            return Collections.emptyList();
        }
        return objectCostValuePlanDao.query(TimeUtil.localDateTime2timestamp(st),
                TimeUtil.localDateTime2timestamp(et), new ArrayList<>(energyType), nodes, cycle);
    }

    public List<CostCheckNodeConfig> getCostCheckNodeConfigList(Collection<BaseVo> nodes) {
        QueryConditionBuilder<BaseEntity> builder = new QueryConditionBuilder<>(ModelLabelDef.COST_CHECK_NODE_CONFIG)
                .composeMethod(true);

        Map<String, List<BaseVo>> map = nodes.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        int group = 1;
        for (Map.Entry<String, List<BaseVo>> entry : map.entrySet()) {
            String label = entry.getKey();
            List<Long> ids = map.get(label).stream().map(BaseVo::getId).distinct().collect(Collectors.toList());
            builder.where(ColumnDef.C_OBJECT_ID, ConditionBlock.OPERATOR_IN, ids, group)
                    .where(ColumnDef.C_OBJECT_Label, ConditionBlock.OPERATOR_EQ, label, group);
            group++;
        }

        return modelServiceUtils.query(builder.build(), CostCheckNodeConfig.class);
    }

    private Set<Integer> getEnergyTypeList(List<BaseVo> nodes) {
        //查询节点关联的核算方案--关联的能源类型
//        costCheckNodeConfigDao.queryCostCheckPlan()
        List<CostCheckNodeConfig> costCheckNodeConfigs = getCostCheckNodeConfigList(nodes);
//        List<CostCheckNodeConfig> costCheckNodeConfigs = costCheckNodeConfigDao.queryCostCheckPlan(nodes);
        if (CollectionUtils.isEmpty(costCheckNodeConfigs)) {
            return new HashSet<>();
        }
        List<CostCheckPlan> costCheckPlans = queryCheckPlan(costCheckNodeConfigs);
        if (CollectionUtils.isEmpty(costCheckNodeConfigs)) {
            return new HashSet<>();
        }
        List<Long> feeSchemeIds = new ArrayList<>();
        costCheckPlans.forEach(it -> {
            if (CollectionUtils.isEmpty(it.getCostcheckitem_model())) {
                return;
            }
            feeSchemeIds.addAll(
                    it.getCostcheckitem_model()
                            .stream()
                            .map(CostCheckItem::getFeescheme_id)
                            .collect(Collectors.toList()));
        });
        List<FeeScheme> feeSchemes = feeSchemeDao.queryFeeRecord(feeSchemeIds, StringUtils.EMPTY);
        Set<Integer> energyType = feeSchemes.stream().map(FeeScheme::getEnergytype).collect(Collectors.toSet());
        //综合成本
        energyType.add(EnergyTypeDef.STANDARD_COAL);
        return energyType;
    }

    /**
     * 组装需要录入的数据格式
     *
     * @param energyEfficiencyDataPlans
     * @param result
     */
    private void assemblyEnergyEfficiencyDataPlan(List<EnergyEfficiencyDataPlan> energyEfficiencyDataPlans, List<AchievementRateReturnVo> result) {
        Map<Long, List<EnergyEfficiencyDataPlan>> planMap = energyEfficiencyDataPlans.stream().collect(Collectors.groupingBy(EnergyEfficiencyDataPlan::getEnergyEfficiencySetId));
        planMap.forEach((key, val) -> {
            AchievementRateReturnVo typeInDataVo = new AchievementRateReturnVo();
            typeInDataVo.setEffSetId(key);
            typeInDataVo.setDetails(val.stream().map(s -> {
                TypeInDataDetailVo detail = new TypeInDataDetailVo();
                detail.setId(s.getId());
                detail.setValue(s.getValue());
                detail.setLogTime(TimeUtil.timestamp2LocalDateTime(s.getLogTime()));
                return detail;
            }).collect(Collectors.toList()));
            result.add(typeInDataVo);
        });
    }

    private List<EnergyEfficiencyDataPlan> queryPlan(List<BaseVo> nodes, LocalDateTime st, LocalDateTime et, Integer cycle) {

        List<EnergyEfficiencySetVo> efficiencySetVos = queryEnergyEffSetList(nodes, null, null);
        List<Long> setIdList = efficiencySetVos.stream().map(EnergyEfficiencySetVo::getId).distinct().collect(Collectors.toList());
        ////查询和产品有关联的
        return energyEfficiencyDataPlanDao.query(TimeUtil.localDateTime2timestamp(st),
                TimeUtil.localDateTime2timestamp(et), setIdList, nodes, cycle);
    }

    private List<EnergyEfficiencySetVo> queryEnergyEffSetList(List<BaseVo> nodes, Integer cycle, Integer energyType) {
        if (CollectionUtils.isEmpty(nodes)) {
            return queryEnergyEffSetListByEnergyAndCycle(GlobalInfoUtils.getProjectId(), cycle, energyType);
        }
        List<KpiSetVo> kpiSetList = kpiSetDao.getKpiSetList(nodes);
        if (CollectionUtils.isEmpty(kpiSetList)) {
            return Collections.emptyList();
        }
        List<Long> setIds = kpiSetList.stream().map(KpiSetVo::getEnergyefficiencyset_id).distinct().collect(Collectors.toList());

        List<EnergyEfficiencySetVo> energyEfficiencySetVos = energyEfficiencySetDao.queryEnergyEfficiencySetByIds(setIds);
        if (Objects.nonNull(cycle)) {
            energyEfficiencySetVos = energyEfficiencySetVos.stream()
                    .filter(energyEfficiencySetVo -> Objects.equals(energyEfficiencySetVo.getAggregationcycle(), cycle))
                    .collect(Collectors.toList());
        }
        if (Objects.nonNull(energyType)) {
            energyEfficiencySetVos = energyEfficiencySetVos.stream()
                    .filter(energyEfficiencySetVo -> Objects.equals(energyEfficiencySetVo.getEnergytype(), energyType))
                    .collect(Collectors.toList());
        }
        return energyEfficiencySetVos.stream().filter(energyEfficiencySetVo -> Objects.equals(energyEfficiencySetVo.getUnittype(), UNIT_TYPE))
                .collect(Collectors.toList());
    }

    private List<EnergyEfficiencySetVo> queryEnergyEffSetListByEnergyAndCycle(Long projectId, Integer cycle, Integer energyType) {
        QueryConditionBuilder<BaseEntity> builder = new QueryConditionBuilder<>(ModelLabelDef.ENERGY_EFFICIENCY_SET)
                .setParentNode(projectId, NodeLabelDef.PROJECT)
                .eq(ColumnDef.AGGREGATION_CYCLE, cycle)
                .eq(ColumnDef.ENERGY_TYPE, energyType)
                .eq(ColumnDef.UNIT_TYPE, UNIT_TYPE);
        return modelServiceUtils.query(builder.build(), EnergyEfficiencySetVo.class);
    }

    private List<AchievementRateDataNodeReturnVo> queryUnitObjectCostValuePlanData(AchievementRateQueryVo queryVo) {
        //查询节点关联的核算方案--关联的能源类型
        List<CostCheckNodeConfig> costCheckNodeConfigs = getCostCheckNodeConfigList(queryVo.getNodes());
        if (CollectionUtils.isEmpty(costCheckNodeConfigs)) {
            return Collections.emptyList();
        }
        List<CostCheckPlan> costCheckPlans = queryCheckPlan(costCheckNodeConfigs);
        if (CollectionUtils.isEmpty(costCheckNodeConfigs)) {
            return Collections.emptyList();
        }
        List<Long> feeSchemeIds = new ArrayList<>();
        costCheckPlans.forEach(it -> {
            if (CollectionUtils.isEmpty(it.getCostcheckitem_model())) {
                return;
            }
            feeSchemeIds.addAll(
                    it.getCostcheckitem_model()
                            .stream()
                            .map(CostCheckItem::getFeescheme_id)
                            .collect(Collectors.toList()));
        });
        List<FeeScheme> feeSchemes = feeSchemeDao.queryFeeRecord(feeSchemeIds, StringUtils.EMPTY);
        //节点对应的能源类型
        List<BaseVo> nodes = costCheckNodeConfigs.stream().map(it -> new BaseVo(it.getObjectid(), it.getObjectlabel())).distinct().collect(Collectors.toList());
        Map<BaseVo, Set<Integer>> nodeEnergyType = createNodeWithEnergyType(nodes, costCheckNodeConfigs, costCheckPlans,
                feeSchemes);

        Set<Integer> energyType = feeSchemes.stream().map(FeeScheme::getEnergytype).collect(Collectors.toSet());
        //综合成本
        energyType.add(EnergyTypeDef.STANDARD_COAL);
        List<UnitObjectCostValuePlan> query = unitObjectCostValuePlanDao.query(TimeUtil.localDateTime2timestamp(queryVo.getStartTime()),
                TimeUtil.localDateTime2timestamp(queryVo.getEndTime()), energyType, queryVo.getNodes(), queryVo.getAggregationCycle()
                , queryVo.getProductTypes());
        Map<BaseVo, List<AchievementRateDataNodeVo>> baseVoListMap = queryAchievementRateDataVoByUnitObjectCostValuePlan(queryVo.getNodes(), queryVo.getStartTime(),
                queryVo.getEndTime(), queryVo.getAggregationCycle(),
                query,
                new ArrayList<>(energyType), queryVo.getProductTypes(),nodeEnergyType);
        List<AchievementRateDataNodeReturnVo> result = new ArrayList<>();
        for (Map.Entry<BaseVo, List<AchievementRateDataNodeVo>> entry : baseVoListMap.entrySet()) {
            AchievementRateDataNodeReturnVo rateDataNodeReturnVo = new AchievementRateDataNodeReturnVo();
            List<AchievementRateReturnVo> dataNodes = transData(entry.getValue());
            dataNodes = dataNodes.stream().sorted(Comparator.comparing(AchievementRateReturnVo::getProductId).reversed()
                            .thenComparing(AchievementRateReturnVo::getIsCoef).reversed()
                            .thenComparing(AchievementRateReturnVo::getEnergyType))
                    .collect(Collectors.toList());
            rateDataNodeReturnVo.setDataNodes(dataNodes);
            rateDataNodeReturnVo.setObjectId(entry.getKey().getId());
            rateDataNodeReturnVo.setObjectLabel(entry.getKey().getModelLabel());
            result.add(rateDataNodeReturnVo);
        }
        return result;
    }

    private List<AchievementRateReturnVo> transData(List<AchievementRateDataNodeVo> value) {
        List<AchievementRateReturnVo> rateReturnVos = new ArrayList<>();
        Map<String, List<AchievementRateDataNodeVo>> stringListMap = value.stream().collect(Collectors.groupingBy(it -> it.getEffSetName() + SplitCharDef.UNDERLINE + it.getProductTypeName() + SplitCharDef.UNDERLINE +
                it.getEnergyTypeName()));
        //产品类型放能源类型前面
        for (Map.Entry<String, List<AchievementRateDataNodeVo>> entry : stringListMap.entrySet()) {
            AchievementRateReturnVo returnVo = new AchievementRateReturnVo();
            List<AchievementRateDataNodeVo> entryValue = entry.getValue();

            AchievementRateDataNodeVo target = entryValue.get(0);
            BeanUtils.copyProperties(target, returnVo);

            List<TypeInDataDetailVo> details = new ArrayList<>();
            returnVo.setDetails(details);
            rateReturnVos.add(returnVo);
            for (AchievementRateDataNodeVo nodeVo : entryValue) {
                TypeInDataDetailVo typeInDataDetailVo = new TypeInDataDetailVo();
                typeInDataDetailVo.setId(nodeVo.getId());
                typeInDataDetailVo.setLogTime(nodeVo.getLogTime());
                typeInDataDetailVo.setValue(nodeVo.getValue());
                details.add(typeInDataDetailVo);
            }
        }
        return rateReturnVos;
    }

    private Map<BaseVo, List<AchievementRateDataNodeVo>> queryAchievementRateDataVoByUnitObjectCostValuePlan(List<BaseVo> nodes, LocalDateTime st,
                                                                                                             LocalDateTime et, Integer cycle,
                                                                                                             List<UnitObjectCostValuePlan> query,
                                                                                                             List<Integer> energyType, List<Integer> productTypes,
                                                                                                             Map<BaseVo, Set<Integer>> nodeEnergyType) {
        if (CollectionUtils.isEmpty(query)) {
            query = new ArrayList<>();
        }
        Map<BaseVo, List<UnitObjectCostValuePlan>> energyConsumptionMap = query.stream()
                .collect(Collectors.groupingBy(it -> new BaseVo(it.getObjectId(), it.getObjectLabel())));

        // 根据起止时间获取时间列表
        List<LocalDateTime> localDateTimeList = TimeUtil.generateTimeValueOfPeriod(st, et, cycle);
        if (CollectionUtils.isEmpty(localDateTimeList)) {
            return Collections.emptyMap();
        }
        List<Product> products = productDao.queryProducts(GlobalInfoUtils.getProjectId());
        Map<Integer, Product> productMap = products.stream().collect(Collectors.toMap(Product::getProductType, Function.identity()));
//        Map<Integer, String> productTypeMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.PRODUCT_TYPE);
        Map<Integer, String> cycleMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.AGGREGATION_CYCLE);
        // 查询项目能源类型
        List<ProjectEnergyType> projectEnergyTypes = projectEnergyTypeService.queryEnergyTypes(GlobalInfoUtils.getProjectId(), energyType);
        Map<Integer, ProjectEnergyType> energyTypeMap = projectEnergyTypes.stream().collect(Collectors.toMap(ProjectEnergyType::getEnergytype, item -> item, (v1, v2) -> v1));
        //构建节点和能源类型的关系
        //查成本单位和钱的单位
        //查钱的单位
        UserDefineUnitSearchDto dto = new UserDefineUnitSearchDto(GlobalInfoUtils.getProjectId(), OtherUnitTypeDef.RMB, ProjectUnitClassify.OTHER);
        ResultWithTotal<List<UserDefineUnit>> resultWithTotal = energyUnitService.queryUserDefineUnit(dto);
        String unit = null;
        if (CollectionUtils.isNotEmpty(resultWithTotal.getData())) {
            unit = resultWithTotal.getData().get(0).getBasicUnitSymbolName();
        }
        Map<Integer, String> unitStringMap = energyUnitService.queryBasicUnitMap(productMap.keySet(), ProjectUnitClassify.PRODUCT);
        Map<BaseVo, List<AchievementRateDataNodeVo>> result = new HashMap<>();
        for (BaseVo node : nodes) {
            Set<Integer> energys = nodeEnergyType.get(node);
            if (Objects.isNull(energys)){
                continue;
            }
            List<AchievementRateDataNodeVo> achievementRateDataNodeVos = handleSingleNodeEnergyByUnitObjectCostValuePlan(energyConsumptionMap.get(node),
                    localDateTimeList,
                    cycleMap, cycle, energyTypeMap, energyType, productTypes, productMap, unitStringMap, unit
            );
            achievementRateDataNodeVos = achievementRateDataNodeVos.stream().filter(item -> energys.contains(item.getEnergyType())).collect(Collectors.toList());
            result.put(node, achievementRateDataNodeVos);
        }

        return result;
    }


    private List<AchievementRateDataNodeVo> handleSingleNodeEnergyByUnitObjectCostValuePlan(List<UnitObjectCostValuePlan> query,
                                                                                            List<LocalDateTime> localDateTimeList,
                                                                                            Map<Integer, String> cycleMap, Integer cycle, Map<Integer, ProjectEnergyType> energyTypeMap,
                                                                                            List<Integer> energyTypes, List<Integer> productTypes,
                                                                                            Map<Integer, Product> productMap
            , Map<Integer, String> unitStringMap, String unit) {
        if (Objects.isNull(query)) {
            query = Collections.emptyList();
        }
        List<AchievementRateDataNodeVo> result = new ArrayList<>(localDateTimeList.size());
        // 根据能源类型
        Map<Integer, List<UnitObjectCostValuePlan>> effDataMap = query.stream().collect(Collectors.groupingBy(UnitObjectCostValuePlan::getEnergyType));

        // 组装数据
        for (Integer energyType : energyTypes) {
            ProjectEnergyType projectEnergyType = energyTypeMap.getOrDefault(energyType, new ProjectEnergyType());

            String cycleName = cycleMap.get(cycle);
            List<UnitObjectCostValuePlan> energyEfficiencyDataPlans = effDataMap.get(energyType);
            if (CollectionUtils.isEmpty(energyEfficiencyDataPlans)) {
                energyEfficiencyDataPlans = Collections.emptyList();
            }
            // 根据产品类型
            Map<Integer, List<UnitObjectCostValuePlan>> effEnergyDataMap = energyEfficiencyDataPlans.stream().collect(Collectors.groupingBy(UnitObjectCostValuePlan::getProductType));
            for (Integer product : productTypes) {
                List<UnitObjectCostValuePlan> unitObjectCostValuePlans = effEnergyDataMap.get(product);
                if (CollectionUtils.isEmpty(unitObjectCostValuePlans)) {
                    unitObjectCostValuePlans = Collections.emptyList();
                }
                for (LocalDateTime localDateTime : localDateTimeList) {
                    Optional<UnitObjectCostValuePlan> any = unitObjectCostValuePlans.stream()
                            .filter(s -> Objects.equals(s.getLogTime(), TimeUtil.localDateTime2timestamp(localDateTime)))
                            .findFirst();
                    AchievementRateDataNodeVo systemDataNode = new AchievementRateDataNodeVo();
                    systemDataNode.setLogTime(localDateTime);
                    if (any.isPresent()) {
                        UnitObjectCostValuePlan energyPlan = any.get();
                        systemDataNode.setId(energyPlan.getId());
                        systemDataNode.setValue(energyPlan.getValue());
                    }
                    result.add(systemDataNode);
                    systemDataNode.setAggregationCycleName(cycleName);
                    systemDataNode.setProductType(product);
                    systemDataNode.setProductTypeName(productMap.get(product).getName());
                    if (Objects.equals(energyType, EnergyTypeDef.STANDARD_COAL)) {
                        systemDataNode.setEnergyTypeName("综合成本");
                        systemDataNode.setIsCoef(NOT_ALLOWED_INPUT);
                    } else {
                        systemDataNode.setEnergyTypeName(projectEnergyType.getName() + "费");
                        systemDataNode.setIsCoef(ALLOWED_INPUT);
                    }
//                    systemDataNode.setEnergyTypeName(projectEnergyType.getName());
                    systemDataNode.setEnergyType(energyType);
                    systemDataNode.setEnergyTypeId(projectEnergyType.getId());
                    systemDataNode.setProductId(productMap.get(product).getId());
                    if (Objects.nonNull(unit) && Objects.nonNull(unitStringMap.get(product))) {
                        systemDataNode.setUnit(unitStringMap.get(product) + "/" + unit);
                    }
                }
            }

        }
        return result;
    }

    private List<AchievementRateDataNodeReturnVo> queryObjectCostValuePlanData(AchievementRateQueryVo queryVo) {
        //查询节点关联的核算方案--关联的能源类型
        List<CostCheckNodeConfig> costCheckNodeConfigs = getCostCheckNodeConfigList(queryVo.getNodes());
        if (CollectionUtils.isEmpty(costCheckNodeConfigs)) {
            return Collections.emptyList();
        }
        List<CostCheckPlan> costCheckPlans = queryCheckPlan(costCheckNodeConfigs);
        if (CollectionUtils.isEmpty(costCheckNodeConfigs)) {
            return Collections.emptyList();
        }
        List<Long> feeSchemeIds = new ArrayList<>();
        costCheckPlans.forEach(it -> {
            if (CollectionUtils.isEmpty(it.getCostcheckitem_model())) {
                return;
            }
            feeSchemeIds.addAll(
                    it.getCostcheckitem_model()
                            .stream()
                            .map(CostCheckItem::getFeescheme_id)
                            .collect(Collectors.toList()));
        });
        List<FeeScheme> feeSchemes = feeSchemeDao.queryFeeRecord(feeSchemeIds, StringUtils.EMPTY);
        Set<Integer> energyType = feeSchemes.stream().map(FeeScheme::getEnergytype).collect(Collectors.toSet());
        //综合成本
        energyType.add(EnergyTypeDef.STANDARD_COAL);
        List<ObjectCostValuePlan> objectCostValuePlans = objectCostValuePlanDao.query(TimeUtil.localDateTime2timestamp(queryVo.getStartTime()),
                TimeUtil.localDateTime2timestamp(queryVo.getEndTime()), new ArrayList<>(energyType), queryVo.getNodes(), queryVo.getAggregationCycle());
        Map<BaseVo, Set<Integer>> nodeWithEnergyType = createNodeWithEnergyType(queryVo.getNodes(), costCheckNodeConfigs, costCheckPlans, feeSchemes);
        Map<BaseVo, List<AchievementRateDataNodeVo>> baseVoListMap = queryAchievementRateDataVoByObjectCostValuePlan(queryVo.getNodes(), queryVo.getStartTime(), queryVo.getEndTime(), queryVo.getAggregationCycle(),
                objectCostValuePlans,
                nodeWithEnergyType,
                energyType);
        List<AchievementRateDataNodeReturnVo> result = new ArrayList<>();
        for (Map.Entry<BaseVo, List<AchievementRateDataNodeVo>> entry : baseVoListMap.entrySet()) {
            AchievementRateDataNodeReturnVo rateDataNodeReturnVo = new AchievementRateDataNodeReturnVo();
            List<AchievementRateReturnVo> dataNodes = transData(entry.getValue());
//            dataNodes = dataNodes.stream().sorted(Comparator.comparing(AchievementRateReturnVo::getEnergyTypeId)).collect(Collectors.toList());
            dataNodes = dataNodes.stream().sorted(Comparator.comparing(AchievementRateReturnVo::getIsCoef).reversed().thenComparing(AchievementRateReturnVo::getEnergyTypeId))
                    .collect(Collectors.toList());
            rateDataNodeReturnVo.setDataNodes(dataNodes);
            rateDataNodeReturnVo.setObjectId(entry.getKey().getId());
            rateDataNodeReturnVo.setObjectLabel(entry.getKey().getModelLabel());
            result.add(rateDataNodeReturnVo);
        }
        return result;
    }

    private Set<Integer> queryEnergyType(Long projectId) {
        //查询节点关联的核算方案--关联的能源类型
        List<CostCheckNodeConfig> costCheckNodeConfigs = costCheckNodeConfigDao.selectAll();
        if (CollectionUtils.isEmpty(costCheckNodeConfigs)) {
            return Collections.emptySet();
        }
        List<CostCheckPlan> costCheckPlans = queryCheckPlan(costCheckNodeConfigs);
        if (CollectionUtils.isEmpty(costCheckPlans)) {
            return Collections.emptySet();
        }
        costCheckPlans = costCheckPlans.stream().filter(costCheckPlan -> Objects.equals(costCheckPlan.getProjectid(), projectId))
                .collect(Collectors.toList());
        List<Long> feeSchemeIds = new ArrayList<>();
        costCheckPlans.forEach(it -> {
            if (CollectionUtils.isEmpty(it.getCostcheckitem_model())) {
                return;
            }
            feeSchemeIds.addAll(
                    it.getCostcheckitem_model()
                            .stream()
                            .map(CostCheckItem::getFeescheme_id)
                            .collect(Collectors.toList()));
        });
        List<FeeScheme> feeSchemes = feeSchemeDao.queryFeeRecord(feeSchemeIds, StringUtils.EMPTY);
        return feeSchemes.stream().map(FeeScheme::getEnergytype).collect(Collectors.toSet());
    }

    private List<CostCheckPlan> queryCheckPlan(List<CostCheckNodeConfig> costCheckNodeConfigs) {
        if (CollectionUtils.isEmpty(costCheckNodeConfigs)) {
            return Collections.emptyList();
        }
        List<Long> checkPlanIds = costCheckNodeConfigs.stream().map(CostCheckNodeConfig::getCostcheckplan_id).collect(Collectors.toList());
        List<CostCheckPlan> query = modelServiceUtils.queryWithChildren(checkPlanIds, ModelLabelDef.COST_CHECK_PLAN,
                null, Collections.singletonList(ModelLabelDef.COST_CHECK_ITEM), CostCheckPlan.class);
        if (CollectionUtils.isEmpty(query)) {
            return Collections.emptyList();
        }

        return query;
    }

    private Map<BaseVo, Set<Integer>> createNodeWithEnergyType(List<BaseVo> nodes, List<CostCheckNodeConfig> costCheckNodeConfigs, List<CostCheckPlan> costCheckPlans,
                                                               List<FeeScheme> feeSchemes) {
        Map<BaseVo, Set<Integer>> map = new HashMap<>();
        Map<BaseVo, List<CostCheckNodeConfig>> configMap = costCheckNodeConfigs.stream().collect(Collectors.groupingBy(costCheckNodeConfig -> new BaseVo(costCheckNodeConfig.getObjectid(), costCheckNodeConfig.getObjectlabel())));
        for (BaseVo baseVo : nodes) {
            List<CostCheckNodeConfig> checkNodeConfigs = configMap.get(baseVo);
            if (CollectionUtils.isEmpty(checkNodeConfigs)) {
                continue;
            }
            Set<Long> configIds = checkNodeConfigs.stream().map(CostCheckNodeConfig::getCostcheckplan_id).collect(Collectors.toSet());
            Set<Long> feeIds = costCheckPlans.stream().filter(it -> configIds.contains(it.getId()) && CollectionUtils.isNotEmpty(it.getCostcheckitem_model()))
                    .flatMap(it -> it.getCostcheckitem_model().stream())
                    .map(CostCheckItem::getFeescheme_id).collect(Collectors.toSet());
            Set<Integer> energyTypes = feeSchemes.stream().filter(it -> feeIds.contains(it.getId())).map(FeeScheme::getEnergytype).collect(Collectors.toSet());
            energyTypes.add(EnergyTypeDef.STANDARD_COAL);
            map.put(baseVo, energyTypes);
        }
        return map;
    }

    private Map<BaseVo, List<AchievementRateDataNodeVo>> queryAchievementRateDataVoByObjectCostValuePlan(List<BaseVo> nodes, LocalDateTime st, LocalDateTime et, Integer cycle,
                                                                                                         List<ObjectCostValuePlan> query,
                                                                                                         Map<BaseVo, Set<Integer>> map,
                                                                                                         Set<Integer> energyTypes) {
        if (CollectionUtils.isEmpty(query)) {
            query = new ArrayList<>();
        }
        Map<BaseVo, List<ObjectCostValuePlan>> energyConsumptionMap = query.stream()
                .collect(Collectors.groupingBy(it -> new BaseVo(it.getObjectId(), it.getObjectLabel())));

        // 根据起止时间获取时间列表
        List<LocalDateTime> localDateTimeList = TimeUtil.generateTimeValueOfPeriod(st, et, cycle);
        if (CollectionUtils.isEmpty(localDateTimeList)) {
            return Collections.emptyMap();
        }
        Map<Integer, String> productTypeMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.PRODUCT_TYPE);
        Map<Integer, String> cycleMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.AGGREGATION_CYCLE);
        // 查询项目能源类型
        List<ProjectEnergyType> projectEnergyTypes = projectEnergyTypeService.queryEnergyTypes(GlobalInfoUtils.getProjectId(), new ArrayList<>(energyTypes));
        Map<Integer, ProjectEnergyType> energyTypeMap = projectEnergyTypes.stream().collect(Collectors.toMap(ProjectEnergyType::getEnergytype, item -> item, (v1, v2) -> v1));
        //构建节点和能源类型的关系
        //查钱的单位
        UserDefineUnitSearchDto dto = new UserDefineUnitSearchDto(GlobalInfoUtils.getProjectId(), OtherUnitTypeDef.RMB, ProjectUnitClassify.OTHER);
        ResultWithTotal<List<UserDefineUnit>> resultWithTotal = energyUnitService.queryUserDefineUnit(dto);
        String unit = null;
        if (CollectionUtils.isNotEmpty(resultWithTotal.getData())) {
            unit = resultWithTotal.getData().get(0).getBasicUnitSymbolName();
        }
        Map<BaseVo, List<AchievementRateDataNodeVo>> result = new HashMap<>();
        for (BaseVo node : nodes) {

            List<AchievementRateDataNodeVo> achievementRateDataNodeVos = handleSingleNodeEnergyByObjectCostValuePlan(energyConsumptionMap.get(node),
                    localDateTimeList,
                    cycleMap, cycle, energyTypeMap,
                    map.get(node), unit);
            result.put(node, achievementRateDataNodeVos);
        }

        return result;
    }


    private List<AchievementRateDataNodeVo> handleSingleNodeEnergyByObjectCostValuePlan(List<ObjectCostValuePlan> query,
                                                                                        List<LocalDateTime> localDateTimeList,
                                                                                        Map<Integer, String> cycleMap, Integer cycle, Map<Integer, ProjectEnergyType> energyTypeMap,
                                                                                        Set<Integer> energyTypes, String unit) {
        if (Objects.isNull(query)) {
            query = Collections.emptyList();
        }
        List<AchievementRateDataNodeVo> result = new ArrayList<>(localDateTimeList.size());
        // 根据能源类型
        Map<Integer, List<ObjectCostValuePlan>> effDataMap = query.stream().collect(Collectors.groupingBy(ObjectCostValuePlan::getEnergyType));
        if (CollectionUtils.isEmpty(energyTypes)) {
            return result;
        }
        // 组装数据
        for (Integer energyType : energyTypes) {
            ProjectEnergyType projectEnergyType = energyTypeMap.getOrDefault(energyType, new ProjectEnergyType());

            String cycleName = cycleMap.get(cycle);
            List<ObjectCostValuePlan> energyEfficiencyDataPlans = effDataMap.get(energyType);
            if (CollectionUtils.isEmpty(energyEfficiencyDataPlans)) {
                energyEfficiencyDataPlans = Collections.emptyList();
            }
            for (LocalDateTime localDateTime : localDateTimeList) {
                Optional<ObjectCostValuePlan> any = energyEfficiencyDataPlans.stream()
                        .filter(s -> Objects.equals(s.getLogTime(), TimeUtil.localDateTime2timestamp(localDateTime)))
                        .findFirst();
                AchievementRateDataNodeVo systemDataNode = new AchievementRateDataNodeVo();
                systemDataNode.setLogTime(localDateTime);
                if (any.isPresent()) {
                    ObjectCostValuePlan energyPlan = any.get();
                    systemDataNode.setId(energyPlan.getId());
                    systemDataNode.setValue(energyPlan.getValue());
                }
                result.add(systemDataNode);
                systemDataNode.setAggregationCycleName(cycleName);
                if (Objects.equals(energyType, EnergyTypeDef.STANDARD_COAL)) {
                    systemDataNode.setEnergyTypeName("综合成本");
                    systemDataNode.setIsCoef(NOT_ALLOWED_INPUT);
                } else {
                    systemDataNode.setEnergyTypeName(projectEnergyType.getName() + "费");
                    systemDataNode.setIsCoef(ALLOWED_INPUT);
                }

                systemDataNode.setEnergyTypeId(projectEnergyType.getId());
                systemDataNode.setEnergyType(energyType);
                systemDataNode.setUnit(unit);
            }
        }
        return result;
    }

    private List<AchievementRateDataNodeReturnVo> queryEnergyConsumptionPlan(AchievementRateQueryVo queryVo) {
        List<EnergyConsumptionPlan> energyConsumptionPlans = givenEnergyConsumptionPlanDao.queryEnergyConsumptionBatch(queryVo.getNodes(),
                TimeUtil.localDateTime2timestamp(queryVo.getStartTime()),
                TimeUtil.localDateTime2timestamp(queryVo.getEndTime()), queryVo.getAggregationCycle(), queryVo.getEnergyTypes());
        //查询和产品有关联的
        Map<BaseVo, List<AchievementRateReturnVo>> listMap = queryAchievementRateDataVoWithEnergyConsumptionPlan(queryVo,
                energyConsumptionPlans);
        List<AchievementRateDataNodeReturnVo> result = new ArrayList<>();
        for (Map.Entry<BaseVo, List<AchievementRateReturnVo>> entry : listMap.entrySet()) {
            AchievementRateDataNodeReturnVo rateDataNodeReturnVo = new AchievementRateDataNodeReturnVo();
            List<AchievementRateReturnVo> value = entry.getValue();
            value = value.stream().sorted(Comparator.comparing(AchievementRateReturnVo::getIsCoef).reversed().thenComparing(AchievementRateReturnVo::getEnergyTypeId))
                    .collect(Collectors.toList());
            rateDataNodeReturnVo.setDataNodes(value);
            rateDataNodeReturnVo.setObjectId(entry.getKey().getId());
            rateDataNodeReturnVo.setObjectLabel(entry.getKey().getModelLabel());
            result.add(rateDataNodeReturnVo);
        }
        return result;
    }

    private Map<BaseVo, List<AchievementRateReturnVo>> queryAchievementRateDataVoWithEnergyConsumptionPlan(AchievementRateQueryVo queryVo,
                                                                                                           List<EnergyConsumptionPlan> query) {
        if (CollectionUtils.isEmpty(query)) {
            query = new ArrayList<>();
        }
        Map<BaseVo, List<EnergyConsumptionPlan>> energyConsumptionMap = query.stream()
                .collect(Collectors.groupingBy(it -> new BaseVo(it.getObjectId(), it.getObjectLabel())));
        List<ConvertedStandardCoalCoef> coefs = energyConvertDao.queryCoefByProjectId(GlobalInfoUtils.getProjectId());
        Map<Integer, List<ConvertedStandardCoalCoef>> convertMap = coefs.stream().collect(Collectors.groupingBy(ConvertedStandardCoalCoef::getTargetenergytype));
        // 根据起止时间获取时间列表
        List<LocalDateTime> localDateTimeList = TimeUtil.generateTimeValueOfPeriod(queryVo.getStartTime(), queryVo.getEndTime(), queryVo.getAggregationCycle());
        if (CollectionUtils.isEmpty(localDateTimeList)) {
            return Collections.emptyMap();
        }

        Map<Integer, String> cycleMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.AGGREGATION_CYCLE);

        // 查询项目能源类型
        List<ProjectEnergyType> projectEnergyTypes = projectEnergyTypeService.queryEnergyTypes(GlobalInfoUtils.getProjectId(), queryVo.getEnergyTypes());
        Map<Integer, ProjectEnergyType> energyTypeMap = projectEnergyTypes.stream()
                .sorted(Comparator.comparing(ProjectEnergyType::getId))
                .collect(Collectors.toMap(ProjectEnergyType::getEnergytype, item -> item, (v1, v2) -> v1));
        Map<Integer, String> unitStringMap = energyUnitService.queryBasicUnitMap(energyTypeMap.keySet(), ProjectUnitClassify.ENERGY);
        Map<BaseVo, List<AchievementRateReturnVo>> result = new HashMap<>();
        for (BaseVo node : queryVo.getNodes()) {
            List<AchievementRateReturnVo> achievementRateDataNodeVos = handleSingleNodeEnergyConsumptionPlan(energyConsumptionMap.get(node),
                    localDateTimeList,
                    cycleMap, queryVo.getAggregationCycle(), energyTypeMap,
                    unitStringMap, projectEnergyTypes, convertMap);
            result.put(node, achievementRateDataNodeVos);
        }

        return result;
    }

    private List<AchievementRateReturnVo> handleSingleNodeEnergyConsumptionPlan(List<EnergyConsumptionPlan> query,
                                                                                List<LocalDateTime> localDateTimeList,
                                                                                Map<Integer, String> cycleMap, Integer cycle, Map<Integer, ProjectEnergyType> energyTypeMap,
                                                                                Map<Integer, String> unitStringMap,
                                                                                List<ProjectEnergyType> projectEnergyTypes, Map<Integer, List<ConvertedStandardCoalCoef>> convertMap) {
        if (Objects.isNull(query)) {
            query = Collections.emptyList();
        }

        List<AchievementRateReturnVo> rateReturnVos = new ArrayList<>();
        // 根据能源类型
        Map<Integer, List<EnergyConsumptionPlan>> energyPlanMap = query.stream().collect(Collectors.groupingBy(EnergyConsumptionPlan::getEnergyType));
        for (ProjectEnergyType projectEnergyType : projectEnergyTypes) {

            String unit = unitStringMap.get(projectEnergyType.getEnergytype());
            List<EnergyConsumptionPlan> energyConsumptionPlans = energyPlanMap.get(projectEnergyType.getEnergytype());
            if (CollectionUtils.isEmpty(energyConsumptionPlans)) {
                energyConsumptionPlans = new ArrayList<>();
            }
            AchievementRateReturnVo returnVo = new AchievementRateReturnVo();
            returnVo.setEnergyType(projectEnergyType.getEnergytype());
            returnVo.setEnergyTypeName(projectEnergyType.getName());
            returnVo.setUnit(unit);
            returnVo.setAggregationCycle(cycle);
            returnVo.setAggregationCycleName(cycleMap.get(cycle));
            returnVo.setEnergyTypeId(projectEnergyType.getId());
            List<TypeInDataDetailVo> dataDetailVos = new ArrayList<>();
            returnVo.setDetails(dataDetailVos);
            if (CollectionUtils.isNotEmpty(convertMap.get(projectEnergyType.getEnergytype()))) {
                returnVo.setIsCoef(NOT_ALLOWED_INPUT);
            } else {
                returnVo.setIsCoef(ALLOWED_INPUT);
            }
            rateReturnVos.add(returnVo);
            for (LocalDateTime localDateTime : localDateTimeList) {
                Optional<EnergyConsumptionPlan> any = energyConsumptionPlans.stream()
                        .filter(s -> Objects.equals(s.getLogtime(), TimeUtil.localDateTime2timestamp(localDateTime)))
                        .findFirst();
                TypeInDataDetailVo systemDataNode = new TypeInDataDetailVo();
                systemDataNode.setLogTime(localDateTime);
                if (any.isPresent()) {
                    EnergyConsumptionPlan energyPlan = any.get();
                    systemDataNode.setId(energyPlan.getId());
                    systemDataNode.setValue(energyPlan.getValue());
                }
                dataDetailVos.add(systemDataNode);
            }

        }

        return rateReturnVos;
    }

    private List<AchievementRateDataNodeReturnVo> queryEnergyEfficiencyDataPlanData(AchievementRateQueryVo queryVo) {
        List<KpiSetVo> kpiSetList = kpiSetDao.getKpiSetList(queryVo.getNodes());
        if (CollectionUtils.isEmpty(kpiSetList)) {
            return Collections.emptyList();
        }
        List<Long> setIds = kpiSetList.stream().map(KpiSetVo::getEnergyefficiencyset_id).distinct().collect(Collectors.toList());

        List<EnergyEfficiencySetVo> energyEfficiencySetVos = energyEfficiencySetDao.queryEnergyEfficiencySetByIds(setIds);
        List<EnergyEfficiencySetVo> efficiencySetVos = energyEfficiencySetVos.stream()
                .filter(energyEfficiencySetVo -> Objects.equals(energyEfficiencySetVo.getUnittype(), UNIT_TYPE)
                        && Objects.equals(energyEfficiencySetVo.getAggregationcycle(), queryVo.getAggregationCycle()))
                .collect(Collectors.toList());
        List<Long> setIdList = efficiencySetVos.stream().map(EnergyEfficiencySetVo::getId).distinct().collect(Collectors.toList());
        ////查询和产品有关联的
        List<EnergyEfficiencyDataPlan> query = energyEfficiencyDataPlanDao.query(TimeUtil.localDateTime2timestamp(queryVo.getStartTime()),
                TimeUtil.localDateTime2timestamp(queryVo.getEndTime()), setIdList, queryVo.getNodes(), queryVo.getAggregationCycle());
        Map<BaseVo, List<AchievementRateDataNodeVo>> baseVoListMap = queryAchievementRateDataVo(queryVo.getNodes(), queryVo.getStartTime(), queryVo.getEndTime(), queryVo.getAggregationCycle(),
                kpiSetList, query, efficiencySetVos);
        List<AchievementRateDataNodeReturnVo> result = new ArrayList<>();
        Map<Integer, String> energyTypeMap = productDao.queryProjectProductType(GlobalInfoUtils.getProjectId());
        for (Map.Entry<BaseVo, List<AchievementRateDataNodeVo>> entry : baseVoListMap.entrySet()) {
            AchievementRateDataNodeReturnVo rateDataNodeReturnVo = new AchievementRateDataNodeReturnVo();
            List<AchievementRateReturnVo> dataNodes = transData(entry.getValue());
            dataNodes = dataNodes.stream().sorted(Comparator.comparing(AchievementRateReturnVo::getIsCoef).reversed().thenComparing(AchievementRateReturnVo::getEffSetId)).collect(Collectors.toList());
            //使用用户自定义产品类型
            dataNodes.forEach(x -> {
                x.setProductTypeName(energyTypeMap.get(x.getProductType()));
            });
            rateDataNodeReturnVo.setDataNodes(dataNodes);
            rateDataNodeReturnVo.setObjectId(entry.getKey().getId());
            rateDataNodeReturnVo.setObjectLabel(entry.getKey().getModelLabel());
            result.add(rateDataNodeReturnVo);
        }
        return result;
    }


    private Map<BaseVo, List<AchievementRateDataNodeVo>> queryAchievementRateDataVo(List<BaseVo> nodes, LocalDateTime st, LocalDateTime et, Integer cycle,
                                                                                    List<KpiSetVo> kpiSetList, List<EnergyEfficiencyDataPlan> query,
                                                                                    List<EnergyEfficiencySetVo> energyEfficiencySetVos) {
        if (CollectionUtils.isEmpty(query)) {
            query = new ArrayList<>();
        }
        Map<BaseVo, List<EnergyEfficiencyDataPlan>> energyConsumptionMap = query.stream()
                .collect(Collectors.groupingBy(it -> new BaseVo(it.getObjectId(), it.getObjectLabel())));

        // 根据起止时间获取时间列表
        List<LocalDateTime> localDateTimeList = TimeUtil.generateTimeValueOfPeriod(st, et, cycle);
        if (CollectionUtils.isEmpty(localDateTimeList)) {
            return Collections.emptyMap();
        }
        List<ConvertedStandardCoalCoef> coefs = energyConvertDao.queryCoefByProjectId(GlobalInfoUtils.getProjectId());
        Map<Integer, List<ConvertedStandardCoalCoef>> convertMap = coefs.stream().collect(Collectors.groupingBy(ConvertedStandardCoalCoef::getTargetenergytype));
        Map<Integer, String> cycleMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.AGGREGATION_CYCLE);
        Map<Integer, String> productTypeMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.PRODUCT_TYPE);
        List<Integer> energyTypes = energyEfficiencySetVos.stream().map(EnergyEfficiencySetVo::getEnergytype).distinct().collect(Collectors.toList());
        // 查询项目能源类型
        List<ProjectEnergyType> projectEnergyTypes = projectEnergyTypeService.queryEnergyTypes(GlobalInfoUtils.getProjectId(), energyTypes);
        Map<Integer, ProjectEnergyType> energyTypeMap = projectEnergyTypes.stream().collect(Collectors.toMap(ProjectEnergyType::getEnergytype, item -> item, (v1, v2) -> v1));
        Map<BaseVo, List<KpiSetVo>> kpiSetMap = kpiSetList.stream().collect(Collectors.groupingBy(kpiSetVo -> new BaseVo(kpiSetVo.getObjectid(), kpiSetVo.getObjectLabel())));
        Map<BaseVo, List<AchievementRateDataNodeVo>> result = new HashMap<>();
        for (BaseVo node : nodes) {
            List<KpiSetVo> kpiSetVos = kpiSetMap.get(node);
            if (CollectionUtils.isEmpty(kpiSetVos)) {
                continue;
            }
            Set<Long> setIds = kpiSetVos.stream().map(KpiSetVo::getEnergyefficiencyset_id).collect(Collectors.toSet());
            List<EnergyEfficiencySetVo> efficiencySetVos = energyEfficiencySetVos.stream().filter(it -> setIds.contains(it.getId())).collect(Collectors.toList());
            List<AchievementRateDataNodeVo> achievementRateDataNodeVos = handleSingleNodeEnergy(energyConsumptionMap.get(node), localDateTimeList,
                    cycleMap, cycle, energyTypeMap, productTypeMap, efficiencySetVos, convertMap);
            result.put(node, achievementRateDataNodeVos);
        }

        return result;
    }

    private List<AchievementRateDataNodeVo> handleSingleNodeEnergy(List<EnergyEfficiencyDataPlan> query,
                                                                   List<LocalDateTime> localDateTimeList,
                                                                   Map<Integer, String> cycleMap, Integer cycle, Map<Integer, ProjectEnergyType> energyTypeMap,
                                                                   Map<Integer, String> productTypeMap, List<EnergyEfficiencySetVo> energyEfficiencySetVos,
                                                                   Map<Integer, List<ConvertedStandardCoalCoef>> convertMap) {
        if (Objects.isNull(query)) {
            query = Collections.emptyList();
        }
        List<AchievementRateDataNodeVo> result = new ArrayList<>(localDateTimeList.size());
        // 根据指标分组
        Map<Long, List<EnergyEfficiencyDataPlan>> effDataMap = query.stream().collect(Collectors.groupingBy(EnergyEfficiencyDataPlan::getEnergyEfficiencySetId));

        // 组装数据
        for (EnergyEfficiencySetVo setVo : energyEfficiencySetVos) {
            ProjectEnergyType projectEnergyType = energyTypeMap.getOrDefault(setVo.getEnergytype(), new ProjectEnergyType());
            String productTypeName = productTypeMap.get(setVo.getProducttype());
            String cycleName = cycleMap.get(cycle);
            List<EnergyEfficiencyDataPlan> energyEfficiencyDataPlans = effDataMap.get(setVo.getId());
            if (CollectionUtils.isEmpty(energyEfficiencyDataPlans)) {
                energyEfficiencyDataPlans = Collections.emptyList();
            }
            for (LocalDateTime localDateTime : localDateTimeList) {
                long l = TimeUtil.localDateTime2timestamp(localDateTime);
                Optional<EnergyEfficiencyDataPlan> any = energyEfficiencyDataPlans.stream()
                        .filter(s -> Objects.equals(s.getLogTime(), l))
                        .filter(s -> Objects.equals(s.getEnergyEfficiencySetId(), setVo.getId()))
                        .findFirst();
                AchievementRateDataNodeVo systemDataNode = new AchievementRateDataNodeVo();
                systemDataNode.setLogTime(localDateTime);
                if (any.isPresent()) {
                    EnergyEfficiencyDataPlan energyPlan = any.get();
                    systemDataNode.setId(energyPlan.getId());
                    systemDataNode.setValue(energyPlan.getValue());
                }
                if (CollectionUtils.isNotEmpty(convertMap.get(projectEnergyType.getEnergytype()))) {
                    systemDataNode.setIsCoef(NOT_ALLOWED_INPUT);
                } else {
                    systemDataNode.setIsCoef(ALLOWED_INPUT);
                }
                result.add(systemDataNode);
                systemDataNode.setAggregationCycleName(cycleName);
                systemDataNode.setProductTypeName(productTypeName);
                systemDataNode.setEnergyTypeName(projectEnergyType.getName());
                systemDataNode.setProductType(setVo.getProducttype());
                systemDataNode.setEnergyType(setVo.getEnergytype());
                systemDataNode.setUnit(setVo.getSymbol());
                systemDataNode.setEffSetId(setVo.getId());
                systemDataNode.setEffSetName(setVo.getName());
            }
        }
        return result;
    }

}
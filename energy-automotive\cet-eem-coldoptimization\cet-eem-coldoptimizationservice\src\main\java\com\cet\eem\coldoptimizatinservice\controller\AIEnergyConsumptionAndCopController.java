package com.cet.eem.coldoptimizatinservice.controller;

import com.cet.eem.bll.common.model.CompareResult;
import com.cet.eem.bll.common.model.energy.EnergyParam;
import com.cet.eem.bll.common.model.energy.EnergyResult;
import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.bll.energy.model.consumption.vo.ConsumptionSearchVo;
import com.cet.eem.bll.energy.model.consumption.vo.EnergyWithTbHbVo;
import com.cet.eem.bll.energy.service.consumption.EnergyConsumptionBffService;
import com.cet.eem.bll.energy.service.consumption.OilFieldService;
import com.cet.eem.bll.energysaving.model.aiconsumption.AiConsumptionParam;
import com.cet.eem.bll.energysaving.model.aiconsumption.AiConsumptionSearchVo;
import com.cet.eem.bll.energysaving.model.aiconsumption.AiEnergyResult;
import com.cet.eem.bll.energysaving.model.aiconsumption.AiTbHbEnergyVo;
import com.cet.eem.bll.energysaving.service.aioptimization.AIEnergyConsumptionAndCopService;
import com.cet.eem.common.ParamUtils;
import com.cet.eem.common.constant.ErrorCode;
import com.cet.eem.common.constant.QueryType;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.Result;
import com.cet.eem.common.model.datalog.DataLogData;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.parse.JsonTransferUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * @ClassName : AIEnergyConsumptionAndCopController
 * @Description : 制冷ai能耗和cop查询
 * <AUTHOR> jiangzixuan
 * @Date: 2023-05-31 13:41
 */
@Api(value = "AIEnergyConsumptionAndCopController", tags = {"ai能耗查询和cop查询的接口"})
@RestController
@RequestMapping(value = "/eem/v1/aiOptimization/consumption")
public class AIEnergyConsumptionAndCopController {
    //树的接口  --同环比cop的接口 --节点对比cop的接口  2种导出的接口   cop底下的1个接口
    @Autowired
    OilFieldService oilFieldService;
    @Autowired
    EnergyConsumptionBffService energyConsumptionBffService;
    @Autowired
    AIEnergyConsumptionAndCopService aiEnergyConsumptionAndCopService;

    @ApiOperation("ai制冷节点树查询")
    @PostMapping(value = "/tree")
    public Result<List<BaseVo>> treeQuery() {
        return Result.ok(aiEnergyConsumptionAndCopService.treeQuery());
    }

    @ApiOperation("cop或能耗--同/环比查询")
    @PostMapping(value = "/tbhb/child")
    public Result<AiEnergyResult> tbhbEnergyData(
            @RequestBody @ApiParam(name = "TimeValueDTO", value = "模型数据对象", required = true) AiConsumptionParam energyParam) {

        return Result.ok(aiEnergyConsumptionAndCopService.tbhbEnergyData(energyParam,GlobalInfoUtils.getUserId()));

    }


    @ApiOperation(value = "查询下方表格同环比能耗数据")
    @PostMapping(value = "/tbhb", produces = "application/json")
    public Result<List<AiTbHbEnergyVo>> getProjectConsumption(
            @RequestBody @ApiParam(name = "searchVo", value = "查询条件", required = true) AiConsumptionSearchVo searchVo) {

        return Result.ok(aiEnergyConsumptionAndCopService.getTbHbEnergy(searchVo));
    }

    @ApiOperation("数据导出")
    @PostMapping(value = "/export/{analysisType}")
    public void exportEnergyData(@PathVariable @ApiParam(name = "analysisType", value = "分析类型", required = true) Integer analysisType,
                                 @RequestBody @ApiParam(name = "TimeValueDTO", value = "模型数据对象", required = true) AiConsumptionParam energyParam,
                                 HttpServletResponse response) throws IOException {

        aiEnergyConsumptionAndCopService.exportEnergyData(analysisType,energyParam,response,GlobalInfoUtils.getUserId());
    }

    @ApiOperation("能耗计量点日、月、年、自定义时段查询-节点对比查询")
    @PostMapping(value = "/compare")
    public Result<List<CompareResult>> compareEnergyData(
            @RequestBody @ApiParam(name = "energyParam", value = "模型数据对象", required = true) AiConsumptionParam energyParam) {

        return Result.ok(aiEnergyConsumptionAndCopService.compareEnergyWithCopData(energyParam,GlobalInfoUtils.getUserId()));
    }

}
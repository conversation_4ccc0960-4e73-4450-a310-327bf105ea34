package com.cet.eem.bll.achievementrate.model.data;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * @ClassName : EnergyEfficiencyDataPlan
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-11-21 14:27
 */
@Getter
@Setter
@ModelLabel("energyefficiencydataplan")
public class EnergyEfficiencyDataPlan extends BaseEntity {
    @JsonProperty(ColumnDef.AGGREGATION_CYCLE)
    private Integer aggregationCycle;
    @JsonProperty(ColumnDef.ENERGY_EFFICIENCY_SET_ID)
    private Long energyEfficiencySetId;
    @JsonProperty(ColumnDef.ENERGY_TYPE)
    private Integer energyType;
    @JsonProperty(ColumnDef.LOG_TIME)
    private Long logTime;
    @JsonProperty(ColumnDef.OBJECTID)
    private Long objectId;
    @JsonProperty(ColumnDef.C_OBJECT_Label)
    private String objectLabel;
    @JsonProperty(ColumnDef.PRODUCT_TYPE)
    private Integer productType;
    //    @JsonProperty("prodenergytype")
//    private Integer prodEnergyType;
//    @JsonProperty("unittype")
//    private Integer unitType;
    private Double value;
    @JsonProperty("m_updatetime")
    private Long mUpdateTime;
    @JsonProperty(ColumnDef.PROJECT_ID)
    private Long projectId;

    public EnergyEfficiencyDataPlan() {
        this.modelLabel = "energyefficiencydataplan";
    }
}
package com.cet.eem.bll.energysaving.model.config;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.bll.energysaving.model.def.ColdOptimizationLabelDef;
import com.cet.eem.bll.energysaving.model.equipmentoperation.BasicData;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : EquipmentCurve
 * @Description : 效率运行曲线
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-21 08:42
 */
@Getter
@Setter
@ModelLabel(ColdOptimizationLabelDef.EQUIPMENT_CURVE)
public class EquipmentCurve extends BaseEntity {
    @JsonProperty(ColumnDef.C_OBJECT_ID)
    private Long objectId;
    @JsonProperty(ColumnDef.C_OBJECT_Label)
    private String objectLabel;
    /**
     * 左边
     */
    private List<BasicData> coordinate;
    @JsonProperty(ColdOptimizationLabelDef.CURVE_TYPE)
    private Integer curveType;
    @JsonProperty(ColumnDef.UPDATE_TIME)
    private Long updateTime;
    @JsonProperty(ColumnDef.PROJECT_ID)
    private Long projectId;
    /**
     * 二次项系数
     */
    @JsonProperty(ColdOptimizationLabelDef.QUADRATIC_TERM_COEFFICIENT)
    private Double quadraticTermCoefficient;
    /**
     * 一次项系数
     */
    @JsonProperty(ColdOptimizationLabelDef.PRIMARY_TERM_COEFFICIENT)
    private Double primaryTermCoefficient;
    /**
     * 方程式常数项
     */
    @JsonProperty(ColdOptimizationLabelDef.CONSTANT)
    private Double constant;
    public EquipmentCurve() {
        this.modelLabel = ColdOptimizationLabelDef.EQUIPMENT_CURVE;
    }

    public EquipmentCurve(Long objectId, String objectLabel, List<BasicData> coordinate,
                          Integer curveType, Long updateTime, Long projectId) {
        this.objectId = objectId;
        this.objectLabel = objectLabel;
        this.coordinate = coordinate;
        this.curveType = curveType;
        this.updateTime = updateTime;
        this.projectId = projectId;
        this.modelLabel = ColdOptimizationLabelDef.EQUIPMENT_CURVE;
    }
}
package com.cet.eem.bll.energysaving.dao.aioptimization;

import com.cet.eem.bll.energysaving.model.config.system.NumericalCondition;
import com.cet.eem.dao.BaseModelDao;

import java.util.List;

/**
 * @ClassName : NumericalConditionDao
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-28 19:05
 */
public interface NumericalConditionDao extends BaseModelDao<NumericalCondition> {
    /**
     * 根据系统id查询内容
     * @param controlSchemeId
     * @return
     */
    List<NumericalCondition> queryCondition(List<Long> controlSchemeId);
}

package com.cet.eem.bll.compressoroptimization.def;

import lombok.Getter;

import java.util.Objects;

/**
 * @ClassName : DataIdInfoEnum
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-25 09:55
 */
@Getter
public enum DataIdInfoUnitEnum {




    BOV(6000747, "%"),
    IGV(6000748, "%"),
    EXHAUST_SYS_PRE(6007210, "ºC"),
    EXHAUST_SYS_PRE_OUT(6008031, "Bar"),
    EXHAUST_FLOW_RATE(6000749, "m/s"),
    START_TIME(6008038, "H"),
    LOAD_TIME(6008036, "H"),
    CONSTANT_CONTROL_SET(6000751, "Bar"),
    UBLOAD_PRE_SET(6000752, "Bar"),
    LOAD_PRE_SET(6000753, "Bar"),
    MAIN_MOTOR_RATED_CUR(6000754, "A"),
    LOW_LIMIT(6000755, "A"),
    MAIN_MOTOR_TEMP(6000756, "ºC"),
    MAIN_MOTOR_CURRENT(6000757, "ºA"),
    MAIN_MOTOR_POWER(6000758, "º千瓦(kw)"),
    CONTROL_SET(1, "Bar"),
    SUPPLY_TEMP(6007211, "ºC"),
    SUPPLY_PRE(6004024, "Bar"),

    STREAM(6008032, "m³/h");



    private int id;
    private String text;
    private DataIdInfoUnitEnum(int id, String text) {
        this.id = id;
        this.text = text;
    }
    public static String valueOf(Integer code) {
        if (Objects.isNull(code)) {
            return "";
        }
        for (DataIdInfoUnitEnum type : DataIdInfoUnitEnum.values()) {
            if (Objects.equals(type.getId(), code)) {
                return type.getText();
            }
        }
        return "";

    }
}
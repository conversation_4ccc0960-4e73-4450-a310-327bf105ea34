package com.cet.eem.compressoroptimizationservice.controller;

import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.bll.compressoroptimization.model.config.CompressorControl;
import com.cet.eem.bll.compressoroptimization.model.config.CompressorSystem;
import com.cet.eem.bll.compressoroptimization.model.strategy.StrategyQueryParam;
import com.cet.eem.bll.compressoroptimization.model.strategy.StrategyReturnVo;
import com.cet.eem.bll.compressoroptimization.service.config.CompressorStrategyService;
import com.cet.eem.common.model.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * @ClassName : CompressorStrategyController
 * @Description : 策略查询
 * <AUTHOR> jiang<PERSON><PERSON>uan
 * @Date: 2023-07-17 09:58
 */
@Api(value = "CompressorStrategyController", tags = {"策略查询"})
@RestController
@RequestMapping(value = "/eem/v1/compressorOptimization/strategy")
public class CompressorStrategyController {
    @Autowired
    CompressorStrategyService compressorStrategyService;
    @ApiOperation("查询实时策略")
    @PostMapping(value = "/realtime")
    public Result<StrategyReturnVo> queryRealtime(@RequestParam(required = false) Long systemId) {
        return Result.ok(compressorStrategyService.queryRealtime(systemId, GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation("查询历史策略")
    @PostMapping(value = "/history")
    public Result<List<StrategyReturnVo>> queryHistory(@RequestBody StrategyQueryParam queryParam) {
        return Result.ok(compressorStrategyService.queryHistory(queryParam,GlobalInfoUtils.getProjectId()));

    }
}
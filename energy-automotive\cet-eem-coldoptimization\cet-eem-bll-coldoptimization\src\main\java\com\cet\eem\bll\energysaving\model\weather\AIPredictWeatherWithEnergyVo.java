package com.cet.eem.bll.energysaving.model.weather;

import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : AIPredictWeatherWithEnergyVo
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-16 16:13
 */
@Getter
@Setter
public class AIPredictWeatherWithEnergyVo {
    private Double weatherFactValue;
    private Double weatherPredictValue;
    private Double coolingLoadFactValue;
    private Double coolingLoadPredictValue;
    private Long logtime;
    public AIPredictWeatherWithEnergyVo(Long logtime){
        this.logtime=logtime;
    }
    public AIPredictWeatherWithEnergyVo(){

    }
}
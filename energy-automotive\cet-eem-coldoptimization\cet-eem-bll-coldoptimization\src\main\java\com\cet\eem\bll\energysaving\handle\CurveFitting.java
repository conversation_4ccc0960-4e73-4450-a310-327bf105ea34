package com.cet.eem.bll.energysaving.handle;

import com.cet.eem.bll.common.dao.node.EnergySupplyDao;
import com.cet.eem.bll.common.dao.node.MeasureByDao;
import com.cet.eem.bll.common.dao.node.NodeDao;
import com.cet.eem.bll.common.dao.pipenetwork.PipeNetworkConnectionModelDao;
import com.cet.eem.bll.common.log.service.CommonUtilsService;
import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;
import com.cet.eem.bll.common.model.domain.object.powersystem.PowerTransformerVo;

import com.cet.eem.bll.common.model.node.relations.EnergySupplyToPo;
import com.cet.eem.bll.energysaving.config.colddata.CurveFittingConfig;
import com.cet.eem.bll.energysaving.dao.weather.DeviceChainDao;
import com.cet.eem.bll.energysaving.dao.weather.RefrigeratingSystemDao;
import com.cet.eem.bll.energysaving.model.aioptimization.ColdWaterMainEngineVo;
import com.cet.eem.bll.energysaving.model.config.ChainWithTrendVo;
import com.cet.eem.bll.energysaving.model.config.DeviceChainParam;
import com.cet.eem.bll.energysaving.model.config.EquipmentCurve;
import com.cet.eem.bll.energysaving.model.config.PumpFunctionType;
import com.cet.eem.bll.energysaving.model.config.system.RefrigeratingSystem;
import com.cet.eem.bll.energysaving.model.def.ColdOptimizationLabelDef;
import com.cet.eem.bll.energysaving.model.def.CurveTypeDef;
import com.cet.eem.bll.energysaving.model.def.QuantityDef;
import com.cet.eem.bll.energysaving.model.equipmentoperation.BasicData;
import com.cet.eem.bll.energysaving.model.weather.PumpVo;
import com.cet.eem.bll.energysaving.model.weather.QueryParam;
import com.cet.eem.bll.energysaving.service.trend.ModelConfigurationService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.constant.EnumDataTypeId;
import com.cet.eem.common.constant.EnumOperationType;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;

import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;
import com.cet.eem.quantity.service.QuantityManageService;
import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.math3.fitting.PolynomialCurveFitter;
import org.apache.commons.math3.fitting.WeightedObservedPoints;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 制冷设备运行曲线拟合
 *
 * <AUTHOR>
 * @date 2022/9/15
 */
@Service
@Slf4j
public class CurveFitting {
    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    EnergySupplyDao energySupplyDao;

    @Autowired
    QuantityManageService quantityManageService;


    @Autowired
    PipeNetworkConnectionModelDao connectionModelDao;

    @Autowired
    ModelConfigurationService modelConfigurationService;

    @Autowired
    DeviceChainDao deviceChainDao;

    @Autowired
    MeasureByDao measureByDao;

    @Autowired
    RefrigeratingSystemDao refrigeratingSystemDao;

    @Autowired
    NodeDao nodeDao;

    @Autowired
    CommonUtilsService commonUtilsService;

    @Autowired
    CurveFittingConfig curveFittingConfig;

    private static final String LOG_KEY = "[设备运行曲线拟合]";

    public static final Integer OPERATION_TYPE = 65;

    public static final Long userId = 1L;

    public static final Double UNIT = 0.0036D;

    //定时记录往前查询的范围
    private static final int[] RANGE = {1, 5};

    public void dataFitting(LocalDateTime time) {
        if (curveFittingConfig.getTimeRange() < RANGE[0] || curveFittingConfig.getTimeRange() > RANGE[1]) {
            log.info("{}：超出定时记录查询范围，定时记录往前查询时间范围为{}~{}个月，运行结束", LOG_KEY, RANGE[0], RANGE[1]);
            commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, LOG_KEY + "运行失败，定时记录往前查询范围为" + RANGE[0] + "~" + RANGE[1] + "个月,未在该范围内", null, userId);
            return;
        }
        Long projectId = getProjectID();
        //查询制冷系统
        List<RefrigeratingSystem> refrigeratingSystems = refrigeratingSystemDao.querySystem(projectId);
        if (CollectionUtils.isEmpty(refrigeratingSystems)) {
            log.info("{}：没有可用的制冷系统，运行结束", LOG_KEY);
            return;
        }
        log.info("{}：共有{}个制冷系统", LOG_KEY, refrigeratingSystems.size());
        for (RefrigeratingSystem item : refrigeratingSystems) {
            QueryParam queryParam = createQueryParam(null, item, 0);
            log.info("{}：现在计算的制冷系统是：{}，对应的房间是：{}", LOG_KEY, item.getId(), item.getRoomId());
            //查询房间下的所有制冷设备nodes
            List<BaseVo> nodes = queryBaseVo(queryParam.getObjectLabel(), queryParam.getObjectId());
            if (CollectionUtils.isEmpty(nodes)) {
                log.info("{}：该制冷系统下没有制冷设备，开始处理下一个制冷系统", LOG_KEY);
                continue;
            }
            log.info("{}：制冷系统{}共有{}个制冷设备", LOG_KEY, item.getId(), nodes.size());
            //查询房间下的所有连锁
            List<DeviceChainParam> deviceChainParams = modelConfigurationService.queryAllDeviceChain(queryParam.getObjectId(), projectId);
            if (CollectionUtils.isEmpty(deviceChainParams)) {
                log.info("{}：该制冷系统下没有连锁，开始处理下一个制冷系统", LOG_KEY);
                continue;
            }

            log.info("{}：制冷系统{}共有{}个连锁", LOG_KEY, item.getId(), deviceChainParams.size());
            //过滤出冷机id,查询冷机额定制冷量和额定功率
            List<ColdWaterMainEngineVo> coldWaterMainEngineVos = getRatedrefrigeratioAndRatedmotorpower(nodes);
            if (CollectionUtils.isEmpty(coldWaterMainEngineVos)) {
                log.info("{}：该制冷系统下没有冷机，开始处理下一个制冷系统", LOG_KEY);
                continue;
            }
            log.info("{}：该制冷系统下共有{}个冷机", LOG_KEY, coldWaterMainEngineVos.size());
            //查询所有的关联设备
            List<EnergySupplyToPo> energySupplyToPos = queryEnergySupplyTo(nodes);
            if (CollectionUtils.isEmpty(energySupplyToPos)) {
                log.info("{}：该制冷系统下的制冷设备都没有关联设备，开始处理下一个制冷系统", LOG_KEY);
                continue;
            }
            log.info("{}：根据制冷设备共查询到{}个关联设备", LOG_KEY, energySupplyToPos.size());
            //查询冷冻水管
            List<BaseVo> pipeLines = queryColdPipe(nodes, projectId);
            if (CollectionUtils.isEmpty(pipeLines)) {
                log.info("{}：该制冷系统下没有冷冻水管，开始处理下一个制冷系统", LOG_KEY);
                continue;
            }
            log.info("{}：制冷系统{}共有{}个冷冻水管", LOG_KEY, item.getId(), pipeLines.size());
            log.info("{}：开始查询和处理定时记录", LOG_KEY);
            List<List<ChainWithTrendVo>> result = new ArrayList<>();
            for (int i = curveFittingConfig.getTimeRange(); i > 0; i--) {
                queryParam = createQueryParam(time, item, i);
                //查询功率定时记录
                log.info("{}：查询定时记录开始时间为{}，结束时间为：{}", LOG_KEY, queryParam.getStartTime(), queryParam.getEndTime());
                List<TrendDataVo> powerDataVoList = queryTrendDataVo(queryParam, energySupplyToPos, QuantityDef.getMainPowerQuantitySetting());
                if (CollectionUtils.isEmpty(powerDataVoList)) {
                    log.info("{}：该时间段{}--{}内没有功率数据，开始查询下一个月的数据", LOG_KEY, queryParam.getStartTime(), queryParam.getEndTime());
                    continue;
                }
                log.info("{}：共有{}个采集设备的功率定时记录", LOG_KEY, powerDataVoList.size());
                //查询瞬时热流量定时记录
                List<TrendDataVo> clodDataVoList = queryTrendDataVoWithCold(queryParam, pipeLines, QuantityDef.getFreezingWaterPipelineForStream());
                if (CollectionUtils.isEmpty(clodDataVoList)) {
                    log.info("{}：该时间段{}--{}内没有冷量数据，开始查询下一个月的数据", LOG_KEY, queryParam.getStartTime(), queryParam.getEndTime());
                    continue;
                }
                log.info("{}：共有{}个采集设备的瞬时热流量定时记录", LOG_KEY, clodDataVoList.size());
                sumColdData(clodDataVoList);
                log.info("{}：共有{}条瞬时热流量定时记录", LOG_KEY, clodDataVoList.get(0).getDataList().size());
                TrimmingData(powerDataVoList, clodDataVoList);
                List<List<ChainWithTrendVo>> allChainInfo = dataProcessing(powerDataVoList, deviceChainParams, energySupplyToPos);
                if (CollectionUtils.isEmpty(allChainInfo)) {
                    continue;
                }
                //数据过滤
                dataFilter(allChainInfo, clodDataVoList);
                if (CollectionUtils.isEmpty(clodDataVoList.get(0).getDataList())) {
                    log.info("{}：该时间段{}--{}内过滤后的定时记录为0条，开始查询下一个月", LOG_KEY, queryParam.getStartTime(), queryParam.getEndTime());
                    continue;
                }
                //计算冷负荷率和COP
                calCoolingLoadRateAndCOP(allChainInfo, clodDataVoList, coldWaterMainEngineVos);
                log.info("{}：计算冷负荷率和cop后的定时记录长度为{}", LOG_KEY, allChainInfo.get(0).size());
                if (CollectionUtils.isEmpty(result)) {
                    result.addAll(allChainInfo);
                } else {
                    for (int k = 0; k < allChainInfo.size(); k++) {
                        if (CollectionUtils.isEmpty(allChainInfo.get(k))) {
                            continue;
                        }
                        result.get(k).addAll(allChainInfo.get(k));
                    }
                }
                log.info("{}：该时间段{}---{}内的定时记录处理完毕", LOG_KEY, queryParam.getStartTime(), queryParam.getEndTime());
            }
//            //曲线拟合
            if (CollectionUtils.isEmpty(result)) {
                log.info("{}：制冷系统{}中没有合适的拟合数据", LOG_KEY, item.getId());
                continue;
            }
//            log.info("{}：共有{}个连锁，每个连锁有{}条数据", LOG_KEY, result.size(), result.get(0).size());
            List<EquipmentCurve> allCurves = new ArrayList<>();
            log.info("{}：开始拟合曲线", LOG_KEY);
            for (List<ChainWithTrendVo> chainWithTrendVos : result) {
                List<EquipmentCurve> chainCurves = new ArrayList<>();
                if (CollectionUtils.isEmpty(chainWithTrendVos)) {
                    continue;
                }
                log.info("{}：现在对连锁id{}中的功率定时记录进行曲线拟合", LOG_KEY, chainWithTrendVos.get(0).getChainId());
                fittingStepOne(chainWithTrendVos, chainCurves);
                if (CollectionUtils.isNotEmpty(chainCurves)) {
                    allCurves.addAll(chainCurves);
                }
                log.info("{}：连锁id{}中的曲线拟合完成", LOG_KEY, chainWithTrendVos.get(0).getChainId());
            }
            log.info("{}：整个制冷系统的拟合曲线结束", LOG_KEY);
            if (CollectionUtils.isEmpty(allCurves)) {
                log.info("{}：该制冷系统中没有曲线需要存入数据库", LOG_KEY);
                continue;
            }
            log.info("{}：该制冷系统中共有{}条拟合曲线需要存入数据库", LOG_KEY, allCurves.size());
            commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, LOG_KEY + "制冷系统" + item.getId() + "中有" + allCurves.size() +
                    "条拟合曲线入库", null, userId);
            updateCurves(allCurves, projectId);
        }
        log.info("{}：已完成所有制冷系统处理，运行结束", LOG_KEY);
    }

    /**
     * 构建参数
     *
     * @param time
     * @param item
     * @param count
     * @return
     */
    private QueryParam createQueryParam(LocalDateTime time, RefrigeratingSystem item, int count) {
        QueryParam queryParam = new QueryParam();
        if (Objects.nonNull(time) && !Objects.equals(count, 0)) {
            queryParam.setStartTime(time.minusMonths(count));
            queryParam.setEndTime(queryParam.getStartTime().plusMonths(1));
        }
        if (Objects.nonNull(item)) {
            queryParam.setCycle(AggregationCycle.FIFTEEN_MINUTES);
            queryParam.setObjectId(item.getRoomId());
            queryParam.setObjectLabel(NodeLabelDef.ROOM);
        }
        return queryParam;
    }

    /**
     * 查询制冷设备
     *
     * @param objectLabel
     * @param objectId
     * @return
     */
    private List<BaseVo> queryBaseVo(String objectLabel, Long objectId) {
        QueryCondition builder = new QueryConditionBuilder<>(objectLabel, objectId).leftJoin(NodeLabelDef.REFRIGRATION_DEVICE).queryAsTree().build();
        List<BaseVo> query = modelServiceUtils.query(builder, BaseVo.class);
        List<BaseVo> nodes = new ArrayList<>();
        for (BaseVo baseVo : query) {
            if (CollectionUtils.isNotEmpty(baseVo.getChildren())) {
                nodes.addAll(baseVo.getChildren());
            }
        }
        if (CollectionUtils.isEmpty(nodes)) {
            return new ArrayList<>();
        }
        return nodes;
    }

    /**
     * 冷机的额定制冷量和额定功率
     *
     * @param nodes
     * @return
     */
    private List<ColdWaterMainEngineVo> getRatedrefrigeratioAndRatedmotorpower(List<BaseVo> nodes) {
        List<Long> coldwatermainengines = nodes.stream().filter(baseVo -> Objects.equals(NodeLabelDef.COLD_WATER_MAINENGINE, baseVo.getModelLabel())).map(BaseVo::getId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(coldwatermainengines)) {
            return new ArrayList<>();
        }
        QueryCondition builder1 = new QueryConditionBuilder<>(NodeLabelDef.COLD_WATER_MAINENGINE).in(ColumnDef.ID, coldwatermainengines).build();
        List<ColdWaterMainEngineVo> coldWaterMainEngineVos = modelServiceUtils.query(builder1, ColdWaterMainEngineVo.class);
        return coldWaterMainEngineVos;
    }

    /**
     * 查询关联设备
     *
     * @param nodes
     * @return
     */
    private List<EnergySupplyToPo> queryEnergySupplyTo(List<BaseVo> nodes) {
        List<EnergySupplyToPo> energySupplyToPos = energySupplyDao.queryEnergySupplyTo(nodes, System.currentTimeMillis(), EnergySupplyToPo.class);
        if (CollectionUtils.isEmpty(energySupplyToPos)) {
            return new ArrayList<>();
        }
        return energySupplyToPos;
    }

    /**
     * 查询功率定时记录
     *
     * @param queryParam
     * @param energySupplyToPos
     * @param quantitySearchVo
     * @return
     */
    private List<TrendDataVo> queryTrendDataVo(QueryParam queryParam, List<EnergySupplyToPo> energySupplyToPos, QuantitySearchVo quantitySearchVo) {
        List<BaseVo> baseVos = energySupplyToPos.stream().map(energySupplyToPo -> new BaseVo(energySupplyToPo.getObjectid(), energySupplyToPo.getObjectlabel())).collect(Collectors.toList());
        QuantityDataBatchSearchVo quantityDataBatchSearchVo = getQuantityDataBatchSearchVo(queryParam, baseVos, quantitySearchVo);
        Map<Integer, List<TrendDataVo>> dataLogResult = quantityManageService.queryDataLogBatch(quantityDataBatchSearchVo);
        if (Objects.isNull(dataLogResult)) {
            return new ArrayList<>();
        }
        List<TrendDataVo> dataVoList = dataLogResult.get(quantitySearchVo.getId());
        return dataVoList;
    }

    /**
     * 构建定时记录查询条件
     *
     * @param queryParam
     * @param baseVos
     * @param quantitySearchVo
     * @return
     */
    private QuantityDataBatchSearchVo getQuantityDataBatchSearchVo(QueryParam queryParam, List<BaseVo> baseVos, QuantitySearchVo quantitySearchVo) {
        QuantityDataBatchSearchVo quantityDataBatchSearchVo = new QuantityDataBatchSearchVo();
        quantityDataBatchSearchVo.setDataTypeId(EnumDataTypeId.REALTIME.getId());
        quantityDataBatchSearchVo.setStartTime(TimeUtil.localDateTime2timestamp(queryParam.getStartTime()));
        quantityDataBatchSearchVo.setEndTime(TimeUtil.localDateTime2timestamp(queryParam.getEndTime()));
        quantityDataBatchSearchVo.setQuantitySettings(Collections.singletonList(quantitySearchVo));
        quantityDataBatchSearchVo.setNodes(baseVos);
        quantityDataBatchSearchVo.setAggregationCycle(AggregationCycle.FIFTEEN_MINUTES);
        return quantityDataBatchSearchVo;
    }

    /**
     * 查询冷冻管
     *
     * @param baseVos
     * @return
     */
    private List<BaseVo> queryColdPipe(List<BaseVo> baseVos, Long projectId) {
        List<BaseVo> mains = baseVos.stream().filter(baseVo -> Objects.equals(NodeLabelDef.COLD_WATER_MAINENGINE, baseVo.getModelLabel())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mains)) {
            return new ArrayList<>();
        }
        List<PipeNetworkConnectionModel> connectionModels = connectionModelDao.getTopOrDownBatchWithNodes(mains, false, projectId);
        if (CollectionUtils.isEmpty(connectionModels)) {
            return new ArrayList<>();
        }
        List<BaseVo> pipeLines = connectionModels.stream().filter(model -> Objects.equals(model.getOutflowLabel(), NodeLabelDef.PIPELINE))
                .map(model -> new BaseVo(model.getOutflowId(), model.getOutflowLabel()))
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pipeLines)) {
            return new ArrayList<>();
        }
        return pipeLines;
    }

    /**
     * 查询瞬时热流量
     *
     * @param queryParam
     * @param baseVos
     * @param quantitySearchVo
     * @return
     */
    private List<TrendDataVo> queryTrendDataVoWithCold(QueryParam queryParam, List<BaseVo> baseVos, QuantitySearchVo quantitySearchVo) {
        QuantityDataBatchSearchVo quantityDataBatchSearchVo = getQuantityDataBatchSearchVo(queryParam, baseVos, quantitySearchVo);
        Map<Integer, List<TrendDataVo>> dataLogResult = quantityManageService.queryDataLogBatch(quantityDataBatchSearchVo);
        if (Objects.isNull(dataLogResult)) {
            return new ArrayList<>();
        }
        List<TrendDataVo> dataVoList = dataLogResult.get(QuantityDef.getFreezingWaterPipelineForStream().getId());
        return dataVoList;
    }

    /**
     * 多个设备的瞬时热流量定时记录求和
     *
     * @param clodDataVoList
     */
    private void sumColdData(List<TrendDataVo> clodDataVoList) {
        clodDataVoList = clodDataVoList.stream().distinct().collect(Collectors.toList());
        if (clodDataVoList.size() > 1) {
            log.info("{}：开始对多个设备的瞬时热流量定时记录进行求和处理", LOG_KEY);
            List<DatalogValue> DatalogValue = new ArrayList<>();
            DatalogValue.addAll(clodDataVoList.get(0).getDataList());
            for (int i = 1; i < clodDataVoList.size(); i++) {
                List<Long> time = DatalogValue.stream().map(com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue::getTime).collect(Collectors.toList());
                List<DatalogValue> logData = clodDataVoList.get(i).getDataList().stream().filter(DatalogValue1 -> time.contains(DatalogValue1.getTime())).collect(Collectors.toList());
                Map<Long, Double> logDataMap = logData.stream().filter(DatalogValue1 -> Objects.nonNull(DatalogValue1.getTime())
                        && Objects.nonNull(DatalogValue1.getValue()))
                        .collect(Collectors.toMap(com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue::getTime,
                                com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue::getValue));
                for (int j = 0; j < time.size(); j++) {
                    if (Objects.isNull(logDataMap.get(time.get(j)))) {
                        continue;
                    }
                    if (Objects.isNull(DatalogValue.get(j).getValue())) {
                        DatalogValue.get(j).setValue(0D);
                    }
                    DatalogValue.get(j).setValue(CommonUtils.calcDouble(DatalogValue.get(j).getValue(), logDataMap.get(time.get(j)), EnumOperationType.ADD.getId()));
                }
                if (logData.size() < clodDataVoList.get(i).getDataList().size()) {
                    List<DatalogValue> otherData = clodDataVoList.get(i).getDataList().stream().filter(DatalogValue1 -> !logData.contains(DatalogValue1)).collect(Collectors.toList());
                    DatalogValue.addAll(otherData);
                }
            }
            Collections.sort(DatalogValue, new Comparator<DatalogValue>() {
                @Override
                public int compare(DatalogValue o1, DatalogValue o2) {
                    return (int) (o1.getTime() - o2.getTime());
                }
            });
            clodDataVoList.get(0).setDataList(DatalogValue);
            log.info("{}：多个设备的瞬时热流量定时记录的求和处理完成", LOG_KEY);
        }
    }

    /**
     * 定时记录处理
     *
     * @param powerDataVoList
     * @param deviceChainParams
     * @param energySupplyToPos
     * @return
     */
    private List<List<ChainWithTrendVo>> dataProcessing(List<TrendDataVo> powerDataVoList, List<DeviceChainParam> deviceChainParams, List<EnergySupplyToPo> energySupplyToPos) {
        log.info("{}：开始将连锁与查询到的功率定时记录进行匹配", LOG_KEY);
        Map<BaseVo, List<EnergySupplyToPo>> baseVoListMap = energySupplyToPos.stream()
                .collect(Collectors.groupingBy(
                        energySupplyToPo -> new BaseVo(energySupplyToPo.getSupplytoid(), energySupplyToPo.getSupplytolabel())));
        QueryCondition condition = new QueryConditionBuilder<>(NodeLabelDef.PUMP).build();
        List<PumpVo> pumpVos = modelServiceUtils.query(condition, PumpVo.class);
        //制冷设备与定时记录关联
        Map<BaseVo, List<DatalogValue>> baseWithTrendData = new HashMap<>();
        for (Map.Entry<BaseVo, List<EnergySupplyToPo>> entryMap : baseVoListMap.entrySet()) {
            List<EnergySupplyToPo> value = entryMap.getValue();
            List<BaseVo> monitor = value.stream().map(energySupplyToPo -> new BaseVo(energySupplyToPo.getObjectid(), energySupplyToPo.getObjectlabel())).collect(Collectors.toList());
            List<TrendDataVo> trendDataVos = powerDataVoList.stream().filter(trendDataVo -> monitor.contains(new BaseVo(trendDataVo.getMonitoredid(), trendDataVo.getMonitoredlabel()))).distinct().collect(Collectors.toList());
            if (Objects.equals(entryMap.getKey().getModelLabel(), NodeLabelDef.PUMP)) {
                List<PumpVo> filterPumpVos = pumpVos.stream().filter(pumpVo -> Objects.equals(pumpVo.getId(), entryMap.getKey().getId())).collect(Collectors.toList());
                if (Objects.equals(filterPumpVos.get(0).getFunctionType(), PumpFunctionType.COOLING_PUMP)) {
                    entryMap.getKey().setName("coolingPump");
                } else {
                    entryMap.getKey().setName("refrigeratingPump");
                }
            }
            if (CollectionUtils.isEmpty(trendDataVos)) {
                baseWithTrendData.put(entryMap.getKey(), new ArrayList<>());
            } else {
                baseWithTrendData.put(entryMap.getKey(), sumDatalogValue(trendDataVos));
            }
        }
        int dataSize = powerDataVoList.get(0).getDataList().size();
        return dataLogToChain(deviceChainParams, dataSize, baseWithTrendData);
    }

    /**
     * 一个设备下存在多个关联设备时，如果多个关联设备都查询出了定时记录，则将定时记录求和
     *
     * @param trendDataVos
     */
    public List<DatalogValue> sumDatalogValue(List<TrendDataVo> trendDataVos) {
        if (trendDataVos.size() > 1) {
            List<DatalogValue> DatalogValue = new ArrayList<>();
            Gson gson = new Gson();
            DatalogValue = gson.fromJson(gson.toJson(trendDataVos.get(0).getDataList()), new TypeToken<ArrayList<DatalogValue>>() {
            }.getType());
//            for (DatalogValue data : trendDataVos.get(0).getDataList()) {
//                DatalogValue data1 = new DatalogValue(data.getTime(), data.getValue());
//                DatalogValue.add(data1);
//            }
            for (int j = 1; j < trendDataVos.size(); j++) {
                for (int i = 0; i < trendDataVos.get(j).getDataList().size(); i++) {
                    if (Objects.isNull(trendDataVos.get(j).getDataList().get(i).getValue())) {
                        trendDataVos.get(j).getDataList().get(i).setValue(0D);
                    }
                    if (Objects.isNull(DatalogValue.get(i).getValue())) {
                        DatalogValue.get(i).setValue(0D);
                    }
                    DatalogValue.get(i).setValue(CommonUtils.calcDouble(trendDataVos.get(j).getDataList().get(i).getValue(), DatalogValue.get(i).getValue(), EnumOperationType.ADD.getId()));
                }
            }
            return DatalogValue;
        }
        return trendDataVos.get(0).getDataList();
    }

    /**
     * 定时记录转存，把某一时刻的所有数据全部存入同一个类中
     *
     * @param deviceChainParams
     * @param dataSize
     * @param baseWithTrendData
     * @return
     */
    private List<List<ChainWithTrendVo>> dataLogToChain(List<DeviceChainParam> deviceChainParams, int dataSize, Map<BaseVo, List<DatalogValue>> baseWithTrendData) {
        List<List<ChainWithTrendVo>> allChainInfo = new ArrayList<>();
        //连锁循环
        for (DeviceChainParam deviceChainParam : deviceChainParams) {
            log.info("{}：现在进行连锁id{}与功率定时记录匹配", LOG_KEY, deviceChainParam.getChainId());
            List<ChainWithTrendVo> chainWithTrendVos = new ArrayList<>();
            //连锁下的设备
            if (Objects.isNull(deviceChainParam.getNodes())) {
                log.info("{}：连锁id{}下没有制冷设备,所以没有功率定时记录，开始处理下一个连锁", LOG_KEY, deviceChainParam.getChainId());
                continue;
            }
            List<BaseVo> chainWithBase = deviceChainParam.getNodes().stream().map(nodeWithSort -> new BaseVo(nodeWithSort.getId(), nodeWithSort.getModelLabel())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(chainWithBase)) {
                log.info("{}：连锁id{}下没有制冷设备，所以没有功率定时记录，开始处理下一个连锁", LOG_KEY, deviceChainParam.getChainId());
                continue;
            }
            log.info("{}：连锁id{}下共有{}个制冷设备", LOG_KEY, deviceChainParam.getChainId(), chainWithBase.size());
            //遍历出连锁下的设备与定时记录关联
            Map<BaseVo, List<DatalogValue>> trendDataMap = new HashMap<>();
            for (Map.Entry<BaseVo, List<DatalogValue>> entry : baseWithTrendData.entrySet()) {
                if (chainWithBase.contains(entry.getKey())) {
                    trendDataMap.put(entry.getKey(), entry.getValue());
//                    log.info("{}：连锁id{}下没有制冷设备的功率定时记录，开始处理下一个连锁", LOG_KEY, deviceChainParam.getChainId());
                }
            }
            if (Objects.equals(trendDataMap.size(), 0)) {
                log.info("{}：连锁id{}下没有制冷设备的功率定时记录，开始处理下一个连锁", LOG_KEY, deviceChainParam.getChainId());
                continue;
            }
            log.info("{}：连锁id{}下共有{}个制冷设备的功率定时记录", LOG_KEY, deviceChainParam.getChainId(), trendDataMap.size());
            //定时记录循环
//            log.info("{}：功率定时记录循环{}次", LOG_KEY, dataSize);
            for (int i = 0; i < dataSize; i++) {
                ChainWithTrendVo chainWithTrendVo = new ChainWithTrendVo();
                chainWithTrendVo.setChainId(deviceChainParam.getChainId());
                //连锁下的制冷设备循环
                for (Map.Entry<BaseVo, List<DatalogValue>> entry : trendDataMap.entrySet()) {
                    if (CollectionUtils.isEmpty(entry.getValue())) {
                        continue;
                    }
                    if (Objects.isNull(entry.getValue().get(i).getValue())) {
                        continue;
                    }
                    setChainDetail(entry.getKey(), entry.getValue().get(i), chainWithTrendVo);
                }
                chainWithTrendVos.add(chainWithTrendVo);
            }
            log.info("{}：连锁id{}下共有{}条数据", LOG_KEY, deviceChainParam.getChainId(), chainWithTrendVos.size());
            log.info("{}：连锁id{}与功率定时记录匹配完成", LOG_KEY, deviceChainParam.getChainId());
            allChainInfo.add(chainWithTrendVos);
        }
        log.info("{}：所有连锁与功率定时记录匹配结束", LOG_KEY);
        return allChainInfo;
    }

    private void setChainDetail(BaseVo key, DatalogValue value, ChainWithTrendVo chainWithTrendVo) {
        switch (key.getModelLabel()) {
            case NodeLabelDef.COLD_WATER_MAINENGINE:
                chainWithTrendVo.setColdWaterMainEnginePower(chainWithTrendVo.getColdWaterMainEnginePower() + value.getValue());
                chainWithTrendVo.setColdWaterMainEngineId(key.getId());
                chainWithTrendVo.setTime(value.getTime());
                break;
            case NodeLabelDef.COOLING_TOWER:
                chainWithTrendVo.setCoolingTowerPower(chainWithTrendVo.getCoolingTowerPower() + value.getValue());
                chainWithTrendVo.setCoolingTowerId(key.getId());
                break;
            case NodeLabelDef.PUMP:
                if (Objects.equals(key.getName(), "coolingPump")) {
                    chainWithTrendVo.setCoolingPumpPower(chainWithTrendVo.getCoolingPumpPower() + value.getValue());
                    chainWithTrendVo.setCoolingPumpId(key.getId());
                } else {
                    chainWithTrendVo.setRefrigeratingPumpPower(chainWithTrendVo.getRefrigeratingPumpPower() + value.getValue());
                    chainWithTrendVo.setRefrigeratingPumpId(key.getId());
                }
                break;
            default:
                break;
        }
    }

    /**
     * 定时记录过滤，过滤出瞬时热流量定时记录小于0（冷量）以及单独运行的冷机的记录
     *
     * @param allChainInfo
     * @param dataVoList1
     */
    private void dataFilter(List<List<ChainWithTrendVo>> allChainInfo, List<TrendDataVo> dataVoList1) {
        log.info("{}：开始过滤定时记录", LOG_KEY);
        int forSize = dataVoList1.get(0).getDataList().size();
        List<DatalogValue> coolingDatas = dataVoList1.get(0).getDataList();
        log.info("{}：瞬时热流量定时记录数据共有{}条", LOG_KEY, coolingDatas.size());
        List<Long> legalTime = new ArrayList<>();
        log.info("{}：开始筛选出瞬时热流量定时记录小于0且冷机单独运行的时间点，共循环{}次", LOG_KEY, forSize);
        for (int i = 0; i < forSize; i++) {
            if (Objects.isNull(coolingDatas.get(i).getValue())) {
                continue;
            }
            if (coolingDatas.get(i).getValue() < 0) {
                int flag = 0;
                for (List<ChainWithTrendVo> chainWithTrendVos : allChainInfo) {
                    if (chainWithTrendVos.get(i).getColdWaterMainEnginePower() > 1) {
                        flag++;
                    }
                }
                if (flag == 1) {
                    legalTime.add(coolingDatas.get(i).getTime());
                }
            }
        }
        log.info("{}：筛选结束，瞬时热流量定时记录小于0且冷机单独运行的时间点有{}个}", LOG_KEY, legalTime.size());
        //冷量数据过滤和处理
        List<DatalogValue> legalCoolingData = dataVoList1.get(0).getDataList().stream().filter(DatalogValue -> legalTime.contains(DatalogValue.getTime())).collect(Collectors.toList());
        for (int i = 0; i < legalCoolingData.size(); i++) {
            legalCoolingData.get(i).setValue(CommonUtils.calcDouble(Math.abs(legalCoolingData.get(i).getValue()),
                    UNIT, EnumOperationType.DIVISION.getId()));
        }
        dataVoList1.get(0).setDataList(legalCoolingData);
        log.info("{}：过滤后的冷量定时记录有{}条", LOG_KEY, dataVoList1.get(0).getDataList().size());
        //功率过滤
        for (int i = 0; i < allChainInfo.size(); i++) {
            log.info("{}：现在过滤的是连锁id{}下的定时记录", LOG_KEY, allChainInfo.get(i).get(0).getChainId());
            List<ChainWithTrendVo> legalPowerData = allChainInfo.get(i).stream().filter(chainWithTrendVo -> legalTime.contains(chainWithTrendVo.getTime()) && chainWithTrendVo.getColdWaterMainEnginePower() > 1 && Objects.nonNull(chainWithTrendVo.getColdWaterMainEnginePower())).collect(Collectors.toList());
            allChainInfo.set(i, legalPowerData);
            if (CollectionUtils.isEmpty(legalPowerData)) {
                log.info("{}：过滤后的该连锁下的功率定时记录共有0条", LOG_KEY);
            } else {
                log.info("{}：过滤后的该连锁下的功率定时记录共有{}条", LOG_KEY, allChainInfo.get(i).size());
            }
        }
        log.info("{}：所有定时记录过滤结束", LOG_KEY);
    }

    /**
     * 计算cop和冷负荷率
     *
     * @param allChainInfo
     * @param coolingDatas
     * @param coldWaterMainEngineVos
     */
    private void calCoolingLoadRateAndCOP(List<List<ChainWithTrendVo>> allChainInfo, List<TrendDataVo> coolingDatas, List<ColdWaterMainEngineVo> coldWaterMainEngineVos) {
        log.info("{}：开始计算cop和冷负荷率", LOG_KEY);
        List<DatalogValue> DatalogValue = coolingDatas.get(0).getDataList();
        for (List<ChainWithTrendVo> chainWithTrendVos : allChainInfo) {
            if (CollectionUtils.isEmpty(chainWithTrendVos)) {
                continue;
            }
            log.info("{}：现在计算的是连锁id{}下的cop和冷负荷率", LOG_KEY, chainWithTrendVos.get(0).getChainId());
            if (chainWithTrendVos.size() < coolingDatas.get(0).getDataList().size()) {
                List<Long> time = chainWithTrendVos.stream().map(ChainWithTrendVo::getTime).collect(Collectors.toList());
                DatalogValue = coolingDatas.get(0).getDataList().stream().filter(DatalogValue1 -> time.contains(DatalogValue1.getTime())).collect(Collectors.toList());
            }
            Long coldWaterMainEngineId = chainWithTrendVos.get(0).getColdWaterMainEngineId();
            List<ColdWaterMainEngineVo> coldWaterMainEngineVoFilter = coldWaterMainEngineVos.stream().filter(coldWaterMainEngineVo -> Objects.equals(coldWaterMainEngineId, coldWaterMainEngineVo.getId())).collect(Collectors.toList());
            //额定制冷量
            Double ratedrefrigeration = coldWaterMainEngineVoFilter.get(0).getRatedrefrigeration();
            if (Objects.isNull(ratedrefrigeration)) {
                log.info("{}：连锁{}下的冷机{}没有配置额定制冷量", LOG_KEY, chainWithTrendVos.get(0).getChainId(), coldWaterMainEngineId);
                chainWithTrendVos.clear();
                continue;
            }
            for (int i = 0; i < chainWithTrendVos.size(); i++) {
                Double coldLoadRate = CommonUtils.calcDouble(DatalogValue.get(i).getValue(), ratedrefrigeration, EnumOperationType.DIVISION.getId());
                Double cop = CommonUtils.calcDouble(DatalogValue.get(i).getValue(),
                        chainWithTrendVos.get(i).getColdWaterMainEnginePower(), EnumOperationType.DIVISION.getId());
                if (coldLoadRate > 1 || cop > 100 || cop < 0) {
                    chainWithTrendVos.get(i).setCoolingLoadRate(0D);
                    chainWithTrendVos.get(i).setCop(0D);
                } else {
                    chainWithTrendVos.get(i).setCoolingLoadRate(coldLoadRate);
                    chainWithTrendVos.get(i).setCop(cop);
                }
            }
            List<ChainWithTrendVo> trendDataVos = chainWithTrendVos.stream().filter(chainWithTrendVo -> !Objects.equals(chainWithTrendVo.getCoolingLoadRate(), 0D)
                    && !Objects.equals(chainWithTrendVo.getCop(), 0D)).collect(Collectors.toList());
            chainWithTrendVos.clear();
            chainWithTrendVos.addAll(trendDataVos);
            log.info("{}：连锁id{}下的cop和冷负荷率计算结束,计算cop和冷负荷率后的定时记录有{}条", LOG_KEY, chainWithTrendVos.get(0).getChainId(), chainWithTrendVos.size());
        }
        log.info("{}：所有cop和冷负荷率计算结束", LOG_KEY);
    }

    /**
     * 曲线拟合第一步，把数据进行提出，分别计算cop和功率曲线
     *
     * @param chainWithTrendVos
     * @param chainCurves
     */
    private void fittingStepOne(List<ChainWithTrendVo> chainWithTrendVos, List<EquipmentCurve> chainCurves) {
        List<Double> coolingLoadRate = chainWithTrendVos.stream().map(ChainWithTrendVo::getCoolingLoadRate).collect(Collectors.toList());
        List<Double> cop = chainWithTrendVos.stream().map(ChainWithTrendVo::getCop).collect(Collectors.toList());
        //cop拟合
        log.info("{}：连锁id{}:冷负荷率与cop拟合，冷负荷率的数据长度为{}，cop的数据长度为{}", LOG_KEY, chainWithTrendVos.get(0).getChainId(), coolingLoadRate.size(), cop.size());
        fittingCurve(coolingLoadRate, cop, chainWithTrendVos.get(0).getColdWaterMainEngineId(), NodeLabelDef.COLD_WATER_MAINENGINE, CurveTypeDef.COP_PERCENT_ACTUAL, chainCurves);
        List<Double> coldWaterMainEnginePowers = chainWithTrendVos.stream().map(ChainWithTrendVo::getColdWaterMainEnginePower).collect(Collectors.toList());
        //冷机功率拟合
        log.info("{}：连锁id{}:冷负荷率与冷机运行功率拟合，冷负荷率的数据长度为{}，冷机运行功率的数据长度为{}", LOG_KEY, chainWithTrendVos.get(0).getChainId(), coolingLoadRate.size(), coldWaterMainEnginePowers.size());
        fittingCurve(coolingLoadRate, coldWaterMainEnginePowers, chainWithTrendVos.get(0).getColdWaterMainEngineId(), NodeLabelDef.COLD_WATER_MAINENGINE, CurveTypeDef.POWER_PERCENT_ACTUAL, chainCurves);
        List<Double> coolingTowerPowers = chainWithTrendVos.stream().map(ChainWithTrendVo::getCoolingTowerPower).collect(Collectors.toList());
        //塔功率拟合
        if (Objects.nonNull(chainWithTrendVos.get(0).getCoolingTowerId())) {
            log.info("{}：连锁id{}:冷负荷率与冷却塔拟合，冷负荷率的数据长度为{}，冷却塔的数据长度为{}", LOG_KEY, chainWithTrendVos.get(0).getChainId(), coolingLoadRate.size(), coolingTowerPowers.size());
            fittingCurve(coolingLoadRate, coolingTowerPowers, chainWithTrendVos.get(0).getCoolingTowerId(), NodeLabelDef.COOLING_TOWER, CurveTypeDef.POWER_PERCENT_ACTUAL, chainCurves);
        }
        List<Double> coolingPumpPowers = chainWithTrendVos.stream().map(ChainWithTrendVo::getCoolingPumpPower).collect(Collectors.toList());
        //冷却泵功率拟合
        if (Objects.nonNull(chainWithTrendVos.get(0).getCoolingPumpId())) {
            log.info("{}：连锁id{}:冷负荷率与冷却泵拟合，冷负荷率的数据长度为{}，冷却泵的数据长度为{}", LOG_KEY, chainWithTrendVos.get(0).getChainId(), coolingLoadRate.size(), coolingPumpPowers.size());
            fittingCurve(coolingLoadRate, coolingPumpPowers, chainWithTrendVos.get(0).getCoolingPumpId(), NodeLabelDef.PUMP, CurveTypeDef.POWER_PERCENT_ACTUAL, chainCurves);
        }
        List<Double> refrigeratingPumpPower = chainWithTrendVos.stream().map(ChainWithTrendVo::getRefrigeratingPumpPower).collect(Collectors.toList());
        //冷冻泵功率拟合
        if (Objects.nonNull(chainWithTrendVos.get(0).getRefrigeratingPumpId())) {
            log.info("{}：连锁id{}:冷负荷率与冷冻泵功率拟合，冷负荷率的数据长度为{}，冷冻泵功率的数据长度为{}", LOG_KEY, chainWithTrendVos.get(0).getChainId(), coolingLoadRate.size(), refrigeratingPumpPower.size());
            fittingCurve(coolingLoadRate, refrigeratingPumpPower, chainWithTrendVos.get(0).getRefrigeratingPumpId(), NodeLabelDef.PUMP, CurveTypeDef.POWER_PERCENT_ACTUAL, chainCurves);
        }
    }

    /**
     * 曲线拟合
     *
     * @param x
     * @param y
     * @param objectId
     * @param objectLabel
     * @param curveType
     * @param curveList
     */
    private void fittingCurve(List<Double> x, List<Double> y, Long objectId, String objectLabel, int curveType, List<EquipmentCurve> curveList) {
        EquipmentCurve equipmentCurve = null;
        WeightedObservedPoints point = new WeightedObservedPoints();
        for (int i = 0; i < x.size(); i++) {
            //设置坐标集
            point.add(x.get(i), y.get(i));
        }
        //设置多项式阶数
        PolynomialCurveFitter polynomialCurveFitter = PolynomialCurveFitter.create(2);
        //曲线拟合，并将多项式系数存于数组
        double[] fit = polynomialCurveFitter.fit(point.toList());
        if (!Objects.equals(fit.length, 3)) {
            return;
        }
        for (int i = 0; i < fit.length; i++) {
            if (Objects.isNull(fit[i])) {
                return;
            }
        }
        if (!Arrays.equals(fit, new double[]{0D, 0D, 0D})) {
            equipmentCurve = new EquipmentCurve();
            equipmentCurve.setCurveType(curveType);
            equipmentCurve.setConstant(Math.round(fit[0] * 100) / 100D);
            equipmentCurve.setObjectId(objectId);
            equipmentCurve.setObjectLabel(objectLabel);
            equipmentCurve.setQuadraticTermCoefficient(Math.round(fit[2] * 100) / 100D);
            equipmentCurve.setPrimaryTermCoefficient(Math.round(fit[1] * 100) / 100D);
        }
        if (Objects.nonNull(equipmentCurve)) {
            //验证cop拟合效果
            Double copRR = regressionModelCheck(fit, x, y);
            log.info("{}：设备类型：{}，设备id：{}，拟合结果验证，R平方 = {}", LOG_KEY, objectLabel, objectId, copRR);
            if (copRR >= curveFittingConfig.getRr()) {
                curveList.add(equipmentCurve);
            }
        }
    }

    /**
     * 计算曲线的最大值
     *
     * @param fit
     * @param coolingLoadRate
     * @return
     */
    private List<BasicData> calMaxValue(double[] fit, List<Double> coolingLoadRate) {
        Double a = fit[2];
        Double b = fit[1];
        Double c = fit[0];
        Double x = 0D;
        Double y = 0D;
        if (a < 0) {
            x = -(b / (2 * a));
//            y = (4 * a * c - b * b) / (4 * a);
            if (x > 1) {
                x = 1D;
                y = a * x * x + b * x + c;
            } else {
                y = (4 * a * c - b * b) / (4 * a);
            }
        }
        if (a > 0) {
//            Collections.sort(coolingLoadRate);
            Double k1 = 0D;
            Double k2 = 1D;
            Double y1 = a * k1 * k1 + b * k1 + c;
            Double y2 = a * k2 * k2 + b * k2 + c;
            if (y1 > y2) {
                x = k1;
                y = y1;
            } else {
                x = k2;
                y = y2;
            }

        }
        if (a == 0) {
            if (b > 0) {
                x = 1D;
                y = b * x + c;
            }
            if (b < 0) {
                x = 0D;
                y = b * x + c;
            }

            if (b == 0) {
                x = 1D;
                y = c;
            }
        }
        x = Math.round(x * 100) / 100D;
        y = Math.round(y * 100) / 100D;
        return Collections.singletonList(new BasicData(x, y));
    }

    /**
     * 定时记录修整，将功率记录按照冷量定时记录进行修整
     *
     * @param powerDataVoList
     * @param clodDataVoList
     */
    private void TrimmingData(List<TrendDataVo> powerDataVoList, List<TrendDataVo> clodDataVoList) {
        List<Long> time = clodDataVoList.get(0).getDataList().stream().map(DatalogValue::getTime).collect(Collectors.toList());
        log.info("{}：开始修整定时记录，将所有功率定时记录按照瞬时热流量定时记录的时间点进行修整", LOG_KEY);
        for (TrendDataVo trendDataVo : powerDataVoList) {
            if (Objects.equals(trendDataVo.getDataList().size(), time.size())) {
                continue;
            } else {
                List<DatalogValue> filterData = trendDataVo.getDataList().stream().filter(DatalogValue -> time.contains(DatalogValue.getTime())).collect(Collectors.toList());
                trendDataVo.setDataList(filterData);
                if (trendDataVo.getDataList().size() < time.size()) {
                    Map<Long, Double> DatalogValueMap = trendDataVo.getDataList().stream().filter(DatalogValue -> Objects.nonNull(DatalogValue.getTime())
                            && Objects.nonNull(DatalogValue.getValue())).collect(Collectors.toMap(DatalogValue::getTime, DatalogValue::getValue));
                    List<DatalogValue> DatalogValues = new ArrayList<>();
                    for (int i = 0; i < time.size(); i++) {
                        DatalogValue DatalogValue = new DatalogValue();
                        DatalogValue.setTime(time.get(i));
                        DatalogValue.setValue(DatalogValueMap.get(time.get(i)));
                        DatalogValues.add(DatalogValue);
                    }
                    trendDataVo.setDataList(DatalogValues);
                }

            }
//            log.info("{}：修整后的监测设备{}下的功率定时记录有{}条", LOG_KEY,trendDataVo.getMonitoredid(), trendDataVo.getDataList().size());
        }
        log.info("{}：定时记录修整结束", LOG_KEY);
    }

    /**
     * 曲线拟合结果更新入库
     *
     * @param equipmentCurves
     */
    private void updateCurves(List<EquipmentCurve> equipmentCurves, Long projectId) {
        //查询出所有的拟合曲线
        QueryCondition condition = new QueryConditionBuilder<>(ColdOptimizationLabelDef.EQUIPMENT_CURVE).build();
        List<EquipmentCurve> equipmentCurves1 = modelServiceUtils.query(condition, EquipmentCurve.class);

        List<EquipmentCurve> writeEquipmentCurves = new ArrayList<>();
        for (EquipmentCurve equipmentCurve : equipmentCurves) {
            List<EquipmentCurve> filterEquipmentCurves = equipmentCurves1.stream().filter(equipmentCurve1 -> Objects.equals(equipmentCurve1.getCurveType(), equipmentCurve.getCurveType())
                    && Objects.equals(equipmentCurve1.getObjectId(), equipmentCurve.getObjectId())
                    && Objects.equals(equipmentCurve1.getObjectLabel(), equipmentCurve.getObjectLabel())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEquipmentCurves)) {
                EquipmentCurve filterEquipmentCurve = filterEquipmentCurves.get(0);
                filterEquipmentCurve.setUpdateTime(System.currentTimeMillis());
                filterEquipmentCurve.setConstant(equipmentCurve.getConstant());
                filterEquipmentCurve.setPrimaryTermCoefficient(equipmentCurve.getPrimaryTermCoefficient());
                filterEquipmentCurve.setQuadraticTermCoefficient(equipmentCurve.getQuadraticTermCoefficient());
                writeEquipmentCurves.add(filterEquipmentCurve);
            } else {
                equipmentCurve.setUpdateTime(System.currentTimeMillis());
                equipmentCurve.setProjectId(projectId);
                writeEquipmentCurves.add(equipmentCurve);
            }
        }
        modelServiceUtils.writeDataBatch(writeEquipmentCurves, EquipmentCurve.class);
    }

    private Double regressionModelCheck(double[] fit, List<Double> x, List<Double> y) {
        List<Double> ye = x.stream().map(aDouble -> fit[0] + fit[1] * aDouble + fit[2] * aDouble * aDouble).collect(Collectors.toList());
        Double ys = y.stream().mapToDouble(value -> value).sum();
        Double ya = CommonUtils.calcDouble(ys, (double) y.size(), EnumOperationType.DIVISION.getId());
        Double yeSum = ye.stream().mapToDouble(value -> CommonUtils.calcDouble(value - ya, value - ya, EnumOperationType.MULTIPLICATION.getId())).sum();
        Double ySum = y.stream().mapToDouble(value -> CommonUtils.calcDouble(value - ya, value - ya, EnumOperationType.MULTIPLICATION.getId())).sum();
        Double rr = CommonUtils.calcDouble(yeSum, ySum, EnumOperationType.DIVISION.getId());
        return rr;
    }

    private Long getProjectID() {
        QueryCondition queryCondition = new QueryConditionBuilder<>(NodeLabelDef.PROJECT).build();
        return modelServiceUtils.query(queryCondition, PowerTransformerVo.class).get(0).getId();
    }
}

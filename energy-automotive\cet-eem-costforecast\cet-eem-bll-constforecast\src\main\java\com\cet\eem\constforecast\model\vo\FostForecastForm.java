package com.cet.eem.constforecast.model.vo;

import lombok.Data;

import java.util.List;

/**
 * @ClassName : FostforecastForm
 * <AUTHOR> yangy
 * @Date: 2022-06-15 10:30
 */
@Data
public class FostForecastForm {
    private Long nodeId;
    private String nodelLabel;
    /**
     * 查询类别
     */
    private Integer type;
    private Integer cycle;
    private Long startTime;
    private Long endTime;
    /**
     * 综合成本的能源类型
     */
    private List<Integer> energyTypes;
    /**
     * 单台成本或者单台能耗的产品类型.
     */
    private Integer singleType;
}

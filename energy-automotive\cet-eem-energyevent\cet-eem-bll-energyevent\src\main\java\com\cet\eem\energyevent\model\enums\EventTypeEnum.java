package com.cet.eem.energyevent.model.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2024/7/24 10:52
 */
public enum EventTypeEnum {
    DATALOG_LOSS(723, "数据缺失"),
    DATALOG_ERROR(724, "数据异常"),
    DATALOG_AUTO_REPAIR(725, "自动补录"),
    CHANGE_METER( 726,  "自动换表"),
    AUTO_PURSUITC (727, "自动追捕");
    private final Integer type;
    private final String desc;
    /**
     * 不参与分组的事件
     */
    public final static List<Integer> noGroupTypes = Arrays.asList(EventTypeEnum.CHANGE_METER.type,EventTypeEnum.AUTO_PURSUITC.type);
    /**
     * 参与分组的事件
     */
    public final static List<Integer> groupTypes = Arrays.asList(EventTypeEnum.DATALOG_LOSS.type,EventTypeEnum.DATALOG_ERROR.type,EventTypeEnum.DATALOG_AUTO_REPAIR.type);
    /**
     * 自动推送 禁用事件
     */
    public final static List<Integer> reaptFalse = Arrays.asList(EventTypeEnum.DATALOG_AUTO_REPAIR.type,EventTypeEnum.CHANGE_METER.type,EventTypeEnum.AUTO_PURSUITC.type);

    EventTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public static String getDesc(Integer type) {
        EventTypeEnum typeEnum = Stream.of(EventTypeEnum.values()).filter(item -> Objects.equals(item.type, type)).findFirst().orElse(null);
        if (Objects.isNull(typeEnum)) {
            return "";
        }
        return typeEnum.desc;
    }
}

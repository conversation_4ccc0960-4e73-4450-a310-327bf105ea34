package com.cet.eem.service;

import com.cet.eem.model.param.DataHandleParam;
import com.cet.eem.model.param.RepairPointFlinkParam;
import com.cet.eem.model.param.RepairPointLineParam;
import com.cet.eem.model.param.RepairPointWebParam;
import com.cet.eem.model.vo.DataLogValue;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/16 9:01
 */
public interface PointSupplyService {


    List<DataLogValue> repairPointForFlink(RepairPointFlinkParam flinkParam,
                                           DataHandleParam handleParam);

    List<DataLogValue> repairPointForWeb(RepairPointWebParam webParam,
                                         DataHandleParam handleParam);

    List<DataLogValue> repairPointByLine(RepairPointLineParam lineParam,
                                         DataHandleParam handleParam);

    /**
     * 处理换表翻转等情况 将数据处理成递增的
     *
     * @param datalogValues 定时记录
     * @return 递增的定时记录
     */
    List<DataLogValue> handleTurnOverOrChangeMeter(List<DataLogValue> datalogValues);

    boolean judgeIfNeedRepairFifteenPoints(Long startTime, Long endTime);

}

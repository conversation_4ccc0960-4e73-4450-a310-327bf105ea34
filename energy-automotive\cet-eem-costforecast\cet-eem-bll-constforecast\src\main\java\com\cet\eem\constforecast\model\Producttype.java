package com.cet.eem.constforecast.model;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @ClassName : Producttype
 * <AUTHOR> yangy
 * @Date: 2022-06-16 13:44
 */
public enum Producttype {
    /**
     * 汽车
     */
    CAR(4, "汽车"),
    GASOLINECAR(8, "汽油车"),
    NEWENERGYCAR(10, "新能源车");
    Integer type;
    String name;

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    Producttype(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public static List<Integer> types() {
        return Stream.of(Producttype.values()).map(Producttype::getType).collect(Collectors.toList());
    }
}

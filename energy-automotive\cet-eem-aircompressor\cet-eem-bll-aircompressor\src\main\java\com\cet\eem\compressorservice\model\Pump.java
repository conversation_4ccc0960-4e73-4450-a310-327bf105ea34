package com.cet.eem.compressorservice.model;

import com.cet.eem.model.model.BaseEntity;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 02-4��-2022 11:11:16
 */
@Data
public class Pump  extends BaseEntity {

	private Integer airconditionsystemtype;
	private Long asset;
	private Long changedate;
	private String coolingmode;
	private Double effevaluation;
	private Double efflimit;
	private String electricalmachinerymanufacturer;
	private String equipmenttype;

	private Integer frequencytype;
	private Integer functiontype;
	private Boolean isscrap;
	private String lubricationmode;
	private Double maxworkingfrequency;
	private Double minworkingfrequency;
	private String model;
	private String motortype;
	private String name;
	private Integer operationmediumtype;
	private Integer physicaltype;
	private String pic;

	private Integer pumpcycletype;
	private int ratedblankingtimes;
	private float rateddischarge;
	private float ratedlift;
	private float ratedmotorcurrent;
	private float ratedmotorefficiency;
	private float ratedmotorpower;
	private float ratedmotorpowerfactor;
	private float ratedspeed;
	private float ratedstroke;
	private float ratedworkingpressure;
	private Long scrapdate;
	private Integer usagestate;
	private Integer m_alarmscheme;


}
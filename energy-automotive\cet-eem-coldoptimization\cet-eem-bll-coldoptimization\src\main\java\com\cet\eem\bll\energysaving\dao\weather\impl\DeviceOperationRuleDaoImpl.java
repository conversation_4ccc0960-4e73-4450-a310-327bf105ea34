package com.cet.eem.bll.energysaving.dao.weather.impl;

import com.cet.eem.bll.common.model.domain.subject.energysaving.DeviceOperationRule;

import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.bll.energysaving.dao.weather.DeviceOperationRuleDao;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * @ClassName : DeviceOperationRuleDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-17 13:36
 */
@Repository
public class DeviceOperationRuleDaoImpl extends ModelDaoImpl<DeviceOperationRule> implements DeviceOperationRuleDao {

    @Override
    public List<DeviceOperationRule> queryRule(Long room, Integer type,Long projectId) {
        LambdaQueryWrapper<DeviceOperationRule> wrapper = LambdaQueryWrapper.of(DeviceOperationRule.class);
        wrapper.eq(DeviceOperationRule::getRoomId, room)
                .eq(DeviceOperationRule::getProjectId, projectId);
        if (Objects.nonNull(type)){
            wrapper.eq(DeviceOperationRule::getDeviceOperationType,type);
        }
        return this.selectList(wrapper);
    }
}
package com.cet.eem.bll.compressoroptimization.dao;

import com.cet.eem.bll.compressoroptimization.model.config.CompressorSystem;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.dao.BaseModelDao;

import java.util.List;

/**
 * @ClassName : CompressorSystemDao
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-20 16:00
 */
public interface CompressorSystemDao extends BaseModelDao<CompressorSystem> {
    /**
     * 查询空压系统
     * @param systemId
     * @param projectId
     * @return
     */
    CompressorSystem queryCompressorSystem(Long systemId, Long projectId);

    /**
     * 查询空压机
     * @param nodes
     * @param projectId
     * @return
     */
    List<CompressorSystem> queryCompressorDevice(List<BaseVo> nodes, Long projectId);
}
package com.cet.eem.energyevent.dao;

import com.cet.eem.dao.BaseModelDao;
import com.cet.eem.energyevent.model.pojo.EnergyEventPush;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/24 17:21
 */
public interface EnergyEventPushDao  extends BaseModelDao<EnergyEventPush> {

    void editRepeat(List<EnergyEventPush> repeats);

    List<EnergyEventPush> queryRepeats(List<EnergyEventPush> param);

    List<EnergyEventPush> queryRepeatsByStartTime(Long st,Long end, List<Long> deviceIds,List<Long> dataIds);

}

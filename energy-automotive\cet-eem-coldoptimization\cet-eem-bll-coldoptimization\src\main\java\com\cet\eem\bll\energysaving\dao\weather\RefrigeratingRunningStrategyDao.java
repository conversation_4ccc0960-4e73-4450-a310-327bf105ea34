package com.cet.eem.bll.energysaving.dao.weather;

import com.cet.eem.bll.common.model.domain.subject.energysaving.RefrigeratingRunningStrategy;
import com.cet.eem.dao.BaseModelDao;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName : RefrigeratingRunningStrategyDao
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-16 18:57
 */
public interface RefrigeratingRunningStrategyDao extends BaseModelDao<RefrigeratingRunningStrategy> {
    List<RefrigeratingRunningStrategy> queryStrategy(LocalDateTime st,LocalDateTime et,Integer cycle,Long roomId,Integer type);
}

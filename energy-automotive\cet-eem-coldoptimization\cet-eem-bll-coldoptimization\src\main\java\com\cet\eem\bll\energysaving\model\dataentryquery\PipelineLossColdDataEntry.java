package com.cet.eem.bll.energysaving.model.dataentryquery;


import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : PipelineLossColdDataEntry
 * @Description : 管道损失冷量返回值
 * <AUTHOR> jiangzixuan
 * @Date: 2022-04-11 08:54
 */
@Getter
@Setter
public class PipelineLossColdDataEntry {
    private Long projectId;
    private Long systemId;
    /**
     * 历史冷冻水总管散热量
     */
    private List<DatalogValue> pipelineCold;
    /**
     * 温度预测值
     */
    private List<DatalogValue>  tempPredict;
    /**
     * 湿度预测值
     */
    private List<DatalogValue>  humidityPredict;
    /**
     * 温度实际值
     */
    private List<DatalogValue>  tempActual;
    /**
     * 湿度实际值
     */
    private List<DatalogValue>  humidityActual;
}
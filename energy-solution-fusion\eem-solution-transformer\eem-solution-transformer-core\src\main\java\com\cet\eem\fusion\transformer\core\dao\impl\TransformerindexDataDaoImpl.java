package com.cet.eem.fusion.transformer.core.dao.impl;

import com.cet.eem.bll.common.model.domain.perception.logicaldevice.EemPoiRecord;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import com.cet.eem.fusion.transformer.core.dao.TransformerindexDataDao;
import com.cet.eem.fusion.transformer.core.entity.po.TransformerindexData;
import com.cet.eem.fusion.transformer.core.def.TransformerConstantDef;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName : AvgloadandpowerDaoImpl
 * <AUTHOR> yangy
 * @Date: 2022-03-28 10:33
 */
@Component
public class TransformerindexDataDaoImpl implements TransformerindexDataDao {

    @Autowired
    ModelServiceUtils modelServiceUtilsl;

    @Override
    public List<TransformerindexData> save(List<TransformerindexData> avgloadandpowers) {
        return modelServiceUtilsl.writeData(avgloadandpowers, TransformerindexData.class);
    }

    @Override
    public List<TransformerindexData> queryByTimes(List<LocalDateTime> times) {
        QueryCondition condition = new QueryConditionBuilder(TransformerConstantDef.TRANSFORMERINDEXDATA)
                .in(TransformerConstantDef.LOGTIME, times)
                .build();
        return modelServiceUtilsl.query(condition, TransformerindexData.class);
    }

    @Override
    public List<TransformerindexData> queryByParams(Long logTime, Integer cycle,Integer type, Boolean desc) {
        QueryCondition condition = new QueryConditionBuilder(TransformerConstantDef.TRANSFORMERINDEXDATA)
                .eq(TransformerConstantDef.LOGTIME, logTime)
                .eq(TransformerConstantDef.AGGREGATIONCYCLE,cycle)
                .eq(TransformerConstantDef.TYPE,type)
                .orderBy(TransformerConstantDef.VALUE,desc)
                .build();
        return modelServiceUtilsl.query(condition, TransformerindexData.class);
    }

    @Override
    public List<TransformerindexData> queryByTimes(List<LocalDateTime> times, Integer type, Integer cycle) {
        QueryCondition condition = new QueryConditionBuilder(TransformerConstantDef.TRANSFORMERINDEXDATA)
                .in(TransformerConstantDef.LOGTIME, times)
                .eq(TransformerConstantDef.COLUMN_AGGREGATION_CYCLE, cycle)
                .eq(TransformerConstantDef.TYPE, type)
                .build();
        return modelServiceUtilsl.query(condition, TransformerindexData.class);
    }

    @Override
    public List<EemPoiRecord> saveEemPoiRecord(List<EemPoiRecord> pois) {
        return modelServiceUtilsl.writeData(pois,EemPoiRecord.class);
    }
}

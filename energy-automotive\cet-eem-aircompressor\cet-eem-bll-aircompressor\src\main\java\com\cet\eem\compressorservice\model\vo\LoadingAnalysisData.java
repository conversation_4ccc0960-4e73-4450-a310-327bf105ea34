package com.cet.eem.compressorservice.model.vo;

import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.constant.EnumOperationType;
import lombok.Data;

import java.util.Objects;

/**
 * @ClassName : LoadingAnalysisData
 * <AUTHOR> yangy
 * @Date: 2022-04-07 11:11
 */
@Data
public class LoadingAnalysisData {
    /**
     * 空压机名称
     */
    String name;
    /**
     * 运行时长
     */
    Double runTime;
    /**
     * 加载时长
     */
    Double loadTime;
    /**
     * 卸载时长
     */
    Double unloadTime;
    /**
     * 卸载率
     */
    Double unloadingRate;
    /**
     * 类别
     */
    Integer aggregationCycle;

    Long airCompressorId;

    public LoadingAnalysisData() {
    }

    public LoadingAnalysisData(String name, Integer aggregationCycle, Long airCompressorId) {
        this.name = name;
        this.aggregationCycle = aggregationCycle;
        this.airCompressorId = airCompressorId;
    }

    public Double getUnloadTime() {
        if (Objects.nonNull(runTime)&&Objects.nonNull(loadTime)){
            return runTime-loadTime;
        }
        return unloadTime;
    }

    public Double getUnloadingRate() {
        if (Objects.nonNull(runTime)&&Objects.nonNull(loadTime)){
            return CommonUtils.calcDouble(runTime-loadTime,runTime, EnumOperationType.DIVISION.getId());
        }
        return unloadingRate;
    }
}

package com.cet.eem.fusion.transformer.core.controller;

import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.fusion.transformer.core.entity.dto.LoadRateParam;
import com.cet.eem.fusion.transformer.core.entity.vo.*;
import com.cet.eem.fusion.transformer.core.service.TransformerAnalysisService;
import com.cet.eem.solution.common.def.common.PluginInfoDef;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.entity.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.cet.eem.task.model.TransformerTaskService;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName : TransformerAnalysisController
 * @Description : 变压器分析
 * <AUTHOR> jiangzixuan
 * @Date: 2022-03-07 10:24
 */
@Validated
@RestController
@Api(value = PluginInfoDef.Transformer.INTERFACE_PREFIX + "/v1/analysis", tags = "变压器分析接口")
@RequestMapping(value = PluginInfoDef.Transformer.PLUGIN_NAME_PREFIX + "/v1/analysis")
public class TransformerAnalysisController {
    @Autowired
    TransformerAnalysisService transformerAnalysisService;

    @Autowired
    TransformerTaskService transformerTaskService;

    @ApiOperation(value = "设备监测")
    @PostMapping(value = "/baseInfo", produces = "application/json")
    public ApiResult<EquipmentMonitorVo> queryEquipmentMonitorInfo(@RequestParam @NotNull Long id) {
        return Result.ok(transformerAnalysisService.queryEquipmentMonitorInfo(id));
    }
    
    @ApiOperation(value = "高低压侧电压数据")
    @PostMapping(value = "/voltageSideMonitor", produces = "application/json")
    public ApiResult<List<VoltageSideMonitorVo>> queryVoltageSideMonitor(@RequestParam @NotNull Long id) throws InstantiationException, IllegalAccessException {
        return Result.ok(transformerAnalysisService.queryVoltageSideMonitor(id, GlobalInfoUtils.getProjectId()));
    }
    
    @ApiOperation(value = "实时与历史负载信息")
    @PostMapping(value = "/loadData", produces = "application/json")
    public ApiResult<LoadInfoVo> queryLoadInfo(@RequestParam @NotNull Long id) throws InstantiationException, IllegalAccessException {
        return Result.ok(transformerAnalysisService.queryLoadInfo(id, GlobalInfoUtils.getProjectId()));
    }
    
    @ApiOperation(value = "雷达分析")
    @PostMapping(value = "/radarChart", produces = "application/json")
    public ApiResult<RadarChartInfo> queryRadarChartInfo(@RequestParam @NotNull Long id) throws InstantiationException, IllegalAccessException {
        return Result.ok(transformerAnalysisService.queryRadarChartInfo(id,GlobalInfoUtils.getProjectId()));
    }
    
    @ApiOperation(value = "负载率趋势")
    @PostMapping(value = "/loadCurve", produces = "application/json")
    public ApiResult<LoadRateVo> queryLoadRateTrend(@RequestBody LoadRateParam param) throws InstantiationException, IllegalAccessException {
        return Result.ok(transformerAnalysisService.queryLoadRateTrend(param,GlobalInfoUtils.getProjectId()));
    }
    
    @ApiOperation(value = "计算历史信息")
    @PostMapping(value = "/calHisData", produces = "application/json")
    public void task(@RequestParam @NotNull Long time) throws InstantiationException, IllegalAccessException {
        transformerTaskService.historicalLoadCalculation(time);
    }
}

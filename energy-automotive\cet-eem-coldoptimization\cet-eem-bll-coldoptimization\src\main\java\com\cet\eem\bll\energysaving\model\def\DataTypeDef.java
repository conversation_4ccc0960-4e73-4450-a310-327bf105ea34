package com.cet.eem.bll.energysaving.model.def;

import io.swagger.models.auth.In;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : DataTypeDef
 * @Description : 比较的参数类型
 * <AUTHOR> jiang<PERSON><PERSON>uan
 * @Date: 2022-06-28 14:15
 */
@Getter
@Setter
public class DataTypeDef {
    /**
     * 负载率
     */
    public static final Integer LOAD_RATE = 1;
    /**
     * 冷负荷率
     */
    public static final Integer COLD_LOAD_RATE = 2;
    /**
     * 供水温度
     */
    public static final Integer SUPPLY_TEMP = 3;

    /*
    * 供水温差
    * */
    public static final Integer SUPPLY_TEMP_DIFF = 4;
}
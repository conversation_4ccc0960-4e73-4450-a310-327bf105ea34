package com.cet.eem.bll.achievementrate.model;

import com.cet.eem.common.model.BaseVo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/19 13:43
 */
@Data
public class RecalculateParam {
    //曹雪莹最新逻辑 要求 只计算节点 时间, type下面的所有数据
    private Long startTime;
    private Long endTime;
    //private Long effSetId;
    private List<BaseVo> nodes;
    //private Integer productType;
    //private Integer energType;
    private Integer type;
}

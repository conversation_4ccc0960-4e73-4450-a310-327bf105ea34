package com.cet.eem.compressorservice.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.cet.eem.bll.common.config.EnergyConsumptionConfig;
import com.cet.eem.bll.common.def.quantity.FrequencyDef;
import com.cet.eem.bll.common.def.quantity.PhasorDef;
import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;
import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;
import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityAggregationData;
import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityObject;
import com.cet.eem.bll.common.model.domain.subject.energy.EnergyConsumption;
import com.cet.eem.bll.common.model.node.relations.EnergySupplyToPo;
import com.cet.eem.bll.common.service.impl.EnergySupplyService;
import com.cet.eem.bll.common.util.GlobalInfoUtils;

import com.cet.eem.bll.energy.dao.SystemEventDao;
import com.cet.eem.bll.energy.model.event.SystemEventCountVo;

import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.constant.*;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.AggregationResult;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.model.realtime.RealTimeValue;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.compressorservice.dao.CompressorDao;
import com.cet.eem.compressorservice.model.Aircompressor;
import com.cet.eem.compressorservice.model.Constant.Constant;
import com.cet.eem.compressorservice.model.Coolingtower;
import com.cet.eem.compressorservice.model.Dryingmachine;
import com.cet.eem.compressorservice.model.Pump;
import com.cet.eem.compressorservice.model.vo.*;
import com.cet.eem.compressorservice.service.CompressorService;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.event.model.pecevent.PecEventCountVo;
import com.cet.eem.event.service.PecEventService;
import com.cet.eem.model.model.BaseEntity;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.quantity.dao.QuantityObjectDao;
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;
import com.cet.eem.quantity.service.QuantityManageService;
import com.cet.electric.model.enums.ControlMeanType;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.Collator;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : CompressorServiceImpl
 * <AUTHOR> yangy
 * @Date: 2022-04-02 10:07
 */
@Service
public class CompressorServiceImpl implements CompressorService {
    private final static Integer FOURTH = 4;

    @Autowired
    CompressorDao compressorDao;
    @Autowired
    QuantityManageService quantityManageService;
    @Autowired
    QuantityObjectDao quantityObjectDao;
    @Autowired
    EnergySupplyService energySupplyService;
    @Autowired
    PecEventService pecEventService;
    @Autowired
    SystemEventDao systemEventDao;
    @Autowired
    EnergyConsumptionConfig energyConsumptionConfig;
    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Override
    public List<RoomVo> getCompressorRoom(Long projectId) {
        return compressorDao.queryByRoomtype(projectId, FOURTH);
    }

    @Override
    public StandingBook getStandingBook(Long roomId) {
        StandingBook standingBook = new StandingBook();
        if (Objects.isNull(roomId)) {
            return standingBook;
        }
        List<Equipments> roomVos = compressorDao.queryByRoomId(roomId);
        if (CollectionUtils.isEmpty(roomVos)) {
            return standingBook;
        }
        //运行+机子数量
        List<Operation> operation = getOperation(roomVos);
        //事件数据
        Event event = getEvent(roomVos, roomId);
        standingBook.setOperation(operation);
        standingBook.setEvent(event);
        return standingBook;
    }

    /**
     * 查询总管压力接口
     *
     * @param roomId
     * @return
     */
    @Override
    public AirManagerData getAirManagerData(Long roomId) {
        AirManagerData airManagerData = new AirManagerData();
        List<EnergySupplyToPo> energySupplyToPos = queryEnergySupply(roomId);
        List<BaseVo> baseVos = energySupplyToPos.stream().map(supply -> new BaseVo(supply.getObjectid(), supply.getObjectlabel()))
                .filter(item -> Objects.equals(item.getModelLabel(), NodeLabelDef.PIPELINE)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(baseVos)) {
            return airManagerData;
        }
        Map<Integer, List<RealTimeValue>> listMap;
        try {
            listMap = realTimeValueDatas(baseVos, Arrays.asList(getHeaderPressure(), getHeaderFlow(), getHeaderTemperature()));
        } catch (Throwable throwable) {
            return airManagerData;
        }
        listMap.forEach((key, val) -> {
            Double value = val.stream().filter(item -> Objects.nonNull(item.getValue())).findFirst().map(RealTimeValue::getValue).orElse(null);
            //总管压力
            if (Objects.equals(key, 6004024)) {
                airManagerData.setPressure(value);
                //总管瞬时流量
            } else if (Objects.equals(key, 6008032)) {
                airManagerData.setFlowrate(value);
                //总管露点温度
            } else if (Objects.equals(key, 6007211)) {
                airManagerData.setDewpoint(value);
            }
        });
        return airManagerData;
    }

    public List<EnergySupplyToPo> queryEnergySupply(Long roomId) {
        List<BaseVo> nodes = Arrays.asList(new BaseVo(roomId, NodeLabelDef.ROOM));
        return energySupplyService.queryEnergySupplyTo(nodes, System.currentTimeMillis(), EnergySupplyToPo.class);
    }

    /**
     * 获取电器比分析数据--如果查询日的 则cycle为小时的 ,如果查询月的 ,cycle为日的.
     *
     * @param form
     * @return
     */
    @Override
    public EnergyResult getElectricalRatio(ElectricalRatioForm form) {
        EnergyResult result = new EnergyResult();
        if (Objects.isNull(form.getRoomId())) {
            return new EnergyResult();
        }
        List<Equipments> roomVos = compressorDao.queryByRoomId(form.getRoomId());
        if (Objects.isNull(roomVos) || roomVos.isEmpty()) {
            return new EnergyResult();
        }
        List<Long> compressorIds = roomVos.get(0).getAircompressor_model().stream().map(Aircompressor::getId).collect(Collectors.toList());
        //获取开始时间
        LocalDateTime startTime = getStartTime(form, form.getType());
        List<EnergyConsumption> consumptions = getEnergyConsumption(compressorIds, form, startTime);
        result = setData(result, consumptions, form.getType(), form, startTime);

        return subLenth(result);
    }

    /**
     * 保持时间一致
     *
     * @param result
     * @return
     */
    public EnergyResult subLenth(EnergyResult result) {
        List<PlanTimeData> currentdata = result.getCurrentdata();
        List<PlanTimeData> hbdata = result.getHbdata();
        List<PlanTimeData> tbdata = result.getTbdata();
        if (CollectionUtils.isNotEmpty(currentdata) && CollectionUtils.isNotEmpty(hbdata) && currentdata.size() < hbdata.size()) {
            result.setHbdata(CollUtil.sub(hbdata, 0, currentdata.size()));
        }
        if (CollectionUtils.isNotEmpty(currentdata) && CollectionUtils.isNotEmpty(tbdata) && currentdata.size() < tbdata.size()) {
            result.setTbdata(CollUtil.sub(tbdata, 0, currentdata.size()));
        }
        return result;
    }

    public List<EnergyConsumption> getEnergyConsumption(List<Long> compressorIds, ElectricalRatioForm form, LocalDateTime startTime) {
        if (CollectionUtils.isEmpty(compressorIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<EnergyConsumption> wrapper = LambdaQueryWrapper.of(EnergyConsumption.class)
                .ge(EnergyConsumption::getLogtime, startTime)
                .lt(EnergyConsumption::getLogtime, form.getEndTime())
                .eq(EnergyConsumption::getObjectid, form.getRoomId())
                .eq(EnergyConsumption::getObjectlabel, NodeLabelDef.ROOM)
                .eq(EnergyConsumption::getEnergytype, Constant.SIXTEEN)
                .eq(EnergyConsumption::getAggregationcycle, form.getAggregationCycle())
                .eq(EnergyConsumption::getTimeSharePeriodIdentification, energyConsumptionConfig.getTsIdentification())
                .or(of -> of.ge(EnergyConsumption::getLogtime, startTime)
                        .lt(EnergyConsumption::getLogtime, form.getEndTime())
                        .in(EnergyConsumption::getObjectid, compressorIds)
                        .eq(EnergyConsumption::getObjectlabel, NodeLabelDef.AIR_COMPRESSOR)
                        .eq(EnergyConsumption::getAggregationcycle, form.getAggregationCycle())
                        .eq(EnergyConsumption::getEnergytype, Constant.TWO))
                .eq(EnergyConsumption::getTimeSharePeriodIdentification, energyConsumptionConfig.getTsIdentification());
        return modelServiceUtils.queryWithRedis(wrapper.getQueryCondition().build(), EnergyConsumption.class);
    }

    /**
     * 卸载分析接口
     *
     * @param roomId
     * @return
     */
    @Override
    public List<LoadingAnalysisData> getLoadingAnalysis(Long roomId) {
        if (Objects.isNull(roomId)) {
            return new ArrayList<>();

        }
        List<Equipments> roomVos = compressorDao.queryByRoomId(roomId);

        if (Objects.isNull(roomVos) || roomVos.isEmpty()) {
            return new ArrayList<>();
        }
        List<Aircompressor> aircompressors = roomVos.get(0).getAircompressor_model();
        List<BaseVo> baseVos = aircompressors.stream().map(item -> new BaseVo(item.getId(), item.getModelLabel())).collect(Collectors.toList());
        List<QuantityObject> quantityObjects = quantityObjectDao.queryQuantityObject(baseVos);
        if (CollectionUtils.isEmpty(quantityObjects)) {
            return getEmptyDatas(aircompressors);
        }
        //过滤运行和加载quantityIds
        Map<Long, Long> loadMap = quantityFilterToMap(quantityObjects, getLoadTime());
        Map<Long, Long> runMap = quantityFilterToMap(quantityObjects, getRunningTime());
        List<Long> allQuantIds = quantityObjects.stream().map(QuantityObject::getId).collect(Collectors.toList());
        LambdaQueryWrapper<QuantityAggregationData> wrapperForDay = getWrapper(allQuantIds, AggregationCycle.ONE_DAY);
        LambdaQueryWrapper<QuantityAggregationData> wrapperForMonth = getWrapper(allQuantIds, AggregationCycle.ONE_MONTH);
        List<QuantityAggregationData> aggregationDatasForDay = modelServiceUtils.queryWithRedis(wrapperForDay.getQueryCondition().build(), QuantityAggregationData.class);
        List<QuantityAggregationData> aggregationDatasForMonth = modelServiceUtils.queryWithRedis(wrapperForMonth.getQueryCondition().build(), QuantityAggregationData.class);
        List<LoadingAnalysisData> result = getDatas(aircompressors, loadMap, runMap, aggregationDatasForDay, AggregationCycle.ONE_DAY);
        result.addAll(getDatas(aircompressors, loadMap, runMap, aggregationDatasForMonth, AggregationCycle.ONE_MONTH));

        //按照空压机id排序，然后先日再月
        return sortListById(result);
    }

    /**
     * 获取时间轴
     *
     * @param timeRange
     * @return
     */
    public List<PlanTimeData> getTimeData(List<Long> timeRange) {
        List<PlanTimeData> planTimeData = new ArrayList<>();
        timeRange.forEach(time -> {
            PlanTimeData data = new PlanTimeData();
            data.setTime(time);
            planTimeData.add(data);
        });
        return planTimeData;
    }

    /**
     * 返回空数据
     *
     * @param aircompressors
     * @return
     */
    public List<LoadingAnalysisData> getEmptyDatas(List<Aircompressor> aircompressors) {
        List<LoadingAnalysisData> datas = new ArrayList<>();
        aircompressors.forEach(air -> {
            LoadingAnalysisData dayData = new LoadingAnalysisData(air.getName(), AggregationCycle.ONE_DAY, air.getId());
            LoadingAnalysisData monthData = new LoadingAnalysisData(air.getName(), AggregationCycle.ONE_MONTH, air.getId());
            datas.add(dayData);
            datas.add(monthData);
        });
        return sortListByName(datas);
    }

    /**
     * 卸载率
     *
     * @param form
     * @return
     */
    @Override
    public List<UnloadingRate> getUnloadingRate(UnloadingRateForm form) {
        List<Long> ids = form.getIds();
        List<Long> timeRange = TimeUtil.getTimeRange(form.getStartTime(), form.getEndTime(), form.getAggregationCycle());
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.singletonList(new UnloadingRate(getTimeData(timeRange)));
        }
        List<BaseVo> baseVos = ids.stream().map(item -> new BaseVo(item, NodeLabelDef.AIR_COMPRESSOR)).collect(Collectors.toList());
        List<QuantityObject> quantityObjects = quantityObjectDao.queryQuantityObject(baseVos);
        if (CollectionUtils.isEmpty(quantityObjects)) {
            return Collections.singletonList(new UnloadingRate(getTimeData(timeRange)));
        }
        Map<Long, List<QuantityObject>> airQuantitys = quantityObjects.stream().collect(Collectors.groupingBy(QuantityObject::getMonitoredid));
        //空压机对应的quantityIds
        Map<Long, List<Long>> airQuantitysMap = new HashMap<>();
        airQuantitys.forEach((key, val) -> airQuantitysMap.put(key, val.stream().map(QuantityObject::getId).collect(Collectors.toList())));
        LambdaQueryWrapper<QuantityAggregationData> wrapper = getUnloadingRateWrapper(form, quantityObjects);

        List<QuantityAggregationData> aggregationDatas = modelServiceUtils.queryWithRedis(wrapper.getQueryCondition().build(), QuantityAggregationData.class);
        //根据空压机id分组物理量数数据
        Map<Long, List<QuantityAggregationData>> airAggreDatas = getQuantityMap(airQuantitysMap, aggregationDatas);

        return getUnloadDatas(quantityObjects, ids, airAggreDatas, timeRange);
    }

    /**
     * 获取最后的数据
     *
     * @param quantityObjects
     * @param ids
     * @param airAggreDatas
     * @return
     */
    public List<UnloadingRate> getUnloadDatas(List<QuantityObject> quantityObjects, List<Long> ids, Map<Long, List<QuantityAggregationData>> airAggreDatas, List<Long> timeRange) {
        List<UnloadingRate> rates = new ArrayList<>();
        Map<Long, Long> loadMap = quantityFilterToMap(quantityObjects, getLoadTime());
        Map<Long, Long> runMap = quantityFilterToMap(quantityObjects, getRunningTime());
        ids.forEach(id -> {
            UnloadingRate rate = new UnloadingRate(id);
            rates.add(rate);
            List<QuantityAggregationData> aggregationData = airAggreDatas.get(id);
            if (CollectionUtils.isEmpty(aggregationData)) {
                rate.setData(getPlanTimeDatas(timeRange));
                return;
            }
            List<PlanTimeData> planTimeDatas = new ArrayList<>();
            Map<Long, List<QuantityAggregationData>> timeDatas = aggregationData.stream().collect(Collectors.groupingBy(QuantityAggregationData::getLogtime));
            Long loadQuantityId = loadMap.get(id);
            Long runQuantityId = runMap.get(id);
            timeRange.forEach(time -> {
                PlanTimeData timeData = new PlanTimeData();
                planTimeDatas.add(timeData);
                timeData.setTime(time);
                List<QuantityAggregationData> data = timeDatas.get(time);
                if (CollectionUtils.isEmpty(data)) {
                    return;
                }
                //加载时长
                Double loadVal = data.stream().filter(item -> Objects.equals(item.getQuantityobject_id(), loadQuantityId))
                        .map(QuantityAggregationData::getValue).filter(Objects::nonNull).findFirst().orElse(0D);
                //运行时长
                Double runVal = data.stream().filter(item -> Objects.equals(item.getQuantityobject_id(), runQuantityId))
                        .map(QuantityAggregationData::getValue).filter(Objects::nonNull).findFirst().orElse(0D);
                timeData.setValue(CommonUtils.calcDouble(runVal - loadVal, runVal, EnumOperationType.DIVISION.getId()));
            });
            rate.setData(planTimeDatas);
        });
        return rates;
    }

    /**
     * 无数据的时候 返回空数据时间列表.
     *
     * @param timeRange
     * @return
     */
    public List<PlanTimeData> getPlanTimeDatas(List<Long> timeRange) {
        List<PlanTimeData> planTimeDatas = new ArrayList<>();
        timeRange.forEach(time -> planTimeDatas.add(new PlanTimeData(time)));
        return planTimeDatas;
    }

    /**
     * 卸载率查询sql
     *
     * @param form
     * @param quantityObjects
     * @return
     */
    LambdaQueryWrapper<QuantityAggregationData> getUnloadingRateWrapper(UnloadingRateForm form, List<QuantityObject> quantityObjects) {
        List<Long> quantityIds = quantityObjects.stream().map(QuantityObject::getId).collect(Collectors.toList());
        return LambdaQueryWrapper.of(QuantityAggregationData.class)
                .eq(QuantityAggregationData::getAggregationcycle, form.getAggregationCycle())
                .ge(QuantityAggregationData::getLogtime, form.getStartTime())
                .lt(QuantityAggregationData::getLogtime, form.getEndTime())
                .eq(QuantityAggregationData::getAggregationtype, AggregationType.STEP_ACCUMULATION)
                .in(QuantityAggregationData::getQuantityobject_id, quantityIds);
    }

    /**
     * 根据空压机id 分组物理量数据
     *
     * @param airQuantitysMap
     * @param aggregationDatas
     * @return
     */
    public Map<Long, List<QuantityAggregationData>> getQuantityMap(Map<Long, List<Long>> airQuantitysMap, List<QuantityAggregationData> aggregationDatas) {
        //根据quantity分组
        Map<Long, List<QuantityAggregationData>> listMap = aggregationDatas.stream().collect(Collectors.groupingBy(QuantityAggregationData::getQuantityobject_id));
        Map<Long, List<QuantityAggregationData>> airAggreDatas = new HashMap<>();
        airQuantitysMap.forEach((key, quantityIds) -> {
            List<QuantityAggregationData> data = new ArrayList<>();
            quantityIds.forEach(quantityId -> {
                List<QuantityAggregationData> aggregationData = listMap.get(quantityId);
                if (CollectionUtils.isEmpty(aggregationData)) {
                    return;
                }
                data.addAll(aggregationData);
            });
            airAggreDatas.put(key, data);
        });
        return airAggreDatas;
    }

    /**
     * 查询sql
     *
     * @param allQuantityIds
     * @return
     */
    LambdaQueryWrapper<QuantityAggregationData> getWrapper(List<Long> allQuantityIds, Integer cycle) {
        if (Objects.equals(cycle, AggregationCycle.ONE_DAY)) {
            return LambdaQueryWrapper.of(QuantityAggregationData.class)
                    .ge(QuantityAggregationData::getLogtime, TimeUtil.getFirstTimeOfDay(System.currentTimeMillis()))
                    .lt(QuantityAggregationData::getLogtime, TimeUtil.getFirstTimeOfDay(System.currentTimeMillis()) + 1)
                    .in(QuantityAggregationData::getQuantityobject_id, allQuantityIds)
                    .eq(QuantityAggregationData::getAggregationtype, AggregationType.STEP_ACCUMULATION)
                    .in(QuantityAggregationData::getAggregationcycle, Arrays.asList(AggregationCycle.ONE_DAY, AggregationCycle.ONE_MONTH));
        } else if (Objects.equals(cycle, AggregationCycle.ONE_MONTH)) {
            return LambdaQueryWrapper.of(QuantityAggregationData.class)
                    .ge(QuantityAggregationData::getLogtime, TimeUtil.getFirstDayOfThisMonth(System.currentTimeMillis()))
                    .lt(QuantityAggregationData::getLogtime, TimeUtil.getFirstDayOfThisMonth(System.currentTimeMillis()) + 1)
                    .in(QuantityAggregationData::getQuantityobject_id, allQuantityIds)
                    .eq(QuantityAggregationData::getAggregationtype, AggregationType.STEP_ACCUMULATION)
                    .in(QuantityAggregationData::getAggregationcycle, Arrays.asList(AggregationCycle.ONE_DAY, AggregationCycle.ONE_MONTH));
        } else {
            return LambdaQueryWrapper.of(QuantityAggregationData.class);
        }

    }

    /**
     * 获取数据
     *
     * @param aircompressors
     * @param loadMap
     * @param runMap
     * @param aggregationDatas
     * @return
     */
    public List<LoadingAnalysisData> getDatas(List<Aircompressor> aircompressors, Map<Long, Long> loadMap, Map<Long, Long> runMap, List<QuantityAggregationData> aggregationDatas) {
        List<LoadingAnalysisData> datas = new ArrayList<>();
        Map<Long, List<QuantityAggregationData>> listMap = aggregationDatas.stream().collect(Collectors.groupingBy(QuantityAggregationData::getQuantityobject_id));
        Long firstTimeOfDay = TimeUtil.getFirstTimeOfDay(System.currentTimeMillis());
        Long firstDayOfThisMonth = TimeUtil.getFirstDayOfThisMonth(System.currentTimeMillis());
        aircompressors.forEach(air -> {
            Long loadQuantityId = loadMap.get(air.getId());
            Long runQuantityId = runMap.get(air.getId());
            List<QuantityAggregationData> loadAggregationData = listMap.get(loadQuantityId);
            LoadingAnalysisData dayData = new LoadingAnalysisData(air.getName(), AggregationCycle.ONE_DAY, air.getId());
            LoadingAnalysisData monthData = new LoadingAnalysisData(air.getName(), AggregationCycle.ONE_MONTH, air.getId());
            if (CollectionUtils.isNotEmpty(loadAggregationData)) {
                //加载时长
                loadAggregationData.forEach(load -> {
                    if (Objects.equals(load.getLogtime(), firstTimeOfDay) && Objects.equals(load.getAggregationcycle(), AggregationCycle.ONE_DAY)) {
                        dayData.setLoadTime(load.getValue());
                    }
                    if (Objects.equals(load.getLogtime(), firstDayOfThisMonth) && Objects.equals(load.getAggregationcycle(), AggregationCycle.ONE_MONTH)) {
                        monthData.setLoadTime(load.getValue());
                    }
                });
            }
            //运行时长
            List<QuantityAggregationData> runAggregationData = listMap.get(runQuantityId);
            if (CollectionUtils.isNotEmpty(runAggregationData)) {
                runAggregationData.forEach(run -> {
                    if (Objects.equals(run.getLogtime(), firstTimeOfDay) && Objects.equals(run.getAggregationcycle(), AggregationCycle.ONE_DAY)) {
                        dayData.setRunTime(run.getValue());
                    }
                    if (Objects.equals(run.getLogtime(), firstDayOfThisMonth) && Objects.equals(run.getAggregationcycle(), AggregationCycle.ONE_MONTH)) {
                        monthData.setRunTime(run.getValue());
                    }
                });
            }
            datas.add(dayData);
            datas.add(monthData);
        });
        return sortListByName(datas);
    }

    /**
     * 获取数据
     *
     * @param aircompressors
     * @param loadMap
     * @param runMap
     * @param aggregationDatas
     * @return
     */
    public List<LoadingAnalysisData> getDatas(List<Aircompressor> aircompressors, Map<Long, Long> loadMap, Map<Long, Long> runMap,
                                              List<QuantityAggregationData> aggregationDatas, Integer aggregationCycle) {
        Map<Long, List<QuantityAggregationData>> listMap = aggregationDatas.stream().collect(Collectors.groupingBy(QuantityAggregationData::getQuantityobject_id));
        List<LoadingAnalysisData> datas = new ArrayList<>();
        aircompressors.forEach(air -> {
            Long loadQuantityId = loadMap.get(air.getId());
            Long runQuantityId = runMap.get(air.getId());
            List<QuantityAggregationData> loadAggregationData = listMap.get(loadQuantityId);
            LoadingAnalysisData data = new LoadingAnalysisData(air.getName(), aggregationCycle, air.getId());
            if (CollectionUtils.isNotEmpty(loadAggregationData)) {
                //加载时长
                loadAggregationData.forEach(load -> {
                    data.setLoadTime(load.getValue());
                });
            }
            //运行时长
            List<QuantityAggregationData> runAggregationData = listMap.get(runQuantityId);
            if (CollectionUtils.isNotEmpty(runAggregationData)) {
                runAggregationData.forEach(run -> {
                    data.setRunTime(run.getValue());
                });
            }
            datas.add(data);
        });
        return sortListById(datas);
    }

    /**
     * 过滤quantity
     *
     * @param quantityObjects
     * @param searchVo
     * @return
     */
    public List<QuantityObject> quantityFilter(List<QuantityObject> quantityObjects, QuantitySearchVo searchVo) {
        return quantityObjects.stream().filter(quant -> Objects.equals(searchVo.getQuantitycategory(), quant.getQuantitycategory())
                && Objects.equals(searchVo.getQuantitytype(), quant.getQuantitytype())
                && Objects.equals(searchVo.getFrequency(), quant.getFrequency())
                && Objects.equals(searchVo.getPhasor(), quant.getPhasor())
                && Objects.equals(searchVo.getEnergytype(), quant.getEnergytype())).collect(Collectors.toList());
    }

    public Map<Long, Long> quantityFilterToMap(List<QuantityObject> quantityObjects, QuantitySearchVo searchVo) {
        return quantityObjects.stream().filter(quant -> Objects.equals(searchVo.getQuantitycategory(), quant.getQuantitycategory())
                && Objects.equals(searchVo.getQuantitytype(), quant.getQuantitytype())
                && Objects.equals(searchVo.getFrequency(), quant.getFrequency())
                && Objects.equals(searchVo.getPhasor(), quant.getPhasor())
                && Objects.equals(searchVo.getEnergytype(), quant.getEnergytype()))
                .collect(Collectors.toMap(QuantityObject::getMonitoredid, QuantityObject::getId));
    }

    /**
     * 同环比的开始时间
     *
     * @param form
     * @param type
     * @return
     */
    public LocalDateTime getStartTime(ElectricalRatioForm form, Integer type) {
        switch (type) {
            case QueryType.YEAR_ON_YEAR:
            case QueryType.YEAR_AND_MONTH:
                if (Objects.equals(form.getAggregationCycle(), AggregationCycle.ONE_HOUR)) {
                    return TimeUtil.timestamp2LocalDateTime(form.getStartTime()).plusMonths(-1);
                }
                return TimeUtil.timestamp2LocalDateTime(form.getStartTime()).plusYears(-1);
            case QueryType.MONTH_ON_MONTH:
                if (Objects.equals(form.getAggregationCycle(), AggregationCycle.ONE_HOUR)) {
                    return TimeUtil.timestamp2LocalDateTime(form.getStartTime()).plusDays(-1);
                }
                return TimeUtil.timestamp2LocalDateTime(form.getStartTime()).plusMonths(-1);

            case QueryType.CURRENT:
                return TimeUtil.timestamp2LocalDateTime(form.getStartTime());
            default:
                return null;
        }
    }

    /**
     * 同环比的结束时间
     *
     * @param form
     * @param type
     * @return
     */
    public LocalDateTime getEndTime(ElectricalRatioForm form, Integer type) {
        switch (type) {
            case QueryType.YEAR_ON_YEAR:
            case QueryType.YEAR_AND_MONTH:
                if (Objects.equals(form.getAggregationCycle(), AggregationCycle.ONE_HOUR)) {
                    return TimeUtil.timestamp2LocalDateTime(form.getEndTime()).plusMonths(-1);
                } else if (Objects.equals(form.getAggregationCycle(), AggregationCycle.ONE_DAY)) {
                    return TimeUtil.timestamp2LocalDateTime(form.getEndTime()).plusYears(-1);
                } else if (Objects.equals(form.getAggregationCycle(), AggregationCycle.ONE_MONTH)) {
                    return TimeUtil.timestamp2LocalDateTime(form.getStartTime());
                }
            case QueryType.MONTH_ON_MONTH:
                return TimeUtil.timestamp2LocalDateTime(form.getStartTime());
            case QueryType.CURRENT:
                return TimeUtil.timestamp2LocalDateTime(form.getEndTime());
            default:
                return null;
        }
    }

    /**
     * 同比 环比 数据
     *
     * @param result
     * @param consumptions
     * @param type
     * @param form
     * @param
     * @return
     */
    public EnergyResult setData(EnergyResult result, List<EnergyConsumption> consumptions, Integer type, ElectricalRatioForm form, LocalDateTime st) {
        //今年数据
        List<EnergyConsumption> thisYearDatas = filterDatas(form.getStartTime(), form.getEndTime(), consumptions);
        List<EnergyConsumption> lastYearDatas;
        List<EnergyConsumption> lastMonthDatas;
        LocalDateTime end = getEndTime(form, type);
        switch (type) {
            case QueryType.YEAR_ON_YEAR:
                //去年数据
                lastYearDatas = filterDatas(TimeUtil.localDateTime2timestamp(st), TimeUtil.localDateTime2timestamp(end), consumptions);
                lastMonthDatas = null;
                break;
            case QueryType.MONTH_ON_MONTH:
                //上月数据
                lastMonthDatas = filterDatas(TimeUtil.localDateTime2timestamp(st), TimeUtil.localDateTime2timestamp(end), consumptions);
                lastYearDatas = null;
                break;
            case QueryType.YEAR_AND_MONTH:
                //去年数据
                lastYearDatas = filterDatas(TimeUtil.localDateTime2timestamp(st), TimeUtil.localDateTime2timestamp(end), consumptions);
                LocalDateTime startTime1 = getStartTime(form, QueryType.MONTH_ON_MONTH);
                //上月数据
                lastMonthDatas = filterDatas(TimeUtil.localDateTime2timestamp(startTime1), form.getStartTime(), consumptions);
                break;
            case QueryType.CURRENT:
            default:
                lastYearDatas = null;
                lastMonthDatas = null;
                break;
        }
        List<Long> timeRange = TimeUtil.getTimeRange(form.getStartTime(), form.getEndTime(), form.getAggregationCycle());
        //环比时间戳
        List<Long> timeRange1 = TimeUtil.getTimeRange(TimeUtil.localDateTime2timestamp(getStartTime(form, QueryType.MONTH_ON_MONTH)),
                TimeUtil.localDateTime2timestamp(getEndTime(form, QueryType.MONTH_ON_MONTH)), form.getAggregationCycle());
        //同比时间戳
        List<Long> timeRange2 = TimeUtil.getTimeRange(TimeUtil.localDateTime2timestamp(getStartTime(form, QueryType.YEAR_ON_YEAR)),
                TimeUtil.localDateTime2timestamp(getEndTime(form, QueryType.YEAR_ON_YEAR)), form.getAggregationCycle());
        List<PlanTimeData> currentdata = getValues(thisYearDatas, timeRange);
        List<PlanTimeData> tbdata = getValues(lastYearDatas, timeRange2);
        List<PlanTimeData> hbdata = getValues(lastMonthDatas, timeRange1);
        result.setHbdata(hbdata);
        result.setTbdata(tbdata);
        result.setCurrentdata(currentdata);
        return result;
    }

    /**
     * 数据计算 电气比=输入有功电度 / 压缩空气累计量（kWh/m3）
     *
     * @param datas
     * @return
     */
    public List<PlanTimeData> getValues(List<EnergyConsumption> datas, List<Long> timeRange) {
        if (Objects.isNull(datas)) {
            return Collections.emptyList();
        }
        if (datas.isEmpty()) {
            return getTimeData(timeRange);
        }
        List<PlanTimeData> timeVals = new ArrayList<>();
        List<EnergyConsumption> twoDatas = datas.stream().filter(item -> Objects.equals(item.getEnergytype(), Constant.TWO) && Objects.nonNull(item.getUsage())).collect(Collectors.toList());
        List<EnergyConsumption> sixDatas = datas.stream().filter(item -> Objects.equals(item.getEnergytype(), Constant.SIXTEEN) && Objects.nonNull(item.getUsage())).collect(Collectors.toList());
        Map<Long, List<EnergyConsumption>> listMap1 = twoDatas.stream().collect(Collectors.groupingBy(EnergyConsumption::getLogtime));
        Map<Long, List<EnergyConsumption>> listMap2 = sixDatas.stream().collect(Collectors.groupingBy(EnergyConsumption::getLogtime));
        timeRange.forEach(time -> {
            PlanTimeData timeData = new PlanTimeData();
            timeData.setTime(time);
            timeVals.add(timeData);
            List<EnergyConsumption> list1 = listMap1.get(time);
            List<EnergyConsumption> list2 = listMap2.get(time);
            if (CollectionUtils.isEmpty(list1) || CollectionUtils.isEmpty(list2)) {
                return;
            }
            double sum1 = list1.stream().mapToDouble(EnergyConsumption::getUsage).sum();
            double sum2 = list2.stream().mapToDouble(EnergyConsumption::getUsage).sum();
            timeData.setValue(CommonUtils.calcDouble(sum1, sum2, EnumOperationType.DIVISION.getId()));
        });
        return timeVals;
    }

    /**
     * 根据时间过滤数据
     *
     * @param st
     * @param end
     * @param datas
     * @return
     */
    public List<EnergyConsumption> filterDatas(Long st, Long end, List<EnergyConsumption> datas) {

        return datas.stream().filter(item -> st <= item.getLogtime() && item.getLogtime() < end).collect(Collectors.toList());
    }

    /**
     * 获取事件
     *
     * @param roomVos
     * @return
     */
    public Event getEvent(List<Equipments> roomVos, Long roomId) {
        Event event = new Event();
        if (Objects.isNull(roomVos) || roomVos.isEmpty()) {
            return new Event();
        }
        Equipments roomVo = roomVos.get(0);
        List<BaseVo> baseVos = getBaseVos(roomVo, roomId);
        //月初月末事件.
        LocalDateTime firstDayOfThisMonth = TimeUtil.getFirstDayOfThisMonth(LocalDateTime.now());
        LocalDateTime firstDayOfNextMonth = TimeUtil.getFirstDayOfNextMonth(LocalDateTime.now());
        //今天的零点零时
        LocalDateTime firstTimeOfDay = TimeUtil.getFirstTimeOfDay(LocalDateTime.now());
        //明天的零点零时
        LocalDateTime tomorrow = firstTimeOfDay.plusDays(+1);
        //系统事件 -月度事件
        Integer sysEventCountMonth = getSysEventCount(firstDayOfThisMonth, firstDayOfNextMonth, baseVos);
        Integer sysEventCountToday = getSysEventCount(firstTimeOfDay, tomorrow, baseVos);
        List<EnergySupplyToPo> energySupplyToPos = queryEnergySupply(roomId);
        List<BaseVo> nodes = energySupplyToPos.stream().map(supply -> new BaseVo(supply.getObjectid(), supply.getObjectlabel())).collect(Collectors.toList());
        nodes.add(new BaseVo(roomId, NodeLabelDef.ROOM));
        //percore事件 -月度事件
        Integer pecEventCountMonth = getPecEventCount(firstDayOfThisMonth, firstDayOfNextMonth, nodes);
        //percore事件 -当日事件
        Integer pecEventCountToday = getPecEventCount(firstTimeOfDay, tomorrow, nodes);
        event.setMonthEventCount(sysEventCountMonth + pecEventCountMonth);
        event.setDayEventCount(sysEventCountToday + pecEventCountToday);
        return event;
    }

    /**
     * 获取系统事件的节点
     *
     * @param roomVo
     * @param roomId
     * @return
     */
    List<BaseVo> getBaseVos(Equipments roomVo, Long roomId) {
        List<BaseVo> baseVos = new ArrayList<>();
        baseVos.addAll(getBaseVos(roomVo.getDryingmachine_model()));
        baseVos.addAll(getBaseVos(roomVo.getCoolingtower_model()));
        baseVos.addAll(getBaseVos(roomVo.getPumpNofilter()));
        baseVos.addAll(getBaseVos(roomVo.getAircompressor_model()));
        baseVos.add(new BaseVo(roomId, NodeLabelDef.ROOM));
        return baseVos;
    }

    /**
     * 获取单个类型的baseVo
     *
     * @param datas
     * @param <T>
     * @return
     */
    public <T extends BaseEntity> List<BaseVo> getBaseVos(List<T> datas) {
        List<BaseVo> baseVos = new ArrayList<>();
        if (CollectionUtils.isEmpty(datas)) {
            return baseVos;
        }
        datas.forEach(data -> {
            BaseVo baseVo = new BaseVo();
            baseVo.setId(data.getId());
            baseVo.setModelLabel(data.getModelLabel());
            baseVos.add(baseVo);
        });
        return baseVos;
    }

    /**
     * 获取系统事件
     *
     * @param st
     * @param end
     * @param baseVos
     * @return
     */
    public Integer getSysEventCount(LocalDateTime st, LocalDateTime end, List<BaseVo> baseVos) {
        SystemEventCountVo countV = new SystemEventCountVo();
        countV.setStartTime(st);
        countV.setEndTime(end);
        countV.setGroupField(ColumnDef.CONFIRM_EVENT_STATUS);
        countV.setNodes(baseVos);
        List<AggregationResult<Integer>> aggregationResults = systemEventDao.countEvent(countV);
        return aggregationResults.stream().mapToInt(AggregationResult::getCount).sum();
    }

    /**
     * 获取percore事件
     *
     * @param st
     * @param end
     * @param baseVos
     * @return
     */
    public Integer getPecEventCount(LocalDateTime st, LocalDateTime end, List<BaseVo> baseVos) {
        PecEventCountVo pecEventCountVo = new PecEventCountVo();
        pecEventCountVo.setStartTime(st);
        pecEventCountVo.setEndTime(end);
        pecEventCountVo.setQueryEndEvent(true);
        pecEventCountVo.setGroupField(ColumnDef.CONFIRM_EVENT_STATUS);
        pecEventCountVo.setNodes(baseVos);
        List<AggregationResult<Integer>> aggregationResults = pecEventService.countEvent(pecEventCountVo);
        return aggregationResults.stream().mapToInt(AggregationResult::getCount).sum();
    }

    /**
     * 机房数量+运行数量判断
     *
     * @param roomVos
     * @return
     */
    public List<Operation> getOperation(List<Equipments> roomVos) {
        List<Operation> operations = new ArrayList<>();
        if (Objects.isNull(roomVos) || roomVos.isEmpty()) {
            return new ArrayList<>();
        }
        Equipments roomVo = roomVos.get(0);
        List<Aircompressor> aircompressors = roomVo.getAircompressor_model();
        List<Coolingtower> coolingtowers = roomVo.getCoolingtower_model();
        List<Pump> pumps = roomVo.getPump_model();
        List<Dryingmachine> dryingmachines = roomVo.getDryingmachine_model();
        operations.add(new Operation(aircompressors.size(), NodeLabelDef.AIR_COMPRESSOR));
        operations.add(new Operation(pumps.size(), NodeLabelDef.PUMP));
        operations.add(new Operation(dryingmachines.size(), NodeLabelDef.DRYING_MACHINE));
        operations.add(new Operation(coolingtowers.size(), NodeLabelDef.COOLING_TOWER));
        List<BaseVo> baseVos = new ArrayList<>();
        baseVos.addAll(aircompressors.stream().map(item -> new BaseVo(item.getId(), item.getModelLabel())).collect(Collectors.toList()));
        baseVos.addAll(coolingtowers.stream().map(item -> new BaseVo(item.getId(), item.getModelLabel())).collect(Collectors.toList()));
        baseVos.addAll(pumps.stream().map(item -> new BaseVo(item.getId(), item.getModelLabel())).collect(Collectors.toList()));
        baseVos.addAll(dryingmachines.stream().map(item -> new BaseVo(item.getId(), item.getModelLabel())).collect(Collectors.toList()));
        Map<Integer, List<RealTimeValue>> realTimeValues = realTimeValueDatas(baseVos, Arrays.asList(getFunction(), getClosing()));
        //所有的记录.
        List<RealTimeValue> values = realTimeValues.values().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());
        Map<String, List<RealTimeValue>> listMap = values.stream().collect(Collectors.groupingBy(RealTimeValue::getMonitoredLabel));
        operations.forEach(operation -> {
            List<RealTimeValue> realTimeValue = listMap.get(operation.getModelLabel());
            if (CollectionUtils.isEmpty(realTimeValue)) {
                return;
            }
            operation.setFunctionTotal(getFunction(realTimeValue));
        });
        return operations;
    }

    /**
     * 获取运行台数
     *
     * @param datas
     * @return
     */
    public Integer getFunction(List<RealTimeValue> datas) {
        Map<Long, List<RealTimeValue>> listMap = datas.stream().collect(Collectors.groupingBy(RealTimeValue::getMonitoredId));
        return getCount(listMap, Constant.YUN_XING, Constant.ONE, Constant.TING_JI, Constant.ZERO);
    }

    /**
     * yxVal=1 tjVal=0
     * 停机运行判断方法,有运行且状态为1则为运行.没运行有停机状态为0则为运行
     *
     * @param listMap
     * @param yxDataId
     * @param yxVal
     * @param tjDataId
     * @param tjVal
     * @return
     */
    public Integer getCount(Map<Long, List<RealTimeValue>> listMap, Long yxDataId, Integer yxVal, Long tjDataId, Integer tjVal) {
        int count = 0;
        for (Long key : listMap.keySet()) {
            Map<Long, Double> data = listMap.get(key).stream().filter(item -> Objects.nonNull(item.getValue())).collect(Collectors.toMap(RealTimeValue::getDataId, RealTimeValue::getValue));
            //分闸和合闸共在  取合闸为1 则为运行
            if (data.containsKey(yxDataId) && Objects.equals(data.get(yxDataId).intValue(), yxVal)) {
                count++;
                //只有分闸 则取分闸的值为0 为运行
            } else if (!data.containsKey(yxDataId) && data.containsKey(tjDataId) && Objects.equals(data.get(tjDataId).intValue(), tjVal)) {
                count++;
            } else {
                count += 0;
            }
        }
        return count;
    }

    /**
     * 按名称排序
     *
     * @param list
     * @return
     */
    public List<LoadingAnalysisData> sortListByName(List<LoadingAnalysisData> list) {
        if (com.cet.eem.toolkit.CollectionUtils.isEmpty(list)) {
            return list;
        }
        list.sort(new Comparator<LoadingAnalysisData>() {
            Comparator comparator = Collator.getInstance(Locale.CHINA);

            @Override
            public int compare(LoadingAnalysisData o1, LoadingAnalysisData o2) {
                return comparator.compare(o1.getName(), o2.getName());
            }
        });
        return list;
    }

    public List<LoadingAnalysisData> sortListById(List<LoadingAnalysisData> list) {
        if (com.cet.eem.toolkit.CollectionUtils.isEmpty(list)) {
            return list;
        }
        return list.stream().sorted(Comparator.comparing(LoadingAnalysisData::getAirCompressorId)).collect(Collectors.toList());
    }


    /**
     * 获取实时数据
     *
     * @param baseVos
     * @param quantitySettings
     * @return
     */
    public Map<Integer, List<RealTimeValue>> realTimeValueDatas(List<BaseVo> baseVos, List<QuantitySearchVo> quantitySettings) {
        Long st = handleTime();
        Long et = TimeUtil.addDateTimeByCycle(st, AggregationCycle.ONE_MINUTE, 1);
        return quantityManageService.queryRealTimeBath(createQuantityDataBatchSearchVoWithRealTime(baseVos, st, et, quantitySettings));
    }

    public Long handleTime() {
        long now = System.currentTimeMillis();
        LocalDateTime localDateTime = TimeUtil.timestamp2LocalDateTime(now);
        LocalDate date = localDateTime.toLocalDate();
        LocalTime time = LocalTime.of(localDateTime.getHour(), localDateTime.getMinute());
        LocalDateTime of = LocalDateTime.of(date, time);
        return TimeUtil.localDateTime2timestamp(of);
    }

    /**
     * 查询实时数据
     *
     * @param deviceNodes
     * @param st
     * @param et
     * @param quantitySettings
     * @return
     */
    public QuantityDataBatchSearchVo createQuantityDataBatchSearchVoWithRealTime(List<BaseVo> deviceNodes, Long st, Long et, List<QuantitySearchVo> quantitySettings) {
        QuantityDataBatchSearchVo aggregationDataBatch = new QuantityDataBatchSearchVo();
        aggregationDataBatch.setDataTypeId(EnumDataTypeId.REALTIME.getId());
        aggregationDataBatch.setStartTime(st);
        aggregationDataBatch.setEndTime(et);
        aggregationDataBatch.setQuantitySettings(quantitySettings);
        aggregationDataBatch.setNodes(deviceNodes);
        return aggregationDataBatch;
    }

    /**
     * 运行
     *
     * @return
     */
    public QuantitySearchVo getFunction() {
        QuantitySearchVo searchVo = new QuantitySearchVo(9013003,
                QuantityCategoryDef.STATUS,
                QuantityTypeDef.INSTANTANEOUS,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                Constant.TENTHOUSAND);
        searchVo.setStateMeanType(Constant.EIGHTEEN);
        searchVo.setControlMeanType(ControlMeanType.NONE.getValue());
        return searchVo;
    }

    /**
     * 停机
     *
     * @return
     */
    public QuantitySearchVo getClosing() {
        QuantitySearchVo searchVo = new QuantitySearchVo(9013008,
                QuantityCategoryDef.STATUS,
                QuantityTypeDef.INSTANTANEOUS,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                Constant.TENTHOUSAND);
        searchVo.setStateMeanType(Constant.NINETEEN);
        searchVo.setControlMeanType(ControlMeanType.NONE.getValue());
        return searchVo;
    }

    /**
     * 总管压力
     * EnergyTypeDef改为了16
     *
     * @return
     */
    public QuantitySearchVo getHeaderPressure() {
        return new QuantitySearchVo(6004024,
                Constant.THIRTEEN,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                Constant.TWENTY_NINE,
                EnergyTypeDef.COMPRESSEDAIR);
    }

    /**
     * 总管瞬时流量
     * EnergyTypeDef改为了16
     *
     * @return
     */
    public QuantitySearchVo getHeaderFlow() {
        return new QuantitySearchVo(6008032,
                Constant.TWENTY_ONE,
                QuantityTypeDef.INSTANTANEOUS,
                FrequencyDef.NONE,
                Constant.TWENTY_SEVEN,
                EnergyTypeDef.COMPRESSEDAIR);
    }

    /**
     * 总管露点温度
     * EnergyTypeDef改为了16
     *
     * @return
     */
    public QuantitySearchVo getHeaderTemperature() {
        return new QuantitySearchVo(6007211,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                Constant.TWENTY_NINE,
                EnergyTypeDef.COMPRESSEDAIR);
    }

    /**
     * 运行时长
     *
     * @return 这个里面的id用1 2 3 4都可以，这个就是用来表示你的入参组号的
     */
    public QuantitySearchVo getRunningTime() {
        return new QuantitySearchVo(6008038,
                QuantityCategoryDef.DEVICE_WORK_TIME,
                QuantityTypeDef.DEVICE_WORK_TIME,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                Constant.TENTHOUSAND);
    }

    /**
     * 加载时间
     *
     * @return
     */
    public QuantitySearchVo getLoadTime() {
        return new QuantitySearchVo(6008036,
                QuantityCategoryDef.DEVICE_WORK_TIME,
                Constant.ONE_HUNDRED_AND_NINETY_THREE,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                Constant.TENTHOUSAND);
    }
}

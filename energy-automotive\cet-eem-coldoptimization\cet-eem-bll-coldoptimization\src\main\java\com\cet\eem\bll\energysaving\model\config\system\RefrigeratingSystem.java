package com.cet.eem.bll.energysaving.model.config.system;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.bll.energysaving.model.def.ColdOptimizationLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : RefrigeratingSystem
 * @Description : 制冷系统
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-17 13:42
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.REFRIGERATING_SYSTEM)
public class RefrigeratingSystem extends BaseEntity {
    @ApiModelProperty("板式换热器最小出水温度")
    @JsonProperty("plateheatexchangermintemp")
    private Double plateHeaTexChangerMinTemp;
    @ApiModelProperty("系统id")
    @JsonProperty(ColumnDef.ROOM_ID)
    private Long roomId ;

    @ApiModelProperty("是否使用ai预测")
    @JsonProperty("useai")
    private Boolean useAi;

    @ApiModelProperty("项目id")
    @JsonProperty(ColumnDef.PROJECT_ID)
    private Long projectId;
    public RefrigeratingSystem(){
        this.modelLabel=ModelLabelDef.REFRIGERATING_SYSTEM;
    }

    /**
     * 板换开启温度限制
     */
    @ApiModelProperty("板换开启温度限制")
    @JsonProperty(ColdOptimizationLabelDef.OUTSIDE_TEMP_LIMIT)
    private Double outsideTempLimit;
    /**
     * 板换开启冷机制冷需求限制
     */
    @ApiModelProperty("板换开启冷机制冷需求限制")
    @JsonProperty(ColdOptimizationLabelDef.COOLING_LOAD_DEMAND_LIMIT)
    private Double coolingLoadDemandLimit;
}
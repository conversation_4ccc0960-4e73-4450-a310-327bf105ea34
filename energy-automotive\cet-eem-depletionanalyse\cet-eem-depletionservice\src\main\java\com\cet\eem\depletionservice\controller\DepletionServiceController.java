package com.cet.eem.depletionservice.controller;

import com.cet.eem.common.model.Result;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.depletion.model.vo.Detail;
import com.cet.eem.depletion.model.vo.TempTrend;
import com.cet.eem.depletion.model.vo.WasteOverView;
import com.cet.eem.depletion.service.CalcDepletionConsumptionService;
import com.cet.eem.depletion.service.DepletionDivideForCataphoresisService;
import com.cet.eem.depletion.service.DepletionDivideForLineService;
import com.cet.eem.depletion.service.DepletionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;

@Api(value = "DepletionServiceController", tags = "空耗分析")
@RequestMapping(value = "/eem/v1/wasteAnalysis")
@RestController
public class DepletionServiceController {

    @Autowired
    DepletionService depletionService;

    @Autowired
    DepletionDivideForCataphoresisService depletionDivideForCataphoresisService;

    @Autowired
    DepletionDivideForLineService depletionDivideForLineService;

    @Autowired
    CalcDepletionConsumptionService calcDepletionConsumption;

    @ApiOperation(value = "空耗总览")
    @PostMapping(value = "/overView", produces = "application/json")
    public Result<WasteOverView> queryDepletionOverView(@RequestParam @NotNull Long time, @RequestParam @NotNull Long id, @RequestParam @NotNull String modelLabel) {
        return Result.ok(depletionService.depletionOverView(time, id, modelLabel));
    }

    @ApiOperation(value = "洪流热水洗温度曲线")
    @PostMapping(value = "/trend", produces = "application/json")
    public Result<TempTrend> queryDepletionTempTrend(@RequestParam @NotNull Long time, @RequestParam @NotNull Long id,
                                                     @RequestParam @NotNull String modelLabel,
                                                     @RequestParam @NotNull Integer linebodytype) throws IOException {
        return Result.ok(depletionService.depletionTempTrend(time, id, modelLabel, linebodytype));
    }

//    @ApiOperation(value = "升温空耗详情")
//    @PostMapping(value = "/heatingDetails", produces = "application/json")
//    public Result<List<HeatingDetail>> queryHeatingDetails(@RequestParam @NotNull Long time, @RequestParam @NotNull Long id, @RequestParam @NotNull String modelLabel) {
//        return Result.ok(depletionService.getHeatingDetails(time, id, modelLabel));
//    }
//
//    @ApiOperation(value = "过车空耗详情")
//    @PostMapping(value = "/passingDetails", produces = "application/json")
//    public Result<List<PassingDetail>> queryPassingDetails(@RequestParam @NotNull Long time, @RequestParam @NotNull Long id, @RequestParam @NotNull String modelLabel) {
//        return Result.ok(depletionService.getPassingDetails(time, id, modelLabel));
//    }

    @ApiOperation(value = "各类空耗详情")
    @PostMapping(value = "/details", produces = "application/json")
    public Result<List<Detail>> getDepletionDetails(@RequestParam @NotNull Long time, @RequestParam @NotNull Long id,
                                                    @RequestParam @NotNull String modelLabel, @RequestParam @NotNull Integer depletionType) throws IOException {
        return Result.ok(depletionService.getDepletionDetails(time, id, modelLabel, depletionType));
    }

    @ApiOperation(value = "手动执行电泳池升温、空耗数据处理")
    @PostMapping(value = "/depletionDivideForCataphoresis", produces = "application/json")
    public void executeDepletionDivideForCataphoresis(@RequestParam @NotNull Long startTime, @RequestParam @NotNull Long endTime) throws IOException {
        depletionDivideForCataphoresisService.depletionDivide(startTime, endTime);
    }

    @ApiOperation(value = "手动执行线体、线体空调的空耗数据处理")
    @PostMapping(value = "/depletionDivideForLineService", produces = "application/json")
    public void executeDepletionDivideForLineService(@RequestParam @NotNull Long startTime, @RequestParam @NotNull Long endTime) throws IOException {
        depletionDivideForLineService.depletionDivide(startTime, endTime);
    }

    @ApiOperation(value = "手动执行电泳池空耗能耗计算")
    @PostMapping(value = "/calcDepletionConsumption", produces = "application/json")
    public void executeCalcDepletionConsumption(@RequestParam @NotNull Long startTime, @RequestParam @NotNull Long endTime) {
        calcDepletionConsumption.calcDepletionConsumption(startTime, endTime);
    }

    @ApiOperation(value = "一键执行空耗划分和空耗计算")
    @PostMapping(value = "/oneClick", produces = "application/json")
    public void oneClick(@RequestParam @NotNull Long startTime, @RequestParam @NotNull Long endTime) throws IOException {
        if (endTime <= startTime) {
            return;
        }
        List<Long> timeRange = TimeUtil.getTimeRange(startTime, endTime, AggregationCycle.ONE_DAY);
        for (Long time : timeRange) {
            Long end = TimeUtil.addDateTimeByCycle(time, AggregationCycle.ONE_DAY, 1);
            depletionDivideForCataphoresisService.depletionDivide(time, end);
            depletionDivideForLineService.depletionDivide(time, end);
            calcDepletionConsumption.calcDepletionConsumption(time, end);
        }
    }
}

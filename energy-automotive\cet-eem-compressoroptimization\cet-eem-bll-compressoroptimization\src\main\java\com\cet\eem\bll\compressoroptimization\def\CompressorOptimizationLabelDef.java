package com.cet.eem.bll.compressoroptimization.def;

import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : CompressorOptimizationLabelDef
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-17 10:04
 */
@Getter
@Setter
public class CompressorOptimizationLabelDef {
    /**
     * 空压ai系统模型：
     */
    public static final String COMPRESSOR_SYSTEM = "compressorsystem";
    public static final String USE_AI = "useai";
    public static final String COMPRESSOR_CONTROL = "compressorcontrol";
    public static final String COMPRESSOR_CONFIG = "compressorconfig";
    public static final String CONTROL_STRATEGY = "aicompressorstrategy";
    public static final String STRATEGY_OBJECT_CONFIG = "strategyobjectconfig";

}
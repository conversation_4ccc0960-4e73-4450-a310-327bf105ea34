package com.cet.eem.bll.compressoroptimization.dao.impl;

import com.cet.eem.bll.common.model.domain.subject.energy.EnergyConsumption;
import com.cet.eem.bll.common.model.domain.subject.energysaving.ColdPredict;
import com.cet.eem.bll.compressoroptimization.dao.CompressorSystemDao;
import com.cet.eem.bll.compressoroptimization.def.CompressorOptimizationLabelDef;
import com.cet.eem.bll.compressoroptimization.model.config.CompressorSystem;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import com.cet.eem.model.model.BaseEntity;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : CompressorSystemDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-20 16:04
 */
@Repository
public class CompressorSystemDaoImpl extends ModelDaoImpl<CompressorSystem> implements CompressorSystemDao {

    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Override
    public CompressorSystem queryCompressorSystem(Long systemId, Long projectId) {
        if (Objects.nonNull(systemId)) {
            return this.selectById(systemId);
        } else {
            LambdaQueryWrapper<CompressorSystem> wrapper = LambdaQueryWrapper.of(CompressorSystem.class);
            wrapper.eq(CompressorSystem::getProjectId, projectId)
                    .eq(CompressorSystem::getObjectLabel, NodeLabelDef.ROOM);
            List<CompressorSystem> compressorSystems = this.selectList(wrapper);
            if (CollectionUtils.isNotEmpty(compressorSystems)) {
                return compressorSystems.get(0);
            }
            return null;
        }
    }

    @Override
    public List<CompressorSystem> queryCompressorDevice(List<BaseVo> nodes, Long projectId) {
        if (CollectionUtils.isEmpty(nodes)) {
            LambdaQueryWrapper<CompressorSystem> wrapper = LambdaQueryWrapper.of(CompressorSystem.class);
            wrapper.eq(CompressorSystem::getProjectId, projectId)
                    .eq(CompressorSystem::getObjectLabel, NodeLabelDef.AIR_COMPRESSOR);
            return this.selectList(wrapper);
        } else {
            Map<String, List<BaseVo>> nodeMap = nodes.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));

            int group = 1;

            QueryConditionBuilder<BaseEntity> builder = new QueryConditionBuilder<>(CompressorOptimizationLabelDef.COMPRESSOR_SYSTEM)
                    .composeMethod(true)
                    .removeOrderById();
            for (Map.Entry<String, List<BaseVo>> entry : nodeMap.entrySet()) {
                String label = entry.getKey();
                List<Long> ids = nodeMap.get(label).stream().map(BaseVo::getId).distinct().collect(Collectors.toList());
                builder.eq(ColumnDef.OBJECTID, ids, group);
                builder.eq(ColumnDef.C_OBJECT_Label, entry.getKey(), group);
                group++;
            }
            return modelServiceUtils.query(builder.build(), CompressorSystem.class);
        }

    }
}
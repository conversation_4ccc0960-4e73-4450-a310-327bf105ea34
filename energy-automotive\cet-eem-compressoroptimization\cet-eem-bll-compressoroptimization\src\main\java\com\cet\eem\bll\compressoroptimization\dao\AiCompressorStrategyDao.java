package com.cet.eem.bll.compressoroptimization.dao;

import com.cet.eem.bll.compressoroptimization.model.strategy.AiCompressorStrategy;
import com.cet.eem.bll.compressoroptimization.model.strategy.AiCompressorStrategyWithLayer;
import com.cet.eem.dao.BaseModelDao;

import java.util.List;

/**
 * @ClassName : AiCompressorStrategyDao
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-24 15:52
 */
public interface AiCompressorStrategyDao extends BaseModelDao<AiCompressorStrategy> {
    /**
     * 查询策略
     * @param startTime
     * @param endTime
     * @param systemId
     * @param projectId
     * @return
     */
    List<AiCompressorStrategy> queryRealTime(Long startTime, Long endTime, Long systemId, Long projectId,Integer strategyType);

    /**
     * 查询策略
     * @param startTime
     * @param endTime
     * @param systemId
     * @param projectId
     * @return
     */
    List<AiCompressorStrategyWithLayer> queryCompressorStrategy(Long startTime, Long endTime, Long systemId, Long projectId,Integer strategyType);
}
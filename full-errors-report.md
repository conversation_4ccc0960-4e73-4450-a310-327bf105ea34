﻿# Java Compilation Errors Report

**Generated:** 2025-08-05 16:27:10
**Module:** energy-solution-fusion\eem-solution-transformer\eem-solution-transformer-core
**Total Issues:** 135 (Compilation: 83, Import Analysis: 52)

## ISSUES FOUND

### AvgloadTask.java (2 issues)

**Issue 1 - Line 3:** Compilation Error

`import com.cet.eem.task.model.TransformerindexDataService;`

**Issue 2 - Line 21:** Compilation Error

`TransformerindexDataService transformerindexDataService;`

### PowerTransformerDto.java (6 issues)

**Issue 1 - Line 3:**

`import com.cet.eem.bll.common.model.domain.object.organization.Project;`

**Issue 2 - Line 3:** Compilation Error

`import com.cet.eem.bll.common.model.domain.object.organization.Project;`

**Issue 3 - Line 4:**

`import com.cet.eem.bll.common.model.domain.object.powersystem.PowerTransformerVo;`

**Issue 4 - Line 4:** Compilation Error

`import com.cet.eem.bll.common.model.domain.object.powersystem.PowerTransformerVo;`

**Issue 5 - Line 18:** Compilation Error

`@Setter`

**Issue 6 - Line 19:** Compilation Error

`public class PowerTransformerDto extends PowerTransformerVo {`

### TransformerAnalysisController.java (4 issues)

**Issue 1 - Line 3:**

`import com.cet.eem.bll.common.util.GlobalInfoUtils;`

**Issue 2 - Line 3:** Compilation Error

`import com.cet.eem.bll.common.util.GlobalInfoUtils;`

**Issue 3 - Line 15:** Compilation Error

`import com.cet.eem.task.model.TransformerTaskService;`

**Issue 4 - Line 35:** Compilation Error

`TransformerTaskService transformerTaskService;`

### TransformerAnalysisService.java (11 issues)

**Issue 1 - Line 3:** Compilation Error

`import com.cet.eem.common.model.BaseVo;`

**Issue 2 - Line 4:** Compilation Error

`import com.cet.eem.common.model.datalog.DataLogData;`

**Issue 3 - Line 8:** Compilation Error

`import com.cet.eem.node.model.LinkNode;`

**Issue 4 - Line 9:** Compilation Error

`import com.cet.eem.node.model.PointNode;`

**Issue 5 - Line 10:**

`import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;`

**Issue 6 - Line 10:** Compilation Error

`import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;`

**Issue 7 - Line 74:** Compilation Error

`List<BaseVo> getTopOrDownNodes(Long id, List<PointNode> nodes, List<LinkNode> links, Boolean isTop);`

**Issue 8 - Line 85:** Compilation Error

`List<DataLogData> getLoadRate(Long id, List<PointNode> nodes, List<LinkNode> links, QuantityDataBatchSearchVo aggregationDataBatch, Double ratedCapacity);`

**Issue 9 - Line 93:** Compilation Error

`Map<Long,List<DataLogData>> queryLoadRateBatch(List<PowerTransformerDto> powerTransformerDtos,  QuantityDataBatchSearchVo aggregationDataBatch`

**Issue 10 - Line 109:** Compilation Error

`Double calculateLoadRealTime(Long id,List<PointNode> nodes,List<LinkNode> links);`

**Issue 11 - Line 118:** Compilation Error

`Map<Long,Double> calculateLoadRealTimeBatch(List<Long> ids,List<PointNode> nodes,List<LinkNode> links);`

### TransformerAnalysisServiceImpl.java (67 issues)

**Issue 1 - Line 3:** Compilation Error

`import com.cet.eem.bll.common.dao.node.NodeDao;`

**Issue 2 - Line 3:**

`import com.cet.eem.bll.common.dao.node.NodeDao;`

**Issue 3 - Line 4:** Compilation Error

`import com.cet.eem.bll.common.dao.pipenetwork.PipeNetworkConnectionModelDao;`

**Issue 4 - Line 4:**

`import com.cet.eem.bll.common.dao.pipenetwork.PipeNetworkConnectionModelDao;`

**Issue 5 - Line 5:**

`import com.cet.eem.bll.common.def.quantity.FrequencyDef;`

**Issue 6 - Line 5:** Compilation Error

`import com.cet.eem.bll.common.def.quantity.FrequencyDef;`

**Issue 7 - Line 6:** Compilation Error

`import com.cet.eem.bll.common.def.quantity.PhasorDef;`

**Issue 8 - Line 6:**

`import com.cet.eem.bll.common.def.quantity.PhasorDef;`

**Issue 9 - Line 7:** Compilation Error

`import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;`

**Issue 10 - Line 7:**

`import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;`

**Issue 11 - Line 8:**

`import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;`

**Issue 12 - Line 8:** Compilation Error

`import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;`

**Issue 13 - Line 9:**

`import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;`

**Issue 14 - Line 9:** Compilation Error

`import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;`

**Issue 15 - Line 10:** Compilation Error

`import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityAggregationData;`

**Issue 16 - Line 10:**

`import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityAggregationData;`

**Issue 17 - Line 11:** Compilation Error

`import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityObject;`

**Issue 18 - Line 11:**

`import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityObject;`

**Issue 19 - Line 12:**

`import com.cet.eem.bll.common.model.domain.object.powersystem.PowerTransformerVo;`

**Issue 20 - Line 12:** Compilation Error

`import com.cet.eem.bll.common.model.domain.object.powersystem.PowerTransformerVo;`

**Issue 21 - Line 13:**

`import com.cet.eem.bll.common.model.topology.vo.LinkNode;`

**Issue 22 - Line 13:** Compilation Error

`import com.cet.eem.bll.common.model.topology.vo.LinkNode;`

**Issue 23 - Line 14:** Compilation Error

`import com.cet.eem.bll.common.model.topology.vo.PointNode;`

**Issue 24 - Line 14:**

`import com.cet.eem.bll.common.model.topology.vo.PointNode;`

**Issue 25 - Line 15:** Compilation Error

`import com.cet.eem.common.CommonUtils;`

**Issue 26 - Line 16:** Compilation Error

`import com.cet.eem.common.TransformerConstantDef.AggregationType;`

**Issue 27 - Line 17:** Compilation Error

`import com.cet.eem.common.TransformerConstantDef.EnergyTypeDef;`

**Issue 28 - Line 18:** Compilation Error

`import com.cet.eem.common.TransformerConstantDef.EnumDataTypeId;`

**Issue 29 - Line 19:** Compilation Error

`import com.cet.eem.common.TransformerConstantDef.EnumOperationType;`

**Issue 30 - Line 20:** Compilation Error

`import com.cet.eem.common.definition.ColumnDef;`

**Issue 31 - Line 21:** Compilation Error

`import com.cet.eem.common.definition.NodeLabelDef;`

**Issue 32 - Line 22:** Compilation Error

`import com.cet.eem.common.model.BaseVo;`

**Issue 33 - Line 23:** Compilation Error

`import com.cet.eem.common.model.datalog.DataLogData;`

**Issue 34 - Line 24:** Compilation Error

`import com.cet.eem.common.model.datalog.TrendDataVo;`

**Issue 35 - Line 25:** Compilation Error

`import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;`

**Issue 36 - Line 26:** Compilation Error

`import com.cet.eem.common.model.realtime.RealTimeValue;`

**Issue 37 - Line 27:** Compilation Error

`import com.cet.eem.common.parse.JsonTransferUtils;`

**Issue 38 - Line 28:** Compilation Error

`import com.cet.eem.common.utils.TimeUtil;`

**Issue 39 - Line 29:** Compilation Error

`import com.cet.eem.model.base.ConditionBlock;`

**Issue 40 - Line 30:** Compilation Error

`import com.cet.eem.model.base.QueryCondition;`

**Issue 41 - Line 31:** Compilation Error

`import com.cet.eem.model.model.BaseEntity;`

**Issue 42 - Line 32:** Compilation Error

`import com.cet.eem.model.tool.ModelServiceUtils;`

**Issue 43 - Line 33:** Compilation Error

`import com.cet.eem.model.tool.QueryConditionBuilder;`

**Issue 44 - Line 34:** Compilation Error

`import com.cet.eem.node.service.Topology1Service;`

**Issue 45 - Line 35:**

`import com.cet.eem.quantity.dao.QuantityAggregationDataDao;`

**Issue 46 - Line 35:** Compilation Error

`import com.cet.eem.quantity.dao.QuantityAggregationDataDao;`

**Issue 47 - Line 36:**

`import com.cet.eem.quantity.dao.QuantityObjectDao;`

**Issue 48 - Line 36:** Compilation Error

`import com.cet.eem.quantity.dao.QuantityObjectDao;`

**Issue 49 - Line 37:**

`import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;`

**Issue 50 - Line 37:** Compilation Error

`import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;`

**Issue 51 - Line 38:** Compilation Error

`import com.cet.eem.quantity.model.quantity.QuantitySearchVo;`

**Issue 52 - Line 38:**

`import com.cet.eem.quantity.model.quantity.QuantitySearchVo;`

**Issue 53 - Line 39:** Compilation Error

`import com.cet.eem.quantity.service.QuantityManageService;`

**Issue 54 - Line 39:**

`import com.cet.eem.quantity.service.QuantityManageService;`

**Issue 55 - Line 40:** Compilation Error

`import com.cet.eem.task.model.HistoricalLoadVo;`

**Issue 56 - Line 67:** Compilation Error

`ModelServiceUtils modelServiceUtils;`

**Issue 57 - Line 69:** Compilation Error

`QuantityManageService quantityManageService;`

**Issue 58 - Line 71:** Compilation Error

`Topology1Service topology1Service;`

**Issue 59 - Line 73:** Compilation Error

`QuantityObjectDao quantityObjectDao;`

**Issue 60 - Line 75:** Compilation Error

`QuantityAggregationDataDao quantityAggregationDataDao;`

**Issue 61 - Line 77:** Compilation Error

`PipeNetworkConnectionModelDao pipeNetworkConnectionModelDao;`

**Issue 62 - Line 79:** Compilation Error

`NodeDao nodeDao;`

**Issue 63 - Line 146:** Compilation Error

`public List<BaseVo> getTopOrDownNodes(Long id, List<PointNode> nodes, List<LinkNode> links, Boolean isTop) {`

**Issue 64 - Line 152:** Compilation Error

`public List<DataLogData> getLoadRate(Long id, List<PointNode> nodes, List<LinkNode> links, QuantityDataBatchSearchVo aggregationDataBatch, Double ratedCapacity) {`

**Issue 65 - Line 158:** Compilation Error

`public Map<Long, List<DataLogData>> queryLoadRateBatch(List<PowerTransformerDto> powerTransformerDtos, QuantityDataBatchSearchVo aggregationDataBatch, Long projectId) {`

**Issue 66 - Line 194:** Compilation Error

`public Double calculateLoadRealTime(Long id, List<PointNode> nodes, List<LinkNode> links) {`

**Issue 67 - Line 200:** Compilation Error

`public Map<Long, Double> calculateLoadRealTimeBatch(List<Long> ids, List<PointNode> nodes, List<LinkNode> links) {`

### TransformerindexData.java (2 issues)

**Issue 1 - Line 3:** Compilation Error

`import com.cet.eem.model.model.BaseEntity;`

**Issue 2 - Line 12:** Compilation Error

`public class TransformerindexData extends BaseEntity {`

### TransformerindexDataDao.java (3 issues)

**Issue 1 - Line 3:**

`import com.cet.eem.bll.common.model.domain.perception.logicaldevice.EemPoiRecord;`

**Issue 2 - Line 3:** Compilation Error

`import com.cet.eem.bll.common.model.domain.perception.logicaldevice.EemPoiRecord;`

**Issue 3 - Line 18:** Compilation Error

`List<EemPoiRecord> saveEemPoiRecord(List<EemPoiRecord> pois);`

### TransformerindexDataDaoImpl.java (1 issues)

**Issue 1 - Line 3:**

`import com.cet.eem.bll.common.model.domain.perception.logicaldevice.EemPoiRecord;`

### TransformerOverviewController.java (2 issues)

**Issue 1 - Line 3:**

`import com.cet.eem.bll.common.util.GlobalInfoUtils;`

**Issue 2 - Line 3:** Compilation Error

`import com.cet.eem.bll.common.util.GlobalInfoUtils;`

### TransformerOverviewServiceImpl.java (35 issues)

**Issue 1 - Line 3:** Compilation Error

`import com.cet.eem.bll.common.def.quantity.FrequencyDef;`

**Issue 2 - Line 3:**

`import com.cet.eem.bll.common.def.quantity.FrequencyDef;`

**Issue 3 - Line 4:**

`import com.cet.eem.bll.common.def.quantity.PhasorDef;`

**Issue 4 - Line 4:** Compilation Error

`import com.cet.eem.bll.common.def.quantity.PhasorDef;`

**Issue 5 - Line 5:** Compilation Error

`import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;`

**Issue 6 - Line 5:**

`import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;`

**Issue 7 - Line 6:**

`import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;`

**Issue 8 - Line 6:** Compilation Error

`import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;`

**Issue 9 - Line 7:**

`import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;`

**Issue 10 - Line 7:** Compilation Error

`import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;`

**Issue 11 - Line 8:**

`import com.cet.eem.bll.common.model.domain.object.event.SystemEvent;`

**Issue 12 - Line 8:** Compilation Error

`import com.cet.eem.bll.common.model.domain.object.event.SystemEvent;`

**Issue 13 - Line 9:**

`import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityAggregationData;`

**Issue 14 - Line 9:** Compilation Error

`import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityAggregationData;`

**Issue 15 - Line 10:**

`import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityObject;`

**Issue 16 - Line 11:**

`import com.cet.eem.bll.common.model.domain.perception.logicaldevice.PecEventExtendVo;`

**Issue 17 - Line 12:**

`import com.cet.eem.bll.common.model.ext.objective.event.SystemEventWithText;`

**Issue 18 - Line 13:**

`import com.cet.eem.bll.common.model.topology.vo.ConnectionSearchVo;`

**Issue 19 - Line 14:**

`import com.cet.eem.bll.common.model.topology.vo.LinkNode;`

**Issue 20 - Line 15:**

`import com.cet.eem.bll.common.model.topology.vo.PointNode;`

**Issue 21 - Line 16:**

`import com.cet.eem.bll.common.util.GlobalInfoUtils;`

**Issue 22 - Line 17:**

`import com.cet.eem.bll.energy.dao.SystemEventDao;`

**Issue 23 - Line 18:**

`import com.cet.eem.bll.energy.model.event.SystemEventCountVo;`

**Issue 24 - Line 19:**

`import com.cet.eem.bll.energy.service.event.AlarmEventService;`

**Issue 25 - Line 32:**

`import com.cet.eem.event.model.analysis.ConfirmCountResult;`

**Issue 26 - Line 33:**

`import com.cet.eem.event.model.expert.EventCountSearchVo;`

**Issue 27 - Line 34:**

`import com.cet.eem.event.model.pecevent.PecEventCountVo;`

**Issue 28 - Line 35:**

`import com.cet.eem.event.service.PecEventService;`

**Issue 29 - Line 36:**

`import com.cet.eem.event.service.expert.ExpertAnalysisBffService;`

**Issue 30 - Line 37:**

`import com.cet.eem.event.service.expert.PecCoreEventBffService;`

**Issue 31 - Line 44:**

`import com.cet.eem.quantity.dao.QuantityAggregationDataDao;`

**Issue 32 - Line 45:**

`import com.cet.eem.quantity.dao.QuantityObjectDao;`

**Issue 33 - Line 46:**

`import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;`

**Issue 34 - Line 47:**

`import com.cet.eem.quantity.model.quantity.QuantitySearchVo;`

**Issue 35 - Line 48:**

`import com.cet.eem.quantity.service.QuantityManageService;`

### TransformerTask.java (2 issues)

**Issue 1 - Line 3:** Compilation Error

`import com.cet.eem.task.model.TransformerTaskService;`

**Issue 2 - Line 16:** Compilation Error

`TransformerTaskService transformerTaskService;`

## SUMMARY

- **Compilation Error:** 83 issues
- **:** 52 issues

---
**Generated:** 2025-08-05 16:27:10

package com.cet.eem.bll.energysaving.task;

import com.cet.eem.bll.common.task.TaskSchedule;
import com.cet.eem.bll.energysaving.service.task.ColdPredictDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * @ClassName : TotalPowerPredictTask
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-08-12 09:38
 */
@Component
@Slf4j
public class TotalPowerPredictTask implements TaskSchedule {
    @Autowired
    ColdPredictDataService coldPredictDataService;

    @Scheduled(cron = "${cet.eem.task.energy-saving.systemPower.interval}")
    @Override
    public void execute() throws IOException, InstantiationException, IllegalAccessException {
        coldPredictDataService.saveTotalSystemPowerPredictData();
    }
}
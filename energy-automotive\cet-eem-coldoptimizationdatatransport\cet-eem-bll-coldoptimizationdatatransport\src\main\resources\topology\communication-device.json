[{"type": 1, "relateDevice": true, "name": "交换机", "modelLabel": "interchanger", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 2}], "fieldName": "所属通信机房"}, "fields": [{"field": "asset", "required": false, "fieldName": "资产编码", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "bandwidth", "required": false, "fieldName": "背板带宽", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "brand", "required": false, "fieldName": "品牌", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "code", "required": false, "fieldName": "编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "document", "required": false, "fieldName": "文档", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "interchangertype", "required": false, "fieldName": "产品类型", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "lastpretestdate", "required": false, "fieldName": "上次预检日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "latitude", "required": false, "fieldName": "纬度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "longitude", "required": false, "fieldName": "经度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "maintenanceperiod", "required": false, "fieldName": "保养周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "manufacturedate", "required": false, "fieldName": "出厂日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "name", "required": true, "fieldName": "设备名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "netport", "required": false, "fieldName": "网口数量", "dataType": "int4", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "operationstatus", "required": false, "fieldName": "设备运行状态", "dataType": "deviceoperationstatus", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "opticalport", "required": false, "fieldName": "光口数量", "dataType": "int4", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "pic", "required": false, "fieldName": "图片", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "ratedcapacity", "required": false, "fieldName": "额定功率", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "ratedcurrent", "required": false, "fieldName": "额定电流", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "ratedvoltage", "required": false, "fieldName": "额定电压", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "transmissionrate", "required": false, "fieldName": "传输速率", "dataType": "transmissionrate", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "vendor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "voltagelevel", "required": false, "fieldName": "电压等级", "dataType": "voltagelevel", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "warrantydate", "required": false, "fieldName": "质保期限", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": 0}], "tips": null}, {"type": 1, "relateDevice": true, "name": "光电转换器", "modelLabel": "photoeleconverter", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 2}], "fieldName": "所属通信机房"}, "fields": [{"field": "asset", "required": false, "fieldName": "资产编码", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "brand", "required": false, "fieldName": "品牌", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "code", "required": false, "fieldName": "编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "commissiondate", "required": false, "fieldName": "投运日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "document", "required": false, "fieldName": "文档", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "elecport", "required": false, "fieldName": "电口数量", "dataType": "int4", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "lastpretestdate", "required": false, "fieldName": "上次预检日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "latitude", "required": false, "fieldName": "纬度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "longitude", "required": false, "fieldName": "经度", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "maintenanceperiod", "required": false, "fieldName": "保养周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "manufacturedate", "required": false, "fieldName": "出厂日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "name", "required": true, "fieldName": "设备名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "operationstatus", "required": false, "fieldName": "设备运行状态", "dataType": "deviceoperationstatus", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "opticalport", "required": false, "fieldName": "光口数量", "dataType": "int4", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "pic", "required": false, "fieldName": "图片", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "ratedcapacity", "required": false, "fieldName": "额定功率", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "ratedcurrent", "required": false, "fieldName": "额定电流", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "ratedvoltage", "required": false, "fieldName": "额定电压", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "vendor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "voltagelevel", "required": false, "fieldName": "电压等级", "dataType": "voltagelevel", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "warrantydate", "required": false, "fieldName": "质保期限", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": 0}], "tips": null}, {"type": 1, "relateDevice": true, "name": "计算机", "modelLabel": "computer", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 2}], "fieldName": "所属通信机房"}, "fields": [{"field": "computername", "required": false, "fieldName": "计算机名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "cpuload", "required": false, "fieldName": "cpu负载", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "description", "required": false, "fieldName": "描述", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "diskload", "required": false, "fieldName": "磁盘负载", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "diskremainingcapacity", "required": false, "fieldName": "磁盘剩余空间", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "ip", "required": false, "fieldName": "ip", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "ip2", "required": false, "fieldName": "ip2", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "isbackgroudserver", "required": false, "fieldName": "是否后台服务器", "dataType": "bool", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "isconfigserver", "required": false, "fieldName": "是否是配置服务", "dataType": "bool", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "isfrontserver", "required": false, "fieldName": "是否前台服务器", "dataType": "bool", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "istimeserver", "required": false, "fieldName": "是否时间服务器", "dataType": "bool", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "memoryload", "required": false, "fieldName": "内存负载", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "name", "required": true, "fieldName": "名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "standbymode", "required": false, "fieldName": "主备模式", "dataType": "standbymode", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "standbyserverid", "required": false, "fieldName": "备用服务器的id", "dataType": "long", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "timezone", "required": false, "fieldName": "时区", "dataType": "timezone", "dateFormat": null, "sourceLabel": null, "sourceType": 0}], "tips": null}, {"type": 1, "relateDevice": true, "name": "表计", "modelLabel": "meter", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 2}], "fieldName": "所属通信机房"}, "fields": [{"field": "boudrate", "required": false, "fieldName": "波特率", "dataType": "int4", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "circuitid", "required": false, "fieldName": "回路ID", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "circuitname", "required": false, "fieldName": "回路名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "com", "required": false, "fieldName": "串口号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "commid", "required": false, "fieldName": "通信id", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "communicationmode", "required": false, "fieldName": "通信方式", "dataType": "communicationmode", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "communicationstatus", "required": false, "fieldName": "通信状态", "dataType": "communicationstatus", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "databits", "required": false, "fieldName": "数据位", "dataType": "int4", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "description", "required": false, "fieldName": "描述", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "deviceid", "required": false, "fieldName": "表计ID", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "energytype", "required": false, "fieldName": "能源类型", "dataType": "energytype", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "measuretype", "required": false, "fieldName": "测量类型", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "metermodel", "required": false, "fieldName": "表计型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "metertype", "required": false, "fieldName": "表计类型", "dataType": "metertype", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "name", "required": true, "fieldName": "名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "networkaddress", "required": false, "fieldName": "网络地址", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "paritycheck", "required": false, "fieldName": "奇偶校验", "dataType": "paritycheck", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "protocalname", "required": false, "fieldName": "规约类型名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "protocaltype", "required": false, "fieldName": "通信规约", "dataType": "protocaltype", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "protocalversion", "required": false, "fieldName": "规约版本", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "ratio", "required": false, "fieldName": "变比", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "rollover", "required": false, "fieldName": "翻转量", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "stopbit", "required": false, "fieldName": "停止位", "dataType": "int4", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "successrate", "required": false, "fieldName": "通信成功率", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": 0}], "tips": null}, {"type": 1, "relateDevice": true, "name": "通信管理机", "modelLabel": "gateway", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 2}], "fieldName": "所属通信机房"}, "fields": [{"field": "communicationstatus", "required": false, "fieldName": "通信状态", "dataType": "communicationstatus", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "description", "required": false, "fieldName": "描述", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "gatewaymodel", "required": false, "fieldName": "通信管理机型号", "dataType": "gatewaymodel", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "name", "required": true, "fieldName": "名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "networkaddress1", "required": false, "fieldName": "地址1", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "networkaddress2", "required": false, "fieldName": "地址2", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "networkaddress3", "required": false, "fieldName": "地址3", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "networkaddress4", "required": false, "fieldName": "地址4", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "standbygatewayid", "required": false, "fieldName": "备用管理机的id", "dataType": "long", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "standbymode", "required": false, "fieldName": "主备模式", "dataType": "standbymode", "dateFormat": null, "sourceLabel": null, "sourceType": 0}, {"field": "successrate", "required": false, "fieldName": "通信成功率", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": 0}], "tips": null}]
package com.cet.eem.coldoptimizatinservice.controller;

import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.bll.energysaving.model.cetml.ColdLoadQueryParam;
import com.cet.eem.bll.energysaving.model.cetml.ColdMachineControlParam;
import com.cet.eem.bll.energysaving.model.dataentryquery.*;
import com.cet.eem.bll.energysaving.service.predict.CetMlPredictService;
import com.cet.eem.bll.energysaving.service.trend.DataEntryAndQueryService;
import com.cet.eem.common.model.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName : DataEntryAndQueryController
 * @Description : 数据对接的接口
 * <AUTHOR> jiangzixuan
 * @Date: 2022-04-08 16:45
 */
@Api(value = "DataEntryAndQueryController", tags = {"数据对接的接口"})
@RestController
@RequestMapping(value = "/eem/v1/dataEntryAndQuery")
public class DataEntryAndQueryController {
    @Autowired
    DataEntryAndQueryService entryAndQueryService;
    @Autowired
    CetMlPredictService cetMlPredictService;

    @ApiOperation(value = "末端冷量需求预测查询")
    @PostMapping(value = "/endCold", produces = "application/json")
    public Result<List<EndColdDataEntry>> queryEndColdData(
            @RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) DataQueryParam queryParam) throws InstantiationException, IllegalAccessException {
        return Result.ok(entryAndQueryService.queryEndColdData(queryParam, GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "管道损失冷量查询")
    @PostMapping(value = "/pipelineLossCold", produces = "application/json")
    public Result<List<PipelineLossColdDataEntry>> queryPipelineLossColdData(
            @RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) DataQueryParam queryParam) {
        return Result.ok(entryAndQueryService.queryPipelineLossColdData(queryParam, GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "系统总功率需求预测查询")
    @PostMapping(value = "/totalSystemPower", produces = "application/json")
    public Result<List<TotalSystemPowerEntry>> queryTotalSystemPower(
            @RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) DataQueryParam queryParam) throws InstantiationException, IllegalAccessException {
        return Result.ok(entryAndQueryService.queryTotalSystemPower(queryParam, GlobalInfoUtils.getProjectId()));
    }


    @ApiOperation(value = "板式换热器制冷量求解查询")
    @PostMapping(value = "/refrigerateOfExchanger", produces = "application/json")
    public Result<List<RefrigerateOfExchangerEntry>> queryRefrigerateOfExchanger(
            @RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) DataQueryParam queryParam) {

        RefrigerateOfExchangerEntry vo = new RefrigerateOfExchangerEntry();
        return Result.ok(Collections.singletonList(vo));
    }


    @ApiOperation(value = "冷机出水温度优化模型查询")
    @PostMapping(value = "/optimizationOfRefrigeratorWater", produces = "application/json")
    public Result<List<OptimizationOfRefrigeratorWaterEntry>> queryOptimizationOfRefrigeratorWater(
            @RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) DataQueryParam queryParam) throws InstantiationException, IllegalAccessException {
        return Result.ok(entryAndQueryService.queryOptimizationOfRefrigeratorWater(queryParam, GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "下载天气对比")
    @PostMapping(value = "/exportWeatherDataCompare", produces = "application/json")
    public Result<Object> exportWeatherDataCompare(@RequestBody DataQueryParam param, HttpServletResponse response) throws Exception {
        entryAndQueryService.exportWeatherDataCompare(param, response);
        return Result.ok();
    }
    @ApiOperation(value = "冷机控制查询--新算法")
    @PostMapping(value = "/coldControlParam", produces = "application/json")
    public Result<List<ColdMachineControlParam> > getColdMainControlQueryParamsBatch(
            @RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) DataQueryParam queryParam) throws Exception {
        return Result.ok(cetMlPredictService.getColdMainControlQueryParamsBatch(queryParam.getStartTime(),queryParam.getEndTime(),queryParam.getSystemIds().get(0),queryParam.getProjectId()));
    }

    @ApiOperation(value = "冷负荷查询--新算法")
    @PostMapping(value = "/coldPredictParam", produces = "application/json")
    public Result<List<ColdLoadQueryParam>> getColdPredictQueryParamBatch(
            @RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) DataQueryParam queryParam) throws Exception {
        return Result.ok(cetMlPredictService.getColdPredictQueryParamBatch(queryParam.getStartTime(),queryParam.getEndTime(),queryParam.getSystemIds().get(0),queryParam.getProjectId()));
    }
}
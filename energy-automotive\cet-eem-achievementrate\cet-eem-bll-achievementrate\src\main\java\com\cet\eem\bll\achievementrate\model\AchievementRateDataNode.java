package com.cet.eem.bll.achievementrate.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * @ClassName : AchievementRateDataNode
 * @Description : 达成率录入节点信息
 * <AUTHOR> jiangzixuan
 * @Date: 2023-11-20 14:44
 */
@Getter
@Setter
public class AchievementRateDataNode {
    @ApiModelProperty(value = "数据ID")
    protected Long id;

    /**
     * 录入时间
     */
    @ApiModelProperty(value = "录入时间")
    @NotNull(message = "数据录入时间不能为空")
    protected LocalDateTime logTime;

    /**
     * 录入的值
     */
    @ApiModelProperty(value = "录入的值")
    @NotNull(message = "数据录入的值不能为空")
    protected Double value;

    /**
     * 能源类型
     */
    @ApiModelProperty(value = "能源类型")
    protected Integer energyType;
    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    protected Integer productType;
    /**
     * 指标id
     */
    @ApiModelProperty(value = "指标id")
    protected Long effSetId;

    /**
     * 节点id
     */
    @ApiModelProperty(value = "节点id")
    protected Long objectId;

    /**
     * 节点label
     */
    @ApiModelProperty(value = "节点Label")
    protected String objectLabel;
}
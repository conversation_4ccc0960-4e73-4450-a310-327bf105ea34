package com.cet.eem.bll.energysaving.model.cetml;

import com.cet.eem.common.definition.ColumnDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName : PumpFitParam
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-06-29 17:06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PumpFitParam {
    @JsonProperty(ColumnDef.PROJECT_ID_)
    private Long projectId;
    private List<PumpFitObjectData> data;
}
package com.cet.eem.bll.energysaving.model.aioptimization.plc;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.bll.energysaving.model.def.ColdOptimizationLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : ControlMode
 * @Description : 控制模式
 * <AUTHOR> jiangzixuan
 * @Date: 2022-09-01 15:29
 */
@Getter
@Setter
@ModelLabel(ColdOptimizationLabelDef.CONTROL_MODE)
public class ControlMode extends BaseEntity {
    @JsonProperty(ColumnDef.PROJECT_ID)
    private Long projectId;
    @JsonProperty(ColdOptimizationLabelDef.SYSTEM_ID)
    private Long systemId;
    private Integer mode;
    @JsonProperty(ColumnDef.UPDATE_TIME)
    private Long updateTime;
    private Integer plcMode;

    public ControlMode() {
        this.modelLabel = ColdOptimizationLabelDef.CONTROL_MODE;
    }
}
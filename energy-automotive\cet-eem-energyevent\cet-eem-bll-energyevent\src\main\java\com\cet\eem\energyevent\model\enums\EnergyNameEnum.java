package com.cet.eem.energyevent.model.enums;

import java.util.Arrays;
import java.util.List;

public enum EnergyNameEnum {
    duration("时长", "h"),
    consumption("能耗", "kWh"),
    flux("能耗", ""),
    ;
    private final String energyName;
    private final String unit;
    /**
     * 时长
     */
    public static List<Long> durationDataIds = Arrays.asList(6008038L, 6008036L);

    EnergyNameEnum(String energyName, String unit) {
        this.energyName = energyName;
        this.unit = unit;
    }

    public String getEnergyName() {
        return energyName;
    }

    public String getUnit() {
        return unit;
    }
}

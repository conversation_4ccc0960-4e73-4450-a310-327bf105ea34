package com.cet.eem.bll.energysaving.handle;


import com.cet.eem.bll.common.dao.node.EnergySupplyDao;
import com.cet.eem.bll.common.dao.node.NodeDao;
import com.cet.eem.bll.common.dao.pipenetwork.PipeNetworkConnectionModelDao;
import com.cet.eem.bll.common.dao.poi.EemPoiRecordDao;
import com.cet.eem.bll.common.def.quantity.FrequencyDef;
import com.cet.eem.bll.common.def.quantity.PhasorDef;
import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;
import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;
import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;
import com.cet.eem.bll.common.model.domain.perception.logicaldevice.EemPoiRecord;
import com.cet.eem.bll.common.model.domain.subject.energysaving.ColdActual;
import com.cet.eem.bll.common.model.node.relations.EnergySupplyToPo;
import com.cet.eem.bll.common.util.LockService;
import com.cet.eem.bll.energysaving.config.colddata.LossCapacityConfig;
import com.cet.eem.bll.energysaving.dao.colddata.ColdActualDao;
import com.cet.eem.bll.energysaving.dao.weather.RefrigeratingSystemDao;
import com.cet.eem.bll.energysaving.model.config.system.RefrigeratingSystem;
import com.cet.eem.bll.energysaving.model.weather.ColdLoadType;
import com.cet.eem.bll.energysaving.model.weather.PredictDataType;
import com.cet.eem.bll.energysaving.service.trend.ModelConfigurationService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.constant.EnergyTypeDef;
import com.cet.eem.common.constant.EnumOperationType;
import com.cet.eem.common.constant.PoiTypeEnum;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.exception.ValidationException;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.DataLogDataWithGroup;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryResultContentTaker;
import com.cet.eem.node.service.Topology1Service;
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;
import com.cet.eem.quantity.service.HistoryDataService;
import com.cet.eem.quantity.service.QuantityManageService;
import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 损耗冷量处理
 *
 * <AUTHOR>
 * @date 2021/12/22
 */
@Service
@Slf4j
public class LossActualColdHandle {
    @Autowired
    NodeDao nodeDao;

    @Autowired
    QuantityManageService quantityManageService;

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    Topology1Service topology1Service;

    @Autowired
    ModelConfigurationService modelConfigurationService;

    @Autowired
    EnergySupplyDao energySupplyDao;

    @Autowired
    LockService lockService;

    @Autowired
    EemPoiRecordDao eemPoiRecordDao;

    @Autowired
    LossCapacityConfig lossCapacityConfig;

    @Autowired
    ColdActualDao coldActualDao;

    @Autowired
    HistoryDataService historyDataService;
    @Autowired
    PipeNetworkConnectionModelDao connectionModelDao;
    @Autowired
    RefrigeratingSystemDao refrigeratingSystemDao;
    public static final Double UNIT = 3.6D;
    public static final Double HEAT = 4.2D;
    private List<String> COLD_ENE_NODE_LABELS = Arrays.asList(NodeLabelDef.BUILDING, NodeLabelDef.FLOOR, NodeLabelDef.ROOM);

    public void calcLossData() throws InstantiationException, IllegalAccessException {
        log.info("开始转存管损实际数据");
        int quantityBeginIndex = 1;
        List<BaseVo> projects = modelServiceUtils.query((Long) null, NodeLabelDef.PROJECT, BaseVo.class);
        long now = System.currentTimeMillis();

        for (BaseVo project : projects) {
            // 分系统查询末端管道以及节点

            List<RefrigeratingSystem> refrigeratingSystems = refrigeratingSystemDao.selectAll();
            if (CollectionUtils.isEmpty(refrigeratingSystems)) {
                continue;
            }
            //过滤
            refrigeratingSystems = refrigeratingSystems.stream().filter(it -> Objects.equals(it.getProjectId(), project.getId()) && Boolean.TRUE.equals(it.getUseAi()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(refrigeratingSystems)) {
                continue;
            }
            for (RefrigeratingSystem refrigeratingSystem : refrigeratingSystems) {
                // 查询当前房间下所有制冷相关的设备
                List<Map<String, Object>> roomWithChildren = modelServiceUtils.queryWithChildren(NodeLabelDef.ROOM, Collections.singletonList(refrigeratingSystem.getRoomId()), NodeLabelDef.REFRIGRATION_DEVICE);
                List<BaseVo> coldDevices = resolveChildren(roomWithChildren);
                if (CollectionUtils.isEmpty(coldDevices)) {
                    continue;
                }

                List<BaseVo> mains = coldDevices.stream().filter(baseVo -> Objects.equals(baseVo.getModelLabel(), NodeLabelDef.COLD_WATER_MAINENGINE))
                        .collect(Collectors.toList());
                List<PipeNetworkConnectionModel> connectionModels = connectionModelDao.getTopOrDownBatchWithNodes(mains, false, project.getId());
                // 查询制冷房间关联的管道
                List<BaseVo> pipeLineList = topology1Service.queryFlowNode(project.getId(), EnergyTypeDef.COLD, coldDevices, NodeLabelDef.PIPELINE);
                // 查询冷机下方供水总管
                List<BaseVo> pipeLineNodes = connectionModels.stream().filter(model -> Objects.equals(model.getOutflowLabel(), NodeLabelDef.PIPELINE))
                        .map(model -> new BaseVo(model.getOutflowId(), model.getOutflowLabel()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(pipeLineNodes) || CollectionUtils.isEmpty(pipeLineList)) {
                    continue;
                }
                //该系统冷机拓扑结构下的末端管道
                List<BaseVo> endPipeLines = getEndPipeLine(pipeLineList, pipeLineNodes);
                if (CollectionUtils.isEmpty(endPipeLines)) {
                    continue;
                }
                //查询拓扑末端管道关联的
                List<EnergySupplyToPo> energySupplyToPos = energySupplyDao.queryEnergySupplyByObjectNodes(endPipeLines, EnergySupplyToPo.class);
                if (CollectionUtils.isEmpty(energySupplyToPos)) {
                    continue;
                }

                // 查询末端管道供能到的对象
                Set<BaseVo> nodes = energySupplyToPos.stream()
                        .filter(it -> COLD_ENE_NODE_LABELS.contains(it.getSupplytolabel()))
                        .map(it -> new BaseVo(it.getSupplytoid(), it.getSupplytolabel()))
                        .collect(Collectors.toSet());

                // 根据末端节点查询所有的空调
                List<BaseVo> airConditioners = queryAirConditioners(nodes);
                if (CollectionUtils.isEmpty(airConditioners)) {
                    continue;
                }

                BaseVo roomNode = new BaseVo(refrigeratingSystem.getRoomId(), NodeLabelDef.ROOM);
                EemPoiRecord eemPoiRecord = eemPoiRecordDao.queryPoiRecord(roomNode, PoiTypeEnum.COLD_END_LOSS_POI.getId());
                if (Objects.isNull(eemPoiRecord)) {
                    eemPoiRecord = new EemPoiRecord();
                    eemPoiRecord.setPoiType(PoiTypeEnum.COLD_END_LOSS_POI.getId());
                    eemPoiRecord.setObjectId(refrigeratingSystem.getRoomId());
                    eemPoiRecord.setObjectLabel(NodeLabelDef.ROOM);
                    eemPoiRecord.setValue(TimeUtil.localDateTime2timestamp(lossCapacityConfig.parseOverallStartTime()));
                    eemPoiRecord.setEnergyType(EnergyTypeDef.COLD);
                    eemPoiRecord = modelServiceUtils.writeData(eemPoiRecord, EemPoiRecord.class).get(0);
                }

                Long st = eemPoiRecord.getValue();
                Long et = now;

                List<Long> times = TimeUtil.getTimeRange(st, et, AggregationCycle.ONE_DAY, lossCapacityConfig.getMaxQueryDay());
                for (Long time : times) {
                    // 查询POI
                    if (!BooleanUtils.isTrue(lockService.getLock(eemPoiRecord.getId(), ModelLabelDef.EEM_POI_RECORD))) {
                        throw new ValidationException("加锁失败！");
                    }

                    st = time;
                    et = TimeUtil.addDateTimeByCycle(st, AggregationCycle.ONE_DAY, lossCapacityConfig.getMaxQueryDay());
                    if (now < et) {
                        et = now;
                    }

                    // 读取末端空调温度数据
                    Map<Integer, List<TrendDataVo>> endAirDataLogResult = historyDataService.queryHistoryData(st, et,
                            airConditioners, getEndAirConditioner(quantityBeginIndex));
                    // 读取总管数据，
                    Map<Integer, List<TrendDataVo>> totalDataLogResult = historyDataService.queryHistoryData(st, et,
                            pipeLineNodes, geTotalPipeLineQuantity(quantityBeginIndex));

                    // 计算制冷数据
                    List<DatalogValue> result = calcEndColdCapacity(endAirDataLogResult, totalDataLogResult);
                    if (CollectionUtils.isNotEmpty(result)) {
                        // 入库
                        writeColdActualData(result, project.getId(), refrigeratingSystem.getRoomId());

                        OptionalLong max = result.stream().mapToLong(DatalogValue::getTime).max();
                        if (max.isPresent()) {
                            eemPoiRecord.setValue(max.getAsLong());
                            modelServiceUtils.writeData(Collections.singletonList(eemPoiRecord));
                        }
                    }

                    lockService.unlock(eemPoiRecord.getId(), ModelLabelDef.EEM_POI_RECORD);
                }
            }
        }
        log.info("结束转存管损实际数据");
    }

    /**
     * 这块是使用冷机后面的拓扑结构的节点，和冷机下端的直接节点，来获得供水总管下连着的支管，也考虑的总管支管是一根的情况
     *
     * @param pipeLineList
     * @param pipeLineNodes
     * @return
     */
    private List<BaseVo> getEndPipeLine(List<BaseVo> pipeLineList, List<BaseVo> pipeLineNodes) {
        List<BaseVo> result = new ArrayList<>();
        for (BaseVo baseVo : pipeLineList) {
            BaseVo baseVo2 = pipeLineNodes.stream().filter(baseVo1 -> Objects.equals(baseVo.getId(), baseVo1.getId())
                    && Objects.equals(baseVo.getModelLabel(), baseVo1.getModelLabel()))
                    .findAny().orElse(null);
            if (Objects.nonNull(baseVo2)) {
                if (CollectionUtils.isNotEmpty(baseVo.getChildren())) {
                    result.addAll(baseVo.getChildren());
                } else {
                    result.add(baseVo);
                }
            }
        }
        return result;
    }

    private void writeColdActualData(List<DatalogValue> result, Long id, Long roomId) {
        List<ColdActual> actuals = new ArrayList<>();
        for (DatalogValue DatalogValue : result) {
            ColdActual actual = new ColdActual();
            actual.setProjectId(id);
            actual.setRoomId(roomId);
            actual.setLogTime(TimeUtil.timestamp2LocalDateTime(DatalogValue.getTime()));
            actual.setValue(DatalogValue.getValue());
            actual.setColdLoadType(ColdLoadType.TOTAL);
            actual.setPredictDataType(PredictDataType.LOSS);
            actuals.add(actual);
        }
        coldActualDao.writeColdActualDataList(actuals);
    }

    /**
     * 计算末端冷量
     *
     * @param endAirDataLogResult
     * @param totalDataLogResult
     */
    private List<DatalogValue> calcEndColdCapacity(Map<Integer, List<TrendDataVo>> endAirDataLogResult, Map<Integer, List<TrendDataVo>> totalDataLogResult
    ) {
        // 末端温度
        List<TrendDataVo> trendDataVos = endAirDataLogResult.get(1);
        // 末端风机频率
        List<TrendDataVo> trendDataVos4 = endAirDataLogResult.get(2);
        // 供冷总管温度
        List<TrendDataVo> trendDataVos1 = totalDataLogResult.get(2);
        // 供冷总管流量
        List<TrendDataVo> trendDataVos2 = totalDataLogResult.get(1);

        if (CollectionUtils.isEmpty(trendDataVos) || CollectionUtils.isEmpty(trendDataVos1) || CollectionUtils.isEmpty(trendDataVos2) || CollectionUtils.isEmpty(trendDataVos4)) {
            return Collections.emptyList();
        }

        // 获取总管有值的，取一个
        // 获取末端平均温度
        List<DatalogValue> endTempDataLogList = getAveDatalogValue(trendDataVos, trendDataVos4);
        List<DatalogValue> totalTempDataLogList = getMaxDatalogValue(trendDataVos1);
        List<DatalogValue> totalFowDatalogValue = getSumDatalogValue(trendDataVos2);


        // 对数据进行编组，然后根据时间分组进行数据处理
        int group = 1;
        List<DataLogDataWithGroup> dataLogs = new ArrayList<>();
        addDatalogValue(dataLogs, endTempDataLogList, group++);
        addDatalogValue(dataLogs, totalTempDataLogList, group++);
        addDatalogValue(dataLogs, totalFowDatalogValue, group);
        return calcEndColdCapacity(dataLogs, group);
    }

    private List<DatalogValue> getAveDatalogValue(List<TrendDataVo> trendDataVos, List<TrendDataVo> trendDataVos4) {
        Map<BaseVo, List<TrendDataVo>> map = trendDataVos.stream()
                .collect(Collectors.groupingBy(trendDataVo -> new BaseVo(trendDataVo.getMonitoredid(), trendDataVo.getMonitoredlabel())));
        Map<BaseVo, List<TrendDataVo>> map1 = trendDataVos4.stream()
                .collect(Collectors.groupingBy(trendDataVo -> new BaseVo(trendDataVo.getMonitoredid(), trendDataVo.getMonitoredlabel())));
        List<DatalogValue> endTempDataLogList = new ArrayList<>();
        for (Map.Entry<BaseVo, List<TrendDataVo>> entry : map.entrySet()) {
            List<TrendDataVo> value = entry.getValue();
            List<TrendDataVo> trendDataVos1 = map1.get(entry.getKey());
            List<DatalogValue> runningTemp = getRunningTemp(value, trendDataVos1);
            if (CollectionUtils.isNotEmpty(runningTemp)) {
                endTempDataLogList.addAll(runningTemp);
            }

        }
        List<DatalogValue> result = new ArrayList<>();
        Map<Long, List<DatalogValue>> collect = endTempDataLogList.stream().collect(Collectors.groupingBy(DatalogValue::getTime));
        for (Map.Entry<Long, List<DatalogValue>> entry : collect.entrySet()) {
            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                OptionalDouble average = entry.getValue().stream().mapToDouble(DatalogValue::getValue).average();
                if (average.isPresent()) {
                    DatalogValue e = new DatalogValue();
                    e.setTime(entry.getKey());
                    e.setValue(average.getAsDouble());
                    result.add(e);
                }
            }
        }
        return result;
    }

    private List<DatalogValue> getRunningTemp(List<TrendDataVo> value, List<TrendDataVo> trendDataVos1) {
        List<DatalogValue> endTempDataLogList = new ArrayList<>();
        Map<Long, List<DatalogValue>> endDatalogValueMap = value.stream()
                .filter(it -> Objects.nonNull(it.getDataList()))
                .flatMap(it -> it.getDataList().stream())
                .filter(it -> Objects.nonNull(it.getValue()))
                .collect(Collectors.groupingBy(DatalogValue::getTime));
        if (CollectionUtils.isEmpty(trendDataVos1)) {
            return Collections.emptyList();
        }
        Map<Long, List<DatalogValue>> endDatalogValueMap1 = trendDataVos1.stream()
                .filter(it -> Objects.nonNull(it.getDataList()))
                .flatMap(it -> it.getDataList().stream())
                .filter(it -> Objects.nonNull(it.getValue()) && it.getValue() > 0.0)
                .collect(Collectors.groupingBy(DatalogValue::getTime));
        if (endDatalogValueMap1.isEmpty()) {
            return Collections.emptyList();
        }
        endDatalogValueMap.forEach((key, val) -> {
            List<DatalogValue> DatalogValue1 = endDatalogValueMap1.get(key);
            if (CollectionUtils.isNotEmpty(DatalogValue1)) {
                OptionalDouble average = val.stream().mapToDouble(DatalogValue::getValue).average();
                if (average.isPresent()) {
                    DatalogValue e = new DatalogValue();
                    e.setTime(key);
                    e.setValue(average.getAsDouble());
                    endTempDataLogList.add(e);
                }
            }


        });
        return endTempDataLogList;
    }

    private List<DatalogValue> getMaxDatalogValue(List<TrendDataVo> trendDataVos) {
        List<DatalogValue> endTempDataLogList = new ArrayList<>();
        Map<Long, List<DatalogValue>> endDatalogValueMap = trendDataVos.stream()
                .filter(it -> Objects.nonNull(it.getDataList()))
                .flatMap(it -> it.getDataList().stream())
                .filter(it -> Objects.nonNull(it.getValue()))
                .collect(Collectors.groupingBy(DatalogValue::getTime));

        endDatalogValueMap.forEach((key, val) -> {

            if (CollectionUtils.isNotEmpty(val)) {
                DatalogValue e = new DatalogValue();
                e.setTime(key);
                e.setValue(val.get(0).getValue());
                endTempDataLogList.add(e);
            }

        });
        return endTempDataLogList;
    }

    private List<DatalogValue> getSumDatalogValue(List<TrendDataVo> trendDataVos) {
        List<DatalogValue> endTempDataLogList = new ArrayList<>();
        Map<Long, List<DatalogValue>> endDatalogValueMap = trendDataVos.stream()
                .filter(it -> Objects.nonNull(it.getDataList()))
                .flatMap(it -> it.getDataList().stream())
                .filter(it -> Objects.nonNull(it.getValue()))
                .collect(Collectors.groupingBy(DatalogValue::getTime));

        endDatalogValueMap.forEach((key, val) -> {
            double sum = val.stream().mapToDouble(DatalogValue::getValue).sum();
            DatalogValue e = new DatalogValue();
            e.setTime(key);
            e.setValue(sum);
            endTempDataLogList.add(e);
        });
        return endTempDataLogList;
    }

    private List<DatalogValue> calcEndColdCapacity(List<DataLogDataWithGroup> dataLogs, int totalGroup) {
        List<DatalogValue> result = new ArrayList<>();
        Map<Long, List<DataLogDataWithGroup>> dataLogMap = dataLogs.stream().collect(Collectors.groupingBy(DatalogValue::getTime));
        dataLogMap.forEach((time, val) -> {
            Double tmp = calcSingleTimeEndColdCapacity(val, totalGroup);
            if (Objects.nonNull(tmp)) {
                DatalogValue e = new DatalogValue();
                e.setTime(time);
                e.setValue(tmp);
                result.add(e);
            }
        });

        return result;
    }

    private Double calcSingleTimeEndColdCapacity(List<DataLogDataWithGroup> val, int totalGroup) {
        if (val.size() != totalGroup) {
            return null;
        }

        // 判空
        long count = val.stream().filter(it -> Objects.isNull(it.getValue())).count();
        if (count > 0) {
            return null;
        }

        // 计算损耗量：冷冻水总管散热量=（空调表冷器进水温度（末端最高值）-联合站房总管供水温度（总管流量））*冷热量表中流量值（总管流量））*比热容/3.6 单位换算
        val.sort((v1, v2) -> CommonUtils.sort(v1.getGroup(), v2.getGroup(), true));
        return CommonUtils.calcDouble(CommonUtils.calcDouble((val.get(0).getValue() - val.get(1).getValue()) * val.get(2).getValue(), UNIT, EnumOperationType.DIVISION.getId()),
                HEAT, EnumOperationType.MULTIPLICATION.getId());
    }

    /**
     * 查询所有的空调节点
     *
     * @param nodes 父节点
     * @return
     */
    private List<BaseVo> queryAirConditioners(Collection<BaseVo> nodes) {
        List<Map<String, Object>> maps = nodeDao.queryChildNode(nodes, NodeLabelDef.AIR_CONDITIONER);
        if (CollectionUtils.isEmpty(maps)) {
            return Collections.emptyList();
        }

        return resolveChildren(maps);
    }

    private List<BaseVo> resolveChildren(List<Map<String, Object>> maps) {
        List<Map<String, Object>> airConditionerMapList = new ArrayList<>();
        for (Map<String, Object> map : maps) {
            List<Map<String, Object>> children = QueryResultContentTaker.getChildren(map);
            if (CollectionUtils.isNotEmpty(children)) {
                airConditionerMapList.addAll(children);
            }
        }
        return JsonTransferUtils.transferList(airConditionerMapList, BaseVo.class);
    }

    private void addDatalogValue(List<DataLogDataWithGroup> result, List<DatalogValue> dataList, Integer group) {
        for (DatalogValue DatalogValue : dataList) {
            result.add(new DataLogDataWithGroup(DatalogValue, group));
        }
    }

    /**
     * 获取总管物理量
     *
     * @param index
     * @return
     */
    private List<QuantitySearchVo> geTotalPipeLineQuantity(int index) {
        List<QuantitySearchVo> list = new ArrayList<>();
        // 流量
        list.add(new QuantitySearchVo(index++,
                21,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                29,
                EnergyTypeDef.COLD));

        // 温度
        list.add(new QuantitySearchVo(index,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                29,
                EnergyTypeDef.COLD));

        return list;
    }

    /**
     * 获取末端空调物理量
     *
     * @param index
     * @return
     */
    private List<QuantitySearchVo> getEndAirConditioner(int index) {
        List<QuantitySearchVo> list = new ArrayList<>();
        // 冷器进水温度
        list.add(new QuantitySearchVo(index++,
                6,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.SUPPLY,
                EnergyTypeDef.ENERGY));
        list.add(new QuantitySearchVo(index,
                2,
                QuantityTypeDef.FREQUENCY,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                46));
        return list;
    }
}

package com.cet.eem.bll.energysaving.model.dataentryquery;


import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import lombok.Getter;
import lombok.Setter;
import lombok.Value;

import java.util.List;

/**
 * @ClassName : EndColdDataEntry
 * @Description : 末端冷量需求预测的输入值查询
 * <AUTHOR> jiang<PERSON><PERSON>uan
 * @Date: 2022-04-08 17:20
 */
@Getter
@Setter
public class EndColdDataEntry {
    private Long projectId;
    private Long systemId;
    private List<EndColdDataVo> endColdDataVos;
    /**
     * 温度预测值
     */
    private List<DatalogValue>  tempPredict;
    /**
     * 湿度预测值
     */
    private List<DatalogValue>  humidityPredict;
    /**
     * 温度实际值
     */
    private List<DatalogValue>  tempActual;
    /**
     * 湿度实际值
     */
    private List<DatalogValue>  humidityActual;
}
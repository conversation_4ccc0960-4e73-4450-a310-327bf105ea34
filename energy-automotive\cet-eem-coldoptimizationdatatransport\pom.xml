<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cet.eem</groupId>
        <artifactId>eem-base-service-parent</artifactId>
        <version>1.13.102.18</version>
        <relativePath/>
    </parent>

    <groupId>com.cet.eem</groupId>
    <artifactId>cet-eem-coldoptimizationdatatransport</artifactId>
    <packaging>pom</packaging>
    <version>1.13.0034-am.branch_1.2</version>
    <modules>
        <module>cet-eem-bll-coldoptimizationdatatransport</module>
        <module>cet-eem-coldoptimizationdatatransportservice</module>
    </modules>

    <properties>

        <java.version>1.8</java.version>
        <spring-cloud.version>Greenwich.SR1</spring-cloud.version>
        <docker.registry>*************/eem-autom</docker.registry>
        <tag>v${project.version}</tag>
        <!--是否跳过测试打包 -->
        <skipTests>true</skipTests>
        <dockerHost>http://*************:2375</dockerHost>
        <tomcat.version>9.0.40</tomcat.version>
    </properties>

    <dependencies>
        <!-- 数据访问层 -->
        <!-- 数据访问层 -->
        <dependency>
            <groupId>com.cet.eem</groupId>
            <artifactId>cet-eem-dal-starter</artifactId>
        </dependency>

        <!-- eem基础业务层 -->
        <dependency>
            <groupId>com.cet.eem</groupId>
            <artifactId>cet-eem-bll-base-starter</artifactId>
        </dependency>

        <!-- eem核心业务层 -->
        <dependency>
            <groupId>com.cet.eem</groupId>
            <artifactId>cet-eem-bll-core-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-core</artifactId>
            <version>2.0.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>objenesis</artifactId>
                    <groupId>org.objenesis</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>


    <!--发布配置管理-->
    <distributionManagement>
        <repository>
            <id>*************_9086</id>
            <name>releases</name>
            <url>http://*************:9086/repository/cet/</url>
        </repository>
        <snapshotRepository>
            <id>*************_9086</id>
            <name>snapshots</name>
            <url>http://*************:9086/repository/cet/</url>
        </snapshotRepository>
    </distributionManagement>



</project>
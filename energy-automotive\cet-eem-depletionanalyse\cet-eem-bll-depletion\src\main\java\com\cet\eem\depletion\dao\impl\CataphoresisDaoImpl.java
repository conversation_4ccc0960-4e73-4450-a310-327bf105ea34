package com.cet.eem.depletion.dao.impl;

import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.depletion.dao.CataphoresisDao;
import com.cet.eem.depletion.model.CataphoresisDto;
import com.cet.eem.depletion.model.Constant.Constant;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CataphoresisDaoImpl implements CataphoresisDao {
    @Autowired
    ModelServiceUtils modelServiceUtils;


    @Override
    public List<CataphoresisDto> queryById(Long id) {
        QueryCondition condition = new QueryConditionBuilder<>(Constant.CATAPHORESIS)
                .eq(ColumnDef.ID, id).build();
        return modelServiceUtils.query(condition, CataphoresisDto.class);
    }
}

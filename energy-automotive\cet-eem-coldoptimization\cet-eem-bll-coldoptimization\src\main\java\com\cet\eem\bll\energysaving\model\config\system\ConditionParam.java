package com.cet.eem.bll.energysaving.model.config.system;

import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : ConditionParam
 * @Description : 条件入参
 * <AUTHOR> jiang<PERSON><PERSON><PERSON>
 * @Date: 2022-06-28 16:19
 */
@Getter
@Setter
public class ConditionParam {
    private String objectLabel;
    private Long systemId;
    private Integer dataType;
    private Integer operationSymbol;
    private Double value;
    private Integer operationSymbolOfTime;
    private Long timeValue;
    private Integer controlType;
}
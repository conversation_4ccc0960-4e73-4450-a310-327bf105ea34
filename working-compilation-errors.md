﻿# Java Compilation Errors Report

**Generated:** 2025-08-05 16:05:39
**Module:** energy-solution-fusion\eem-solution-transformer\eem-solution-transformer-core
**Total Issues:** 53 (Compilation: 1, Import Analysis: 52)

## ISSUES FOUND

### PowerTransformerDto.java (2 issues)

**Issue 1 - Line 3: Potential Missing Dependency**
 
import com.cet.eem.bll.common.model.domain.object.organization.Project;
 
- **Issue:** Import may not be available in fusion framework

**Issue 2 - Line 4: Potential Missing Dependency**
 
import com.cet.eem.bll.common.model.domain.object.powersystem.PowerTransformerVo;
 
- **Issue:** Import may not be available in fusion framework

### TransformerAnalysisController.java (1 issues)

**Issue 1 - Line 3: Potential Missing Dependency**
 
import com.cet.eem.bll.common.util.GlobalInfoUtils;
 
- **Issue:** Import may not be available in fusion framework

### TransformerAnalysisService.java (1 issues)

**Issue 1 - Line 10: Potential Missing Dependency**
 
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
 
- **Issue:** Import may not be available in fusion framework

### TransformerAnalysisServiceImpl.java (17 issues)

**Issue 1 - Line 3: Potential Missing Dependency**
 
import com.cet.eem.bll.common.dao.node.NodeDao;
 
- **Issue:** Import may not be available in fusion framework

**Issue 2 - Line 4: Potential Missing Dependency**
 
import com.cet.eem.bll.common.dao.pipenetwork.PipeNetworkConnectionModelDao;
 
- **Issue:** Import may not be available in fusion framework

**Issue 3 - Line 5: Potential Missing Dependency**
 
import com.cet.eem.bll.common.def.quantity.FrequencyDef;
 
- **Issue:** Import may not be available in fusion framework

**Issue 4 - Line 6: Potential Missing Dependency**
 
import com.cet.eem.bll.common.def.quantity.PhasorDef;
 
- **Issue:** Import may not be available in fusion framework

**Issue 5 - Line 7: Potential Missing Dependency**
 
import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;
 
- **Issue:** Import may not be available in fusion framework

**Issue 6 - Line 8: Potential Missing Dependency**
 
import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;
 
- **Issue:** Import may not be available in fusion framework

**Issue 7 - Line 9: Potential Missing Dependency**
 
import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;
 
- **Issue:** Import may not be available in fusion framework

**Issue 8 - Line 10: Potential Missing Dependency**
 
import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityAggregationData;
 
- **Issue:** Import may not be available in fusion framework

**Issue 9 - Line 11: Potential Missing Dependency**
 
import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityObject;
 
- **Issue:** Import may not be available in fusion framework

**Issue 10 - Line 12: Potential Missing Dependency**
 
import com.cet.eem.bll.common.model.domain.object.powersystem.PowerTransformerVo;
 
- **Issue:** Import may not be available in fusion framework

**Issue 11 - Line 13: Potential Missing Dependency**
 
import com.cet.eem.bll.common.model.topology.vo.LinkNode;
 
- **Issue:** Import may not be available in fusion framework

**Issue 12 - Line 14: Potential Missing Dependency**
 
import com.cet.eem.bll.common.model.topology.vo.PointNode;
 
- **Issue:** Import may not be available in fusion framework

**Issue 13 - Line 35: Potential Missing Dependency**
 
import com.cet.eem.quantity.dao.QuantityAggregationDataDao;
 
- **Issue:** Import may not be available in fusion framework

**Issue 14 - Line 36: Potential Missing Dependency**
 
import com.cet.eem.quantity.dao.QuantityObjectDao;
 
- **Issue:** Import may not be available in fusion framework

**Issue 15 - Line 37: Potential Missing Dependency**
 
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
 
- **Issue:** Import may not be available in fusion framework

**Issue 16 - Line 38: Potential Missing Dependency**
 
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;
 
- **Issue:** Import may not be available in fusion framework

**Issue 17 - Line 39: Potential Missing Dependency**
 
import com.cet.eem.quantity.service.QuantityManageService;
 
- **Issue:** Import may not be available in fusion framework

### TransformerindexDataDao.java (1 issues)

**Issue 1 - Line 3: Potential Missing Dependency**
 
import com.cet.eem.bll.common.model.domain.perception.logicaldevice.EemPoiRecord;
 
- **Issue:** Import may not be available in fusion framework

### TransformerindexDataDaoImpl.java (1 issues)

**Issue 1 - Line 3: Potential Missing Dependency**
 
import com.cet.eem.bll.common.model.domain.perception.logicaldevice.EemPoiRecord;
 
- **Issue:** Import may not be available in fusion framework

### TransformerOverviewController.java (1 issues)

**Issue 1 - Line 3: Potential Missing Dependency**
 
import com.cet.eem.bll.common.util.GlobalInfoUtils;
 
- **Issue:** Import may not be available in fusion framework

### TransformerOverviewServiceImpl.java (29 issues)

**Issue 1 - Line 1: Compilation Error**
 
package com.cet.eem.fusion.transformer.core.impl;
 
- **Issue:** Compilation error

**Issue 2 - Line 3: Potential Missing Dependency**
 
import com.cet.eem.bll.common.def.quantity.FrequencyDef;
 
- **Issue:** Import may not be available in fusion framework

**Issue 3 - Line 4: Potential Missing Dependency**
 
import com.cet.eem.bll.common.def.quantity.PhasorDef;
 
- **Issue:** Import may not be available in fusion framework

**Issue 4 - Line 5: Potential Missing Dependency**
 
import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;
 
- **Issue:** Import may not be available in fusion framework

**Issue 5 - Line 6: Potential Missing Dependency**
 
import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;
 
- **Issue:** Import may not be available in fusion framework

**Issue 6 - Line 7: Potential Missing Dependency**
 
import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;
 
- **Issue:** Import may not be available in fusion framework

**Issue 7 - Line 8: Potential Missing Dependency**
 
import com.cet.eem.bll.common.model.domain.object.event.SystemEvent;
 
- **Issue:** Import may not be available in fusion framework

**Issue 8 - Line 9: Potential Missing Dependency**
 
import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityAggregationData;
 
- **Issue:** Import may not be available in fusion framework

**Issue 9 - Line 10: Potential Missing Dependency**
 
import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityObject;
 
- **Issue:** Import may not be available in fusion framework

**Issue 10 - Line 11: Potential Missing Dependency**
 
import com.cet.eem.bll.common.model.domain.perception.logicaldevice.PecEventExtendVo;
 
- **Issue:** Import may not be available in fusion framework

**Issue 11 - Line 12: Potential Missing Dependency**
 
import com.cet.eem.bll.common.model.ext.objective.event.SystemEventWithText;
 
- **Issue:** Import may not be available in fusion framework

**Issue 12 - Line 13: Potential Missing Dependency**
 
import com.cet.eem.bll.common.model.topology.vo.ConnectionSearchVo;
 
- **Issue:** Import may not be available in fusion framework

**Issue 13 - Line 14: Potential Missing Dependency**
 
import com.cet.eem.bll.common.model.topology.vo.LinkNode;
 
- **Issue:** Import may not be available in fusion framework

**Issue 14 - Line 15: Potential Missing Dependency**
 
import com.cet.eem.bll.common.model.topology.vo.PointNode;
 
- **Issue:** Import may not be available in fusion framework

**Issue 15 - Line 16: Potential Missing Dependency**
 
import com.cet.eem.bll.common.util.GlobalInfoUtils;
 
- **Issue:** Import may not be available in fusion framework

**Issue 16 - Line 17: Potential Missing Dependency**
 
import com.cet.eem.bll.energy.dao.SystemEventDao;
 
- **Issue:** Import may not be available in fusion framework

**Issue 17 - Line 18: Potential Missing Dependency**
 
import com.cet.eem.bll.energy.model.event.SystemEventCountVo;
 
- **Issue:** Import may not be available in fusion framework

**Issue 18 - Line 19: Potential Missing Dependency**
 
import com.cet.eem.bll.energy.service.event.AlarmEventService;
 
- **Issue:** Import may not be available in fusion framework

**Issue 19 - Line 32: Potential Missing Dependency**
 
import com.cet.eem.event.model.analysis.ConfirmCountResult;
 
- **Issue:** Import may not be available in fusion framework

**Issue 20 - Line 33: Potential Missing Dependency**
 
import com.cet.eem.event.model.expert.EventCountSearchVo;
 
- **Issue:** Import may not be available in fusion framework

**Issue 21 - Line 34: Potential Missing Dependency**
 
import com.cet.eem.event.model.pecevent.PecEventCountVo;
 
- **Issue:** Import may not be available in fusion framework

**Issue 22 - Line 35: Potential Missing Dependency**
 
import com.cet.eem.event.service.PecEventService;
 
- **Issue:** Import may not be available in fusion framework

**Issue 23 - Line 36: Potential Missing Dependency**
 
import com.cet.eem.event.service.expert.ExpertAnalysisBffService;
 
- **Issue:** Import may not be available in fusion framework

**Issue 24 - Line 37: Potential Missing Dependency**
 
import com.cet.eem.event.service.expert.PecCoreEventBffService;
 
- **Issue:** Import may not be available in fusion framework

**Issue 25 - Line 44: Potential Missing Dependency**
 
import com.cet.eem.quantity.dao.QuantityAggregationDataDao;
 
- **Issue:** Import may not be available in fusion framework

**Issue 26 - Line 45: Potential Missing Dependency**
 
import com.cet.eem.quantity.dao.QuantityObjectDao;
 
- **Issue:** Import may not be available in fusion framework

**Issue 27 - Line 46: Potential Missing Dependency**
 
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
 
- **Issue:** Import may not be available in fusion framework

**Issue 28 - Line 47: Potential Missing Dependency**
 
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;
 
- **Issue:** Import may not be available in fusion framework

**Issue 29 - Line 48: Potential Missing Dependency**
 
import com.cet.eem.quantity.service.QuantityManageService;
 
- **Issue:** Import may not be available in fusion framework

## SUMMARY

- **Compilation Error:** 1 issues
- **Potential Missing Dependency:** 52 issues

---
**Generated:** 2025-08-05 16:05:39

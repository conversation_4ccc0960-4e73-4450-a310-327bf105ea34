package com.cet.eem.energyevent.model.pojo;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.energyevent.model.Constant;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/7/24 15:38
 */
@Getter
@Setter
@ModelLabel(Constant.ENERGYEVENTPUSH)
public class EnergyEventPush extends BaseEntity {
    @JsonProperty(Constant.LOGICALID)
    private Integer logicalId;
    @JsonProperty(Constant.EVENTTYPE)
    private Integer eventType;
    @JsonProperty(Constant.DATAID)
    private Long dataId;
    @JsonProperty(Constant.DEVICEID)
    private Long deviceId;
    @JsonProperty(Constant.STARTTIME)
    private Long startTime;

    private Boolean repeat;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EnergyEventPush repeat = (EnergyEventPush) o;
        return Objects.equals(logicalId, repeat.logicalId) && Objects.equals(eventType, repeat.eventType) &&
                Objects.equals(dataId, repeat.dataId) && Objects.equals(deviceId, repeat.deviceId);
    }

    @Override
    public int hashCode() {
        return Objects.hash( logicalId, eventType, dataId, deviceId);
    }
}

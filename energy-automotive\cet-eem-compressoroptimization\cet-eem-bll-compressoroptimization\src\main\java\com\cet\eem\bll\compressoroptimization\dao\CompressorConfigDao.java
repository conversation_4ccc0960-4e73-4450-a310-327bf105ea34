package com.cet.eem.bll.compressoroptimization.dao;

import com.cet.eem.bll.compressoroptimization.model.config.CompressorConfig;
import com.cet.eem.dao.BaseModelDao;

/**
 * @ClassName : CompressorConfigDao
 * @Description : 系统普通配置和高级配置
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-24 14:27
 */
public interface CompressorConfigDao extends BaseModelDao<CompressorConfig> {
    /**
     * 查询系统配置
     * @param systemId
     * @param projectId
     * @return
     */
    CompressorConfig queryCompressorConfig(Long systemId, Long projectId);
}
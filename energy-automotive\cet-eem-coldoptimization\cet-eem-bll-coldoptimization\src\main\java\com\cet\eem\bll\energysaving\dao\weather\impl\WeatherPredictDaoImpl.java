package com.cet.eem.bll.energysaving.dao.weather.impl;

import com.cet.eem.bll.energysaving.dao.weather.WeatherPredictDao;
import com.cet.eem.bll.common.model.domain.subject.energysaving.WeatherPredict;
import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.common.ParamUtils;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import com.cet.eem.model.base.ModelSingeWriteVo;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.toolkit.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @ClassName : WeatherPredictDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-14 15:43
 */
@Repository
public class WeatherPredictDaoImpl extends ModelDaoImpl<WeatherPredict> implements WeatherPredictDao {
    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Override
    public List<WeatherPredict> queryWeather(LocalDateTime st, LocalDateTime et) {
        LambdaQueryWrapper<WeatherPredict> wrapper = LambdaQueryWrapper.of(WeatherPredict.class);
        wrapper.ge(WeatherPredict::getLogTime, st).le(WeatherPredict::getLogTime, et);
        return this.selectList(wrapper);
    }

    @Override
    public List<WeatherPredict> writeOrUpdateData(List<WeatherPredict> weatherPredicts) {
        if (CollectionUtils.isEmpty(weatherPredicts)) {
            return Collections.emptyList();
        }
        List<List<Object>> addDataList = new ArrayList<>();
        List<List<Object>> updateDataList = new ArrayList<>();
        List<List<Object>> updateIds = new ArrayList<>();
        for (WeatherPredict connection : weatherPredicts) {
            List<Object> dataList = new ArrayList<>();
            if (ParamUtils.checkPrimaryKeyValid(connection.getId())) {
                updateIds.add(Collections.singletonList(connection.getId()));
                updateDataList.add(dataList);
            } else {
                addDataList.add(dataList);
            }

            dataList.add(connection.getLon());
            dataList.add(connection.getLat());
            dataList.add(connection.getTemp());
            dataList.add(connection.getHumidity());
            dataList.add(connection.getLogTime());
            dataList.add(connection.getAggregationCycle());
            dataList.add(connection.getRoomId());
            dataList.add(connection.getProjectId());
        }

        List<Map<String, Object>> result = new ArrayList<>();
        ModelSingeWriteVo writeResult;
        if (CollectionUtils.isNotEmpty(addDataList)) {
            writeResult = modelServiceUtils.writeDataBatch(ModelLabelDef.WEATHER_PREDICT, true, getQuantityObjectFields(), addDataList, null);
            result.addAll(modelServiceUtils.convertModelSingeWrite(writeResult));
        }

        if (CollectionUtils.isNotEmpty(updateDataList)) {
            writeResult = modelServiceUtils.writeDataBatch(ModelLabelDef.WEATHER_PREDICT, false, getQuantityObjectFields(), updateDataList, updateIds);
            result.addAll(modelServiceUtils.convertModelSingeWrite(writeResult));
        }

        return JsonTransferUtils.transferList(result, WeatherPredict.class);
    }

    @Override
    public List<WeatherPredict> queryWeatherData(LocalDateTime st, LocalDateTime et, Integer cycle,Long projectId) {
        LambdaQueryWrapper<WeatherPredict> wrapper = LambdaQueryWrapper.of(WeatherPredict.class);
        wrapper.ge(WeatherPredict::getLogTime, st)
                .le(WeatherPredict::getLogTime, et)
                .eq(WeatherPredict::getAggregationCycle, cycle)
                .eq(WeatherPredict::getProjectId, projectId);
        return this.selectList(wrapper);
    }

    private List<String> getQuantityObjectFields() {
        List<String> result = new ArrayList<>();
        result.add(ColumnDef.COLUMN_LON);
        result.add(ColumnDef.COLUMN_LAT);
        result.add(ColumnDef.COLUMN_TEMP);
        result.add(ColumnDef.COLUMN_HUMIDITY);
        result.add(ColumnDef.LOGTIME);
        result.add(ColumnDef.AGGREGATION_CYCLE);
        result.add(ColumnDef.ROOM_ID);
        result.add(ColumnDef.PROJECT_ID);
        return result;
    }
}
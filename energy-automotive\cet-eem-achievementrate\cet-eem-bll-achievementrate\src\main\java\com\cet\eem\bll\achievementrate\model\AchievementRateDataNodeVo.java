package com.cet.eem.bll.achievementrate.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : AchievementRateDataNodeVo
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-11-21 11:20
 */
@Getter
@Setter
public class AchievementRateDataNodeVo extends AchievementRateDataNode {
    @ApiModelProperty(value = "能源类型名称")
    private String energyTypeName;
    @ApiModelProperty(value = "产品类型名称")
    private String productTypeName;

    @ApiModelProperty(value = "指标名称")
    private String effSetName;

    @ApiModelProperty(value = "聚合周期名称")
    private String aggregationCycleName;

    private String unit;
    /**
     * 这两个id是为了排序用
     */
    private Long energyTypeId;
    private Long productId;
    /**
     * 0是普通，1是折标
     */
    private Integer isCoef;

}
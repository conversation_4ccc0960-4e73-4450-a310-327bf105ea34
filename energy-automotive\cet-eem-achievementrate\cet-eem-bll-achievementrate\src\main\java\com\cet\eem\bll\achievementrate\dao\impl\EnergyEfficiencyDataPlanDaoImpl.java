package com.cet.eem.bll.achievementrate.dao.impl;

import com.cet.eem.bll.achievementrate.dao.EnergyEfficiencyDataPlanDao;
import com.cet.eem.bll.achievementrate.model.EnergyEfficiencyParam;
import com.cet.eem.bll.achievementrate.model.data.EnergyEfficiencyDataPlan;
import com.cet.eem.bll.achievementrate.model.def.AchievementRateDataLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import com.cet.eem.model.model.BaseEntity;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotEmpty;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : EnergyEfficiencyDataPlanDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-11-21 17:13
 */
@Repository
public class EnergyEfficiencyDataPlanDaoImpl extends ModelDaoImpl<EnergyEfficiencyDataPlan> implements EnergyEfficiencyDataPlanDao {
    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Override
    public List<EnergyEfficiencyDataPlan> query(Long st, Long et, Collection<Long> setIds, Collection<BaseVo> nodes, Integer cycle) {
        if (CollectionUtils.isEmpty(nodes)) {
            return new ArrayList<>();
        }

        Map<String, List<BaseVo>> nodeMap = nodes.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        int group = 1;

        QueryConditionBuilder<BaseEntity> builder = new QueryConditionBuilder<>(AchievementRateDataLabelDef.EFF_PLAN)
                .composeMethod(true)
                .orderBy(ColumnDef.LOG_TIME)
                .removeOrderById();
        for (Map.Entry<String, List<BaseVo>> entry : nodeMap.entrySet()) {
            String label = entry.getKey();
            List<Long> ids = nodeMap.get(label).stream().map(BaseVo::getId).distinct().collect(Collectors.toList());
            builder.eq(ColumnDef.C_OBJECT_Label, label, group)
                    .in(ColumnDef.C_OBJECT_ID, ids, group)
                    .ge(ColumnDef.LOG_TIME, st, group)
                    .lt(ColumnDef.LOG_TIME, et, group)
                    .eq(ColumnDef.AGGREGATION_CYCLE, cycle, group)
                    .in(ColumnDef.ENERGY_EFFICIENCY_SET_ID, setIds, group);
            group++;
        }

        return modelServiceUtils.queryWithRedis(builder.build(), EnergyEfficiencyDataPlan.class);
    }

    @Override
    public List<EnergyEfficiencyDataPlan> query(EnergyEfficiencyParam param) {
        QueryConditionBuilder<BaseEntity> builder = new QueryConditionBuilder<>(AchievementRateDataLabelDef.EFF_PLAN)
                .orderBy(ColumnDef.LOG_TIME)
                .removeOrderById();
        if (CollectionUtils.isEmpty(param.getNodes())) {
            builder.ge(ColumnDef.LOG_TIME, param.getSt())
                    .lt(ColumnDef.LOG_TIME, param.getEnd())
                    .eq(ColumnDef.AGGREGATION_CYCLE, param.getCycle());
            if (Objects.nonNull(param.getEffSetId())) {
                builder.eq(ColumnDef.ENERGY_EFFICIENCY_SET_ID, param.getEffSetId());
            }
            if (Objects.nonNull(param.getEnergType())) {
                builder.eq(ColumnDef.ENERGY_TYPE, param.getEnergType());
            }
            if (Objects.nonNull(param.getProductType())) {
                builder.eq(ColumnDef.PRODUCT_TYPE, param.getProductType());
            }
            return modelServiceUtils.queryWithRedis(builder.build(), EnergyEfficiencyDataPlan.class);
        }
        builder.composeMethod(true);
        Map<String, List<BaseVo>> nodeMap = param.getNodes().stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        int group = 1;
        for (Map.Entry<String, List<BaseVo>> entry : nodeMap.entrySet()) {
            String label = entry.getKey();
            List<Long> ids = nodeMap.get(label).stream().map(BaseVo::getId).distinct().collect(Collectors.toList());
            builder.eq(ColumnDef.C_OBJECT_Label, label, group)
                    .in(ColumnDef.C_OBJECT_ID, ids, group)
                    .ge(ColumnDef.LOG_TIME, param.getSt(), group)
                    .lt(ColumnDef.LOG_TIME, param.getEnd(), group)
                    .eq(ColumnDef.AGGREGATION_CYCLE, param.getCycle(), group);
            if (Objects.nonNull(param.getEffSetId())) {
                builder.eq(ColumnDef.ENERGY_EFFICIENCY_SET_ID, param.getEffSetId(), group);
            }
            if (Objects.nonNull(param.getEnergType())) {
                builder.eq(ColumnDef.ENERGY_TYPE, param.getEnergType(), group);
            }
            if (Objects.nonNull(param.getProductType())) {
                builder.eq(ColumnDef.PRODUCT_TYPE, param.getProductType(),group);
            }
            group++;
        }
        return modelServiceUtils.queryWithRedis(builder.build(), EnergyEfficiencyDataPlan.class);
    }

    @Override
    public void insertData(Collection<EnergyEfficiencyDataPlan> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        List<EnergyEfficiencyDataPlan> updateDataList = dataList.stream().filter(it -> !Objects.isNull(it.getId())).collect(Collectors.toList());
        updateDataList(updateDataList);

        List<EnergyEfficiencyDataPlan> addDataList = dataList.stream().filter(it -> Objects.isNull(it.getId())).collect(Collectors.toList());
        addDataList(addDataList);
    }

    @Override
    public void writeEnergyEffData(Collection<EnergyEfficiencyDataPlan> plans) {
        if (CollectionUtils.isEmpty(plans)) {
            return;
        }

        HashSet<BaseVo> nodes = new HashSet<>();
        HashSet<Long> logTimes = new HashSet<>();
        HashSet<Long> effSetIds = new HashSet<>();
        HashSet<Integer> cycles = new HashSet<>();
        for (EnergyEfficiencyDataPlan energyConsumption : plans) {
            nodes.add(new BaseVo(energyConsumption.getObjectId(), energyConsumption.getObjectLabel()));
            logTimes.add(energyConsumption.getLogTime());
            effSetIds.add(energyConsumption.getEnergyEfficiencySetId());
            cycles.add(energyConsumption.getAggregationCycle());
        }
        List<EnergyEfficiencyDataPlan> oldEnergyEfficiencyDataPlan = queryAllEnergyEfficiencyDataPlan(nodes, logTimes, effSetIds, cycles);
        for (EnergyEfficiencyDataPlan energyConsumption : plans) {
            Optional<EnergyEfficiencyDataPlan> any = oldEnergyEfficiencyDataPlan.stream().filter(it -> checkEqual(energyConsumption, it)).findAny();
            any.ifPresent(it -> energyConsumption.setId(it.getId()));
        }

        insertData(plans);
    }

    private boolean checkEqual(EnergyEfficiencyDataPlan newObj, EnergyEfficiencyDataPlan oldObj) {
        return Objects.equals(newObj.getObjectLabel(), oldObj.getObjectLabel()) &&
                Objects.equals(newObj.getObjectId(), oldObj.getObjectId()) &&
                Objects.equals(newObj.getLogTime(), oldObj.getLogTime()) &&
                Objects.equals(newObj.getAggregationCycle(), oldObj.getAggregationCycle()) &&
                Objects.equals(newObj.getEnergyEfficiencySetId(), oldObj.getEnergyEfficiencySetId());
    }

    private List<EnergyEfficiencyDataPlan> queryAllEnergyEfficiencyDataPlan(@NotEmpty Collection<BaseVo> nodes, @NotEmpty Collection<Long> logTimes,
                                                                            @NotEmpty Collection<Long> energyEffIds, @NotEmpty Collection<Integer> cycles) {
        Map<String, List<BaseVo>> nodeMap = nodes.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        LambdaQueryWrapper<EnergyEfficiencyDataPlan> wrapper = LambdaQueryWrapper.of(EnergyEfficiencyDataPlan.class);
        nodeMap.forEach((label, nodeList) -> {
            Set<Long> ids = nodeList.stream().map(BaseVo::getId).collect(Collectors.toSet());
            wrapper.or(it -> it.eq(EnergyEfficiencyDataPlan::getObjectLabel, label)
                    .in(EnergyEfficiencyDataPlan::getObjectId, ids)
                    .in(EnergyEfficiencyDataPlan::getLogTime, logTimes)
                    .in(EnergyEfficiencyDataPlan::getAggregationCycle, cycles)
                    .in(EnergyEfficiencyDataPlan::getEnergyEfficiencySetId, energyEffIds));
        });

        return this.selectListRedisWithOutOrderById(wrapper);
    }

    private void addDataList(List<EnergyEfficiencyDataPlan> addDataList) {
        if (CollectionUtils.isEmpty(addDataList)) {
            return;
        }

        List<String> updateFields = Arrays.asList(ColumnDef.C_OBJECT_ID,
                ColumnDef.C_OBJECT_Label,
                ColumnDef.AGGREGATION_CYCLE,
                ColumnDef.ENERGY_TYPE,
                ColumnDef.LOG_TIME,
                ColumnDef.ENERGY_EFFICIENCY_SET_ID,
                ColumnDef.VALUE,
                ColumnDef.PROJECT_ID,
                ColumnDef.PRODUCT_TYPE);
        List<List<Object>> writeDataList = new ArrayList<>();
        for (EnergyEfficiencyDataPlan data : addDataList) {
            writeDataList.add(Arrays.asList(data.getObjectId(),
                    data.getObjectLabel(),
                    data.getAggregationCycle(),
                    data.getEnergyType(),
                    data.getLogTime(),
                    data.getEnergyEfficiencySetId(),
                    data.getValue(),
                    data.getProjectId(),
                    data.getProductType()));
        }
        modelServiceUtils.writeDataBatch(AchievementRateDataLabelDef.EFF_PLAN, true, updateFields, writeDataList, null);
    }

    private void updateDataList(List<EnergyEfficiencyDataPlan> updateDataList) {
        if (CollectionUtils.isEmpty(updateDataList)) {
            return;
        }

        List<String> filterFields = Arrays.asList(ColumnDef.C_OBJECT_ID,
                ColumnDef.C_OBJECT_Label,
                ColumnDef.AGGREGATION_CYCLE,
                ColumnDef.ENERGY_TYPE,
                ColumnDef.LOG_TIME,
                ColumnDef.ENERGY_EFFICIENCY_SET_ID,
                ColumnDef.PRODUCT_TYPE);

        List<String> updateFields = Collections.singletonList(
                ColumnDef.VALUE);
        List<List<Object>> writeDataList = new ArrayList<>();
        List<List<Object>> filterDataList = new ArrayList<>();
        for (EnergyEfficiencyDataPlan data : updateDataList) {
            writeDataList.add(Arrays.asList(
                    data.getValue()));

            filterDataList.add(Arrays.asList(data.getObjectId(),
                    data.getObjectLabel(),
                    data.getAggregationCycle(),
                    data.getEnergyType(),
                    data.getLogTime(),
                    data.getEnergyEfficiencySetId(),
                    data.getProductType()));
        }
        modelServiceUtils.writeDataBatch(AchievementRateDataLabelDef.EFF_PLAN, false, updateFields, writeDataList, filterFields, filterDataList);
    }

}
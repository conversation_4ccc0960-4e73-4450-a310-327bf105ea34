package com.cet.eem.bll.energysaving.model.aioptimization;

import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : WaterTempVo
 * @Description : 冷站和末端水温返回值
 * <AUTHOR> jiangzixuan
 * @Date: 2022-04-15 11:12
 */
@Getter
@Setter
public class WaterTempVo {

    /**
     * 冷却塔出水温度
     */
    private Double outletWaterTemp;
    /**
     * 冷冻水出水温度设定值
     */
    private Double outletWaterTempSettingValue;
    /**
     * 供回水压差设定值
     */
    private Double pressureSettingValue;
    /**
     * 供水温度
     */
    private Double supplyTemp;
    /**
     * 回水温度
     */
    private Double returnTemp;
}
package com.cet.eem.bll.energysaving.model.aioptimization;

import com.cet.eem.common.model.BaseVo;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : AiStartStopStrategyVo
 * @Description : 智能启停策略返回值
 * <AUTHOR> jiangzixuan
 * @Date: 2022-04-15 13:33
 */
@Getter
@Setter
public class AiStartStopStrategyVo extends AiStartStopStrategy {
    private List<BaseVo> coldWaterMainEngines;
    private List<BaseVo> plateHeatExChangers;
    private List<BaseVo> coolingPumps;
    private List<BaseVo> freezingPumps;
    private List<BaseVo> coolingTowers;
    /**
     * 冷冻泵频率
     */
    @JsonProperty("refrigerationpumpfrequency")
    private Double refrigerationPumpFrequency;
    /**
     * 冷却泵频率
     */
    @JsonProperty("coolingpumpfrequency")
    private Double coolingPumpFrequency;
    /**
     * 风机频率
     */
    @JsonProperty("fanfrequency")
    private Double fanFrequency;
    /**
     * 冷冻水出水温度设定值
     */
    @JsonProperty("waterouttemp")
    private Double waterOutTemp;
    /**
     * 冷冻水供回水压差设定值
     */
    @JsonProperty("waterreturnpressurediff")
    private Double waterReturnPressureDiff;
}
package com.cet.eem.bll.energysaving.model.cetml;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : CoolingResult
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-06-08 09:47
 */
@Getter
@Setter
public class CoolingResult {
    @JsonProperty("cold_machine_results")
    private List<ColdMachineResult> coldMachineResults;
    @JsonProperty("total_need_cooling_load")
    private Double totalNeedCoolingLoad;
    @JsonProperty("total_supply_cooling_load")
    private Double totalSupplyCoolingLoad;
    @JsonProperty("cooling_deviation")
    private Double coolingDeviation;
    @JsonProperty("total_least_power")
    private Double totalLeastPower;
    @JsonProperty("total_electricity")
    private Double totalElectricity;

}
package com.cet.eem.compressorservice.service;

import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.compressorservice.model.vo.*;

import java.util.List;

/**
 * @ClassName : CompressorService
 * <AUTHOR> yangy
 * @Date: 2022-04-02 10:06
 */
public interface CompressorService {

    List<RoomVo> getCompressorRoom(Long projectId);

    StandingBook getStandingBook(Long roomId);

    AirManagerData getAirManagerData(Long roomId);

    EnergyResult getElectricalRatio(ElectricalRatioForm form);

    List<LoadingAnalysisData> getLoadingAnalysis(Long roomId);

    List<UnloadingRate> getUnloadingRate(UnloadingRateForm form);
}

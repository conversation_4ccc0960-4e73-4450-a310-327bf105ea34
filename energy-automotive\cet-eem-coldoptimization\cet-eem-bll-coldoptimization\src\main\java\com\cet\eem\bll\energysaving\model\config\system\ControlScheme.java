package com.cet.eem.bll.energysaving.model.config.system;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.bll.energysaving.model.def.ColdOptimizationLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : ControlScheme
 * @Description : 控制方案
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-28 14:17
 */
@Getter
@Setter
@ModelLabel(ColdOptimizationLabelDef.CONTROL_SCHEME)
public class ControlScheme extends BaseEntity {
    @JsonProperty(ColdOptimizationLabelDef.CONTROL_SCHEME_NAME)
    private String controlSchemeName;
    @JsonProperty(ColumnDef.CREATE_TIME)
    private Long createTime;
    private LogicJson expression;
    @JsonProperty(ColumnDef.PROJECT_ID)
    private Long projectId;
    @JsonProperty(ColdOptimizationLabelDef.REFRIGERATING_SYSTEM_ID)
    private Long refrigeratingSystemId;
    @JsonProperty(ColdOptimizationLabelDef.CONTROL_TYPE)
    private Integer controlType;
    public ControlScheme() {
        this.modelLabel = ColdOptimizationLabelDef.CONTROL_SCHEME;
    }
}
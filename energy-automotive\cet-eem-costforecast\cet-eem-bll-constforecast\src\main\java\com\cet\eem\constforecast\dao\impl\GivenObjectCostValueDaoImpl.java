package com.cet.eem.constforecast.dao.impl;

import com.cet.eem.constforecast.dao.GivenObjectCostValueDao;
import com.cet.eem.bll.energy.model.costvalue.ObjectCostValueVo;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.model.base.ConditionBlock;
import com.cet.eem.model.model.BaseEntity;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName : ObjectCostValueDaoImpl
 * <AUTHOR> yangy
 * @Date: 2022-06-15 17:47
 */
@Component
public class GivenObjectCostValueDaoImpl implements GivenObjectCostValueDao {
    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Override
    public List<ObjectCostValueVo> queryObjectCostValueVo(BaseVo node, long st, long et, int cycle, List<Integer> energyTypes) {
        QueryConditionBuilder<BaseEntity> builder = new QueryConditionBuilder<>(ModelLabelDef.OBJECT_COST_VALUE)
                .where(ColumnDef.C_OBJECT_Label, ConditionBlock.OPERATOR_EQ, node.getModelLabel())
                .where(ColumnDef.C_OBJECT_ID, ConditionBlock.OPERATOR_EQ, node.getId())
                .where(ColumnDef.LOG_TIME, ConditionBlock.OPERATOR_GE, st)
                .where(ColumnDef.LOG_TIME, ConditionBlock.OPERATOR_LT, et)
                .where(ColumnDef.AGGREGATION_CYCLE, ConditionBlock.OPERATOR_EQ, cycle)
                .orderBy(ColumnDef.LOG_TIME);

        if (CollectionUtils.isNotEmpty(energyTypes)) {
            builder.where(ColumnDef.ENERGY_TYPE, ConditionBlock.OPERATOR_IN, energyTypes);
        }
        return modelServiceUtils.query(builder.build(), ObjectCostValueVo.class);
    }
}

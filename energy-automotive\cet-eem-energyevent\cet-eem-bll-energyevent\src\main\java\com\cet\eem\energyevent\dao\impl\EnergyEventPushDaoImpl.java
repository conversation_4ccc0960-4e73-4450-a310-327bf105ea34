package com.cet.eem.energyevent.dao.impl;

import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import com.cet.eem.energyevent.dao.EnergyEventPushDao;
import com.cet.eem.energyevent.model.Constant;
import com.cet.eem.energyevent.model.pojo.EnergyEventPush;
import com.cet.eem.model.tool.ModelServiceUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2024/7/24 17:22
 */
@Component
public class EnergyEventPushDaoImpl extends ModelDaoImpl<EnergyEventPush> implements EnergyEventPushDao {
    @Autowired
    private ModelServiceUtils modelServiceUtils;

    @Override
    public void editRepeat(List<EnergyEventPush> repeats) {
        if (CollectionUtils.isEmpty(repeats)) {
            return;
        }
        // 唯一键字段
        List<String> uniqueFields = Arrays.asList(
                ColumnDef.C_LOGICALID, ColumnDef.DATA_ID, ColumnDef.DEVICE_ID, Constant.EVENTTYPE,
                ColumnDef.START_TIME
        );
        List<String> writeFields = Stream.of(uniqueFields, Collections.singletonList(
                Constant.REPEAT
        )).flatMap(Collection::stream).collect(Collectors.toList());
        List<List<Object>> writeDataList = new ArrayList<>();
        for (EnergyEventPush it : repeats) {
            List<Object> dataList = new ArrayList<>();
            dataList.add(it.getLogicalId());
            dataList.add(it.getDataId());
            dataList.add(it.getDeviceId());
            dataList.add(it.getEventType());
            dataList.add(it.getStartTime());
            dataList.add(it.getRepeat());
            writeDataList.add(dataList);
        }
        modelServiceUtils.upsertDataBatch(Constant.ENERGYEVENTPUSH, null, writeFields, writeDataList, uniqueFields);
    }

    @Override
    public List<EnergyEventPush> queryRepeats(List<EnergyEventPush> repeats) {
        if (CollectionUtils.isEmpty(repeats)) {
            return Collections.emptyList();
        }
        Map<EnergyEventPush, List<Long>> repeatTimes = repeats.stream().collect(Collectors.groupingBy(val -> val, Collectors.mapping(EnergyEventPush::getStartTime, Collectors.toList())));
        LambdaQueryWrapper<EnergyEventPush> wrapper = LambdaQueryWrapper.of(EnergyEventPush.class);
        for (Map.Entry<EnergyEventPush, List<Long>> next : repeatTimes.entrySet()) {
            EnergyEventPush repeat = next.getKey();
            List<Long> times = next.getValue();
            Long startTime = times.stream().reduce(Long::min).orElse(null);
            Long endTime = times.stream().reduce(Long::max).orElse(null);
            wrapper.or();
            wrapper.eq(EnergyEventPush::getLogicalId, repeat.getLogicalId());
            wrapper.eq(EnergyEventPush::getDataId, repeat.getDataId());
            wrapper.eq(EnergyEventPush::getDeviceId, repeat.getDeviceId());
            wrapper.eq(EnergyEventPush::getEventType, repeat.getEventType());
            wrapper.ge(EnergyEventPush::getStartTime, startTime);
            wrapper.le(EnergyEventPush::getStartTime, endTime);
        }
        return selectList(wrapper);
    }

    @Override
    public List<EnergyEventPush> queryRepeatsByStartTime(Long startTime, Long endTime, List<Long> deviceIds,List<Long> dataIds) {
        if (Objects.isNull(startTime)||Objects.isNull(endTime)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<EnergyEventPush> wrapper = LambdaQueryWrapper.of(EnergyEventPush.class);
        wrapper.ge(EnergyEventPush::getStartTime, startTime);
        wrapper.le(EnergyEventPush::getStartTime, endTime);
        if (CollectionUtils.isNotEmpty(dataIds)){
            wrapper.in(EnergyEventPush::getDataId, dataIds);
        }
        if (CollectionUtils.isNotEmpty(deviceIds)){
            wrapper.in(EnergyEventPush::getDeviceId, deviceIds);
        }
        return selectList(wrapper);
    }
}

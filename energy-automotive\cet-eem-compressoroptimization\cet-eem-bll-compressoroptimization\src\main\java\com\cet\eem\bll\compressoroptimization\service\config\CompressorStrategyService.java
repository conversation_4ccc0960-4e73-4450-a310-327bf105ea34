package com.cet.eem.bll.compressoroptimization.service.config;

import com.cet.eem.bll.compressoroptimization.model.strategy.StrategyQueryParam;
import com.cet.eem.bll.compressoroptimization.model.strategy.StrategyReturnVo;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @ClassName : CompressorStrategyService
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-24 15:46
 */
public interface CompressorStrategyService {
    /**
     * 查询实时策略
     *
     * @param systemId
     * @param projectId
     * @return
     */
    StrategyReturnVo queryRealtime(Long systemId, Long projectId);

    /**
     * 查询历史策略
     * @param queryParam
     * @param projectId
     * @return
     */
    List<StrategyReturnVo> queryHistory(StrategyQueryParam queryParam, Long projectId);
}

package com.cet.eem.customreport.service;

import com.cet.eem.customreport.model.ReportData;
import com.cet.eem.customreport.model.ReportParam;

import java.util.List;

/**
 * 定制报表
 * <AUTHOR>
 * @date 2023/11/14
 */
public interface CustomReportService {
    /**
     * 查询定制报表
     */
    List<ReportData> queryReport(ReportParam reportParam, Long userId, Long projectId);

    /**
     * 导出报表
     * @param reportParam 报表查询参数
     * @param userId 用户id
     * @param projectId 项目id
     */
    void exportReport(ReportParam reportParam, Long userId, Long projectId);
}

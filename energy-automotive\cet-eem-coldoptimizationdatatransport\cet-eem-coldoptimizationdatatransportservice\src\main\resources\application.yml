server:
  port: 5200
  compression: # 对返回数据进行压缩
    enabled: true
    mime-types: application/json,application/xml,text/html,text/plain,text/css,application/x-javascript
    min-response-size: 10240 # 10Kb
info:
  #通过pom的tag获取版本变量
  version: @version@
spring:
  application:
    name: mes-data-transport-2
  profiles:
    active: 2
  servlet:
    multipart:
      enabled: true
      max-file-size: 101MB
      max-request-size: 101MB
      file-size-threshold: 10MB
  rabbitmq:
    host: ***********
    port: 5673
    username: admin123456
    password: admin123456
    listener:
      simple:
        retry:
          enabled: true
          max-attempts: 5
          initial-interval: 1000
    virtual-host: '/'
  # quartz的配置，避免工单漏生成的问题
  quartz:
    properties:
      org:
        quartz:
          jobStore:
            misfireThreshold: 90000 # 判断已经过期的定时任务中有哪些需要执行的向前推的时间，比如从当前时间向前推指定的时间内，如果有过期的也会执行
          scheduler:
            batchTriggerAcquisitionMaxCount: 10
  datasource:
    username: postgres
    url: **************************************************
    password: Ceiec4567$%^&
    driver-class-name: org.postgresql.Driver
  main:
    allow-bean-definition-overriding: true
  task:
    scheduling:
      pool:
        size: 5
  kettle:
    host: ************ # kettle服务地址
    port: 7890 # kettle服务端口
    username: 'admin' #kettle资源库登录用户名,默认为admin
    password: 'admin' #kettle资源库登录密码，默认为admin
    recountjobname: '重算' #重算流程名称，默认为重算

logging:
  file: logs/${spring.application.name}.log
  config: classpath:logback-boot.xml
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

hystrix:
  command:
    default: #default全局有效，service id指定应用有效
      execution:
        timeout:
          #如果enabled设置为false，则请求超时交给ribbon控制,为true,则超时作为熔断根据
          enabled: true
        isolation:
          thread:
            timeoutInMilliseconds: 15000 #断路器超时时间，默认1000ms

ribbon:
  ReadTimeout: 3000
  ConnectTimeout: 15000

eem:
  transformeranalysis:
    cron: '-'
  date:
    startHour: 0 # 非自然日配置，取值0-23
    startDay: 1 # 非自然月配置，取值1-28
    startMonth: 1 # 非自然年配置，取值1-12
    unnaturalType: 1 # 非自然计算类型，如果为0向后计算，如果为1向前计算
  common:
    defaultRemainLife: 25 # 额定生命电池，单位（年）
    aboutExpiredDay: 15 # 合作即将过期提醒时间
  http:
    proxyHost:
    proxyPort: 0
  event:
    system-event-offset: 200 # 系统事件收敛匹配的偏移量
  energyefficiency:
    year: 1000
    month: 800
    day: 500
  cycle_type_carbon: '12,2,100;12,3,100;12,5,100;14,2,200;14,3,200;14,5,200;17,2,300;17,3,300;17,5,300'    #不同的碳排放通过;进行隔开,同一个碳排放配置通过','隔开标识,第一个数字是聚合周期,第二个数字是指标类型,第三个数字是碳排放量
common:
  date:
    startMinute: 0 # 起始分钟,取值0-55
    startHour: 0 # 起始小时，取值0-23
    startDay: 1 # 起始天，取值1-28
    startMonth: 1 # 其实月，取值1-12
    belong: 1 # 非自然计算类型，1 表示开始时段,2表示结束时段

test:
  allow-write-test-data: false

service:
  system: cloud # 配置当前是哪个系统，cloud为云平台，oil为油气田
  redis:
    keyPrefix: eem # 配置redis中key的前缀
  swagger:
    enable: false

cet:
  feign:
    url:
      cloud-auth-service: ''
      model-service: ''
  grayrelease:
    enabled: true  # 使用feign调用其他服务的时候，需要将此项配置为true，否则不会转发header信息
  base-service:
    model-service:
      name: 'MODEL-SERVICE' #模型服务名称。不配置，则默认使用MODEL-SERVICE
      url: '' #节点服务的地址，调试时可以直接配上地址。默认不配置，使用名称进行调用
    device-data-service:
      name: 'DEVICE-DATA-SERVICE' #设备数据服务名称。不配置，则默认使用DEVICE-DATA-SERVICE
      url: '' #节点服务的地址，调试时可以直接配上地址。默认不配置，使用名称进行调用
    workflow-service:
      name: 'WORKFLOW-SERVICE'
      url: ''
  eem:
    model:
      write-count: 2000
      max-page-limit: 20000
    cache:
      modelLabelEnergyType: '' #modelLabel默认的能源类型配置
    pec-report:
      cache:
        enable: true
        interval: '0 0/5 * * * ?'
        expired-minute: 5
    data-entry: # 数据录入相关
      maxExportNodeNumber: 10 # 允许最大导出节点
      maxImportNodeNumber: 10 # 允许最大导入节点
    production-data:
      aggregation-cycle-chain: "12,14,17"
    task:
      system-event:
        updateEndtimeAndConvergent:
          interval: '-'
      kpi:
        interval: '-'
      energy-consumption:
        predict:
          interval: '-'
      percent:
        startTime: '2021-01-01 00:00:00' # 全局统计开始时间，注意格式是固定的：年-月-日 时:分:秒
        cycleChain: '12'
        interval: '-'
      period:
        startTime: '2021-01-01 00:00:00' # 全局统计开始时间，注意格式是固定的：年-月-日 时:分:秒
        interval: '-'
      consumption:
        startTime: '2021-01-01 00:00:00' # 全局统计开始时间，注意格式是固定的：年-月-日 时:分:秒
        cycleChain: '12'
        interval: '-'
      transformerindex:
        interval: '-'
      weather:
        interval: '-' # cron的格式，可以在线生成，如：0 0 1 * * ?，(每天凌晨1点执行一次)如果为'-'表示不执行定时任务
        minute-interval: '-'
        hour-interval: '-'
        history-interval: '-'
      energy-saving:
        end-cold-capacity: # 空调末端制冷
          interval: '-'
        loss: # 损耗
          interval: '-'
        weather: #温湿度
          interval: '-'
          startTime: ''
        pipeLoss:
          interval: '-'
          startTime: ''
        endCold:
          interval: '-'
          startTime: ''
        systemPower:
          interval: '-'
          startTime: ''
        outWaterTemp:
          interval: '-'
          startTime: ''
        control:
          delayed: 5
          operation: 5
          wait: 5
          retryCount: 3
          remote:
            interval: '-'
          heartBeat:
            interval: '-'
        diagnosis:
            basic:
              interval: '-'
              OVERALL-START-TIME: '2022-01-01 00:00:00'
              MAX-DEMAND-OVER-SACLE: 0.1
            power-tariff:
              interval: '-'
              OVERALL-START-TIME: '2022-01-01 00:00:00'
            ELECTRIC-CHARGE:
              INTERVAL: '-'
              OVERALL-START-TIME: '2022-01-01 00:00:00'
              TIME-SHARE-PERIODRATIO-TYPE: 1
        curvefitting:
          internal: '0 0 0 1 * ?' # 执行时间，每月1号0点执行一次
          time-range: 3 #任务往前查询定时记录的范围，单位month,定时记录的查询范围为1~5个月
          rr: 0.6 #cop拟合结果验证，R平方大于0.6可存入数据库
      demand:
        device-max-demand:
          interval: '-'
        max-demand:
          interval: '-'
    szw:
      eemtoibslog: false
    energy-consumption:
      electricity-charge: ''
      ts-identification: 'null' # 分时字段的值，如果为''，那么分时字段会使用''进行判断，其他情况会使用null进行判断
    data:
      export:
        needthreemonth: false
    group:
      energytype:
        configurepath: ''
      platform:
        tree:
          type: ''
    enterprise:
      file: C:\Users\<USER>\Desktop\config.json
      dimension: 104
    knowledge:
      file:
        size: 20971520  # 20M
    system: cloud # 配置当前是哪个系统，cloud为云平台，oil为油气田
    swagger:
      enabled: false # 控制是否启用swagger
    auth:
      impl-name: 'native-auth'  # 实现节点权限查询的类名，通过提供不同的类名，以达到在不同服务中有不同的权限实现
      authType: normal # 权限类型取值，云管理：cloud；常规权限：normal；深圳湾：shenzhenwan
      nodeRelationShipPath: ''
      tenantDefaultRolePath: '' # 平台角色默认权限节点树
      tenantRolePageNodesPath: '' #平台角色页面权限节点树
      use-simple-rule: false
      importPowerTariffPath: '' # 力调费率配置路径
      root-id: 1
      root-name: ROOT
      root-password: sA123456@
      root-exist: true
      urlWhiteListPath: '' #白名单配置
    sync:
      pq-event:
        sag:
          count: 10000
          interval: '-'
      convergence-event-relation:
        interval: '-' # cron的格式，可以在线生成，如：0 0/10 * * * ?，如果为'-'表示不执行定时任务
        count: 10000  # 每次处理的事件量
        pre-month: 1  # 向前追溯数据的月份，为0时表示追溯所有数据
      convergence-event: # 收敛事件转存
        count: 1000  # 每次处理的事件量
        interval: '-' # cron的格式，可以在线生成，如：0 0/1 * * * ?，如果为'-'表示不执行定时任务
        maxTimeDuration: 3 # 匹配结束事件最大天数
        beginYear: 2019 # 开始年份
        match-end-time-offset-minute: 60
        batch-handle-event-count: 25000
      convergence-event-count: # 收敛事件统计
        count: 10000  # 每次处理的事件量
        interval: '-' # cron的格式，可以在线生成，如：0 0/5 * * * ?，如果为'-'表示不执行定时任务
      peccore-event:
        count: 5000  # 每次处理的事件量
        interval: '-' # cron的格式，可以在线生成，如：0 0/1 * * * ?，如果为'-'表示不执行定时任务
        maxTimeDuration: 3 # 匹配结束事件最大天数
        beginYear: 2019 # 开始年份
        match-end-time-offset-minute: 60
      peccore-event-relation:
        interval: '-'
        count: 10000  # 每次处理的事件量
        pre-month: 1  # 向前追溯数据的月份，为0时表示追溯所有数据
      peccore-event-count:
        count: 10000  # 每次处理的事件量
        interval: '-' # cron的格式，可以在线生成，如：0 0/5 * * * ?，如果为'-'表示不执行定时任务
      pec-device-extend: # 同步peccore表计信息
        interval: '-'
    topology:
      template-path: ''  # 拓扑导入模板配置路径，如果没有配置取系统默认
    event:
      peccore:
        export-max-size: 20000
      pq:
        export-max-size: 20000
        group-time: 10
      inspection:
        export-max-size: 10000
    node:
      mobile:
        system-info-path: ''
        module:
          path: ''
      order-by-field: 'id'
      import:
        max-count: 500 # 单次导入允许最大的节点数量
      line-depth: 2  # 从房间到开关柜的层级深度
      rootNodeLabel: project
      connectionLabel: pipenetworkconnectionmodel
      check-repeat-range: parent  # global：全局范围检查重复，parent：父节点范围内检查重复
      android_module:
        path: ''   # 安卓模块配置
    redis:
      key-prefix: eem # 配置redis中key的前缀
    service:
      url:
        cloud-auth-service: ''
        model-service: ''
        alarm-service: ''
        demand-service: ''
        device-data-service: ''
        dimension-service: ''
        eem-common-service: ''
        cetml-service: http://cetml1:5000
        notice-service: ''
        mreport-service: ''
        video-service: 'video:8082'
        curve-service: ''
        only-report: ''
        eem-service: ''
        lgbmodel-service: ''
    demand:
      point:
        maxPointCount: 2000  # 需量中查询定时记录显示最大点数
    common:
      query-by-redis: false # 判断是否从缓存中查询能耗、产品等数据
      file:
        fileRootPath: C:\Users\<USER>\Desktop
        maxSize: 102400 # 单位为Kb
        maxExportSize: 10000
        validExt: '.png,.jpg,.gif,.bmp,.jpeg,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.mp4,.3gp,.mkv,.avi,.mepg,.mpg'
    notice:
      sender: '综合能源平台'
      sendApp: true
      setting-path: ''
      recordSaveDays: 2  # 已经推送消息记录保存时间
      sendMessage: true
    work-order:
      load-plan-sheet: false # 是否加载巡检/维修计划
      repair:
        check-over-time:
          interval: '-' # 检查工单是否超时定时任务，cron格式
      signpoint:
        sign-status-reset-interval: '-'
        sign-status-update-interval: '-'
      inspect: # 巡检工单
        check-over-time-notice:
          pre-time: 30 # 工单超时预警提前的时间
          interval: '-' # 检查工单是否超时定时任务，cron格式
        check-over-time:
          interval: '-' # 检查工单是否超时定时任务，cron格式
        app:
          summary-query-pre-day: 1  # 移动端总览查询提前时间
      maintenance: # 维保工单
        generate-work-order:
          device-work-time: 60000  # 设备运行的时间达到这个值，生成维保工单
          interval: '-' # 根据设备运行时长生成工单定时任务，cron格式
    transformeranalysis:
      cron: '-'
    optimizationstrategy:
      cron: '-' # 执行间隔，最小不小于每半小时一次，
      query-interval: 60 # 任务往前查询定时记录的范围，单位min，最小不小于60min
    mes:
      cron: '0 0/1 * * * ?' # 执行间隔，每小时执行一次
      filepath: 'D:/xxxx/energy-automotive/cet-eem-coldoptimizationdatatransport/cet-eem-bll-coldoptimizationdatatransport/target/classes/config/workshipcodemap.txt'
      cycle: 12
      code: '20220803'
      his:
        cron: '-' # 执行间隔，每分钟执行一次
        keep: true
      passpoint:
        cron: '0 0 1 * * ?' #每天零点或1点执行一次
        url: '**************************************************'
        user: 'postgres'
        password: 'Ceiec4567$%^&'
package com.cet.eem.bll.energysaving.dao.aioptimization.impl;

import com.cet.eem.bll.energysaving.dao.aioptimization.EquipmentCurveDao;
import com.cet.eem.bll.energysaving.model.aioptimization.WorkSection;
import com.cet.eem.bll.energysaving.model.config.EquipmentCurve;
import com.cet.eem.bll.energysaving.model.def.ColdOptimizationLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import com.cet.eem.model.model.BaseEntity;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName : EquipmentCurveDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-24 16:55
 */
@Repository
public class EquipmentCurveDaoImpl extends ModelDaoImpl<EquipmentCurve> implements EquipmentCurveDao {
    @Autowired
    ModelServiceUtils modelServiceUtils;
    public static final String LABEL= ColdOptimizationLabelDef.EQUIPMENT_CURVE;
    public static final String TYPE=ColdOptimizationLabelDef.CURVE_TYPE;
    @Override
    public List<EquipmentCurve> queryEquipmentCurve(String label, Long id,List<Integer> type) {
        LambdaQueryWrapper<EquipmentCurve> wrapper = LambdaQueryWrapper.of(EquipmentCurve.class);
        wrapper.eq(EquipmentCurve::getObjectId, id)
                .eq(EquipmentCurve::getObjectLabel, label)
                .in(EquipmentCurve::getCurveType,type);
        List<EquipmentCurve> equipmentCurves = this.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(equipmentCurves)) {
            return equipmentCurves;
        }
        return Collections.emptyList();
    }

    @Override
    public List<EquipmentCurve> queryCurveList(List<BaseVo> nodes,List<Integer> type) {
        if (CollectionUtils.isEmpty(nodes)) {
            return Collections.emptyList();
        }
        Map<String, List<BaseVo>> nodeMap = nodes.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        int group = 1;

        QueryConditionBuilder<BaseEntity> builder = new QueryConditionBuilder<>(LABEL)
                .composeMethod(true);
        for (Map.Entry<String, List<BaseVo>> entry : nodeMap.entrySet()) {
            String label = entry.getKey();
            List<Long> ids = nodeMap.get(label).stream().map(BaseVo::getId).distinct().collect(Collectors.toList());
            builder.eq(ColumnDef.C_OBJECT_Label, label, group);
            builder.in(ColumnDef.C_OBJECT_ID, ids, group);
            builder.in(TYPE,type,group);
            group++;
        }

        return modelServiceUtils.query(builder.build(), EquipmentCurve.class);
    }

}
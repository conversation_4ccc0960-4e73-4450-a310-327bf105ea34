﻿# Java Compilation Errors Report

**Generated:** 2025-08-05 16:11:01
**Module:** energy-solution-fusion\eem-solution-transformer\eem-solution-transformer-core
**Total Issues:** 53 (Compilation: 1, Import Analysis: 52)

## ISSUES FOUND

### PowerTransformerDto.java (2 issues)

**Issue 1 - Line 3:** Potential Missing Dependency

`import com.cet.eem.bll.common.model.domain.object.organization.Project;`

**Issue 2 - Line 4:** Potential Missing Dependency

`import com.cet.eem.bll.common.model.domain.object.powersystem.PowerTransformerVo;`

### TransformerAnalysisController.java (1 issues)

**Issue 1 - Line 3:** Potential Missing Dependency

`import com.cet.eem.bll.common.util.GlobalInfoUtils;`

### TransformerAnalysisService.java (1 issues)

**Issue 1 - Line 10:** Potential Missing Dependency

`import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;`

### TransformerAnalysisServiceImpl.java (17 issues)

**Issue 1 - Line 3:** Potential Missing Dependency

`import com.cet.eem.bll.common.dao.node.NodeDao;`

**Issue 2 - Line 4:** Potential Missing Dependency

`import com.cet.eem.bll.common.dao.pipenetwork.PipeNetworkConnectionModelDao;`

**Issue 3 - Line 5:** Potential Missing Dependency

`import com.cet.eem.bll.common.def.quantity.FrequencyDef;`

**Issue 4 - Line 6:** Potential Missing Dependency

`import com.cet.eem.bll.common.def.quantity.PhasorDef;`

**Issue 5 - Line 7:** Potential Missing Dependency

`import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;`

**Issue 6 - Line 8:** Potential Missing Dependency

`import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;`

**Issue 7 - Line 9:** Potential Missing Dependency

`import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;`

**Issue 8 - Line 10:** Potential Missing Dependency

`import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityAggregationData;`

**Issue 9 - Line 11:** Potential Missing Dependency

`import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityObject;`

**Issue 10 - Line 12:** Potential Missing Dependency

`import com.cet.eem.bll.common.model.domain.object.powersystem.PowerTransformerVo;`

**Issue 11 - Line 13:** Potential Missing Dependency

`import com.cet.eem.bll.common.model.topology.vo.LinkNode;`

**Issue 12 - Line 14:** Potential Missing Dependency

`import com.cet.eem.bll.common.model.topology.vo.PointNode;`

**Issue 13 - Line 35:** Potential Missing Dependency

`import com.cet.eem.quantity.dao.QuantityAggregationDataDao;`

**Issue 14 - Line 36:** Potential Missing Dependency

`import com.cet.eem.quantity.dao.QuantityObjectDao;`

**Issue 15 - Line 37:** Potential Missing Dependency

`import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;`

**Issue 16 - Line 38:** Potential Missing Dependency

`import com.cet.eem.quantity.model.quantity.QuantitySearchVo;`

**Issue 17 - Line 39:** Potential Missing Dependency

`import com.cet.eem.quantity.service.QuantityManageService;`

### TransformerindexDataDao.java (1 issues)

**Issue 1 - Line 3:** Potential Missing Dependency

`import com.cet.eem.bll.common.model.domain.perception.logicaldevice.EemPoiRecord;`

### TransformerindexDataDaoImpl.java (1 issues)

**Issue 1 - Line 3:** Potential Missing Dependency

`import com.cet.eem.bll.common.model.domain.perception.logicaldevice.EemPoiRecord;`

### TransformerOverviewController.java (1 issues)

**Issue 1 - Line 3:** Potential Missing Dependency

`import com.cet.eem.bll.common.util.GlobalInfoUtils;`

### TransformerOverviewServiceImpl.java (29 issues)

**Issue 1 - Line 1:** Compilation Error

`package com.cet.eem.fusion.transformer.core.impl;`

**Issue 2 - Line 3:** Potential Missing Dependency

`import com.cet.eem.bll.common.def.quantity.FrequencyDef;`

**Issue 3 - Line 4:** Potential Missing Dependency

`import com.cet.eem.bll.common.def.quantity.PhasorDef;`

**Issue 4 - Line 5:** Potential Missing Dependency

`import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;`

**Issue 5 - Line 6:** Potential Missing Dependency

`import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;`

**Issue 6 - Line 7:** Potential Missing Dependency

`import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;`

**Issue 7 - Line 8:** Potential Missing Dependency

`import com.cet.eem.bll.common.model.domain.object.event.SystemEvent;`

**Issue 8 - Line 9:** Potential Missing Dependency

`import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityAggregationData;`

**Issue 9 - Line 10:** Potential Missing Dependency

`import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityObject;`

**Issue 10 - Line 11:** Potential Missing Dependency

`import com.cet.eem.bll.common.model.domain.perception.logicaldevice.PecEventExtendVo;`

**Issue 11 - Line 12:** Potential Missing Dependency

`import com.cet.eem.bll.common.model.ext.objective.event.SystemEventWithText;`

**Issue 12 - Line 13:** Potential Missing Dependency

`import com.cet.eem.bll.common.model.topology.vo.ConnectionSearchVo;`

**Issue 13 - Line 14:** Potential Missing Dependency

`import com.cet.eem.bll.common.model.topology.vo.LinkNode;`

**Issue 14 - Line 15:** Potential Missing Dependency

`import com.cet.eem.bll.common.model.topology.vo.PointNode;`

**Issue 15 - Line 16:** Potential Missing Dependency

`import com.cet.eem.bll.common.util.GlobalInfoUtils;`

**Issue 16 - Line 17:** Potential Missing Dependency

`import com.cet.eem.bll.energy.dao.SystemEventDao;`

**Issue 17 - Line 18:** Potential Missing Dependency

`import com.cet.eem.bll.energy.model.event.SystemEventCountVo;`

**Issue 18 - Line 19:** Potential Missing Dependency

`import com.cet.eem.bll.energy.service.event.AlarmEventService;`

**Issue 19 - Line 32:** Potential Missing Dependency

`import com.cet.eem.event.model.analysis.ConfirmCountResult;`

**Issue 20 - Line 33:** Potential Missing Dependency

`import com.cet.eem.event.model.expert.EventCountSearchVo;`

**Issue 21 - Line 34:** Potential Missing Dependency

`import com.cet.eem.event.model.pecevent.PecEventCountVo;`

**Issue 22 - Line 35:** Potential Missing Dependency

`import com.cet.eem.event.service.PecEventService;`

**Issue 23 - Line 36:** Potential Missing Dependency

`import com.cet.eem.event.service.expert.ExpertAnalysisBffService;`

**Issue 24 - Line 37:** Potential Missing Dependency

`import com.cet.eem.event.service.expert.PecCoreEventBffService;`

**Issue 25 - Line 44:** Potential Missing Dependency

`import com.cet.eem.quantity.dao.QuantityAggregationDataDao;`

**Issue 26 - Line 45:** Potential Missing Dependency

`import com.cet.eem.quantity.dao.QuantityObjectDao;`

**Issue 27 - Line 46:** Potential Missing Dependency

`import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;`

**Issue 28 - Line 47:** Potential Missing Dependency

`import com.cet.eem.quantity.model.quantity.QuantitySearchVo;`

**Issue 29 - Line 48:** Potential Missing Dependency

`import com.cet.eem.quantity.service.QuantityManageService;`

## SUMMARY

- **Compilation Error:** 1 issues
- **Potential Missing Dependency:** 52 issues

---
**Generated:** 2025-08-05 16:11:01

server:
  port: 5200

spring:
  application:
    name: mes-data-transport-2
  redis:
    host: ***********
    port: 26379
    jedis:
      pool:
        max-active: 8 # 最大连接数
        max-wait: -1 # 最大阻塞等待时间
        max-idle: 8 # 最大空闲
        min-idle: 0 # 最小空闲redis
    database: 8
    password: sA123456
  rabbitmq:
    host: ***********
    port: 5673
    username: admin123456
    password: admin123456
    listener:
      simple:
        retry:
          enabled: true
          max-attempts: 5
          initial-interval: 1000
    virtual-host: '/'
  datasource:
    druid:
      driver-class-name: org.postgresql.Driver
      username: postgres
      url: **************************************************
      password: Ceiec4567$%^&
      matterhorn:
        driver-class-name: org.postgresql.Driver
        username: postgres
        url: **************************************************
        password: Ceiec4567$%^&
eureka:
  client:
    service-url:
      defaultZone: http://***********:1001/eureka/
      #defaultZone: http://localhost:1001/eureka/
    #    register-with-eureka: true
    lease-renewal-interval-in-seconds: 15
    prefer-ip-address: true
  instance:
    lease-renewal-interval-in-seconds: 15
    prefer-ip-address: true
#    下面该配置不要参与打包
#    ip-address: ************

service:
  system: cloud # 配置当前是哪个系统，cloud为云平台，oil为油气田
  redis:
    keyPrefix: eem # 配置redis中key的前缀
  swagger:
    enable: false

cet:
  loss:
    standard:
      complex: 0.05
    daily:
      cron: '-'
    cron: '-'
    alert: true
    keepTransformer: true
    queryLineSegment: true
    hide-zero-drift-data: false
  grayrelease:
    enabled: true  # 使用feign调用其他服务的时候，需要将此项配置为true，否则不会转发header信息
  base-service:
    notice-service:
      name: 'NOTICE-SERVICE'
      url: '***********:5070'
    cloud-auth-service:
      name: 'CLOUD-AUTH-SERVICE'
      url: '***********:2014'
    model-service:
      name: 'MODEL-SERVICE'
      url: '***********:8085'
    device-data-service:
      name: 'DEVICE-DATA-SERVICE'
      url: '***********:5050'
    workflow-service:
      name: 'WORKFLOW-SERVICE'
      url: '***********:8012'
  eem:
    energy-consumption:
      electricity-charge: 峰,平,谷
    system: cloud # 配置当前是哪个系统，cloud为云平台，oil为油气田
    swagger:
      enabled: true # 控制是否启用swagger
    sync:
      convergence-event-relation:
        interval: '-' # cron的格式，可以在线生成，如：0 0/10 * * * ?，如果为'-'表示不执行定时任务
        count: 10000  # 每次处理的事件量
        pre-month: 12  # 向前追溯数据的月份，为0时表示追溯所有数据
      convergence-event:  # 收敛事件转存
        count: 1000  # 每次处理的事件量
        interval: '-' # cron的格式，可以在线生成，如：0 0/1 * * * ?，如果为'-'表示不执行定时任务
        maxTimeDuration: 3 # 匹配结束事件最大天数
        beginYear: 2019 # 开始年份
        match-end-time-offset-minute: 60
      convergence-event-count: # 收敛事件统计
        count: 10000  # 每次处理的事件量
        interval: '-' # cron的格式，可以在线生成，如：0 0/5 * * * ?，如果为'-'表示不执行定时任务
      peccore-event:
        count: 5000  # 每次处理的事件量
        interval: '-' # cron的格式，可以在线生成，如：0 0/1 * * * ?，如果为'-'表示不执行定时任务
        maxTimeDuration: 3 # 匹配结束事件最大天数
        beginYear: 2019 # 开始年份
        match-end-time-offset-minute: 60
      peccore-event-relation:
        interval: '-'
        count: 10000  # 每次处理的事件量
        pre-month: 12  # 向前追溯数据的月份，为0时表示追溯所有数据
      peccore-event-count:
        count: 10000  # 每次处理的事件量
        interval: '-' # cron的格式，可以在线生成，如：0 0/5 * * * ?，如果为'-'表示不执行定时任务
    topology:
      template-path: ''  # 拓扑导入模板配置路径，如果没有配置取系统默认
    auth:
      impl-name: 'native-auth'  # 实现节点权限查询的类名，通过提供不同的类名，以达到在不同服务中有不同的权限实现
      authType: normal # 权限类型取值，云管理：cloud；常规权限：normal
      nodeRelationShipPath: ''
      tenantRolePageNodesPath: ''
      root-exist: false
    node:
      order-by-field: 'id'
      rootNodeLabel: project
      connectionLabel: pipenetworkconnectionmodel
      check-repeat-range: parent  # global：全局范围检查重复，parent：父节点范围内检查重复
    redis:
      key-prefix: eem # 配置redis中key的前缀
    service:
      url:
        cloud-auth-service: ***********:2014
        model-service: ***********:8085
        alarm-service: ***********:5061
        demand-service: ''
        cetml-service: http://***********:5000
        device-data-service: ***********:5050
        dimension-service: ***********:5092
        eem-common-service: ***********:8094
        notice-service: ***********:5070
        mreport-service: ***********:8070
        video-service: ***********:8082
        only-report: ***********:4000
    notice:
      sender: '综合能源平台'
      sendApp: false
      setting-path: ''
      recordSaveDays: 2  # 已经推送消息记录保存时间
      sendMessage: false
    work-order:
      load-plan-sheet: true # 是否加载巡检/维修计划
      repair:
        check-over-time:
          interval: '-' # 检查维修工单是否超时定时任务，cron格式
      signpoint:
        sign-status-reset-interval: '-'  # 重置签到点状态，每天重置一次
        sign-status-update-interval: '-' # 定时根据工单更新签到点状态
      inspect: # 巡检工单
        check-over-time-notice:
          pre-time: 30 # 工单超时预警提前的时间
          interval: '-' # 检查工单是否超时，并进行通知，cron格式
        check-over-time:
          interval: '-' # 检查巡检工单是否超时，cron格式
        app:
          summary-query-pre-day: 1  # 移动端总览查询提前时间
      maintenance: # 维保工单
        generate-work-order:
          device-work-time: 60000  # 设备运行的时间达到这个值，生成维保工单
          interval: '-' # 根据设备运行时长生成工单定时任务，cron格式
    model-config:
      temp: 6.5
    transformeranalysis:
      cron: '-'
    mes:
      cron: '0 0/1 * * * ?' # 执行间隔，每小时执行一次
      filepath: 'D:/xxxx/energy-automotive/cet-eem-coldoptimizationdatatransport/cet-eem-bll-coldoptimizationdatatransport/target/classes/config/workshipcodemap.txt'
      cycle: 12
      code: '20220803'
      his:
        cron: '-' # 执行间隔，每分钟执行一次
        keep: true
      passpoint:
        cron: '0 * * * * ?' #每天零点或1点执行一次
        url: '**************************************************'
        user: 'postgres'
        password: 'Ceiec4567$%^&'
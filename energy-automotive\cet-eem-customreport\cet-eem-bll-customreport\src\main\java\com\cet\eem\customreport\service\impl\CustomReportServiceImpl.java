package com.cet.eem.customreport.service.impl;

import com.cet.eem.auth.def.EnumCustomReport;
import com.cet.eem.auth.service.NodeManageWithAuthService;
import com.cet.eem.bll.common.dao.node.NodeDao;
import com.cet.eem.bll.common.def.constant.enumeration.subjective.energypackage.AggregationCycle;
import com.cet.eem.bll.common.model.domain.object.physicalquantity.MeasuredbyVo;
import com.cet.eem.bll.common.model.ext.modelentity.EemQueryCondition;
import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.bll.datamaintain.service.ConnectionService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.constant.EnumOperationType;
import com.cet.eem.common.constant.ExcelType;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.file.FileUtils;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.Result;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.model.datalog.TrendSearchListVo;
import com.cet.eem.common.model.datalog.TrendSearchVo;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.common.service.EemDeviceDataService;
import com.cet.eem.common.utils.PoiExcelUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.customreport.common.CustomReportDef;
import com.cet.eem.customreport.model.ReportData;
import com.cet.eem.customreport.model.ReportParam;
import com.cet.eem.customreport.model.ValueAndTime;
import com.cet.eem.customreport.service.CustomReportService;
import com.cet.eem.model.base.SingleModelConditionDTO;
import com.cet.eem.model.tool.QueryResultContentTaker;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.matterhorn.devicedataservice.api.PecCoreConfigRestApi;
import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import com.cet.electric.matterhorn.devicedataservice.common.entity.station.DatalogMeasItemDTO;
import com.cet.electric.model.enums.RoomTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 定制报表业务层
 * @date 2023/11/9
 */

@Service
@Slf4j
public class CustomReportServiceImpl implements CustomReportService {
    @Autowired
    private NodeManageWithAuthService nodeManageBffService;

    @Autowired
    private ConnectionService connectionService;

    @Autowired
    private PecCoreConfigRestApi pecCoreConfigRestApi;

    @Autowired
    private NodeDao nodeDao;

    @Autowired
    private EemDeviceDataService eemDeviceDataService;

    @Override
    public List<ReportData> queryReport(ReportParam reportParam, Long userId, Long projectId) {
        //将查询参数中的报表类型转换成房间类型
        this.convertParam(reportParam);

        //管网设备和测点的映射关系
        Map<BaseVo, List<DatalogMeasItemDTO>> datalogMeasItemDTOMap = new HashMap<>();

        TrendSearchListVo trendSearchListVo = this.queryDeviceAndDataId(reportParam, userId, projectId, datalogMeasItemDTOMap);
        //查定时记录，填充空数据
        Result<List<TrendDataVo>> trendDataResult = eemDeviceDataService.queryDataLogs(trendSearchListVo, true, false, true);
        List<TrendDataVo> trendDataVoList = trendDataResult.getData();
        //根据查询参数获取整个月的时间点序列
        List<Long> timeList = this.getTimeList(reportParam);
        if (CollectionUtils.isEmpty(timeList)) {
            return new ArrayList<>();
        }
        //过滤定时记录
        this.filterDataLogByTime(trendDataVoList, timeList);
        return this.handleReportData(trendDataVoList, timeList, datalogMeasItemDTOMap, reportParam);
    }

    @Override
    public void exportReport(ReportParam reportParam, Long userId, Long projectId) {
        List<ReportData> reportDataList = queryReport(reportParam, userId, projectId);
        String sheetName = reportDataList.get(0).getRoomName();
        String fileName = this.createFileName(reportParam, sheetName);
        //excel中的列数 = 指示值列数 + 头3列
        int colCount = reportDataList.get(0).getIndicateValueList().size() + 3;
        //一个月的天数
        int dayCount = reportDataList.get(0).getDaySumList().size();
        try(Workbook workbook = PoiExcelUtils.createWorkBook(ExcelType.XLS_X);) {
            PoiExcelUtils.createSheet(workbook, sheetName, ((sheet, baseCellStyle, rowIndex) -> {
                List<CellRangeAddress> cellRangeAddressList = new ArrayList<>();
                //表头填充
                rowIndex = this.creatHeaderRow(reportParam, dayCount, sheet, baseCellStyle, cellRangeAddressList);
                //数据填充
                cellRangeAddressList.addAll(this.createDataRow(reportDataList, workbook, baseCellStyle, reportParam.getTimeList().size(), rowIndex));
                //合并单元格
                this.mergeCell(cellRangeAddressList, sheet);
            }), CommonUtils.initList(colCount, 18));
            FileUtils.downloadExcel(GlobalInfoUtils.getHttpResponse(), workbook, fileName, CommonUtils.APPLICATION_MS_EXCEL_07);
        } catch (Exception e) {
            log.error("导出发生异常", e);
            throw new RuntimeException(e);
        }

    }

    /**
     * 查询参数转换，转换为房间类型
     * @param reportParam
     */
    private void convertParam(ReportParam reportParam) {
        if (Objects.equals(reportParam.getRoomType(), EnumCustomReport.POWER_REPORT.getNodeType())) {
            reportParam.setRoomType(RoomTypeEnum.POWER_ROOM.getCode());
        }
        if (Objects.equals(reportParam.getRoomType(), EnumCustomReport.PIPELINE.getNodeType())) {
            reportParam.setRoomType(RoomTypeEnum.PIPE_LINE_ROOM.getCode());
        }
    }

    /**
     * 创建导出文件名
     * @param reportParam
     * @param sheetName
     * @return
     */
    private String createFileName(ReportParam reportParam, String sheetName) {
        String time = TimeUtil.timestamp2LocalDateTime(reportParam.getTimeList().get(0)).format(DateTimeFormatter.ofPattern(TimeUtil.YYYY_MM));
        return sheetName + CustomReportDef.STATISTIC_REPORT + CustomReportDef.H_LINE + time;
    }
    /**
     * 合并的单元格
     * @param cellRangeAddressList
     * @param sheet
     */
    private void mergeCell(List<CellRangeAddress> cellRangeAddressList, Sheet sheet) {
        //过滤掉只有一个单元格的合并
        List<CellRangeAddress> filterCellRangeList = cellRangeAddressList.stream().filter(cellRangeAddress -> !Objects.equals(cellRangeAddress.getFirstRow(), cellRangeAddress.getLastRow()) ||
                !Objects.equals(cellRangeAddress.getFirstColumn(), cellRangeAddress.getLastColumn())).collect(Collectors.toList());
        for (CellRangeAddress cellRangeAddress : filterCellRangeList) {
            sheet.addMergedRegion(cellRangeAddress);
        }

    }

    /**
     * 创建表头，两行时间,第一行为日，第二行为时间点
     * @param reportParam
     * @param dayCount 一个月的天数
     */
    private int creatHeaderRow(ReportParam reportParam, int dayCount, Sheet sheet, CellStyle baseCellStyle, List<CellRangeAddress> cellRangeAddressList) {
        List<String> timeList = new ArrayList<>();
        //合并单元格，前两行的表头有两个合并单元格
        cellRangeAddressList.add(new CellRangeAddress(0,1,0, 1));
        cellRangeAddressList.add(new CellRangeAddress(0, 1, 2, 2));
        int rowIndex = 0;
        int col = 0;
        Row row = sheet.createRow(rowIndex++);
        PoiExcelUtils.createCell(row, col, baseCellStyle, CustomReportDef.POSITION);
        col+=2;
        PoiExcelUtils.createCell(row, col++, baseCellStyle, CustomReportDef.CHECKPOINT);
        int daySize = reportParam.getTimeList().size();
        for (int i = 0; i < daySize; i++) {
            LocalTime localTime = TimeUtil.timestamp2LocalDateTime(reportParam.getTimeList().get(i)).toLocalTime();
            timeList.add(String.valueOf(localTime));
        }
        Row timeRow = sheet.createRow(rowIndex);
        //表头填充
        for (int i = 0; i < dayCount; i++) {
            //第一行填充日
            if (i+1 == dayCount) {
                PoiExcelUtils.createCell(row, col, baseCellStyle, CustomReportDef.TOTAL);
            } else {
                PoiExcelUtils.createCell(row, col, baseCellStyle, (i + 1) + CustomReportDef.DAY);
            }
            //第二行填充每天的时间点
            for (int j = 0; j < timeList.size(); j++) {
                PoiExcelUtils.createCell(timeRow, col + j, baseCellStyle, timeList.get(j));
            }
            //第一行合并单元格
            cellRangeAddressList.add(new CellRangeAddress(row.getRowNum(), row.getRowNum(), col, col + timeList.size() - 1));
            col += timeList.size();
        }
        return ++rowIndex;
    }

    /**
     * excel填充数据行
     * @param reportDataList 报表数据
     * @param workbook
     * @param baseCellStyle
     * @param timePointCount 一天的时间点个数，1个或两个或三个
     * @param rowIndex
     * @return
     */
    private List<CellRangeAddress> createDataRow(List<ReportData> reportDataList, Workbook workbook, CellStyle baseCellStyle, Integer timePointCount, Integer rowIndex) {
        Sheet sheet = workbook.getSheetAt(0);
        //单元格样式-绿色背景色
        CellStyle greenStyle = workbook.createCellStyle();
        greenStyle.cloneStyleFrom(baseCellStyle);
        greenStyle.setFillForegroundColor(IndexedColors.SEA_GREEN.getIndex());
        greenStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        //单元格样式-黄色背景色
        CellStyle yellowStyle = workbook.createCellStyle();
        yellowStyle.cloneStyleFrom(greenStyle);
        yellowStyle.setFillForegroundColor(IndexedColors.LEMON_CHIFFON.getIndex());
        List<CellRangeAddress> cellRangeAddressList = new ArrayList<>();
        //第一列的房间合并单元格
        cellRangeAddressList.add(new CellRangeAddress(rowIndex, rowIndex - 1 + reportDataList.size() * 3, 0, 0));
        for (ReportData reportData : reportDataList) {
            int col = 0;
            Row row = sheet.createRow(rowIndex++);
            //指示值填充
            cellRangeAddressList.add(this.createIndicatorDataRow(reportData, row, col, greenStyle));
            //使用值填充
            this.createUsedDataRow(sheet, reportData, rowIndex++, col, yellowStyle);
            //日合计填充
            cellRangeAddressList.addAll(this.createDaySumDataRow(reportData, sheet.createRow(rowIndex++), col, baseCellStyle, timePointCount));
        }
        return cellRangeAddressList;
    }

    /**
     * 指示值数据单元格创建
     * @param reportData 报表数据
     * @param row
     * @param col
     * @param baseCellStyle
     */
    private CellRangeAddress createIndicatorDataRow(ReportData reportData, Row row, Integer col, CellStyle baseCellStyle) {
        List<ValueAndTime> indicateValueList = reportData.getIndicateValueList();
        col = this.createRoomAndDeviceCell(row, col, baseCellStyle, reportData);
        //设备列合并单元格
        CellRangeAddress cellRangeAddress = new CellRangeAddress(row.getRowNum(), row.getRowNum() + 2, col - 1, col - 1);
        PoiExcelUtils.createCell(row, col++, baseCellStyle, CustomReportDef.INDICATED_VALUE);
        //指示值数据填充
        for (ValueAndTime valueAndTime : indicateValueList) {
            PoiExcelUtils.createCell(row, col++, baseCellStyle, CommonUtils.formatDouble(valueAndTime.getValue()));
        }
        return cellRangeAddress;
    }

    /**
     * 房间和设备填充单元格
     * @param row
     * @param col
     * @param baseCellStyle
     * @param reportData
     * @return
     */
    private Integer createRoomAndDeviceCell(Row row, Integer col, CellStyle baseCellStyle, ReportData reportData) {
        PoiExcelUtils.createCell(row, col++, baseCellStyle, reportData.getRoomName());
        PoiExcelUtils.createCell(row, col++, baseCellStyle, reportData.getDeviceName());
        return col;
    }

    /**
     * 使用值数据单元格创建
     * @param sheet
     * @param reportData 报表数据
     * @param rowIndex
     * @param col
     * @param baseCellStyle
     */
    private void createUsedDataRow(Sheet sheet, ReportData reportData, Integer rowIndex, Integer col, CellStyle baseCellStyle) {
        List<ValueAndTime> usedValueList = reportData.getUsedValueList();
        Row row = sheet.createRow(rowIndex);
        col = createRoomAndDeviceCell(row, col, baseCellStyle, reportData);
        PoiExcelUtils.createCell(row, col++, baseCellStyle, CustomReportDef.USED_VALUE);
        //使用值数据填充
        for (ValueAndTime valueAndTime : usedValueList) {
            PoiExcelUtils.createCell(row, col++, baseCellStyle, CommonUtils.formatDouble(valueAndTime.getValue()));
        }
    }

    /**
     * 日合计数据单元格创建
     * @param reportData 报表数据
     * @param row
     * @param col
     * @param baseCellStyle
     * @param size
     * @return
     */
    private List<CellRangeAddress> createDaySumDataRow(ReportData reportData, Row row, Integer col, CellStyle baseCellStyle, Integer size) {
        List<ValueAndTime> daySumList = reportData.getDaySumList();
        List<CellRangeAddress> cellRangeAddressList = new ArrayList<>();
        //填充房间和设备
        col = this.createRoomAndDeviceCell(row, col, baseCellStyle, reportData);
        PoiExcelUtils.createCell(row, col++, baseCellStyle, CustomReportDef.DAY_TOTAL);
        //日合计数据填充
        for (ValueAndTime valueAndTime : daySumList) {
            for (int i = 0; i < size; i++) {
                PoiExcelUtils.createCell(row, col + i, baseCellStyle, CommonUtils.formatDouble(valueAndTime.getValue()));
            }
            cellRangeAddressList.add(new CellRangeAddress(row.getRowNum(), row.getRowNum(),col,  col + size - 1));
            col += size;
        }
        return cellRangeAddressList;
    }

    /**
     * 处理报表数据，调整数据结构
     * @param trendDataVoList
     * @param timeList
     * @param datalogMeasItemDTOMap
     * @param reportParam
     * @return
     */
    private List<ReportData> handleReportData(List<TrendDataVo> trendDataVoList, List<Long> timeList, Map<BaseVo, List<DatalogMeasItemDTO>> datalogMeasItemDTOMap, ReportParam reportParam) {
        List<ReportData> reportDataList = new ArrayList<>();
        //查询房间名称
        BaseVo room = queryRoomInfo(reportParam.getObjectId());
        //获取管网设备并按照id进行排序
        List<BaseVo>  deviceBaseVo = new ArrayList<>(datalogMeasItemDTOMap.keySet());
        deviceBaseVo.sort(Comparator.comparing(BaseVo::getId));
        //遍历房间下的设备
        for (BaseVo device : deviceBaseVo) {
            ReportData reportData = new ReportData();
            reportData.setRoomName(room.getName());
            reportData.setDeviceName(device.getName());
            reportData.setObjectId(device.getId());
            reportData.setObjectLabel(device.getModelLabel());
            //采集设备id
            Set<Integer> datalogMeasIds = datalogMeasItemDTOMap.get(device).stream().map(DatalogMeasItemDTO::getDeviceId).collect(Collectors.toSet());
            //根据关联的采集设备获取管网设备的定时记录
            List<TrendDataVo> trendDataVoMeasureList = trendDataVoList.stream().filter(trendDataVo1 -> datalogMeasIds.contains(trendDataVo1.getDeviceId().intValue()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(trendDataVoMeasureList)) {
                //如果管网设备关联的多个采集设备都有4000004或10000001的定时记录，则相同时间点的定时记录加和
                List<DatalogValue> datalogValueList = this.sumDataLog(trendDataVoMeasureList);
                //报表中的指示值
                List<ValueAndTime> indicateValueList = datalogValueList.stream().map(datalogValue -> new ValueAndTime(datalogValue.getValue(), datalogValue.getTime()))
                        .collect(Collectors.toList());
                reportData.setIndicateValueList(indicateValueList);
                this.calUsageAndSum(reportData, reportParam);
                reportDataList.add(reportData);
            }
        }
        return reportDataList;
    }

    /**
     * 定时记录相加
     * @param trendDataVoMeasureList 定时记录
     * @return 求和后的定时记录
     */
    private List<DatalogValue> sumDataLog(List<TrendDataVo> trendDataVoMeasureList) {
        List<DatalogValue> datalogValueSumList = trendDataVoMeasureList.get(0).getDataList();
        for (int i = 1; i < trendDataVoMeasureList.size(); i++) {
            List<DatalogValue> datalogValueList = trendDataVoMeasureList.get(i).getDataList();
            //由于之前已经按照时间排序和补点了，这里直接顺序相加
            for (int j = 0; j < datalogValueList.size(); j++) {
                Double addValue = this.addDoubleValue(datalogValueSumList.get(j).getValue(), datalogValueList.get(j).getValue());
                datalogValueSumList.get(j).setValue(addValue);
            }
        }
        return  datalogValueSumList;
    }

    /**
     * 计算使用值和合计值
     * @param reportData
     * @param reportParam
     */
    private void calUsageAndSum(ReportData reportData, ReportParam reportParam) {
        int size = reportParam.getTimeList().size();
        List<Long> nextMontTimeList = reportParam.getTimeList().stream().map(time -> {
            LocalDateTime localDateTime = TimeUtil.timestamp2LocalDateTime(time).plusMonths(1);
            return TimeUtil.localDateTime2timestamp(localDateTime);
        }).collect(Collectors.toList());
        List<ValueAndTime> indicateValueList = reportData.getIndicateValueList();
        List<ValueAndTime> usedValueList = new ArrayList<>();
        List<ValueAndTime> daySumList = new ArrayList<>();
        //使用值的合计值计算
        List<Double> usedSumList = new ArrayList<>(Collections.nCopies(size, 0D));
        Double daySum = null;
        Double monthSum = 0D;
        int length = indicateValueList.size()-size+1;
        for (int i = 1; i < length; i++) {
            //计算两个定时记录之间的差值
            Double usedValue = CommonUtils.calcDouble(indicateValueList.get(i).getValue(), indicateValueList.get(i-1).getValue(), EnumOperationType.SUBTRACT.getId());
            //计算日合计
            daySum = this.addDoubleValue(usedValue, daySum);
            usedValueList.add(new ValueAndTime(usedValue, indicateValueList.get(i-1).getLogTime()));
            int timePoint = usedValueList.size() % size;
            //使用值的相同时间点的合计值,如果计算数据有一方为null，求和不应该为空
            usedSumList.set(timePoint, this.addDoubleValue(usedSumList.get(timePoint), usedValue));
            //如果是当天的最后一个时间点，则计算当天的日合计值
            if (timePoint == 0) {
                daySumList.add(new ValueAndTime(daySum, indicateValueList.get(i - size).getLogTime()));
                daySum = null;
            }
        }
        //使用量的合计值添加到最后,合计值的时间戳传下个月1号的时间点
        for (int i = 1; i < nextMontTimeList.size(); i++) {
            usedValueList.add(new ValueAndTime(usedSumList.get(i), nextMontTimeList.get(i)));
        }
        //根据取余运算，get(0)中的使用量合计值是最后一个时间点的合计值
        usedValueList.add(new ValueAndTime(usedSumList.get(0), nextMontTimeList.get(0)));
        //日合计的合计值添加到最后，时间戳为下个月1号的第一个时间点
        monthSum = daySumList.stream().map(ValueAndTime::getValue).filter(Objects::nonNull).mapToDouble(Double::doubleValue).sum();
        daySumList.add(new ValueAndTime(monthSum, nextMontTimeList.get(0)));
        reportData.setDaySumList(daySumList);
        reportData.setUsedValueList(usedValueList);
        //指示值去除最后一天参与使用值计算的数据
        indicateValueList.replaceAll(valueAndTime -> {
            if (valueAndTime.getLogTime() >= TimeUtil.getFirstTimeOfNextMonth(LocalDate.from(TimeUtil.timestamp2LocalDateTime(reportParam.getTimeList().get(0)))) ) {
                return new ValueAndTime(null, valueAndTime.getLogTime());
            }
            return valueAndTime;
        });
    }

    /**
     * 数据求和，如果参与计算的数据中存在一个null，则相加的值为另一个非null的值
     * @param a
     * @param b
     * @return
     */
    private Double addDoubleValue(Double a, Double b) {
        if (Objects.isNull(a)) {
            return b;
        } else if (Objects.isNull(b)) {
            return a;
        } else {
            return a + b;
        }
    }

    private BaseVo queryRoomInfo(Long roomId) {
        List<Map<String, Object>> room = nodeDao.queryNodes(Collections.singletonList(new BaseVo(roomId, NodeLabelDef.ROOM)));
        if (CollectionUtils.isEmpty(room)) {
            return new BaseVo();
        }
        return new BaseVo(roomId, NodeLabelDef.ROOM, QueryResultContentTaker.getName(room.get(0)));
    }

    /**
     * 按照时间点过滤定时记录
     * @param trendDataVoList 从数据库查询的原始的定时记录
     * @param timeList 报表需要的时间点列
     */
    private void filterDataLogByTime(List<TrendDataVo> trendDataVoList, List<Long> timeList) {
        Set<Long> timeSet = new HashSet<>(timeList);
        for (TrendDataVo trendDataVo : trendDataVoList) {
            List<DatalogValue> datalogValueList = trendDataVo.getDataList();
            List<DatalogValue> filterDatalogValueList = datalogValueList.stream().filter(datalogValue -> timeSet.contains(datalogValue.getTime()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(filterDatalogValueList)) {
                trendDataVo.setDataList(timeList.stream().map(value -> new DatalogValue(value, null, 0)).collect(Collectors.toList()));
                continue;
            }
            //Set无序，需要重新排序
            filterDatalogValueList.sort(Comparator.comparing(DatalogValue::getTime));
            trendDataVo.setDataList(filterDatalogValueList);
        }
    }

    /**
     * 获取时间列
     * @param reportParam
     * @return
     */
    private List<Long> getTimeList(ReportParam reportParam) {
        if (CollectionUtils.isEmpty(reportParam.getTimeList())) {
            return new ArrayList<>();
        }
        List<Long> queryTimeList = reportParam.getTimeList();
        List<Long> timeList = new ArrayList<>();
        
        //根据查询参数中的时间点数，生成多个时间列合并在一起，再按照时间排序
        for (Long queryStartTime : queryTimeList) {
            //时间列的结束时间在下个月的第二天0点，多一天时间的数据用于计算本月最后一天的使用值
            Long queryEndTime = TimeUtil.localDateTime2timestamp(TimeUtil.getFirstDayOfNextMonth(TimeUtil.timestamp2LocalDateTime(queryStartTime)).plusDays(1));
            timeList.addAll(TimeUtil.getTimeRange(queryStartTime, queryEndTime, AggregationCycle.ONE_DAY.getValue()));
        }
        timeList.sort(Comparator.comparing(value -> value));
        return timeList;
    }


    /**
     * 查询管网设备、采集设备、测点，组装成管网设备和测点的映射关系
     * @param reportParam 前端传过来的定制报表查询参数
     * @param userId 用户id
     * @param projectId 项目id
     * @param datalogMeasItemDTOMap 管网设备和测点的映射关系
     * @return 定时记录查询参数
     */
    private TrendSearchListVo queryDeviceAndDataId(ReportParam reportParam, Long userId, Long projectId, Map<BaseVo, List<DatalogMeasItemDTO>> datalogMeasItemDTOMap) {
        Set<TrendSearchVo> meterConfigs = new HashSet<>();
        //查询管网设备
        List<Map<String, Object>> nodeResult = nodeManageBffService.querySimpleNodeTree(createQueryCondition(reportParam.getObjectId()), userId);
        List<BaseVo> baseVoList = JsonTransferUtils.transferList(nodeResult, BaseVo.class);
        List<BaseVo> deviceList = baseVoList.get(0).getChildren();
        //根据管网设备查询采集设备
        List<MeasuredbyVo> measuredbyVoList = connectionService.queryDeviceByMonitoredNode(deviceList, projectId);
        //按照管网设备分组
        Map<BaseVo, List<MeasuredbyVo>> measuredbyMap = measuredbyVoList.stream().collect(Collectors.groupingBy(measuredbyVo -> {
            Optional<BaseVo> deviceOptional = deviceList.stream().filter(baseVo -> Objects.equals(baseVo.getId(), measuredbyVo.getMonitoredid()) &&
                    Objects.equals(baseVo.getModelLabel(), measuredbyVo.getMonitoredlabel())).findFirst();
            return deviceOptional.orElseGet(() -> new BaseVo(measuredbyVo.getMonitoredid(), measuredbyVo.getMonitoredlabel()));
        }));
        List<Integer> deviceIds = measuredbyVoList.stream().map(measuredbyVo -> measuredbyVo.getMeasuredby().intValue()).collect(Collectors.toList());
        //查测点
        ApiResult<Map<Integer, List<DatalogMeasItemDTO>>> result = pecCoreConfigRestApi.getDeviceDatalogPoints(deviceIds, 0);
        Map<Integer, List<DatalogMeasItemDTO>> dataIdMap = result.getData();
        //管网设备和测点的映射关系
        datalogMeasItemDTOMap.putAll(this.getDeviceAndDateIdMap(measuredbyMap, reportParam, dataIdMap, meterConfigs));

        return this.createDataLogSearchDto(reportParam, meterConfigs);
    }

    /**
     * 获取管网设备与测点的映射关系
     * @param measuredbyMap 管网设备id和采集设备的映射关系
     * @param reportParam 前端传过来的定制报表查询参数
     * @param dataIdMap 采集设备和测点的映射关系
     * @param meterConfigs 定时记录查询参数
     * @return 管网设备和测点的映射关系
     */
    private Map<BaseVo, List<DatalogMeasItemDTO>> getDeviceAndDateIdMap(Map<BaseVo, List<MeasuredbyVo>> measuredbyMap, ReportParam reportParam, Map<Integer, List<DatalogMeasItemDTO>> dataIdMap,
                                       Set<TrendSearchVo> meterConfigs) {
        Map<BaseVo, List<DatalogMeasItemDTO>> datalogMeasItemDTOMap = new HashMap<>();

        Integer dataId = getDataIdByRoomType(reportParam);
        //循环遍历每个管网设备，获得对应的采集设备和测点的映射关系
        for (Map.Entry<BaseVo, List<MeasuredbyVo>> measure : measuredbyMap.entrySet()) {
            List<Long> measuredbyList = measure.getValue().stream().map(MeasuredbyVo::getMeasuredby).collect(Collectors.toList());
            for (Long measuredby : measuredbyList) {

                //采集设备的测点
                List<DatalogMeasItemDTO>  datalogMeasItemList = dataIdMap.get(measuredby.intValue());
                if (CollectionUtils.isEmpty(datalogMeasItemList)) {
                    continue;
                }
                //根据管网层级房间类型获取对应测点，配电室获取4000004，管道房获取1000001
                List<DatalogMeasItemDTO> datalogMeasItemDTOList = datalogMeasItemList.stream().filter(datalogMeasItemDTO ->
                        Objects.equals(datalogMeasItemDTO.getDataId(), dataId)).collect(Collectors.toList());
                //找到对应测点，并添加到定时记录查询参数中
                if (CollectionUtils.isNotEmpty(datalogMeasItemDTOList)) {
                    //原则上一个管网设备只配置一个4000004和1000001测点，如果有多个，则定时记录求和
                    if (datalogMeasItemDTOMap.containsKey(measure.getKey())) {
                        datalogMeasItemDTOMap.get(measure.getKey()).addAll(datalogMeasItemDTOList);
                    } else {
                        datalogMeasItemDTOMap.put(measure.getKey(), datalogMeasItemDTOList);
                    }
                    this.createTrendSearchVo(datalogMeasItemDTOList, meterConfigs);
                }
            }
        }
        return datalogMeasItemDTOMap;
    }

    private void createTrendSearchVo(List<DatalogMeasItemDTO> datalogMeasItemDTOList, Set<TrendSearchVo> meterConfigs) {
        for (DatalogMeasItemDTO datalogMeasItemDTO : datalogMeasItemDTOList) {
            TrendSearchVo trendSearchVo = new TrendSearchVo();
            BeanUtils.copyProperties(datalogMeasItemDTO, trendSearchVo);
            trendSearchVo.setDeviceId(Long.valueOf(datalogMeasItemDTO.getDeviceId()));
            trendSearchVo.setDataId(Long.valueOf(datalogMeasItemDTO.getDataId()));
            trendSearchVo.setLogicalId(datalogMeasItemDTO.getLogicalDeviceIndex());
            meterConfigs.add(trendSearchVo);
        }
    }
    /**
     * 根据房间类型确定查询定时记录的测点
     * @param reportParam
     */
    private Integer getDataIdByRoomType(ReportParam reportParam) {
        Integer dataId = null;
        //配电室找正向有功电能，管道房找水、燃气等
        if (Objects.equals(reportParam.getRoomType(),RoomTypeEnum.POWER_ROOM.getCode())) {
            dataId = 4000004;
        } else {
            dataId = 10000001;
        }
        return dataId;
    }

    /**
     * 构建查询配电室或管道房下的设备的查询参数
     * @param roomId 房间id
     * @return 配电室或管道房下的设备的查询参数
     */
    private EemQueryCondition createQueryCondition(Long roomId) {
        EemQueryCondition queryCondition = new EemQueryCondition();
        queryCondition.setRootID(roomId);
        queryCondition.setRootLabel(NodeLabelDef.ROOM);
        //只查询开关柜或一段线，管道这两种设备类型
        List<SingleModelConditionDTO> singleModelConditionDTOS = Arrays.asList(new SingleModelConditionDTO(NodeLabelDef.LINE_SEGMENT_WITH_SWITCH),
                new SingleModelConditionDTO(NodeLabelDef.PIPELINE));
        queryCondition.setSubLayerConditions(singleModelConditionDTOS);
        return queryCondition;
    }

    /**
     * 构建查询定时记录的参数
     * @param reportParam 前端传过来的定制报表查询参数
     * @param meterConfigs 查询定时记录需要的测点
     * @return 定时记录查询参数
     */
    private TrendSearchListVo createDataLogSearchDto(ReportParam reportParam, Set<TrendSearchVo> meterConfigs) {
        String startTime = TimeUtil.timestamp2LocalDateTime(reportParam.getTimeList().get(0)).format(TimeUtil.LONG_TIME_FORMAT);
        String endTime = TimeUtil.timestamp2LocalDateTime(reportParam.getTimeList().get(0)).plusMonths(1).plusDays(1).format(TimeUtil.LONG_TIME_FORMAT);
        TrendSearchListVo trendSearchListVo = new TrendSearchListVo();
        trendSearchListVo.setMeterConfigs(new ArrayList<>(meterConfigs));
        trendSearchListVo.setStartTime(startTime);
        //结束时间为开始时间加一个月零一天，目的是计算当月最后一天的使用量
        trendSearchListVo.setEndTime(endTime);
        trendSearchListVo.setInterval(30);
        return trendSearchListVo;
    }
}

package com.cet.eem.bll.energysaving.model.cetml;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : ColdMachineResult
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-06-08 09:44
 */
@Getter
@Setter
public class ColdMachineResult {
    @JsonProperty("object_id")
    private Long objectId;
    @JsonProperty("cooling_load_rate")
    private Double coolingLoadRate;
    @JsonProperty("water_supply_temp")
    private Double waterTemp;
    @JsonProperty("cooling_load")
    private Double coolingLoad;

}
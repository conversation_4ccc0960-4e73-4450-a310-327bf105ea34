package com.cet.eem.bll.energysaving.dao.aioptimization.impl;

import com.cet.eem.bll.energysaving.dao.aioptimization.StrategyObjectMapDao;
import com.cet.eem.bll.energysaving.model.aioptimization.StrategyObjectMap;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName : StrategyObjectMapDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-04-19 10:42
 */
@Repository
public class StrategyObjectMapDaoImpl extends ModelDaoImpl<StrategyObjectMap> implements StrategyObjectMapDao {

    @Override
    public List<StrategyObjectMap> queryStrategyObjectMap(List<Long> strategyIds) {
        if (CollectionUtils.isEmpty(strategyIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<StrategyObjectMap> wrapper = LambdaQueryWrapper.of(StrategyObjectMap.class);
        wrapper.in(StrategyObjectMap::getStrategyId, strategyIds);
        List<StrategyObjectMap> strategyObjectMaps = this.selectList(wrapper);
        if (CollectionUtils.isEmpty(strategyObjectMaps)) {
            return Collections.emptyList();
        }
        return strategyObjectMaps;
    }
}
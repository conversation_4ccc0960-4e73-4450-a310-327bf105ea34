package com.cet.eem.bll.energysaving.model.aioptimization;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.bll.energysaving.model.def.ColdOptimizationLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : AiStartStopStrategy
 * @Description : 智能启停策略
 * <AUTHOR> jiangzixuan
 * @Date: 2022-04-15 11:35
 */
@Getter
@Setter
@ModelLabel(ColdOptimizationLabelDef.AI_START_STOP_STRATEGY)
public class AiStartStopStrategy extends BaseEntity {

    /**
     * 启停机时间
     */
    @JsonProperty(ColdOptimizationLabelDef.OPERATION_TIME)
    private Long operationTime;


    /**
     * 策略类型
     */
    @JsonProperty(ColdOptimizationLabelDef.STRATEGY_TYPE)
    private Integer strategyType;
    /**
     * 更新时间
     */
    @JsonProperty(ColumnDef.UPDATE_TIME)
    private Long updateTime;


    /**
     * 制冷系统id
     */
    @JsonProperty(ColdOptimizationLabelDef.REFRIGERATING_SYSTEM_ID)
    private Long refrigeratingSystemId;

    public AiStartStopStrategy() {
        this.modelLabel = ColdOptimizationLabelDef.AI_START_STOP_STRATEGY;
    }

    public AiStartStopStrategy(Long operationTime, Integer strategyType, Long updateTime, Long refrigeratingSystemId) {
        this.operationTime = operationTime;
        this.strategyType = strategyType;
        this.updateTime = updateTime;
        this.refrigeratingSystemId = refrigeratingSystemId;
    }
}
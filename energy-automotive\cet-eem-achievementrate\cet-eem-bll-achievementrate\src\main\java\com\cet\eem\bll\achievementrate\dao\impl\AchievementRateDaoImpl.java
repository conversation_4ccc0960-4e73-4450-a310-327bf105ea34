package com.cet.eem.bll.achievementrate.dao.impl;

import com.cet.eem.bll.achievementrate.dao.AchievementRateDao;
import com.cet.eem.bll.achievementrate.model.Constant;
import com.cet.eem.bll.achievementrate.model.EnergyEfficiencyParam;
import com.cet.eem.bll.achievementrate.model.pojo.AchievementRate;
import com.cet.eem.bll.common.model.domain.subject.energy.EnergyConsumptionPlan;
import com.cet.eem.bll.common.model.domain.subject.energyefficiency.EnergyEfficiencyData;
import com.cet.eem.bll.common.model.domain.subject.production.ObjectCosValue;
import com.cet.eem.bll.common.model.domain.subject.production.UnitObjectCostValue;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.model.BaseEntity;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2024/3/15 16:39
 */
@Service
public class AchievementRateDaoImpl implements AchievementRateDao {
    @Autowired
    ModelServiceUtils modelServiceUtils;


    @Override
    public void writeDatas(List<AchievementRate> rates) {
        // 唯一键字段
        List<String> uniqueFields = Arrays.asList(
                ColumnDef.OBJECTID, ColumnDef.OBJECTLABEL, ColumnDef.LOG_TIME, ColumnDef.AGGREGATION_CYCLE,
                ColumnDef.TYPE, ColumnDef.ENERGY_TYPE, ColumnDef.PRODUCT_TYPE, ColumnDef.INDICATOR_TYPE
        );
        List<String> writeFields = Stream.of(uniqueFields, Arrays.asList(
                Constant.RATE,
                ColumnDef.PROJECT_ID,
                ColumnDef.UPDATE_TIME
        )).flatMap(Collection::stream).collect(Collectors.toList());
        List<List<Object>> writeDataList = new ArrayList<>();
        for (AchievementRate it : rates) {
            List<Object> dataList = new ArrayList<>();
            dataList.add(it.getObjectid());
            dataList.add(it.getObjectlabel());
            dataList.add(it.getLogtime());
            dataList.add(it.getAggregationcycle());
            dataList.add(it.getType());
            dataList.add(it.getEnergytype());
            dataList.add(it.getProducttype());
            dataList.add(it.getIndicatortype());
            dataList.add(it.getRate());
            dataList.add(it.getProjectid());
            dataList.add(it.getUpdatetime());
            writeDataList.add(dataList);
        }
        modelServiceUtils.upsertDataBatch(Constant.ACHIEVEMENTRATE, null, writeFields, writeDataList, uniqueFields);
    }


    @Override
    public List<Map<String, Object>> selectAchievementByType(Integer type) {
        QueryCondition build = QueryConditionBuilder.of(Constant.ACHIEVEMENTRATE)
                .eq(ColumnDef.TYPE,type)
                .limit(Constant.ZERO_INT, Constant.ONE)
                .removeOrderById()
                .orderBy(ColumnDef.LOG_TIME).build();
        return modelServiceUtils.query(build);
    }

    @Override
    public List<AchievementRate> querAchievementRates(LocalDateTime st, LocalDateTime end, Integer cycle, List<Integer> energyTypes, List<Long> effSetIds,
                                                      Integer type, List<Integer> productTypes, List<BaseVo> nodes) {
        if (CollectionUtils.isEmpty(nodes)) {
            return Collections.emptyList();
        }
        QueryConditionBuilder<BaseEntity> builder = QueryConditionBuilder.of(Constant.ACHIEVEMENTRATE);
        Map<String, List<BaseVo>> nodeMap = nodes.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        int group = 1;
        builder.composeMethod(true)
                .orderBy(ColumnDef.LOG_TIME)
                .removeOrderById();
        for (Map.Entry<String, List<BaseVo>> entry : nodeMap.entrySet()) {
            List<BaseVo> baseVos = entry.getValue();
            List<Long> ids = baseVos.stream().map(BaseVo::getId).distinct().collect(Collectors.toList());
            builder.eq(ColumnDef.C_OBJECT_Label, entry.getKey(), group)
                    .in(ColumnDef.C_OBJECT_ID, ids, group)
                    .ge(ColumnDef.LOG_TIME, st, group)
                    .lt(ColumnDef.LOG_TIME, end, group)
                    .eq(ColumnDef.AGGREGATION_CYCLE, cycle, group)
                    .in(ColumnDef.ENERGY_TYPE, energyTypes, group)
                    .in(Constant.INDICATORTYPE, effSetIds, group)
                    .eq(ColumnDef.TYPE, type, group)
                    .in(ColumnDef.PRODUCT_TYPE, productTypes, group);
            group++;
        }
        return modelServiceUtils.query(builder.build(),AchievementRate.class);
    }

    @Override
    public List<ObjectCosValue> queryObjectCostValues(LocalDateTime st, LocalDateTime end, Integer cycle, List<Integer> energyType, List<BaseVo> nodes) {
        QueryConditionBuilder<BaseEntity> builder = QueryConditionBuilder.of(ModelLabelDef.OBJECT_COST_VALUE);
        if (CollectionUtils.isEmpty(nodes)) {
            builder.ge(ColumnDef.LOG_TIME, st).lt(ColumnDef.LOG_TIME, end).in(ColumnDef.ENERGY_TYPE, energyType).eq(ColumnDef.AGGREGATION_CYCLE, cycle);
            return modelServiceUtils.query(builder.build(), ObjectCosValue.class);
        }
        Map<String, List<BaseVo>> nodeMap = nodes.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        int group = 1;
        builder.composeMethod(true)
                .orderBy(ColumnDef.LOG_TIME)
                .removeOrderById();
        for (Map.Entry<String, List<BaseVo>> entry : nodeMap.entrySet()) {
            List<BaseVo> baseVos = entry.getValue();
            List<Long> ids = baseVos.stream().map(BaseVo::getId).distinct().collect(Collectors.toList());
            builder.eq(ColumnDef.C_OBJECT_Label, entry.getKey(), group)
                    .in(ColumnDef.C_OBJECT_ID, ids, group)
                    .ge(ColumnDef.LOG_TIME, st, group)
                    .lt(ColumnDef.LOG_TIME, end, group)
                    .in(ColumnDef.ENERGY_TYPE, energyType, group)
                    .eq(ColumnDef.AGGREGATION_CYCLE, cycle, group);
            group++;
        }
        return modelServiceUtils.query(builder.build(), ObjectCosValue.class);
    }

    @Override
    public LambdaQueryWrapper<EnergyConsumptionPlan> getPlanWrapper(LocalDateTime st, LocalDateTime end, Integer cycle, List<BaseVo> nodes, Integer energyType) {
        LambdaQueryWrapper<EnergyConsumptionPlan> wrapper = LambdaQueryWrapper.of(EnergyConsumptionPlan.class);
        if (CollectionUtils.isEmpty(nodes)) {
            if (Objects.nonNull(energyType)) {
                wrapper.eq(EnergyConsumptionPlan::getEnergyType, energyType);
            }
            return wrapper.ge(EnergyConsumptionPlan::getLogtime, st).lt(EnergyConsumptionPlan::getLogtime, end).eq(EnergyConsumptionPlan::getAggregationCycle, cycle);
        }
        Map<String, List<BaseVo>> nodeMap = nodes.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        for (Map.Entry<String, List<BaseVo>> entry : nodeMap.entrySet()) {
            List<BaseVo> baseVos = entry.getValue();
            List<Long> ids = baseVos.stream().map(BaseVo::getId).distinct().collect(Collectors.toList());
            wrapper.or(or -> {
                or.eq(EnergyConsumptionPlan::getObjectLabel, entry.getKey())
                        .in(EnergyConsumptionPlan::getObjectId, ids)
                        .ge(EnergyConsumptionPlan::getLogtime, st)
                        .lt(EnergyConsumptionPlan::getLogtime, end)
                        .eq(EnergyConsumptionPlan::getAggregationCycle, cycle);
                if (Objects.nonNull(energyType)) {
                    wrapper.eq(EnergyConsumptionPlan::getEnergyType, energyType);
                }
            });
        }
        return wrapper;
    }

    @Override
    public LambdaQueryWrapper<EnergyEfficiencyData> getEffWrapper(EnergyEfficiencyParam param) {
        LambdaQueryWrapper<EnergyEfficiencyData> wrapper = LambdaQueryWrapper.of(EnergyEfficiencyData.class);
        if (CollectionUtils.isEmpty(param.getNodes())) {
            if (Objects.nonNull(param.getEnergType())) {
                wrapper.eq(EnergyEfficiencyData::getEnergyType, param.getEnergType());
            }
            if (Objects.nonNull(param.getEffSetId())) {
                wrapper.eq(EnergyEfficiencyData::getEnergyEfficiencySetId, param.getEffSetId());
            }
            if (Objects.nonNull(param.getProductType())) {
                wrapper.eq(EnergyEfficiencyData::getProductType, param.getProductType());
            }
            return wrapper.ge(EnergyEfficiencyData::getLogTime, param.getSt()).lt(EnergyEfficiencyData::getLogTime, param.getEnd())
                    .eq(EnergyEfficiencyData::getAggregationCycle, param.getCycle());
        }
        Map<String, List<BaseVo>> nodeMap = param.getNodes().stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        for (Map.Entry<String, List<BaseVo>> entry : nodeMap.entrySet()) {
            List<BaseVo> baseVos = entry.getValue();
            List<Long> ids = baseVos.stream().map(BaseVo::getId).distinct().collect(Collectors.toList());
            wrapper.or(or -> {
                or.eq(EnergyEfficiencyData::getObjectLabel, entry.getKey())
                        .in(EnergyEfficiencyData::getObjectId, ids)
                        .ge(EnergyEfficiencyData::getLogTime, param.getSt())
                        .lt(EnergyEfficiencyData::getLogTime, param.getEnd())
                        .eq(EnergyEfficiencyData::getAggregationCycle, param.getCycle());
                if (Objects.nonNull(param.getEnergType())) {
                    wrapper.eq(EnergyEfficiencyData::getEnergyType, param.getEnergType());
                }
                if (Objects.nonNull(param.getEffSetId())) {
                    wrapper.eq(EnergyEfficiencyData::getEnergyEfficiencySetId, param.getEffSetId());
                }
                if (Objects.nonNull(param.getProductType())) {
                    wrapper.eq(EnergyEfficiencyData::getProductType, param.getProductType());
                }
            });
        }
        return wrapper;
    }

    @Override
    public LambdaQueryWrapper<UnitObjectCostValue> getUnitEffWrapper(EnergyEfficiencyParam param) {
        LambdaQueryWrapper<UnitObjectCostValue> wrapper = LambdaQueryWrapper.of(UnitObjectCostValue.class);
        if (CollectionUtils.isEmpty(param.getNodes())) {
            if (Objects.nonNull(param.getEnergType())) {
                wrapper.eq(UnitObjectCostValue::getEnergyType, param.getEnergType());
            }
            if (Objects.nonNull(param.getProductType())) {
                wrapper.eq(UnitObjectCostValue::getProductType, param.getProductType());
            }
            return wrapper.ge(UnitObjectCostValue::getLogTime, param.getSt()).lt(UnitObjectCostValue::getLogTime, param.getEnd())
                    .eq(UnitObjectCostValue::getAggregationCycle, param.getCycle());
        }
        Map<String, List<BaseVo>> nodeMap = param.getNodes().stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        for (Map.Entry<String, List<BaseVo>> entry : nodeMap.entrySet()) {
            List<BaseVo> baseVos = entry.getValue();
            List<Long> ids = baseVos.stream().map(BaseVo::getId).distinct().collect(Collectors.toList());
            wrapper.or(or -> {
                or.eq(UnitObjectCostValue::getObjectLabel, entry.getKey())
                        .in(UnitObjectCostValue::getObjectId, ids)
                        .ge(UnitObjectCostValue::getLogTime, param.getSt())
                        .lt(UnitObjectCostValue::getLogTime, param.getEnd())
                        .eq(UnitObjectCostValue::getAggregationCycle, param.getCycle());
                if (Objects.nonNull(param.getEnergType())) {
                    wrapper.eq(UnitObjectCostValue::getEnergyType, param.getEnergType());
                }
                if (Objects.nonNull(param.getProductType())) {
                    wrapper.eq(UnitObjectCostValue::getProductType, param.getProductType());
                }
            });
        }
        return wrapper;
    }
}

package com.cet.eem.bll.compressoroptimization.dao.impl;

import com.cet.eem.bll.compressoroptimization.dao.CompressorConfigDao;
import com.cet.eem.bll.compressoroptimization.model.config.CompressorConfig;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * @ClassName : CompressorConfigDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-24 14:27
 */
@Repository
public class CompressorConfigDaoImpl extends ModelDaoImpl<CompressorConfig> implements CompressorConfigDao {

    @Override
    public CompressorConfig queryCompressorConfig(Long systemId, Long projectId) {
        if (Objects.nonNull(systemId)) {
            LambdaQueryWrapper<CompressorConfig> wrapper = LambdaQueryWrapper.of(CompressorConfig.class);
            wrapper.eq(CompressorConfig::getSystemId, systemId);
            List<CompressorConfig> compressorSystems = this.selectList(wrapper);
            if (CollectionUtils.isNotEmpty(compressorSystems)) {
                return compressorSystems.get(0);
            }
        } else {
            LambdaQueryWrapper<CompressorConfig> wrapper = LambdaQueryWrapper.of(CompressorConfig.class);
            wrapper.eq(CompressorConfig::getProjectId, projectId);
            List<CompressorConfig> compressorSystems = this.selectList(wrapper);
            if (CollectionUtils.isNotEmpty(compressorSystems)) {
                return compressorSystems.get(0);
            }

        }
        return null;
    }
}
package com.cet.eem.bll.energysaving.model.dataentryquery;

import com.cet.eem.common.model.datalog.DataLogData;
import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : TotalSystemPowerEntry
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-14 14:01
 */
@Getter
@Setter
public class TotalSystemPowerEntry {
    private Long projectId;
    private Long systemId;
    private List<ColdMachineVo> coldMachineVos;
    /**
     * 温度预测值
     */
    private List<DatalogValue> tempPredict;
    /**
     * 湿度预测值
     */
    private List<DatalogValue> humidityPredict;
    /**
     * 总冷负荷值
     */
    private List<DatalogValue> totalColdLoad;

    /**
     * 总冷负荷需求预测值
     */
    private List<DatalogValue> totalColdLoadPredict;
    /**
     * 温度实际值
     */
    private List<DatalogValue>  tempActual;
    /**
     * 湿度实际值
     */
    private List<DatalogValue>  humidityActual;
}
package com.cet.eem.bll.energysaving.model.cetml;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : OptimizeControlResultReturn
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-06-08 10:04
 */
@Getter
@Setter
public class OptimizeControlResultReturn {
    @JsonProperty("project_id")
    private Long projectId;
    @JsonProperty("optimize_control_results")
    private List<OptimizeControlResult> optimizeControlResults;
    
}
package com.cet.eem.depletion.model.Constant;



public enum EnumEnergyType {
    ELECTRICITY(2, "电", "kWh"),
    WATER(1, "水", "m³"),
    HEAT(46, "热量", "GJ"),
    NATURALGAS(15, "天然气", "m³"),
    COLD(24, "冷量", "J"),
    COMPRESSEDAIR(16, "压缩空气", "m³"),
    GAS(14, "燃气", "m³");



    private Integer energyType;
    private String energyName;
    private String unit;

    private EnumEnergyType(Integer energyType, String energyName, String unit) {
        this.energyType = energyType;
        this.energyName = energyName;
        this.unit = unit;
    }

    public static String getEnergyName(Integer energyType){
        EnumEnergyType[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            EnumEnergyType status = var1[var3];
            if (status.energyType == energyType) {
                return status.energyName;
            }
        }
        return null;
    }

    public static String getUnit(Integer energyType){
        EnumEnergyType[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            EnumEnergyType status = var1[var3];
            if (status.energyType == energyType) {
                return status.unit;
            }
        }
        return null;
    }

    private EnumEnergyType() {
    }

    public Integer getEnergyType() {return this.energyType; }
    public String getEnergyName() {return this.energyName; }
    public String getUnit() {return this.unit; }
}

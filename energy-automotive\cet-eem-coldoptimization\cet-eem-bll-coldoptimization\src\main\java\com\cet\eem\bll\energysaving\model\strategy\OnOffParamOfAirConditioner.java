package com.cet.eem.bll.energysaving.model.strategy;

import com.cet.eem.model.model.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : OnOffParamOfAirConditioner
 * @Description : 末端空调开关机参数
 * <AUTHOR> jiangzixuan
 * @Date: 2022-04-13 16:41
 */
@Getter
@Setter
public class OnOffParamOfAirConditioner extends BaseEntity {
    /**
     * 时间
     */
    private Long time;
    /**
     * 状态，true是开机，false是关机
     */
    private Boolean status;
    /**
     * 冷量需求
     */
    private Double coolingDemand;
    /**
     * 冷量提供预测
     */
    private Double coolingPredict;
    /**
     * 实际提供的冷量
     */
    private Double coolingActual;
    private String objectLabel;
    private Long objectId;
}
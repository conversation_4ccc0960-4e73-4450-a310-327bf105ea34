package com.cet.eem.bll.achievementrate.service.impl;

import com.cet.eem.bll.achievementrate.dao.EnergyEfficiencyDataPlanDao;
import com.cet.eem.bll.achievementrate.dao.GivenEnergyConsumptionPlanDao;
import com.cet.eem.bll.achievementrate.dao.ObjectCostValuePlanDao;
import com.cet.eem.bll.achievementrate.dao.UnitObjectCostValuePlanDao;
import com.cet.eem.bll.achievementrate.model.data.EnergyEfficiencyDataPlan;
import com.cet.eem.bll.achievementrate.model.data.ObjectCostValuePlan;
import com.cet.eem.bll.achievementrate.model.data.UnitObjectCostValuePlan;
import com.cet.eem.bll.achievementrate.service.AchievementRateDataHandleService;
import com.cet.eem.bll.common.config.ProductionDataConfig;
import com.cet.eem.bll.common.dao.energy.EnergyConvertDao;
import com.cet.eem.bll.common.dao.node.NodeDao;
import com.cet.eem.bll.common.dao.product.ProductionDataDao;
import com.cet.eem.bll.common.dao.product.UnitCostDao;
import com.cet.eem.bll.common.dao.project.ProductDao;
import com.cet.eem.bll.common.log.service.CommonUtilsService;
import com.cet.eem.bll.common.model.domain.subject.energy.ConvertedStandardCoalCoef;
import com.cet.eem.bll.common.model.domain.subject.energy.EnergyConsumptionPlan;
import com.cet.eem.bll.common.model.domain.subject.energyefficiency.EnergyEfficiencySetVo;
import com.cet.eem.bll.common.model.domain.subject.energyefficiency.KpiSetVo;
import com.cet.eem.bll.common.model.domain.subject.generalrules.UnnaturalSetVo;
import com.cet.eem.bll.common.service.EnergyConvertService;
import com.cet.eem.bll.common.service.EnergyUnitService;
import com.cet.eem.bll.common.service.ProjectEnergyTypeService;
import com.cet.eem.bll.common.service.UnnaturalTimeService;
import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.bll.energy.config.DataEntryConfig;
import com.cet.eem.bll.energy.dao.EnergyEfficiencyDataDao;
import com.cet.eem.bll.energy.dao.EnergyEfficiencySetDao;
import com.cet.eem.bll.energy.dao.KpiSetDao;
import com.cet.eem.bll.energy.dao.consumption.EnergyConsumptionDao;
import com.cet.eem.bll.energy.dao.consumption.EnergyConsumptionPlanDao;
import com.cet.eem.bll.energy.dao.cost.CostCheckNodeConfigDao;
import com.cet.eem.bll.energy.dao.cost.FeeSchemeDao;
import com.cet.eem.bll.energy.dao.impl.ProductionPlanDao;
import com.cet.eem.common.constant.EnergyTypeDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.definition.SplitCharDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.model.BaseEntity;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : AchievementRateDataHandleServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-12-07 09:06
 */
@Service
@Slf4j
public class AchievementRateDataHandleServiceImpl implements AchievementRateDataHandleService {
    @Autowired
    EnergyEfficiencyDataPlanDao energyEfficiencyDataPlanDao;
    @Autowired
    ObjectCostValuePlanDao objectCostValuePlanDao;
    @Autowired
    UnitObjectCostValuePlanDao unitObjectCostValuePlanDao;
    @Autowired
    EnergyEfficiencySetDao energyEfficiencySetDao;
    @Autowired
    KpiSetDao kpiSetDao;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    ProjectEnergyTypeService projectEnergyTypeService;
    @Autowired
    CostCheckNodeConfigDao costCheckNodeConfigDao;
    @Autowired
    FeeSchemeDao feeSchemeDao;
    @Resource
    DataEntryConfig dataEntryConfig;
    @Autowired
    NodeDao nodeDao;
    @Autowired
    EnergyConvertService energyConvertService;
    @Autowired
    ProductDao productDao;
    @Autowired
    EnergyUnitService energyUnitService;
    @Autowired
    EnergyConsumptionDao energyConsumptionDao;
    @Autowired
    EnergyConsumptionPlanDao energyConsumptionPlanDao;
    @Autowired
    UnitCostDao unitCostDao;
    @Autowired
    ProductionDataDao productionDataDao;
    @Autowired
    ProductionPlanDao productionPlanDao;
    @Autowired
    EnergyEfficiencyDataDao energyEfficiencyDataDao;
    @Autowired
    CommonUtilsService commonUtilsService;
    @Autowired
    EnergyConvertDao energyConvertDao;
    @Autowired
    GivenEnergyConsumptionPlanDao givenEnergyConsumptionPlanDao;
    @Autowired
    UnnaturalTimeService unnaturalTimeService;

    @Resource
    protected ProductionDataConfig productionDataConfig;
    private static final String PRODUCTION_SAVE_LOG = "[保存单耗计划数据]";
    private static final String OBJECT_SAVE_LOG = "[保存成本计划数据]";
    private static final String UNIT_OBJECT_SAVE_LOG = "[保存单位成本计划数据]";
    private static final String ENERGY_SAVE_LOG = "[保存能耗计划数据]";
    private static final String SPILT = "(";

    @Override
    public void writeEnergyConsumptionPlanByCycle(List<EnergyConsumptionPlan> dataList, Long projectId) {
//        if (CollectionUtils.isEmpty(dataList)) {
//            return;
//        }
//
//        // 写入能耗数据
//        givenEnergyConsumptionPlanDao.writeEnergyConsumptionPlan(dataList);
//
//        // 统计折标
//        Map<Integer, List<EnergyConsumptionPlan>> energyConsumptionByCycle = dataList.stream()
//                .collect(Collectors.groupingBy(EnergyConsumptionPlan::getAggregationCycle));
//        energyConsumptionByCycle.forEach((cycle, val) -> {
//            Set<BaseVo> nodes = val.stream().map(it -> new BaseVo(it.getObjectId(), it.getObjectLabel())).collect(Collectors.toSet());
//            OptionalLong min = val.stream().filter(it -> Objects.nonNull(it.getLogtime())).mapToLong(EnergyConsumptionPlan::getLogtime).min();
//            OptionalLong max = val.stream().filter(it -> Objects.nonNull(it.getLogtime())).mapToLong(EnergyConsumptionPlan::getLogtime).max();
//            if (min.isPresent() && max.isPresent()) {
//                summarizedByCoef(min.getAsLong(), max.getAsLong() + 1, projectId, nodes, cycle);
//            }
//
//        });
    }

    @Override
    public void writeEffDataByCycle(List<EnergyEfficiencyDataPlan> productionDataList, Long projectId) {
        if (CollectionUtils.isEmpty(productionDataList)) {
            return;
        }
        Map<Integer, List<EnergyEfficiencyDataPlan>> productionDataMap = productionDataList.stream().collect(Collectors.groupingBy(EnergyEfficiencyDataPlan::getAggregationCycle));
        productionDataMap.forEach((cycle, val) -> {
            writeEffDataDataList(val, cycle, projectId);
        });

    }

    private void calculateEnergyEfficiencyDataPlan(List<EnergyEfficiencyDataPlan> dataList, Map<Integer, Double> coefMap, Integer targetEnergyType) {
        //多节点的情况，节点分组， 周期已经分组了，时间分组
        Map<BaseVo, List<EnergyEfficiencyDataPlan>> planMap = dataList.stream().collect(Collectors.groupingBy(it -> new BaseVo(it.getObjectId(), it.getObjectLabel())));
        for (Map.Entry<BaseVo, List<EnergyEfficiencyDataPlan>> entry : planMap.entrySet()) {
            Map<Long, List<EnergyEfficiencyDataPlan>> planDataMap = entry.getValue().stream().collect(Collectors.groupingBy(EnergyEfficiencyDataPlan::getLogTime));

            planDataMap.forEach((time, val) -> {
                double total = val.stream().mapToDouble(it -> {
                    Double coef = coefMap.get(it.getEnergyType());
                    if (coef == null) {
                        return 0;
                    }

                    return coef * (it.getValue() == null ? 0D : it.getValue());
                }).sum();
                if (Objects.equals(total, 0.0)) {
                    return;
                }
                Optional<EnergyEfficiencyDataPlan> any = val.stream()
                        .filter(it -> it.getLogTime().equals(time) && Objects.equals(it.getEnergyType(), targetEnergyType)).findFirst();
                if (!any.isPresent()) {
                    return;
                }
                EnergyEfficiencyDataPlan obj = any.get();
                obj.setValue(total);
                obj.setLogTime(time);

            });
        }

    }

    @Override
    public void writeObjectCostValuePlanByCycle(List<ObjectCostValuePlan> dataList, Long projectId) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        Map<Integer, List<ObjectCostValuePlan>> productionDataMap = dataList.stream().collect(Collectors.groupingBy(ObjectCostValuePlan::getAggregationCycle));
        productionDataMap.forEach((cycle, val) -> {
            calculateObjectCostValuePlanData(val, cycle);
            writeObjectCostValuePlanaList(val, productionDataConfig.getAggregationCycleChainList(cycle), projectId);
        });
    }

    private void calculateObjectCostValuePlanData(List<ObjectCostValuePlan> dataList, Integer cycle) {
        //多节点的情况，节点分组， 周期已经分组了，再按时间分组
        Map<BaseVo, List<ObjectCostValuePlan>> planMap = dataList.stream().collect(Collectors.groupingBy(it -> new BaseVo(it.getObjectId(), it.getObjectLabel())));
        for (Map.Entry<BaseVo, List<ObjectCostValuePlan>> entry : planMap.entrySet()) {
            Map<Long, List<ObjectCostValuePlan>> planDataMap = entry.getValue().stream().collect(Collectors.groupingBy(ObjectCostValuePlan::getLogTime));

            planDataMap.forEach((time, val) -> {
                double total = val.stream().mapToDouble(it -> {
                    if (Objects.equals(it.getEnergyType(), EnergyTypeDef.STANDARD_COAL)) {
                        return 0;
                    }

                    return (it.getValue() == null ? 0D : it.getValue());
                }).sum();
                Optional<ObjectCostValuePlan> any = val.stream()
                        .filter(it -> it.getLogTime().equals(time) && it.getEnergyType() == EnergyTypeDef.STANDARD_COAL).findFirst();
                if (!any.isPresent()) {
                    return;
                }
                ObjectCostValuePlan obj = any.get();

                obj.setObjectId(entry.getKey().getId());
                obj.setObjectLabel(entry.getKey().getModelLabel());
                obj.setValue(total);
                obj.setEnergyType(EnergyTypeDef.STANDARD_COAL);
                obj.setAggregationCycle(cycle);
                obj.setLogTime(time);

            });
        }

    }

    private void calculateUnitObjectCostValuePlanData(List<UnitObjectCostValuePlan> dataList) {
        //多节点的情况，节点分组， 周期已经分组了，再按产品类型分组，再按时间分组
        Map<BaseVo, List<UnitObjectCostValuePlan>> planMap = dataList.stream().collect(Collectors.groupingBy(it -> new BaseVo(it.getObjectId(), it.getObjectLabel())));
        for (Map.Entry<BaseVo, List<UnitObjectCostValuePlan>> entry : planMap.entrySet()) {
            Map<Integer, List<UnitObjectCostValuePlan>> planDataMap = entry.getValue().stream().collect(Collectors.groupingBy(UnitObjectCostValuePlan::getProductType));
            for (Map.Entry<Integer, List<UnitObjectCostValuePlan>> entryData : planDataMap.entrySet()) {
                Map<Long, List<UnitObjectCostValuePlan>> timeDataMap = entryData.getValue().stream().collect(Collectors.groupingBy(UnitObjectCostValuePlan::getLogTime));

                timeDataMap.forEach((time, val) -> {
                    double total = val.stream().mapToDouble(it -> {
                        if (Objects.equals(it.getEnergyType(), EnergyTypeDef.STANDARD_COAL)) {
                            return 0;
                        }

                        return (it.getValue() == null ? 0D : it.getValue());
                    }).sum();
                    Optional<UnitObjectCostValuePlan> any = val.stream()
                            .filter(it -> it.getLogTime().equals(time) && it.getEnergyType() == EnergyTypeDef.STANDARD_COAL).findFirst();
                    if (!any.isPresent()) {
                        return;
                    }
                    UnitObjectCostValuePlan obj = any.get();
                    obj.setValue(total);


                });
            }

        }

    }

    @Override
    public void writeUnitObjectCostValuePlanByCycle(List<UnitObjectCostValuePlan> dataList, Long projectId) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        dataList.forEach(data->data.setProjectId(projectId));
        Map<Integer, List<UnitObjectCostValuePlan>> productionDataMap = dataList.stream().collect(Collectors.groupingBy(UnitObjectCostValuePlan::getAggregationCycle));
        productionDataMap.forEach((cycle, val) -> {
            calculateUnitObjectCostValuePlanData(val);
            writeUnitObjectCostValuePlanaList(val, cycle, projectId);
        });
    }

    @Override
    public void writeEnergyConsumptionPlanDataByCycle(List<EnergyConsumptionPlan> dataList, Long projectId) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        Map<Integer, List<EnergyConsumptionPlan>> productionDataMap = dataList.stream().collect(Collectors.groupingBy(EnergyConsumptionPlan::getAggregationCycle));
        productionDataMap.forEach((cycle, val) -> {
            writeEnergyConsumptionPlanList(val, productionDataConfig.getAggregationCycleChainList(cycle), projectId);
        });

        // 统计折标
        Map<Integer, List<EnergyConsumptionPlan>> energyConsumptionByCycle = dataList.stream()
                .collect(Collectors.groupingBy(EnergyConsumptionPlan::getAggregationCycle));
        List<ConvertedStandardCoalCoef> coefs = energyConvertDao.queryCoefByProjectId(GlobalInfoUtils.getProjectId());
        Map<Integer, List<ConvertedStandardCoalCoef>> convertMap = coefs.stream().collect(Collectors.groupingBy(ConvertedStandardCoalCoef::getTargetenergytype));
        energyConsumptionByCycle.forEach((cycle, val) -> {
            Set<BaseVo> nodes = val.stream().map(it -> new BaseVo(it.getObjectId(), it.getObjectLabel())).collect(Collectors.toSet());
            OptionalLong min = val.stream().filter(it -> Objects.nonNull(it.getLogtime())).mapToLong(EnergyConsumptionPlan::getLogtime).min();
            OptionalLong max = val.stream().filter(it -> Objects.nonNull(it.getLogtime())).mapToLong(EnergyConsumptionPlan::getLogtime).max();
            if (min.isPresent() && max.isPresent()) {
                convertMap.forEach((targetEnergyType, valueList) -> {
                    Map<Integer, Double> coefMap = valueList.stream().collect(Collectors.toMap(ConvertedStandardCoalCoef::getSourceenergytype, ConvertedStandardCoalCoef::getCoef, (v1, v2) -> v2));
                    summarizedByCoef(min.getAsLong(), max.getAsLong() + 1, projectId, nodes, cycle, coefMap, targetEnergyType);
                });


            }

        });


    }

    private void writeEffDataDataList(List<EnergyEfficiencyDataPlan> effPlanDataList, Integer cycle, Long projectId) {
        log.info("{}入参：effPlanDataList={}， cycles={}， projectId = {}", PRODUCTION_SAVE_LOG,
                JsonTransferUtils.toJSONString(effPlanDataList), JsonTransferUtils.toJSONString(cycle), projectId);
        effPlanDataList.forEach(item->item.setProjectId(GlobalInfoUtils.getProjectId()));
        energyEfficiencyDataPlanDao.writeEnergyEffData(effPlanDataList);
    }

    private void writeObjectCostValuePlanaList(List<ObjectCostValuePlan> objectCostValuePlans, List<Integer> cycles, Long projectId) {

        log.info("{}入参：objectCostValuePlans={}， cycles={}， projectId = {}", OBJECT_SAVE_LOG,
                JsonTransferUtils.toJSONString(objectCostValuePlans), JsonTransferUtils.toJSONString(cycles), projectId);

        List<ObjectCostValuePlan> writeProductionData = summarizedByCycleWithObjectCostValuePlan(objectCostValuePlans, cycles, projectId);
        writeProductionData.forEach(item->item.setProjectId(GlobalInfoUtils.getProjectId()));
        objectCostValuePlanDao.writeObjectCostValuePlan(writeProductionData);
    }

    private void writeEnergyConsumptionPlanList(List<EnergyConsumptionPlan> energyConsumptionPlans, List<Integer> cycles, Long projectId) {

        log.info("{}入参：energyConsumptionPlans={}， cycles={}， projectId = {}", ENERGY_SAVE_LOG,
                JsonTransferUtils.toJSONString(energyConsumptionPlans), JsonTransferUtils.toJSONString(cycles), projectId);

        List<EnergyConsumptionPlan> writeProductionData = summarizedByCycleWithEnergyConsumptionPlan(energyConsumptionPlans, cycles, projectId);
        givenEnergyConsumptionPlanDao.writeEnergyConsumptionPlan(writeProductionData);

    }

    private void writeUnitObjectCostValuePlanaList(List<UnitObjectCostValuePlan> unitObjectCostValuePlans, Integer cycle, Long projectId) {

        log.info("{}入参：unitObjectCostValuePlans={}， cycles={}， projectId = {}", UNIT_OBJECT_SAVE_LOG,
                JsonTransferUtils.toJSONString(unitObjectCostValuePlans), JsonTransferUtils.toJSONString(cycle), projectId);
        unitObjectCostValuePlans.forEach(item->item.setProjectId(GlobalInfoUtils.getProjectId()));
        unitObjectCostValuePlanDao.writeData(unitObjectCostValuePlans);
    }


    /**
     * 根据周期进行汇总数据
     *
     * @param effPlanDataList
     * @param cycles
     * @param projectId
     * @return
     */
    private List<ObjectCostValuePlan> summarizedByCycleWithObjectCostValuePlan(List<ObjectCostValuePlan> effPlanDataList, List<Integer> cycles, Long projectId) {
        if (CollectionUtils.isEmpty(effPlanDataList)) {
            return effPlanDataList;
        }

        if (CollectionUtils.isEmpty(cycles)) {
            return effPlanDataList;
        }

        List<ObjectCostValuePlan> writeProductionData = new ArrayList<>(effPlanDataList);
        Set<BaseVo> nodes = effPlanDataList.stream().map(it -> new BaseVo(it.getObjectId(), it.getObjectLabel())).collect(Collectors.toSet());
        Set<Integer> energyTypes = effPlanDataList.stream().map(ObjectCostValuePlan::getEnergyType).collect(Collectors.toSet());
        for (int i = 1; i < cycles.size(); i++) {
            Integer preCycle = cycles.get(i - 1);
            Integer nextCycle = cycles.get(i);

            // 筛选上一周期的数据
            List<ObjectCostValuePlan> childDataList = effPlanDataList.stream().filter(it ->
                    Objects.equals(it.getAggregationCycle(), preCycle)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(childDataList)) {
                continue;
            }

            // 获取起止时间
            Set<Long> times = new HashSet<>();
            for (ObjectCostValuePlan predict : childDataList) {
                long startTimeByTime = unnaturalTimeService.getStartTimeByTime(projectId, (predict.getLogTime()), nextCycle);
                times.add((startTimeByTime));
            }
            Long startTime = times.stream().min(Comparator.comparing(it -> it)).orElse(null);
            Long endTime = times.stream().max(Comparator.comparing(it -> it)).orElse(null);
            // 说明：下面两个判断没有什么实际意义，但是不加的情况sonarlint一直提示存在空指针情况
            Assert.notNull(startTime, "开始时间不为null！");
            Assert.notNull(endTime, "开始时间不为null！");
            endTime = TimeUtil.addDateTimeByCycle(endTime, nextCycle, 1);
            //指标找下一周期的
            List<ObjectCostValuePlan> nextPlanData = objectCostValuePlanDao.query(startTime, endTime, energyTypes, nodes, preCycle);


            // 将数据库中已经存在的数据和新增的数据进行融合
            List<ObjectCostValuePlan> productionData = combineObjectCostValuePlan(childDataList, nextPlanData);
            // 汇总出下一周期数据
            List<ObjectCostValuePlan> nextProductionDataList = summarizedByCycleWithObjectCostValuePlan(productionData, nodes, startTime, endTime, preCycle, nextCycle, projectId);
            effPlanDataList.addAll(nextProductionDataList);
            writeProductionData.addAll(nextProductionDataList);
        }
        return writeProductionData;
    }

    /**
     * 根据周期进行汇总数据
     *
     * @param effPlanDataList
     * @param cycles
     * @param projectId
     * @return
     */
    private List<EnergyConsumptionPlan> summarizedByCycleWithEnergyConsumptionPlan(List<EnergyConsumptionPlan> effPlanDataList, List<Integer> cycles, Long projectId) {
        if (CollectionUtils.isEmpty(effPlanDataList)) {
            return effPlanDataList;
        }

        if (CollectionUtils.isEmpty(cycles)) {
            return effPlanDataList;
        }

        List<EnergyConsumptionPlan> writeProductionData = new ArrayList<>(effPlanDataList);
        Set<BaseVo> nodes = effPlanDataList.stream().map(it -> new BaseVo(it.getObjectId(), it.getObjectLabel())).collect(Collectors.toSet());
        Set<Integer> energyTypes = effPlanDataList.stream().map(EnergyConsumptionPlan::getEnergyType).collect(Collectors.toSet());
        for (int i = 1; i < cycles.size(); i++) {
            Integer preCycle = cycles.get(i - 1);
            Integer nextCycle = cycles.get(i);

            // 筛选上一周期的数据
            List<EnergyConsumptionPlan> childDataList = effPlanDataList.stream().filter(it ->
                    Objects.equals(it.getAggregationCycle(), preCycle)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(childDataList)) {
                continue;
            }

            // 获取起止时间
            Set<Long> times = new HashSet<>();
            for (EnergyConsumptionPlan predict : childDataList) {
                long startTimeByTime = unnaturalTimeService.getStartTimeByTime(projectId, (predict.getLogtime()), nextCycle);
                times.add((startTimeByTime));
            }
            Long startTime = times.stream().min(Comparator.comparing(it -> it)).orElse(null);
            Long endTime = times.stream().max(Comparator.comparing(it -> it)).orElse(null);
            // 说明：下面两个判断没有什么实际意义，但是不加的情况sonarlint一直提示存在空指针情况
            Assert.notNull(startTime, "开始时间不为null！");
            Assert.notNull(endTime, "开始时间不为null！");
            endTime = TimeUtil.addDateTimeByCycle(endTime, nextCycle, 1);
            //指标找下一周期的
            List<EnergyConsumptionPlan> nextPlanData = givenEnergyConsumptionPlanDao.queryEnergyConsumptionBatch(nodes, startTime, endTime, preCycle, energyTypes);


            // 将数据库中已经存在的数据和新增的数据进行融合
            List<EnergyConsumptionPlan> productionData = combineEnergyConsumptionPlan(childDataList, nextPlanData);
            // 汇总出下一周期数据
            List<EnergyConsumptionPlan> nextProductionDataList = summarizedByCycleWithEnergyPlanData(productionData, nodes, startTime, endTime, preCycle, nextCycle, projectId);
            effPlanDataList.addAll(nextProductionDataList);
            writeProductionData.addAll(nextProductionDataList);
        }
        return writeProductionData;
    }

    /**
     * 根据周期进行汇总数据
     *
     * @param effPlanDataList
     * @param cycles
     * @param projectId
     * @return
     */
    private List<UnitObjectCostValuePlan> summarizedByCycleWithUnitObjectCostValuePlan(List<UnitObjectCostValuePlan> effPlanDataList, List<Integer> cycles, Long projectId) {
        if (CollectionUtils.isEmpty(effPlanDataList)) {
            return effPlanDataList;
        }

        if (CollectionUtils.isEmpty(cycles)) {
            return effPlanDataList;
        }

        List<UnitObjectCostValuePlan> writeProductionData = new ArrayList<>(effPlanDataList);
        Set<BaseVo> nodes = effPlanDataList.stream().map(it -> new BaseVo(it.getObjectId(), it.getObjectLabel())).collect(Collectors.toSet());
        Set<Integer> energyTypes = effPlanDataList.stream().map(UnitObjectCostValuePlan::getEnergyType).collect(Collectors.toSet());
        Set<Integer> productTypes = effPlanDataList.stream().map(UnitObjectCostValuePlan::getProductType).collect(Collectors.toSet());
        for (int i = 1; i < cycles.size(); i++) {
            Integer preCycle = cycles.get(i - 1);
            Integer nextCycle = cycles.get(i);

            // 筛选上一周期的数据
            List<UnitObjectCostValuePlan> childDataList = effPlanDataList.stream().filter(it ->
                    Objects.equals(it.getAggregationCycle(), preCycle)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(childDataList)) {
                continue;
            }

            // 获取起止时间
            Set<Long> times = new HashSet<>();
            for (UnitObjectCostValuePlan predict : childDataList) {
                long startTimeByTime = unnaturalTimeService.getStartTimeByTime(projectId, (predict.getLogTime()), nextCycle);
                times.add((startTimeByTime));
            }
            Long startTime = times.stream().min(Comparator.comparing(it -> it)).orElse(null);
            Long endTime = times.stream().max(Comparator.comparing(it -> it)).orElse(null);
            // 说明：下面两个判断没有什么实际意义，但是不加的情况sonarlint一直提示存在空指针情况
            Assert.notNull(startTime, "开始时间不为null！");
            Assert.notNull(endTime, "开始时间不为null！");
            endTime = TimeUtil.addDateTimeByCycle(endTime, nextCycle, 1);
            //指标找下一周期的
            List<UnitObjectCostValuePlan> nextPlanData = unitObjectCostValuePlanDao.query(startTime, endTime, energyTypes, nodes, preCycle, productTypes);


            // 将数据库中已经存在的数据和新增的数据进行融合
            List<UnitObjectCostValuePlan> productionData = combineUnitObjectCostValuePlan(childDataList, nextPlanData);
            // 汇总出下一周期数据
            List<UnitObjectCostValuePlan> nextProductionDataList = summarizedByCycleWithUnitObjectCostValuePlan(productionData, nodes, startTime, endTime, preCycle, nextCycle, projectId);
            effPlanDataList.addAll(nextProductionDataList);
            writeProductionData.addAll(nextProductionDataList);
        }
        return writeProductionData;
    }

    private List<EnergyEfficiencyDataPlan> summarizedByCycle(List<EnergyEfficiencyDataPlan> oldDataList, Collection<BaseVo> nodes,
                                                             Long startTime, Long endTime,
                                                             Integer preCycle, Integer nextCycle, Long projectId,
                                                             List<EnergyEfficiencySetVo> energyEfficiencySetVos) {
        Map<Long, List<EnergyEfficiencyDataPlan>> productDataMap = oldDataList.stream().collect(Collectors.groupingBy(EnergyEfficiencyDataPlan::getEnergyEfficiencySetId));
        List<EnergyEfficiencyDataPlan> result = new ArrayList<>();
        productDataMap.forEach((productType, val) ->
                result.addAll(summarizedByCycleAndProductType(val, nodes, TimeUtil.timestamp2LocalDateTime(startTime),
                        TimeUtil.timestamp2LocalDateTime(endTime), preCycle, nextCycle, projectId, energyEfficiencySetVos)));

        return result;
    }

    private List<UnitObjectCostValuePlan> summarizedByCycleWithUnitObjectCostValuePlan(List<UnitObjectCostValuePlan> oldDataList, Collection<BaseVo> nodes,
                                                                                       Long startTime, Long endTime,
                                                                                       Integer preCycle, Integer nextCycle, Long projectId) {

        Map<String, List<UnitObjectCostValuePlan>> productDataMap = oldDataList.stream().collect(
                Collectors.groupingBy(it -> it.getProductType() + SplitCharDef.UNDERLINE + it.getEnergyType()));
        List<UnitObjectCostValuePlan> result = new ArrayList<>();
        productDataMap.forEach((productType, val) ->
                result.addAll(summarizedWithUnitObjectCostValuePlan(val, nodes, TimeUtil.timestamp2LocalDateTime(startTime),
                        TimeUtil.timestamp2LocalDateTime(endTime), preCycle, nextCycle, projectId)));

        return result;
    }

    private List<UnitObjectCostValuePlan> summarizedWithUnitObjectCostValuePlan(List<UnitObjectCostValuePlan> oldDataList, Collection<BaseVo> nodes,
                                                                                LocalDateTime startTime, LocalDateTime endTime,
                                                                                Integer preCycle, Integer nextCycle, Long projectId) {
        List<UnitObjectCostValuePlan> newDataList = new ArrayList<>();

        UnnaturalSetVo unnaturalSet = unnaturalTimeService.getUnnaturalSetVo(projectId, nextCycle);
        for (LocalDateTime time = startTime; time.isBefore(endTime); ) {
            LocalDateTime thisTime = time;
            LocalDateTime nextTime = TimeUtil.addDateTimeByCycle(time, nextCycle, 1);

            LocalDateTime st = unnaturalTimeService.convertUnnaturalTime(nextCycle, thisTime, unnaturalSet);
            LocalDateTime et = unnaturalTimeService.convertUnnaturalTime(nextCycle, nextTime, unnaturalSet);


            for (BaseVo node : nodes) {
                Map<String, List<UnitObjectCostValuePlan>> productionDataAndTypeMap = oldDataList.stream()
                        .filter(it -> Objects.equals(it.getObjectId(), node.getId()) &&
                                Objects.equals(it.getObjectLabel(), node.getModelLabel()) &&
                                Objects.equals(it.getAggregationCycle(), preCycle) &&
                                TimeUtil.timestamp2LocalDateTime(it.getLogTime()).isBefore(et) &&
                                !st.isAfter(TimeUtil.timestamp2LocalDateTime(it.getLogTime())))
                        .collect(Collectors.groupingBy(it -> it.getProductType() + SplitCharDef.UNDERLINE + it.getEnergyType()));

                productionDataAndTypeMap.forEach((effId, val) -> {
                    UnitObjectCostValuePlan obj = new UnitObjectCostValuePlan();
                    String[] split = effId.split(SplitCharDef.UNDERLINE);
                    obj.setEnergyType(Integer.parseInt(split[1]));
                    obj.setProductType(Integer.parseInt(split[0]));
                    obj.setAggregationCycle(nextCycle);
                    obj.setLogTime(TimeUtil.localDateTime2timestamp(thisTime));
                    obj.setObjectLabel(node.getModelLabel());
                    obj.setObjectId(node.getId());
                    List<UnitObjectCostValuePlan> data = val.stream().filter(it -> Objects.nonNull(it.getValue())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(data)) {
                        return;
                    }
                    obj.setValue(data.stream().mapToDouble(UnitObjectCostValuePlan::getValue).sum());
                    newDataList.add(obj);
                });
            }

            time = nextTime;
        }

        return newDataList;
    }

    private List<ObjectCostValuePlan> summarizedByCycleWithObjectCostValuePlan(List<ObjectCostValuePlan> oldDataList, Collection<BaseVo> nodes,
                                                                               Long startTime, Long endTime,
                                                                               Integer preCycle, Integer nextCycle, Long projectId) {
        Map<Integer, List<ObjectCostValuePlan>> productDataMap = oldDataList.stream().collect(Collectors.groupingBy(ObjectCostValuePlan::getEnergyType));
        List<ObjectCostValuePlan> result = new ArrayList<>();
        productDataMap.forEach((productType, val) ->
                result.addAll(summarizedWithObjectCostValuePlan(val, nodes, TimeUtil.timestamp2LocalDateTime(startTime),
                        TimeUtil.timestamp2LocalDateTime(endTime), preCycle, nextCycle, projectId)));

        return result;
    }

    private List<EnergyConsumptionPlan> summarizedByCycleWithEnergyPlanData(List<EnergyConsumptionPlan> oldDataList, Collection<BaseVo> nodes,
                                                                            Long startTime, Long endTime,
                                                                            Integer preCycle, Integer nextCycle, Long projectId) {
        Map<Integer, List<EnergyConsumptionPlan>> productDataMap = oldDataList.stream().collect(Collectors.groupingBy(EnergyConsumptionPlan::getEnergyType));
        List<EnergyConsumptionPlan> result = new ArrayList<>();
        productDataMap.forEach((productType, val) ->
                result.addAll(summarizedWithEnergyConsumptionPlan(val, nodes, TimeUtil.timestamp2LocalDateTime(startTime),
                        TimeUtil.timestamp2LocalDateTime(endTime), preCycle, nextCycle, projectId)));

        return result;
    }

    private List<EnergyEfficiencyDataPlan> summarizedByCycleAndProductType(List<EnergyEfficiencyDataPlan> oldDataList, Collection<BaseVo> nodes,
                                                                           LocalDateTime startTime, LocalDateTime endTime,
                                                                           Integer preCycle, Integer nextCycle, Long projectId
            , List<EnergyEfficiencySetVo> energyEfficiencySetVos) {
        List<EnergyEfficiencyDataPlan> newDataList = new ArrayList<>();

        UnnaturalSetVo unnaturalSet = unnaturalTimeService.getUnnaturalSetVo(projectId, nextCycle);

        for (LocalDateTime time = startTime; time.isBefore(endTime); ) {
            LocalDateTime thisTime = time;
            LocalDateTime nextTime = TimeUtil.addDateTimeByCycle(time, nextCycle, 1);

            LocalDateTime st = unnaturalTimeService.convertUnnaturalTime(nextCycle, thisTime, unnaturalSet);
            LocalDateTime et = unnaturalTimeService.convertUnnaturalTime(nextCycle, nextTime, unnaturalSet);


            for (BaseVo node : nodes) {


                Map<Long, List<EnergyEfficiencyDataPlan>> productionDataAndTypeMap = oldDataList.stream()
                        .filter(it -> Objects.equals(it.getObjectId(), node.getId()) &&
                                Objects.equals(it.getObjectLabel(), node.getModelLabel()) &&
                                Objects.equals(it.getAggregationCycle(), preCycle) &&
                                TimeUtil.timestamp2LocalDateTime(it.getLogTime()).isBefore(et) &&
                                !st.isAfter(TimeUtil.timestamp2LocalDateTime(it.getLogTime())))
                        .collect(Collectors.groupingBy(EnergyEfficiencyDataPlan::getEnergyEfficiencySetId));

                productionDataAndTypeMap.forEach((effId, val) -> {
                    EnergyEfficiencyDataPlan obj = new EnergyEfficiencyDataPlan();
                    //需要获得下一周期的指标配置
                    EnergyEfficiencySetVo setVo = energyEfficiencySetVos.stream().filter(it -> Objects.equals(it.getId(), effId)).findAny().orElse(new EnergyEfficiencySetVo());
                    String name = setVo.getName();
                    String queryName;
                    if (name.contains(SPILT)) {
                        String[] split = StringUtils.split(name, SPILT);
                        queryName = split[0];
                    } else {
                        queryName = name;
                    }
                    Optional<EnergyEfficiencySetVo> any = energyEfficiencySetVos.stream().filter(it -> Objects.equals(nextCycle, it.getAggregationcycle())
                            && it.getName().contains(queryName) && Objects.equals(setVo.getProducttype(), it.getProducttype())
                            && Objects.equals(setVo.getEnergytype(), it.getEnergytype()) && Objects.equals(it.getUnittype(), setVo.getUnittype()))
                            .findAny();
                    if (any.isPresent()) {
                        obj.setProductType(setVo.getProducttype());
                        obj.setEnergyType(setVo.getEnergytype());
                        obj.setEnergyEfficiencySetId(any.get().getId());
                        obj.setAggregationCycle(nextCycle);
                        obj.setLogTime(TimeUtil.localDateTime2timestamp(thisTime));
                        obj.setObjectLabel(node.getModelLabel());
                        obj.setObjectId(node.getId());
                        List<EnergyEfficiencyDataPlan> data = val.stream().filter(it -> Objects.nonNull(it.getValue())).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(data)) {
                            return;
                        }
                        obj.setValue(data.stream().mapToDouble(EnergyEfficiencyDataPlan::getValue).sum());
                        newDataList.add(obj);
                    }

                });
            }

            time = nextTime;
        }

        return newDataList;
    }

    private List<ObjectCostValuePlan> summarizedWithObjectCostValuePlan(List<ObjectCostValuePlan> oldDataList, Collection<BaseVo> nodes,
                                                                        LocalDateTime startTime, LocalDateTime endTime,
                                                                        Integer preCycle, Integer nextCycle, Long projectId) {
        List<ObjectCostValuePlan> newDataList = new ArrayList<>();

        UnnaturalSetVo unnaturalSet = unnaturalTimeService.getUnnaturalSetVo(projectId, nextCycle);
        for (LocalDateTime time = startTime; time.isBefore(endTime); ) {
            LocalDateTime thisTime = time;
            LocalDateTime nextTime = TimeUtil.addDateTimeByCycle(time, nextCycle, 1);

            LocalDateTime st = unnaturalTimeService.convertUnnaturalTime(nextCycle, thisTime, unnaturalSet);
            LocalDateTime et = unnaturalTimeService.convertUnnaturalTime(nextCycle, nextTime, unnaturalSet);


            for (BaseVo node : nodes) {
                Map<Integer, List<ObjectCostValuePlan>> productionDataAndTypeMap = oldDataList.stream()
                        .filter(it -> Objects.equals(it.getObjectId(), node.getId()) &&
                                Objects.equals(it.getObjectLabel(), node.getModelLabel()) &&
                                Objects.equals(it.getAggregationCycle(), preCycle) &&
                                TimeUtil.timestamp2LocalDateTime(it.getLogTime()).isBefore(et) &&
                                !st.isAfter(TimeUtil.timestamp2LocalDateTime(it.getLogTime())))
                        .collect(Collectors.groupingBy(ObjectCostValuePlan::getEnergyType));

                productionDataAndTypeMap.forEach((effId, val) -> {
                    ObjectCostValuePlan obj = new ObjectCostValuePlan();
                    obj.setEnergyType(effId);
                    obj.setAggregationCycle(nextCycle);
                    obj.setLogTime(TimeUtil.localDateTime2timestamp(thisTime));
                    obj.setObjectLabel(node.getModelLabel());
                    obj.setObjectId(node.getId());
                    if (CollectionUtils.isEmpty(val)) {
                        return;
                    }
                    obj.setValue(val.stream().filter(item->Objects.nonNull(item.getValue())).mapToDouble(ObjectCostValuePlan::getValue).sum());
                    newDataList.add(obj);
                });
            }

            time = nextTime;
        }

        return newDataList;
    }

    private List<EnergyConsumptionPlan> summarizedWithEnergyConsumptionPlan(List<EnergyConsumptionPlan> oldDataList, Collection<BaseVo> nodes,
                                                                            LocalDateTime startTime, LocalDateTime endTime,
                                                                            Integer preCycle, Integer nextCycle, Long projectId) {
        List<EnergyConsumptionPlan> newDataList = new ArrayList<>();

        UnnaturalSetVo unnaturalSet = unnaturalTimeService.getUnnaturalSetVo(projectId, nextCycle);
        for (LocalDateTime time = startTime; time.isBefore(endTime); ) {
            LocalDateTime thisTime = time;
            LocalDateTime nextTime = TimeUtil.addDateTimeByCycle(time, nextCycle, 1);

            LocalDateTime st = unnaturalTimeService.convertUnnaturalTime(nextCycle, thisTime, unnaturalSet);
            LocalDateTime et = unnaturalTimeService.convertUnnaturalTime(nextCycle, nextTime, unnaturalSet);


            for (BaseVo node : nodes) {
                Map<Integer, List<EnergyConsumptionPlan>> productionDataAndTypeMap = oldDataList.stream()
                        .filter(it -> Objects.equals(it.getObjectId(), node.getId()) &&
                                Objects.equals(it.getObjectLabel(), node.getModelLabel()) &&
                                Objects.equals(it.getAggregationCycle(), preCycle) &&
                                TimeUtil.timestamp2LocalDateTime(it.getLogtime()).isBefore(et) &&
                                !st.isAfter(TimeUtil.timestamp2LocalDateTime(it.getLogtime())))
                        .collect(Collectors.groupingBy(EnergyConsumptionPlan::getEnergyType));

                productionDataAndTypeMap.forEach((effId, val) -> {
                    EnergyConsumptionPlan obj = new EnergyConsumptionPlan();
                    obj.setEnergyType(effId);
                    obj.setAggregationCycle(nextCycle);
                    obj.setLogtime(TimeUtil.localDateTime2timestamp(thisTime));
                    obj.setObjectLabel(node.getModelLabel());
                    obj.setObjectId(node.getId());
                    if (CollectionUtils.isEmpty(val)) {
                        return;
                    }
                    obj.setValue(val.stream().filter(it -> Objects.nonNull(it.getValue())).mapToDouble(EnergyConsumptionPlan::getValue).sum());
                    newDataList.add(obj);
                });
            }

            time = nextTime;
        }

        return newDataList;
    }

    private List<EnergyEfficiencyDataPlan> combineProductionData(List<EnergyEfficiencyDataPlan> childDataList, List<EnergyEfficiencyDataPlan> nextProductionData) {
        if (CollectionUtils.isEmpty(childDataList)) {
            return nextProductionData;
        }

        if (CollectionUtils.isEmpty(nextProductionData)) {
            return childDataList;
        }

        List<EnergyEfficiencyDataPlan> dataList = new ArrayList<>();
        for (EnergyEfficiencyDataPlan productionData : childDataList) {
            Optional<EnergyEfficiencyDataPlan> any = nextProductionData.stream().filter(it -> compareEffPlan(it, productionData)).findAny();
            dataList.add(productionData);
            any.ifPresent(data -> productionData.setId(data.getId()));
        }

        for (EnergyEfficiencyDataPlan nextProductionDatum : nextProductionData) {
            Optional<EnergyEfficiencyDataPlan> any = childDataList.stream().filter(it -> compareEffPlan(it, nextProductionDatum)).findAny();
            if (!any.isPresent()) {
                dataList.add(nextProductionDatum);
            }
        }

        return dataList;
    }

    private List<ObjectCostValuePlan> combineObjectCostValuePlan(List<ObjectCostValuePlan> childDataList, List<ObjectCostValuePlan> nextProductionData) {
        if (CollectionUtils.isEmpty(childDataList)) {
            return nextProductionData;
        }

        if (CollectionUtils.isEmpty(nextProductionData)) {
            return childDataList;
        }

        List<ObjectCostValuePlan> dataList = new ArrayList<>();
        for (ObjectCostValuePlan productionData : childDataList) {
            Optional<ObjectCostValuePlan> any = nextProductionData.stream().filter(it -> compareObjectCostValuePlan(it, productionData)).findAny();
            dataList.add(productionData);
            any.ifPresent(data -> productionData.setId(data.getId()));
        }

        for (ObjectCostValuePlan nextProductionDatum : nextProductionData) {
            Optional<ObjectCostValuePlan> any = childDataList.stream().filter(it -> compareObjectCostValuePlan(it, nextProductionDatum)).findAny();
            if (!any.isPresent()) {
                dataList.add(nextProductionDatum);
            }
        }

        return dataList;
    }

    private List<EnergyConsumptionPlan> combineEnergyConsumptionPlan(List<EnergyConsumptionPlan> childDataList, List<EnergyConsumptionPlan> nextProductionData) {
        if (CollectionUtils.isEmpty(childDataList)) {
            return nextProductionData;
        }

        if (CollectionUtils.isEmpty(nextProductionData)) {
            return childDataList;
        }

        List<EnergyConsumptionPlan> dataList = new ArrayList<>();
        for (EnergyConsumptionPlan productionData : childDataList) {
            Optional<EnergyConsumptionPlan> any = nextProductionData.stream().filter(it -> compareEnergyPlan(it, productionData)).findAny();
            dataList.add(productionData);
            any.ifPresent(data -> productionData.setId(data.getId()));
        }

        for (EnergyConsumptionPlan nextProductionDatum : nextProductionData) {
            Optional<EnergyConsumptionPlan> any = childDataList.stream().filter(it -> compareEnergyPlan(it, nextProductionDatum)).findAny();
            if (!any.isPresent()) {
                dataList.add(nextProductionDatum);
            }
        }

        return dataList;
    }

    private List<UnitObjectCostValuePlan> combineUnitObjectCostValuePlan(List<UnitObjectCostValuePlan> childDataList, List<UnitObjectCostValuePlan> nextProductionData) {
        if (CollectionUtils.isEmpty(childDataList)) {
            return nextProductionData;
        }

        if (CollectionUtils.isEmpty(nextProductionData)) {
            return childDataList;
        }

        List<UnitObjectCostValuePlan> dataList = new ArrayList<>();
        for (UnitObjectCostValuePlan productionData : childDataList) {
            Optional<UnitObjectCostValuePlan> any = nextProductionData.stream().filter(it -> compareUnitObjectCostValuePlan(it, productionData)).findAny();
            dataList.add(productionData);
            any.ifPresent(data -> productionData.setId(data.getId()));
        }

        for (UnitObjectCostValuePlan nextProductionDatum : nextProductionData) {
            Optional<UnitObjectCostValuePlan> any = childDataList.stream().filter(it -> compareUnitObjectCostValuePlan(it, nextProductionDatum)).findAny();
            if (!any.isPresent()) {
                dataList.add(nextProductionDatum);
            }
        }

        return dataList;
    }

    public static boolean compareEffPlan(EnergyEfficiencyDataPlan obj1, EnergyEfficiencyDataPlan obj2) {
        if (obj1 == obj2) {
            return true;
        }

        if (Objects.isNull(obj1) || Objects.isNull(obj2)) {
            return false;
        }

        return Objects.equals(obj1.getObjectId(), obj2.getObjectId()) &&
                Objects.equals(obj1.getObjectLabel(), obj2.getObjectLabel()) &&
                Objects.equals(obj1.getLogTime(), obj2.getLogTime()) &&
                Objects.equals(obj1.getEnergyEfficiencySetId(), obj2.getEnergyEfficiencySetId());
    }

    public static boolean compareObjectCostValuePlan(ObjectCostValuePlan obj1, ObjectCostValuePlan obj2) {
        if (obj1 == obj2) {
            return true;
        }

        if (Objects.isNull(obj1) || Objects.isNull(obj2)) {
            return false;
        }

        return Objects.equals(obj1.getObjectId(), obj2.getObjectId()) &&
                Objects.equals(obj1.getObjectLabel(), obj2.getObjectLabel()) &&
                Objects.equals(obj1.getLogTime(), obj2.getLogTime()) &&
                Objects.equals(obj1.getEnergyType(), obj2.getEnergyType()) &&
                Objects.equals(obj1.getAggregationCycle(), obj2.getAggregationCycle());
    }

    public static boolean compareEnergyPlan(EnergyConsumptionPlan obj1, EnergyConsumptionPlan obj2) {
        if (obj1 == obj2) {
            return true;
        }

        if (Objects.isNull(obj1) || Objects.isNull(obj2)) {
            return false;
        }

        return Objects.equals(obj1.getObjectId(), obj2.getObjectId()) &&
                Objects.equals(obj1.getObjectLabel(), obj2.getObjectLabel()) &&
                Objects.equals(obj1.getLogtime(), obj2.getLogtime()) &&
                Objects.equals(obj1.getEnergyType(), obj2.getEnergyType()) &&
                Objects.equals(obj1.getAggregationCycle(), obj2.getAggregationCycle());
    }

    public static boolean compareUnitObjectCostValuePlan(UnitObjectCostValuePlan obj1, UnitObjectCostValuePlan obj2) {
        if (obj1 == obj2) {
            return true;
        }

        if (Objects.isNull(obj1) || Objects.isNull(obj2)) {
            return false;
        }

        return Objects.equals(obj1.getObjectId(), obj2.getObjectId()) &&
                Objects.equals(obj1.getObjectLabel(), obj2.getObjectLabel()) &&
                Objects.equals(obj1.getLogTime(), obj2.getLogTime()) &&
                Objects.equals(obj1.getEnergyType(), obj2.getEnergyType()) &&
                Objects.equals(obj1.getProductType(), obj2.getProductType()) &&
                Objects.equals(obj1.getAggregationCycle(), obj2.getAggregationCycle());
    }

    private List<EnergyEfficiencySetVo> queryByEffId(Set<BaseVo> nodes, Long projectId) {
        List<KpiSetVo> kpiSetList = kpiSetDao.getKpiSetList(nodes);
        Set<Long> effSetIds = kpiSetList.stream().map(KpiSetVo::getEnergyefficiencyset_id).collect(Collectors.toSet());
        return energyEfficiencySetDao.queryEnergyEfficiencySetByIds(effSetIds);

    }


    private List<EnergyEfficiencySetVo> queryEnergyEfficiencySetBatch(Long projectId, String name, Integer energyType, Integer productType) {
        //能源类型，选择产品，指标属性（就是产品），名称;
        String queryName;
        //判断指标有无括号内容：正常应该是指标名称加周期信息，如果修改删除，考虑这个情况去查询其他周期的指标信息
        if (name.contains(SPILT)) {
            String[] split = StringUtils.split(name, SPILT);
            queryName = split[0];
        } else {
            queryName = name;
        }
        return queryEfSets(projectId, (energyType),
                productType, queryName);

    }

    public List<EnergyEfficiencySetVo> queryEfSets(long projectId, Integer energyType, Integer productType, String name) {
        QueryConditionBuilder<BaseEntity> builder = new QueryConditionBuilder<>(ModelLabelDef.ENERGY_EFFICIENCY_SET)
                .setParentNode(projectId, NodeLabelDef.PROJECT)
                .eq(ColumnDef.ENERGY_TYPE, energyType)
                .eq(ColumnDef.PRODUCT_TYPE, productType)
                .like(ColumnDef.NAME, name);

        return modelServiceUtils.query(builder.build(), EnergyEfficiencySetVo.class);
    }

    private String handleName(String name, Integer cycle) {
        if (cycle.equals(AggregationCycle.SEVEN_DAYS)) {
            return name + "(周)";
        } else if (cycle.equals(AggregationCycle.ONE_DAY)) {
            return name + "(日度)";
        } else if (cycle.equals(AggregationCycle.ONE_YEAR)) {
            return name + "(年度)";
        } else if (cycle.equals(AggregationCycle.ONE_MONTH)) {
            return name + "(月度)";
        } else if (cycle.equals(AggregationCycle.ONE_HOUR)) {
            return name + "(时)";
        }
        return name;
    }

    /**
     * 统计折标能耗
     *
     * @param st
     * @param et
     * @param projectId
     */

    public void summarizedByCoef(Long st, Long et, Long projectId, Collection<BaseVo> nodes, Integer aggregationCycle, Map<Integer, Double> coefMap, Integer targetEnergyType) {
        List<EnergyConsumptionPlan> oldDataList = givenEnergyConsumptionPlanDao.queryEnergyConsumptionBatch(nodes, st, et, aggregationCycle, Collections.emptyList());

        Map<BaseVo, List<EnergyConsumptionPlan>> energyConsumptionMap = oldDataList.stream()
                .collect(Collectors.groupingBy(it -> new BaseVo(it.getObjectId(), it.getObjectLabel())));
        List<EnergyConsumptionPlan> newDataList = new ArrayList<>();
        energyConsumptionMap.forEach((node, consumptions) -> {
            newDataList.addAll(handleSingleNode(aggregationCycle, consumptions, coefMap, node, targetEnergyType));
        });

//        givenEnergyConsumptionPlanDao.insertData(newDataList);

        writeEnergyConsumptionPlanList(newDataList, productionDataConfig.getAggregationCycleChainList(aggregationCycle), projectId);

//        commonUtilsService.writeUpdateOperationLogs(EEMOperationLogType.BILL, "保存折标账单数据", newDataList);
    }

    private List<EnergyConsumptionPlan> handleSingleNode(Integer aggregationCycle, List<EnergyConsumptionPlan> oldDataList, Map<Integer, Double> coefMap, BaseVo node,
                                                         Integer targetEnergyType) {
        List<EnergyConsumptionPlan> newDataList = new ArrayList<>();
        // 过滤出非折标的能耗
        Map<Long, List<EnergyConsumptionPlan>> oldTimeDataMap = oldDataList.stream()
                .filter(it -> !Objects.equals(it.getEnergyType(), targetEnergyType)).collect(Collectors.groupingBy(EnergyConsumptionPlan::getLogtime));
        oldTimeDataMap.forEach((time, val) -> {
            double total = val.stream().mapToDouble(it -> {
                Double coef = coefMap.get(it.getEnergyType());
                if (coef == null) {
                    return 0;
                }

                return coef * (it.getValue() == null ? 0D : it.getValue());
            }).sum();
           /* if (Objects.equals(total, 0.0)) {
                return;
            }*/
            Optional<EnergyConsumptionPlan> any = oldDataList.stream().filter(it -> it.getLogtime().equals(time) && Objects.equals(it.getEnergyType(), targetEnergyType)).findFirst();
            EnergyConsumptionPlan obj = any.orElseGet(EnergyConsumptionPlan::new);

            obj.setObjectId(node.getId());
            obj.setObjectLabel(node.getModelLabel());
            obj.setValue(total);
            obj.setEnergyType(targetEnergyType);
            obj.setAggregationCycle(aggregationCycle);
            obj.setLogtime(time);

            newDataList.add(obj);
        });

        return newDataList;
    }
}
package com.cet.eem.datatransportservice;

import com.cet.eem.bll.datatransport.handle.MesDataHandle;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.Result;
import com.cet.eem.common.utils.TimeUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;


@Api(value = "MesDataTransportController", tags = {"mes数据转存接口"})
@RestController
@RequestMapping(value = "/eem/v1/mesData")
public class MesDataTransportController {

    @Autowired
    MesDataHandle mesDataHandle;

    @ApiOperation(value = "手动执行mes数据转存")
    @PostMapping(value = "/transportMesData", produces = "application/json")
    public Result<Object> transportMesData(@RequestParam Long time) throws IOException {
        mesDataHandle.transport(TimeUtil.timestamp2LocalDateTime(time));
        return Result.ok();
    }

    @ApiOperation(value = "手动执行mes过车数据转存")
    @PostMapping(value = "/transportMesPassPoint", produces = "application/json")
    public Result<Object> transportMesPassPoint(@RequestParam Long startTime, @RequestParam Long endTime) throws Exception {
        mesDataHandle.transportMesPassPoint(startTime, endTime);
        return Result.ok();
    }

    @ApiOperation(value = "手动执行mes平均过车时间的计算")
    @PostMapping(value = "/calcAveragePassTime", produces = "application/json")
    public Result<Object> calcAveragePassTime(@RequestParam Long startTime, @RequestParam Long endTime) throws Exception {
        mesDataHandle.calcAveragePassTime(startTime, endTime);
        return Result.ok();
    }
}

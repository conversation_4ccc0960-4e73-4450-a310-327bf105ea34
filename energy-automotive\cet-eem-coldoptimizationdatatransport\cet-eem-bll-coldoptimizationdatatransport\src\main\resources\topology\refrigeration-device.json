[{"type": 1, "relateDevice": true, "name": "风柜", "modelLabel": "windset", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 3}], "fieldName": "所属空调机房"}, "fields": [{"field": "name", "required": true, "fieldName": "名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "设备编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "commissiondate", "required": false, "fieldName": "投用日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "检修周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "出厂日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "devicesize", "required": false, "fieldName": "机组尺寸", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceweight", "required": false, "fieldName": "机组总重量(吨)", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "airconditionsystemtype", "required": false, "fieldName": "空调系统类型", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}], "tips": null}, {"type": 1, "relateDevice": true, "name": "冷水主机", "modelLabel": "coldwatermainengine", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 3}], "fieldName": "所属空调机房"}, "fields": [{"field": "name", "required": true, "fieldName": "名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "设备编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "commissiondate", "required": false, "fieldName": "投用日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "检修周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "出厂日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "devicesize", "required": false, "fieldName": "机组尺寸", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceweight", "required": false, "fieldName": "机组总重量(吨)", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "airconditionsystemtype", "required": false, "fieldName": "空调系统类型", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "engineattr", "required": false, "fieldName": "机组属性", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "coolingmethod", "required": false, "fieldName": "制冷方式", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "voltagelevel", "required": false, "fieldName": "电压等级", "dataType": "voltagelevel", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "coolingcyclewater", "required": false, "fieldName": "冷却循环水量(m³/h)", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maxconstantoutwatertemp", "required": false, "fieldName": "最大恒定出水温度(℃)", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maxcoolingcycletemp", "required": false, "fieldName": "冷却循环水最大温度(℃)", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maxrefrigeratingcycletemp", "required": false, "fieldName": "冷冻循环水最大温度(℃)", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "minconstantoutwatertemp", "required": false, "fieldName": "最小恒定出水温度(℃)", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "mincoolingcycletemp", "required": false, "fieldName": "冷却循环水最小温度(℃)", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "minrefrigeratingcycletemp", "required": false, "fieldName": "冷冻循环水最小温度(℃)", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedmotorpower", "required": false, "fieldName": "额定功率（kW）", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ratedrefrigeration", "required": false, "fieldName": "额定制冷量（kW）", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "refrigeratingcyclewater", "required": false, "fieldName": "冷冻循环水量(m³/h)", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}], "tips": null}, {"type": 1, "relateDevice": true, "name": "冷却塔", "modelLabel": "coolingtower", "parentNode": {"modelLabel": "room", "filers": [{"prop": "roomtype", "operator": "EQ", "tagid": null, "limit": 4}], "fieldName": "所属空压机房"}, "fields": [{"field": "name", "required": true, "fieldName": "名称", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "code", "required": false, "fieldName": "设备编号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "location", "required": false, "fieldName": "安装位置", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "commissiondate", "required": false, "fieldName": "投用日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "maintenanceperiod", "required": false, "fieldName": "检修周期", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "lastoverhauldate", "required": false, "fieldName": "上次检修日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "nextoverhauldate", "required": false, "fieldName": "下次检修周期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufacturedate", "required": false, "fieldName": "出厂日期", "dataType": "date", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "model", "required": false, "fieldName": "型号", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "manufactor", "required": false, "fieldName": "生产厂家", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "ownship", "required": false, "fieldName": "资产归属", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceclassification", "required": false, "fieldName": "设备归类", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "devicesize", "required": false, "fieldName": "机组尺寸", "dataType": "string", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "deviceweight", "required": false, "fieldName": "机组总重量(吨)", "dataType": "float", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}, {"field": "airconditionsystemtype", "required": false, "fieldName": "空调系统类型", "dataType": "int8", "dateFormat": null, "sourceLabel": null, "sourceType": null, "sourceField": null}], "tips": null}]
package com.cet.eem.bll.achievementrate.dao;

import com.cet.eem.bll.achievementrate.model.CostcheckitemVO;
import com.cet.eem.bll.achievementrate.model.data.ObjectCostValuePlan;
import com.cet.eem.bll.common.model.domain.subject.generalrules.FeeScheme;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.dao.BaseModelDao;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * @ClassName : ObjectCostValuePlanDao
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-11-21 17:14
 */
public interface ObjectCostValuePlanDao extends BaseModelDao<ObjectCostValuePlan> {

    List<ObjectCostValuePlan> query(Long st, Long et, Collection<Integer> energyType, Collection<BaseVo> nodes, Integer cycle);
    void insertObjectCostValuePlan(List<ObjectCostValuePlan> plans);
    void writeObjectCostValuePlan(List<ObjectCostValuePlan> plans);
    List<FeeScheme> queryFeeSchemesByType(Integer type);
    List<CostcheckitemVO> queryCostCheckPlanByFeeSchemeIds(List<Long> feeIds);
    List<ObjectCostValuePlan> query(LocalDateTime st, LocalDateTime end, Integer cycle, Integer energyType,List<BaseVo> nodes);
}

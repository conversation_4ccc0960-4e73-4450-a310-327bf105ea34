package com.cet.eem.bll.achievementrate.model;

import com.cet.eem.common.model.BaseVo;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName : AchievementRateQueryVo
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-11-20 15:31
 */
@Getter
@Setter
public class AchievementRateQueryVo {
    private Integer dataEntryType;
    private List<Integer> energyTypes;
    private List<Integer> productTypes;
    private List<Long> effSetIds;
    private List<BaseVo> nodes;
    private Integer aggregationCycle;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
}
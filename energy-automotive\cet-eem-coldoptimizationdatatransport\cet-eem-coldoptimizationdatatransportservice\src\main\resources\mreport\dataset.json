[{"name": "区间季度数据查询", "key": "energy-consumption-quarter-sum", "fields": [{"value": "name", "name": "aggregationcycle"}, {"value": "name", "name": "aggregationCycleName"}, {"value": "name", "name": "logtime"}, {"value": "name", "name": "energytype"}, {"value": "name", "name": "energyTypeName"}, {"value": "name", "name": "objectid"}, {"value": "name", "name": "objectlabel"}, {"value": "name", "name": "timeshareperiod_identification"}, {"value": "name", "name": "total"}, {"value": "name", "name": "loss"}, {"value": "name", "name": "usage"}, {"value": "name", "name": "areaUnit"}, {"value": "name", "name": "populationUnit"}, {"value": "name", "name": "logTimeStr"}], "parameters": []}, {"name": "单季度数据查询", "key": "energy-consumption-quarter-sum", "fields": [{"value": "name", "name": "aggregationcycle"}, {"value": "name", "name": "logtime"}, {"value": "name", "name": "energytype"}, {"value": "name", "name": "objectid"}, {"value": "name", "name": "objectlabel"}, {"value": "name", "name": "timeshareperiod_identification"}, {"value": "name", "name": "total"}, {"value": "name", "name": "loss"}, {"value": "name", "name": "usage"}, {"value": "name", "name": "areaUnit"}, {"value": "name", "name": "populationUnit"}, {"value": "name", "name": "logTimeStr"}], "parameters": []}, {"name": "多节点单季度数据查询", "key": "energy-consumption-quarter-object-sum", "fields": [{"value": "name", "name": "aggregationcycle"}, {"value": "name", "name": "logtime"}, {"value": "name", "name": "energytype"}, {"value": "name", "name": "objectid"}, {"value": "name", "name": "objectlabel"}, {"value": "name", "name": "timeshareperiod_identification"}, {"value": "name", "name": "total"}, {"value": "name", "name": "loss"}, {"value": "name", "name": "usage"}, {"value": "name", "name": "areaUnit"}, {"value": "name", "name": "populationUnit"}, {"value": "name", "name": "logTimeStr"}], "parameters": []}, {"name": "年度能耗查询", "key": "energy-consumption", "fields": [{"value": "name", "name": "aggregationcycle"}, {"value": "name", "name": "logtime"}, {"value": "name", "name": "energytype"}, {"value": "name", "name": "objectid"}, {"value": "name", "name": "objectlabel"}, {"value": "name", "name": "timeshareperiod_identification"}, {"value": "name", "name": "total"}, {"value": "name", "name": "loss"}, {"value": "name", "name": "usage"}, {"value": "name", "name": "areaUnit"}, {"value": "name", "name": "populationUnit"}, {"value": "name", "name": "logTimeStr"}], "parameters": []}, {"name": "季度能耗查询", "key": "energy-consumption", "fields": [{"value": "name", "name": "aggregationcycle"}, {"value": "name", "name": "logtime"}, {"value": "name", "name": "energytype"}, {"value": "name", "name": "objectid"}, {"value": "name", "name": "objectlabel"}, {"value": "name", "name": "timeshareperiod_identification"}, {"value": "name", "name": "total"}, {"value": "name", "name": "loss"}, {"value": "name", "name": "usage"}, {"value": "name", "name": "areaUnit"}, {"value": "name", "name": "populationUnit"}, {"value": "name", "name": "logTimeStr"}], "parameters": []}, {"name": "月度能耗查询", "key": "energy-consumption", "fields": [{"value": "name", "name": "aggregationcycle"}, {"value": "name", "name": "logtime"}, {"value": "name", "name": "energytype"}, {"value": "name", "name": "objectid"}, {"value": "name", "name": "objectlabel"}, {"value": "name", "name": "timeshareperiod_identification"}, {"value": "name", "name": "total"}, {"value": "name", "name": "loss"}, {"value": "name", "name": "usage"}, {"value": "name", "name": "areaUnit"}, {"value": "name", "name": "populationUnit"}, {"value": "name", "name": "logTimeStr"}], "parameters": []}, {"name": "季度账单", "key": "quarter-bill-data", "fields": [{"value": "name", "name": "aggregationcycle"}, {"value": "name", "name": "logtime"}, {"value": "name", "name": "energytype"}, {"value": "name", "name": "projectid"}, {"value": "name", "name": "objectid"}, {"value": "name", "name": "objectlabel"}, {"value": "name", "name": "totalusage"}, {"value": "name", "name": "unit"}, {"value": "name", "name": "areaUnit"}, {"value": "name", "name": "populationUnit"}, {"value": "name", "name": "aggregationCycleName"}, {"value": "name", "name": "energyTypeName"}, {"value": "name", "name": "year"}, {"value": "name", "name": "step"}, {"value": "name", "name": "tbPercentValue"}, {"value": "name", "name": "tbValue"}, {"value": "name", "name": "hbPercentValue"}, {"value": "name", "name": "hbValue"}], "parameters": []}, {"name": "年度账单", "key": "quarter-bill-data", "fields": [{"value": "name", "name": "aggregationcycle"}, {"value": "name", "name": "logtime"}, {"value": "name", "name": "energytype"}, {"value": "name", "name": "projectid"}, {"value": "name", "name": "objectid"}, {"value": "name", "name": "objectlabel"}, {"value": "name", "name": "totalusage"}, {"value": "name", "name": "unit"}, {"value": "name", "name": "areaUnit"}, {"value": "name", "name": "populationUnit"}, {"value": "name", "name": "aggregationCycleName"}, {"value": "name", "name": "energyTypeName"}, {"value": "name", "name": "year"}, {"value": "name", "name": "step"}, {"value": "name", "name": "tbPercentValue"}, {"value": "name", "name": "tbValue"}, {"value": "name", "name": "hbPercentValue"}, {"value": "name", "name": "hbValue"}], "parameters": []}, {"name": "月度账单", "key": "quarter-bill-data", "fields": [{"value": "name", "name": "aggregationcycle"}, {"value": "name", "name": "logtime"}, {"value": "name", "name": "energytype"}, {"value": "name", "name": "projectid"}, {"value": "name", "name": "objectid"}, {"value": "name", "name": "objectlabel"}, {"value": "name", "name": "totalusage"}, {"value": "name", "name": "unit"}, {"value": "name", "name": "areaUnit"}, {"value": "name", "name": "populationUnit"}, {"value": "name", "name": "aggregationCycleName"}, {"value": "name", "name": "energyTypeName"}, {"value": "name", "name": "year"}, {"value": "name", "name": "step"}, {"value": "name", "name": "tbPercentValue"}, {"value": "name", "name": "tbValue"}, {"value": "name", "name": "hbPercentValue"}, {"value": "name", "name": "hbValue"}], "parameters": []}, {"name": "时间处理", "key": "time-format", "fields": [{"value": "name", "name": "index"}, {"value": "name", "name": "date"}], "parameters": []}, {"name": "月度能效账单", "key": "ef-bill-data", "fields": [{"value": "name", "name": "value"}, {"value": "name", "name": "step"}, {"value": "name", "name": "logtime"}, {"value": "name", "name": "energytype"}, {"value": "name", "name": "producttype"}, {"value": "name", "name": "objectid"}, {"value": "name", "name": "objectlabel"}, {"value": "name", "name": "tbPercentValue"}, {"value": "name", "name": "tbValue"}, {"value": "name", "name": "hbPercentValue"}, {"value": "name", "name": "hbValue"}], "parameters": []}, {"name": "季度能效账单", "key": "ef-bill-data", "fields": [{"value": "name", "name": "value"}, {"value": "name", "name": "step"}, {"value": "name", "name": "logtime"}, {"value": "name", "name": "energytype"}, {"value": "name", "name": "producttype"}, {"value": "name", "name": "objectid"}, {"value": "name", "name": "objectlabel"}, {"value": "name", "name": "tbPercentValue"}, {"value": "name", "name": "tbValue"}, {"value": "name", "name": "hbPercentValue"}, {"value": "name", "name": "hbValue"}], "parameters": []}, {"name": "年度能效账单", "key": "ef-bill-data", "fields": [{"value": "name", "name": "value"}, {"value": "name", "name": "step"}, {"value": "name", "name": "logtime"}, {"value": "name", "name": "energytype"}, {"value": "name", "name": "producttype"}, {"value": "name", "name": "objectid"}, {"value": "name", "name": "objectlabel"}, {"value": "name", "name": "tbPercentValue"}, {"value": "name", "name": "tbValue"}, {"value": "name", "name": "hbPercentValue"}, {"value": "name", "name": "hbValue"}], "parameters": []}, {"name": "年度产量账单", "key": "production-data", "fields": [{"value": "name", "name": "value"}, {"value": "name", "name": "step"}, {"value": "name", "name": "logtime"}, {"value": "name", "name": "product"}, {"value": "name", "name": "producttype"}, {"value": "name", "name": "aggregationcycle"}, {"value": "name", "name": "objectid"}, {"value": "name", "name": "objectlabel"}], "parameters": []}, {"name": "季度产量账单", "key": "production-data", "fields": [{"value": "name", "name": "value"}, {"value": "name", "name": "step"}, {"value": "name", "name": "logtime"}, {"value": "name", "name": "product"}, {"value": "name", "name": "producttype"}, {"value": "name", "name": "aggregationcycle"}, {"value": "name", "name": "objectid"}, {"value": "name", "name": "objectlabel"}], "parameters": []}, {"name": "月度产量账单", "key": "production-data", "fields": [{"value": "name", "name": "value"}, {"value": "name", "name": "step"}, {"value": "name", "name": "logtime"}, {"value": "name", "name": "product"}, {"value": "name", "name": "producttype"}, {"value": "name", "name": "aggregationcycle"}, {"value": "name", "name": "objectid"}, {"value": "name", "name": "objectlabel"}], "parameters": []}, {"name": "年度多维度能耗", "key": "dimension-consumption-data", "fields": [{"value": "name", "name": "dimensionId"}, {"value": "name", "name": "dimensionName"}, {"value": "name", "name": "levelId"}, {"value": "name", "name": "levelName"}, {"value": "name", "name": "tagId"}, {"value": "name", "name": "tagName"}, {"value": "name", "name": "logTime"}, {"value": "name", "name": "energyType"}, {"value": "name", "name": "aggregationCycle"}, {"value": "name", "name": "value"}, {"value": "name", "name": "step"}], "parameters": []}, {"name": "季度多维度能耗", "key": "dimension-consumption-data", "fields": [{"value": "name", "name": "dimensionId"}, {"value": "name", "name": "dimensionName"}, {"value": "name", "name": "levelId"}, {"value": "name", "name": "levelName"}, {"value": "name", "name": "tagId"}, {"value": "name", "name": "tagName"}, {"value": "name", "name": "logTime"}, {"value": "name", "name": "energyType"}, {"value": "name", "name": "aggregationCycle"}, {"value": "name", "name": "value"}, {"value": "name", "name": "step"}], "parameters": []}, {"name": "月度多维度能耗", "key": "dimension-consumption-data", "fields": [{"value": "name", "name": "dimensionId"}, {"value": "name", "name": "dimensionName"}, {"value": "name", "name": "levelId"}, {"value": "name", "name": "levelName"}, {"value": "name", "name": "tagId"}, {"value": "name", "name": "tagName"}, {"value": "name", "name": "logTime"}, {"value": "name", "name": "energyType"}, {"value": "name", "name": "aggregationCycle"}, {"value": "name", "name": "value"}, {"value": "name", "name": "step"}], "parameters": []}, {"name": "武汉分行能源消费总体资源年度报表", "key": "branch-energy-resource-consumption-sum-year-data", "fields": [{"value": "name", "name": "dataLabel"}, {"value": "name", "name": "energyProductType"}, {"value": "name", "name": "provinceId"}, {"value": "name", "name": "startTime"}, {"value": "name", "name": "typeName"}, {"value": "name", "name": "unitName"}, {"value": "name", "name": "lastYearValue"}, {"value": "name", "name": "cur<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"value": "name", "name": "changeValue"}, {"value": "name", "name": "changeRate"}], "parameters": []}, {"name": "武汉分行能源消费总体资源半年报表", "key": "branch-energy-resource-consumption-sum-half-year-data", "fields": [{"value": "name", "name": "dataLabel"}, {"value": "name", "name": "energyProductType"}, {"value": "name", "name": "provinceId"}, {"value": "name", "name": "startTime"}, {"value": "name", "name": "typeName"}, {"value": "name", "name": "unitName"}, {"value": "name", "name": "lastFirstHalf"}, {"value": "name", "name": "lastSecondHalf"}, {"value": "name", "name": "cur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"value": "name", "name": "cur<PERSON><PERSON>ond<PERSON><PERSON><PERSON>"}, {"value": "name", "name": "firstHalfTbValue"}, {"value": "name", "name": "secondHalfTbValue"}, {"value": "name", "name": "firstHalfTbRate"}, {"value": "name", "name": "secondHalfTbRate"}], "parameters": []}, {"name": "武汉分行各单位主要能源消费资源年度报表", "key": "branch-energy-resource-consumption-item-year-data", "fields": [{"value": "name", "name": "dataLabel"}, {"value": "name", "name": "energyProductType"}, {"value": "name", "name": "objectIdLabel"}, {"value": "name", "name": "startTime"}, {"value": "name", "name": "nodeName"}, {"value": "name", "name": "lastYearValue"}, {"value": "name", "name": "cur<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"value": "name", "name": "changeRate"}], "parameters": []}, {"name": "武汉分行各单位主要能源消费资源半年报表", "key": "branch-energy-resource-consumption-item-half-year-data", "fields": [{"value": "name", "name": "dataLabel"}, {"value": "name", "name": "energyProductType"}, {"value": "name", "name": "objectIdLabel"}, {"value": "name", "name": "startTime"}, {"value": "name", "name": "nodeName"}, {"value": "name", "name": "lastFirstHalf"}, {"value": "name", "name": "lastSecondHalf"}, {"value": "name", "name": "cur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"value": "name", "name": "cur<PERSON><PERSON>ond<PERSON><PERSON><PERSON>"}, {"value": "name", "name": "firstHalfTbRate"}, {"value": "name", "name": "secondHalfTbRate"}], "parameters": []}, {"name": "武汉分行营业管理部能源消费总体资源年度报表", "key": "manage-energy-resource-consumption-sum-year-data", "fields": [{"value": "name", "name": "dataLabel"}, {"value": "name", "name": "energyProductType"}, {"value": "name", "name": "projectId"}, {"value": "name", "name": "startTime"}, {"value": "name", "name": "typeName"}, {"value": "name", "name": "unitName"}, {"value": "name", "name": "lastYearValue"}, {"value": "name", "name": "cur<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"value": "name", "name": "changeValue"}, {"value": "name", "name": "changeRate"}], "parameters": []}, {"name": "武汉分行营业管理部能源消费总体资源半年报表", "key": "manage-energy-resource-consumption-sum-half-year-data", "fields": [{"value": "name", "name": "dataLabel"}, {"value": "name", "name": "energyProductType"}, {"value": "name", "name": "projectId"}, {"value": "name", "name": "startTime"}, {"value": "name", "name": "typeName"}, {"value": "name", "name": "unitName"}, {"value": "name", "name": "lastFirstHalf"}, {"value": "name", "name": "lastSecondHalf"}, {"value": "name", "name": "cur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"value": "name", "name": "cur<PERSON><PERSON>ond<PERSON><PERSON><PERSON>"}, {"value": "name", "name": "firstHalfTbValue"}, {"value": "name", "name": "secondHalfTbValue"}, {"value": "name", "name": "firstHalfTbRate"}, {"value": "name", "name": "secondHalfTbRate"}], "parameters": []}, {"name": "武汉分行营业管理部各单位主要能源消费资源年度报表", "key": "manage-energy-resource-consumption-item-year-data", "fields": [{"value": "name", "name": "dataLabel"}, {"value": "name", "name": "energyProductType"}, {"value": "name", "name": "objectIdLabel"}, {"value": "name", "name": "startTime"}, {"value": "name", "name": "nodeName"}, {"value": "name", "name": "lastYearValue"}, {"value": "name", "name": "cur<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"value": "name", "name": "changeRate"}], "parameters": []}, {"name": "武汉分行营业管理部各单位主要能源消费资源半年报表", "key": "manage-energy-resource-consumption-item-half-year-data", "fields": [{"value": "name", "name": "dataLabel"}, {"value": "name", "name": "energyProductType"}, {"value": "name", "name": "objectIdLabel"}, {"value": "name", "name": "startTime"}, {"value": "name", "name": "nodeName"}, {"value": "name", "name": "lastFirstHalf"}, {"value": "name", "name": "lastSecondHalf"}, {"value": "name", "name": "cur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"value": "name", "name": "cur<PERSON><PERSON>ond<PERSON><PERSON><PERSON>"}, {"value": "name", "name": "firstHalfTbRate"}, {"value": "name", "name": "secondHalfTbRate"}], "parameters": []}, {"name": "武汉分行辖区单位汇总", "key": "branch_bank_unit_sum_data", "fields": [{"value": "name", "name": "objectIdLabel"}, {"value": "name", "name": "startTime"}, {"value": "name", "name": "nodeName"}, {"value": "name", "name": "landArea"}, {"value": "name", "name": "buildingArea"}, {"value": "name", "name": "energyUserNum"}, {"value": "name", "name": "carTotalNum"}, {"value": "name", "name": "gasOnlineCar"}, {"value": "name", "name": "dieselOilCar"}, {"value": "name", "name": "newEnergyCar"}, {"value": "name", "name": "electricUsage"}, {"value": "name", "name": "electricFee"}, {"value": "name", "name": "waterUsage"}, {"value": "name", "name": "waterFee"}, {"value": "name", "name": "coalUsage"}, {"value": "name", "name": "coalFee"}, {"value": "name", "name": "naturalGasUsage"}, {"value": "name", "name": "naturalGasFee"}, {"value": "name", "name": "totalGasOnlineUsage"}, {"value": "name", "name": "totalGasOnlineFee"}, {"value": "name", "name": "carGasOnlineUsage"}, {"value": "name", "name": "carGasOnlineUsage"}, {"value": "name", "name": "otherGasOnlineUsage"}, {"value": "name", "name": "otherGasOnlineFee"}, {"value": "name", "name": "totalDieselOilUsage"}, {"value": "name", "name": "totalDieselOilFee"}, {"value": "name", "name": "carDieselOilUsage"}, {"value": "name", "name": "carDieselOilFee"}, {"value": "name", "name": "otherDieselOilUsage"}, {"value": "name", "name": "otherDieselOilFee"}, {"value": "name", "name": "gasUsage"}, {"value": "name", "name": "gasFee"}, {"value": "name", "name": "hotWaterUsage"}, {"value": "name", "name": "hotWaterFee"}, {"value": "name", "name": "otherEnergyUsage"}, {"value": "name", "name": "otherEnergyFee"}, {"value": "name", "name": "chargingPileNum"}, {"value": "name", "name": "perCapitaArea"}, {"value": "name", "name": "perCapitaWater"}, {"value": "name", "name": "perCapitaElectric"}, {"value": "name", "name": "surfaceAvgElectric"}, {"value": "name", "name": "complexEnergy"}, {"value": "name", "name": "buildingEnergy"}, {"value": "name", "name": "perCapitaEnergy"}, {"value": "name", "name": "surfaceAvgEnergy"}, {"value": "name", "name": "electricUnitPrice"}, {"value": "name", "name": "coalUnitPrice"}, {"value": "name", "name": "naturalGasUnitPrice"}, {"value": "name", "name": "gasOnlineUnitPrice"}, {"value": "name", "name": "dieselOilUnitPrice"}, {"value": "name", "name": "gasConvertedStandardCoal"}, {"value": "name", "name": "hotWaterAreaUnitPrice"}, {"value": "name", "name": "hotWaterUnitPrice"}, {"value": "name", "name": "waterUnitPrice"}, {"value": "name", "name": "waterConvertedStandardCoal"}, {"value": "name", "name": "electricConvertedStandardCoal"}, {"value": "name", "name": "coalConvertedStandardCoal"}, {"value": "name", "name": "naturalGasConvertedStandardCoal"}, {"value": "name", "name": "gasOnlineConvertedStandardCoal"}, {"value": "name", "name": "dieselOilConvertedStandardCoal"}, {"value": "name", "name": "hotWaterConvertedStandardCoal"}, {"value": "name", "name": "otherEnergyConvertedStandardCoal"}], "parameters": []}]
package com.cet.eem.bll.compressoroptimization.model.config;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.bll.compressoroptimization.def.CompressorOptimizationLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : CompressorSystem
 * @Description : 空压机系统
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-17 10:03
 */
@Getter
@Setter
@ModelLabel(CompressorOptimizationLabelDef.COMPRESSOR_SYSTEM)
public class CompressorSystem extends BaseEntity {

    @ApiModelProperty("节点id")
    @JsonProperty(ColumnDef.OBJECTID)
    private Long objectId;
    @ApiModelProperty("节点id")
    @JsonProperty(ColumnDef.OBJECTLABEL)
    private String objectLabel;
    @ApiModelProperty("是否使用ai预测")
    @JsonProperty(CompressorOptimizationLabelDef.USE_AI)
    private Boolean useAi;

    @ApiModelProperty("项目id")
    @JsonProperty(ColumnDef.PROJECT_ID)
    private Long projectId;

    public CompressorSystem() {
        this.modelLabel = CompressorOptimizationLabelDef.COMPRESSOR_SYSTEM;
    }

    public CompressorSystem(Long projectId, Boolean useAi, Long objectId, String objectLabel) {
        this.projectId = projectId;
        this.objectId = objectId;
        this.objectLabel = objectLabel;
        this.useAi = useAi;
        this.modelLabel = CompressorOptimizationLabelDef.COMPRESSOR_SYSTEM;
    }
    public CompressorSystem(Long projectId, Boolean useAi, Long objectId, String objectLabel,String name) {
        this.projectId = projectId;
        this.objectId = objectId;
        this.objectLabel = objectLabel;
        this.useAi = useAi;
        this.name=name;
        this.modelLabel = CompressorOptimizationLabelDef.COMPRESSOR_SYSTEM;
    }
}
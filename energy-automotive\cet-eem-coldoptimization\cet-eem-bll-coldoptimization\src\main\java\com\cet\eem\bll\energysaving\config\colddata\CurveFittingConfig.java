package com.cet.eem.bll.energysaving.config.colddata;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "cet.eem.task.energy-saving.curvefitting")
@Slf4j
public class CurveFittingConfig {
    /**
     * 定时记录往前查询范围，单位为month
     */
    private int timeRange;
    /**
     * 曲线拟合验证R平方
     */
    private Double rr;
    /**
     * 定时任务执行时间
     */
    private String internal;
}

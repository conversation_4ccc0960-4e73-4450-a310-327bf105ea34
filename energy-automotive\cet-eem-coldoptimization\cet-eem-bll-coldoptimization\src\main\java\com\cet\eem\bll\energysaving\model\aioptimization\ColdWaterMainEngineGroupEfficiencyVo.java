package com.cet.eem.bll.energysaving.model.aioptimization;

import io.swagger.models.auth.In;
import lombok.Data;

@Data
public class ColdWaterMainEngineGroupEfficiencyVo {
    //制冷系统ID
    public Long systemid;
    //离心机数量
    public Integer numOfCentrifugalMachine;
    //螺杆机数量
    public Integer numOfScrewMachine;
    //组合总额定制冷量
    public Double totalRatedRefrigeration;
    //最佳COP
    public Double bestCOP;

    public ColdWaterMainEngineGroupEfficiencyVo(Long systemid, Integer numOfCentrifugalMachine, Integer numOfScrewMachine) {
        this.systemid = systemid;
        this.numOfCentrifugalMachine = numOfCentrifugalMachine;
        this.numOfScrewMachine = numOfScrewMachine;
    }

    public ColdWaterMainEngineGroupEfficiencyVo() {

    }
}

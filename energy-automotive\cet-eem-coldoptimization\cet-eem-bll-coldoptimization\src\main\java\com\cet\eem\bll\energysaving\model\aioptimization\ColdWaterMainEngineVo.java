package com.cet.eem.bll.energysaving.model.aioptimization;

import com.cet.eem.common.model.BaseVo;
import lombok.Data;

@Data
public class ColdWaterMainEngineVo extends BaseVo {
    //空调系统类型
    public Integer airconditionsystemtype;
    //冷却循环水量（m3/h）
    public Double coolingcyclewater;
    //制冷方式
    public Integer coolingmethod;
    //机组尺寸
    public String devicesize;
    //机组重量
    public Double deviceweight;
    //机组属性
    public Integer engineaattr;
    //最大恒定出水温度（℃）
    public Double maxconstantoutwatertemp;
    //冷却循环水最大温度（℃）
    public Double maxcoolingcycletemp;
    //冷冻循环水最大温度（℃）
    public Double maxrefrigeratingcycletemp;
    //最小恒定出水温度（℃）
    public Double minconstantoutwatertemp;
    //冷却循环水最小温度（℃）
    public Double mincoolingcycletemp;
    //冷冻循环水最小温度（℃）
    public Double minrefrigeratingcycletemp;
    //型号
    public String model;
    //名称
    public String name;
    //图片
    public String pic;
    //额定功率（KW）
    public Double ratedmotorpower;
    //额定制冷量（KW）
    public Double ratedrefrigeration;
    //冷冻循环水量（m3/h）
    public Double refrigeratingcyclewater;
    //制冷介质
    public Integer refrigerationmediumtype;
    //电压等级
    public Integer voltageLevel;
}

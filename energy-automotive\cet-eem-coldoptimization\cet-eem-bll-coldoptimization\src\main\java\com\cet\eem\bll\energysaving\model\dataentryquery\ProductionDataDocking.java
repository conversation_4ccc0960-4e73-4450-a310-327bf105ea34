package com.cet.eem.bll.energysaving.model.dataentryquery;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.bll.energysaving.model.def.ColdOptimizationLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : ProductionDataDocking
 * @Description : mes对接信息
 * <AUTHOR> jiangzixuan
 * @Date: 2022-09-15 09:35
 */
@Getter
@Setter
@ModelLabel(ColdOptimizationLabelDef.PRODUCTION_DATA_DOCKING)
public class ProductionDataDocking extends BaseEntity {
    @JsonProperty("completenum")
    private Integer completeNum;
    @JsonProperty(ColumnDef.LOG_TIME)
    private Long logTime;
    @JsonProperty(ColumnDef.C_OBJECT_ID)
    private Long objectId;
    @JsonProperty(ColumnDef.C_OBJECT_Label)
    private String objectLabel;
    @JsonProperty("workshipname")
    private String workShipName;
    @JsonProperty("shiftname")
    private String shiftName;
    @JsonProperty("plannum")
    private Integer planNum;
    /**
     * 班次
     */
    @JsonProperty("takttime")
    private Integer taktTime;

    public ProductionDataDocking() {
        this.modelLabel = ColdOptimizationLabelDef.PRODUCTION_DATA_DOCKING;
    }
}
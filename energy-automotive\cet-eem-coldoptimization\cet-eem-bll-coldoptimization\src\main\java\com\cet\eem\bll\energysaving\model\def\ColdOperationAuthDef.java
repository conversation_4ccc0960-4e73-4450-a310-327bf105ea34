package com.cet.eem.bll.energysaving.model.def;

import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : ColdOperationAuthDef
 * @Description : 制冷权限定义
 * <AUTHOR> jiangzixuan
 * @Date: 2022-07-06 08:49
 */
@Getter
@Setter
public class ColdOperationAuthDef {
    /**
     * 冷机运行约束
     */
    public static final String MODEL_CONFIG_MACHINE_OPERATION_UPDATE = "modelconfig_machineoperationupdate";
    /**
     * 系统供水
     */
    public static final String MODEL_CONFIG_SYSTEM_SUPPLY_WATER_UPDATE = "modelconfig_systemsupplywaterupdate";
    /**
     * 加减机子
     */
    public static final String MODEL_CONFIG_ADD_SUB_CONDITION_UPDATE = "modelconfig_addsubconditionupdate";
    /**
     * mes配置
     */
    public static final String MODEL_CONFIG_MES_SHIFT_UPDATE = "modelconfig_messhiftupdate";
    /**
     * 制冷控制配置
     */
    public static final String MODEL_CONFIG_CONTRL_MODE_UPDATE = "modelconfig_controlmodetupdate";

}
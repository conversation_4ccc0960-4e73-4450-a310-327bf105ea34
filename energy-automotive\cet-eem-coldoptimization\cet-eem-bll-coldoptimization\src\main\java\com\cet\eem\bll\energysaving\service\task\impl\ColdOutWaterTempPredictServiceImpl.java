package com.cet.eem.bll.energysaving.service.task.impl;

import com.cet.eem.bll.common.dao.project.ProjectDao;

import com.cet.eem.bll.energysaving.dao.aioptimization.AiStartStopStrategyDao;
import com.cet.eem.bll.energysaving.dao.aioptimization.StrategyObjectMapDao;
import com.cet.eem.bll.energysaving.dao.weather.RefrigeratingSystemDao;
import com.cet.eem.bll.energysaving.model.aioptimization.AiStartStopStrategy;
import com.cet.eem.bll.energysaving.model.aioptimization.StrategyObjectMap;
import com.cet.eem.bll.energysaving.model.aioptimization.StrategyTypeDef;
import com.cet.eem.bll.energysaving.model.config.system.RefrigeratingSystem;
import com.cet.eem.bll.energysaving.model.dataentryquery.DataLogDataWrite;
import com.cet.eem.bll.energysaving.model.dataentryquery.DataQueryParam;
import com.cet.eem.bll.energysaving.service.predict.LgbModelPredictService;
import com.cet.eem.bll.energysaving.service.task.ColdOutWaterTempPredictService;
import com.cet.eem.bll.energysaving.service.task.ColdPredictDataService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.DataLogData;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.tool.ModelServiceUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : ColdOutWaterTempPredictServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-08-01 16:23
 */
@Slf4j
@Service
public class ColdOutWaterTempPredictServiceImpl implements ColdOutWaterTempPredictService {
    @Value("${cet.eem.task.energy-saving.outWaterTemp.startTime}")
    private String startTime;
    @Autowired
    RefrigeratingSystemDao refrigeratingSystemDao;
    @Autowired
    LgbModelPredictService lgbModelPredictService;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    ProjectDao projectDao;
    @Autowired
    AiStartStopStrategyDao aiStartStopStrategyDao;
    @Autowired
    StrategyObjectMapDao strategyObjectMapDao;
    @Autowired
    ColdPredictDataService coldPredictDataService;

    @Override
    public void saveColdOutWaterTempPredictData() {
        log.info("开始转存冷机出水温度预测结果数据。");
        long beginTime = System.currentTimeMillis();


        long count = 0;

        try {
            count = queryColdOutWaterTempData();
        } catch (Exception e) {
            log.error("[转存冷机出水温度预测结果数据]转存末端冷量预测结果异常", e);
        }

        long overTime = System.currentTimeMillis();
        log.info("本次转存冷机出水温度预测结果数据：{}，耗时(s)：{}", count, (overTime - beginTime) * CommonUtils.DOUBLE_CONVERSION_COEFFICIENT / 1000);
        log.info("结束转存冷机出水温度预测结果数据。");
    }

    private long queryColdOutWaterTempData() throws Exception {
        LocalDateTime time = parsePercentStartTime();
        List<RefrigeratingSystem> refrigeratingSystems = refrigeratingSystemDao.selectAll();
        if (CollectionUtils.isEmpty(refrigeratingSystems)) {
            return 0;
        }
        refrigeratingSystems = refrigeratingSystems.stream().filter(it ->
                Boolean.TRUE.equals(it.getUseAi()) ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(refrigeratingSystems)) {
            return 0;
        }
        List<Long> ids = refrigeratingSystems.stream().map(RefrigeratingSystem::getRoomId).collect(Collectors.toList());
        List<Map<String, Object>> maps = modelServiceUtils.queryWithChildren(NodeLabelDef.ROOM, ids, Collections.singletonList(NodeLabelDef.COLD_WATER_MAINENGINE));
        List<BaseVo> baseVos = JsonTransferUtils.transferList(maps, BaseVo.class);
        if (CollectionUtils.isEmpty(baseVos)) {
            return 0;
        }
        long count = 0L;
        for (RefrigeratingSystem refrigeratingSystem : refrigeratingSystems) {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            BaseVo node = baseVos.stream().filter(baseVo -> Objects.equals(refrigeratingSystem.getRoomId(), baseVo.getId()))
                    .findFirst().orElse(new BaseVo());
            if (CollectionUtils.isEmpty(node.getChildren())) {
                continue;
            }
            long l = queryColdOutWaterTempBatch(time, refrigeratingSystem, node.getChildren());

            stopWatch.stop();
            count += l;
            log.info("每个系统冷机出水温度数据转存执行时间:{}", stopWatch.getLastTaskTimeMillis());
        }

        return count;
    }

    /**
     * @return
     */
    private long queryColdOutWaterTempBatch(LocalDateTime time, RefrigeratingSystem refrigeratingSystem, List<BaseVo> nodes) throws Exception {
        List<LocalDateTime> timeRange = TimeUtil.getTimeRange(time, LocalDateTime.now(), AggregationCycle.ONE_HOUR);
        long count = 0;
        for (LocalDateTime time1 : timeRange) {
            count += queryColdOutWaterTemp(time1, refrigeratingSystem, nodes);
        }
        return count;
    }

    /**
     * @return
     */
    private long queryColdOutWaterTemp(LocalDateTime time, RefrigeratingSystem refrigeratingSystem, List<BaseVo> nodes) throws Exception {

        DataQueryParam query = coldPredictDataService.assembleTime(time);
        List<DataLogDataWrite> optimizationOfRefrigeratorWaterPredict = lgbModelPredictService.getOptimizationOfRefrigeratorWaterPredict
                (query, refrigeratingSystem.getProjectId());
        List<AiStartStopStrategy> strategies = aiStartStopStrategyDao.queryAiStartStopStrategy(refrigeratingSystem.getId(),
                Collections.singletonList(StrategyTypeDef.optimization), query.getStartTimePredict(), query.getEndTimePredict());
        List<Long> ids = strategies.stream().map(AiStartStopStrategy::getId).distinct().collect(Collectors.toList());
        List<StrategyObjectMap> strategyObjectMaps = strategyObjectMapDao.queryStrategyObjectMap(ids);

        return writeAndCheckOldData(optimizationOfRefrigeratorWaterPredict, strategies,
                strategyObjectMaps, refrigeratingSystem, time
                , nodes);
    }

    private List<AiStartStopStrategy> createAiStartStopStrategyList(List<DataLogData> dataList, RefrigeratingSystem refrigeratingSystem
            , LocalDateTime time) {
        List<AiStartStopStrategy> result = new ArrayList<>();
        for (DataLogData data : dataList) {
            AiStartStopStrategy strategy = new AiStartStopStrategy();
            strategy.setStrategyType(StrategyTypeDef.optimization);
            strategy.setOperationTime(data.getTime());
            strategy.setUpdateTime(TimeUtil.localDateTime2timestamp(time));
            strategy.setRefrigeratingSystemId(refrigeratingSystem.getId());
            result.add(strategy);
        }
        return result;
    }

    private List<StrategyObjectMap> createAiStartStopStrategyList(List<DataLogData> dataList,
                                                                  List<AiStartStopStrategy> strategies1, List<BaseVo> nodes) {
        List<StrategyObjectMap> result = new ArrayList<>();
        for (DataLogData data : dataList) {
            AiStartStopStrategy orElse = strategies1.stream().filter(aiStartStopStrategy -> Objects.equals(aiStartStopStrategy.getOperationTime(), data.getTime()))
                    .findFirst().orElse(new AiStartStopStrategy());
            for (BaseVo baseVo : nodes) {
                StrategyObjectMap strategy = new StrategyObjectMap();
                strategy.setStrategyId(orElse.getId());
                strategy.setTemp(data.getValue());
                strategy.setObjectId(baseVo.getId());
                strategy.setObjectLabel(baseVo.getModelLabel());
                result.add(strategy);
            }
        }
        return result;
    }

    private long writeAndCheckOldData(List<DataLogDataWrite> optimizationOfRefrigeratorWaterPredict, List<AiStartStopStrategy> strategies,
                                      List<StrategyObjectMap> strategyObjectMaps, RefrigeratingSystem refrigeratingSystem, LocalDateTime time
            , List<BaseVo> nodes) {
        if (CollectionUtils.isEmpty(optimizationOfRefrigeratorWaterPredict)) {
            return 0;
        }
        List<DataLogData> dataList = new ArrayList<>();
        for (DataLogDataWrite write : optimizationOfRefrigeratorWaterPredict) {
            dataList.addAll(write.getDataLogData());
        }
        //先比较策略，再比较策略详情
        List<AiStartStopStrategy> newStrategy = createAiStartStopStrategyList(dataList, refrigeratingSystem, time);
        List<AiStartStopStrategy> strategies1 = checkOldData(newStrategy, strategies);
        List<StrategyObjectMap> newMap = createAiStartStopStrategyList(dataList,
                strategies1, nodes);
        // 匹配数据库中已经有的数据
        checkOldDataStrategyObjectMap(newMap, strategyObjectMaps);
        return strategies1.size();
    }

    private List<AiStartStopStrategy> checkOldData(List<AiStartStopStrategy> predicts, List<AiStartStopStrategy> old) {

        // 匹配数据库中已经有的数据
        for (AiStartStopStrategy weather : predicts) {
            Optional<AiStartStopStrategy> any = old.stream().filter(item ->
                    Objects.equals(item.getOperationTime(), weather.getOperationTime()) &&
                            Objects.equals(weather.getRefrigeratingSystemId(), item.getRefrigeratingSystemId())
                            && Objects.equals(weather.getStrategyType(), item.getStrategyType())).findAny();
            any.ifPresent(item -> weather.setId(item.getId()));
        }
        return modelServiceUtils.writeData(predicts, AiStartStopStrategy.class);
    }

    private void checkOldDataStrategyObjectMap(List<StrategyObjectMap> predicts, List<StrategyObjectMap> old) {

        // 匹配数据库中已经有的数据
        for (StrategyObjectMap weather : predicts) {
            Optional<StrategyObjectMap> any = old.stream().filter(item ->
                    Objects.equals(item.getStrategyId(), weather.getStrategyId()) &&
                            Objects.equals(weather.getObjectId(), item.getObjectId())
                            && Objects.equals(weather.getObjectLabel(), item.getObjectLabel())).findAny();
            any.ifPresent(item -> weather.setId(item.getId()));
        }
        modelServiceUtils.writeData(predicts, StrategyObjectMap.class);
    }

    /**
     * 解析全局开始时间
     *
     * @return 全局开始时间
     */
    public LocalDateTime parsePercentStartTime() {
        if (StringUtils.isBlank(startTime)) {
            return LocalDateTime.now();
        }
        return TimeUtil.parse(startTime, TimeUtil.LONG_TIME_FORMAT);
    }
}
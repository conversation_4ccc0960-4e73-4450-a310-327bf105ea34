spring:
  application:
    name: mesdata-service
    jedis:
      pool:
        max-active: 8 # 最大连接数
        max-wait: -1 # 最大阻塞等待时间
        max-idle: 8 # 最大空闲
        min-idle: 0 # 最小空闲
    database: ${SPRING_REDIS_DATABASE}
    password: ${SPRING_REDIS_PASSWORD}
eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_CLIENT_SERVICEURL_DEFAULTZONE}
  instance:
    lease-renewal-interval-in-seconds: 15
    prefer-ip-address: true

cet:
  eem:
    event:
      pq:
        group-time: 10
    energy-saving:
      end-cold-capacity:  # 空调末端制冷
        coef: 3600
        specific-volume: 1.2
    task:
      energy-saving:
        diagnosis:
          basic:
            overall-start-time: '2021-12-01 00:00:00'
            interval: '-'
            max-demand-over-sacle: 0.1
          power-tariff:
            overall-start-time: '2021-01-01 00:00:00'
            interval: '-'
          electric-charge:
            overall-start-time: '2021-12-01 00:00:00'
            interval: '-'
            time-share-periodratio-type: 1
      percent:
        startTime: '2021-01-01 00:00:00' # 全局统计开始时间，注意格式是固定的：年-月-日 时:分:秒
        cycleChain: '12'
        interval: '-'
        eachNodes: 500
      period:
        startTime: '2021-01-01 00:00:00' # 全局统计开始时间，注意格式是固定的：年-月-日 时:分:秒
        interval: '-'
      consumption:
        startTime: '2021-01-01 00:00:00' # 全局统计开始时间，注意格式是固定的：年-月-日 时:分:秒
        cycleChain: '12'
        interval: '-'
eem:
  energyefficiency:
    year:
    month:
    day:




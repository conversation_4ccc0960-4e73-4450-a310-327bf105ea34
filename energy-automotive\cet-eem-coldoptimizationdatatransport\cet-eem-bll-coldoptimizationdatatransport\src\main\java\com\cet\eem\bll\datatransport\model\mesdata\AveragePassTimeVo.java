package com.cet.eem.bll.datatransport.model.mesdata;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.bll.datatransport.model.ModelDef;
import com.cet.eem.model.model.BaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ModelLabel(ModelDef.AVERAGE_PASS_TIME)
public class AveragePassTimeVo extends BaseEntity {
    public Long averagetime;
    public Long objectid;
    public String objectlabel;
    public String stationcode;
    public AveragePassTimeVo(){
        super.modelLabel = ModelDef.AVERAGE_PASS_TIME;
    }
}

package com.cet.eem.depletion.model.Constant;

public enum DepletionType {
    HEATING(1, "升温"),
    PASSING(2, "过车"),
    STOPPING(3, "结束"),
    OVER(4, "过线"),
    ENTER(5, "进车");

    private Integer depletionType;
    private String depletionTypeName;

    DepletionType(Integer depletionType, String depletionTypeName) {
        this.depletionType = depletionType;
        this.depletionTypeName = depletionTypeName;
    }

    public Integer getDepletionType() {return this.depletionType; }
    public String getDepletionTypeName() {return this.depletionTypeName; }

    public static String getDepletionTypeName(Integer depletionType){
        DepletionType[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            DepletionType status = var1[var3];
            if (status.depletionType == depletionType) {
                return status.depletionTypeName;
            }
        }
        return null;
    }
}

package com.cet.eem.bll.energysaving.model.config;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName : OperatingEfficiencyCurveVo
 * @Description : 运行曲线导入的
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-27 14:25
 */
@Data
@NoArgsConstructor
@ApiModel("运行曲线导入模型")
public class OperatingEfficiencyCurveVo {
    @ApiModelProperty("序号")
    @ExcelProperty(index = 0)
    private String number;

    @ApiModelProperty("冷量百分比")
    @ExcelProperty(index = 1)
    private String coldPercent;

    @ApiModelProperty("冷负荷")
    @ExcelProperty(index = 2)
    private String coldLoad;

    @ApiModelProperty("功率")
    @ExcelProperty(index = 3)
    private String power;

    @ApiModelProperty("cop")
    @ExcelProperty(index = 4)
    private String cop;

}
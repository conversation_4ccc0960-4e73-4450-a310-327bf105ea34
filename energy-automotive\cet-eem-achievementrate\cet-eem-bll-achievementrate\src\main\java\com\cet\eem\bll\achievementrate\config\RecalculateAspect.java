package com.cet.eem.bll.achievementrate.config;

import com.cet.eem.bll.achievementrate.model.Constant;
import com.cet.eem.common.exception.ValidationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/3/20 9:05
 */
@Component
public class RecalculateAspect {
    @Autowired
    @Qualifier("eem-redis-template")
    RedisTemplate<String,String> redisTemplate;


    public void before() {
        Boolean hasKey = redisTemplate.hasKey(Constant.RECALCULATE_KEY);
        /*if (Objects.equals(hasKey,true)){
            throw new ValidationException("当前重算方法未执行完,请执行完之后再请求!");
        }*/
        redisTemplate.opsForValue().append(Constant.RECALCULATE_KEY,LocalDateTime.now().toString());
    }


    public void after() {
        redisTemplate.unlink(Constant.RECALCULATE_KEY);
    }

}

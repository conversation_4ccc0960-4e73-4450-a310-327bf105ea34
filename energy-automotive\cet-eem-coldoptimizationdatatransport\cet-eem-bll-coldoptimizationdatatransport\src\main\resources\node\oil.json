[{"parentLabel": "project", "childLabel": ["oilcompany"], "startLabel": true}, {"parentLabel": "oilcompany", "childLabel": ["oilproductionplant", "gasmine", "purificationcompany", "room"]}, {"parentLabel": "oilproductionplant", "childLabel": ["operationarea"]}, {"parentLabel": "gasmine", "childLabel": ["gasoperationarea"]}, {"parentLabel": "purificationcompany", "childLabel": ["purificationplant"]}, {"parentLabel": "purificationcompany", "childLabel": ["purificationplant"]}, {"parentLabel": "operationarea", "childLabel": ["oilproductioncrew", "combinedstation"]}, {"parentLabel": "gasoperationarea", "childLabel": ["gasplatform", "dehydratingstation", "gasgatheringstation", "purificationplant", "waterinjectionstation"]}, {"parentLabel": "dehydratingstation", "childLabel": ["pump", "heatingfurnace", "pipeline", "gascompressor"]}, {"parentLabel": "purificationplant", "childLabel": ["pump", "heatingfurnace", "pipeline", "gascompressor"]}, {"parentLabel": "dehydratingstation", "childLabel": ["pump", "heatingfurnace", "pipeline", "gascompressor"]}, {"parentLabel": "combinedstation", "childLabel": ["pump", "heatingfurnace", "pipeline"]}, {"parentLabel": "oilproductioncrew", "childLabel": ["platform", "oiltransferstation", "waterinjectionstation"]}, {"parentLabel": "gasplatform", "childLabel": ["gaswell", "gascompressor"]}, {"parentLabel": "platform", "childLabel": ["oilwell", "waterwell", "electricheating"]}, {"parentLabel": "gaswell", "childLabel": ["mechanicalminingmachine"]}, {"parentLabel": "oilwell", "childLabel": ["mechanicalminingmachine"]}, {"parentLabel": "waterwell", "childLabel": ["pump", "flowmeter"]}, {"parentLabel": "oiltransferstation", "childLabel": ["measureroom", "pump", "heatingfurnace", "pipeline"]}, {"parentLabel": "waterinjectionstation", "childLabel": ["pump", "waterdistributionroom", "pipeline"]}, {"parentLabel": "room", "childLabel": ["linesegmentwithswitch", "powertransformer", "pump", "pipeline"]}]
package com.cet.eem.bll.energysaving.dao.aioptimization.impl;

import com.cet.eem.bll.energysaving.dao.aioptimization.ControlSchemeDao;
import com.cet.eem.bll.energysaving.model.config.system.ControlScheme;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * @ClassName : ControlSchemeDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-28 19:04
 */
@Repository
public class ControlSchemeDaoImpl extends ModelDaoImpl<ControlScheme> implements ControlSchemeDao {

    @Override
    public List<ControlScheme> queryControlSchemeBySystemId(Long systemId) {
        LambdaQueryWrapper<ControlScheme> wrapper = LambdaQueryWrapper.of(ControlScheme.class);
        wrapper.eq(ControlScheme::getRefrigeratingSystemId, systemId);
        List<ControlScheme> controlSchemes = this.selectList(wrapper);
        if (CollectionUtils.isEmpty(controlSchemes)) {
            return Collections.emptyList();
        }
        return controlSchemes;
    }
}
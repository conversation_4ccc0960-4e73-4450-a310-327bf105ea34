package com.cet.eem.bll.achievementrate.dao;

import com.cet.eem.bll.achievementrate.model.EnergyEfficiencyParam;
import com.cet.eem.bll.achievementrate.model.pojo.AchievementRate;
import com.cet.eem.bll.common.model.domain.subject.energy.EnergyConsumptionPlan;
import com.cet.eem.bll.common.model.domain.subject.energyefficiency.EnergyEfficiencyData;
import com.cet.eem.bll.common.model.domain.subject.production.ObjectCosValue;
import com.cet.eem.bll.common.model.domain.subject.production.UnitObjectCostValue;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.conditions.query.LambdaQueryWrapper;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/3/15 16:39
 */
public interface AchievementRateDao {

    void writeDatas(List<AchievementRate> rates);

    List<Map<String, Object>> selectAchievementByType(Integer type);

    List<AchievementRate> querAchievementRates(LocalDateTime st,LocalDateTime end,Integer cycle,List<Integer> energyTypes,List<Long> effSetIds,
                                               Integer type,List<Integer> productTypes,List<BaseVo> nodes);

    List<ObjectCosValue> queryObjectCostValues(LocalDateTime st, LocalDateTime end, Integer cycle, List<Integer> energyType, List<BaseVo> nodes);

    LambdaQueryWrapper<EnergyConsumptionPlan> getPlanWrapper(LocalDateTime st, LocalDateTime end, Integer cycle, List<BaseVo> nodes, Integer energyType);

    LambdaQueryWrapper<EnergyEfficiencyData> getEffWrapper(EnergyEfficiencyParam param);

    LambdaQueryWrapper<UnitObjectCostValue> getUnitEffWrapper(EnergyEfficiencyParam param);
}

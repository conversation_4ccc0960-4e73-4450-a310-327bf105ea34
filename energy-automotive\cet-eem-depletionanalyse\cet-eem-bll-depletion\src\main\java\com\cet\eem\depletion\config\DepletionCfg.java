package com.cet.eem.depletion.config;

import com.cet.eem.depletion.model.vo.DepletionCfgVo;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

@Service
public class DepletionCfg{
    @Value("${cet.eem.depletion.filepath:''}")
    private String filePath;

    protected static final Logger logger = LoggerFactory.getLogger(DepletionCfg.class);
    private static final String LOG_KEY = "{读取空耗划分的相关配置}";

    /**
     * 从指定路径读取电泳池的配置信息
     * */
    public List<DepletionCfgVo> getDepletionCfg() throws IOException {
        logger.info("{}：读取车间名称与编号对应关系", LOG_KEY);
        List<String> allLines = Files.readAllLines(Paths.get(filePath));
        List<DepletionCfgVo> res = new ArrayList<>();
        if (CollectionUtils.isEmpty(allLines)) {
            logger.info("{}：没有找到车间名称与编号对应关系，路径：{}", LOG_KEY, filePath);
            return res;
        }
        for (int i = 1; i < allLines.size(); i++) {
            String[] split = allLines.get(i).split(",");
            DepletionCfgVo depletionCfgVo = new DepletionCfgVo();
            depletionCfgVo.setObjectID(Long.valueOf(split[0]));
            depletionCfgVo.setObjectLabel(split[1]);
            depletionCfgVo.setStationID(Integer.valueOf(split[2]));
            depletionCfgVo.setChannelID(Integer.valueOf(split[3]));
            depletionCfgVo.setKeyWord(split[4]);
            depletionCfgVo.setStationCode(split[5]);
            depletionCfgVo.setNextStationCode(split[6]);
            res.add(depletionCfgVo);
        }
        return res;
    }
}

package com.cet.eem.bll.energysaving.model.config;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.bll.energysaving.model.def.ColdOptimizationLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : MesShift
 * @Description : mes班次
 * <AUTHOR> jiangzixuan
 * @Date: 2022-08-25 09:21
 */
@Getter
@Setter
@ModelLabel(ColdOptimizationLabelDef.MES_SHIFT)
public class MesShift extends BaseEntity {
    @JsonProperty(ColumnDef.PROJECT_ID)
    private Long projectId;
    @JsonProperty(ColumnDef.C_OBJECT_ID)
    private Long objectId;
    @JsonProperty(ColumnDef.C_OBJECT_Label)
    private String objectLabel;
    @JsonProperty(ColdOptimizationLabelDef.SHIFT_TYPE)
    private String shiftType;
    @JsonProperty(ColumnDef.START_TIME)
    private Long startTime;
    @JsonProperty(ColumnDef.END_TIME)
    private Long endTime;
    /**
     * 持续时间-小时
     */
    private Double hour;
    /**
     * 持续时间 ms
     */
    private Long duration;
    @JsonProperty(ColumnDef.LOG_TIME)
    private Long logTime;

    public MesShift() {
        this.modelLabel = ColdOptimizationLabelDef.MES_SHIFT;
    }

}
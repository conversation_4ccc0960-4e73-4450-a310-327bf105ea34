package com.cet.eem.constforecast.dao;

import com.cet.eem.bll.energy.model.costvalue.ObjectCostValueVo;
import com.cet.eem.common.model.BaseVo;

import java.util.List;

/**
 * @ClassName : ObjectCostValueDao
 * <AUTHOR> yangy
 * @Date: 2022-06-15 17:47
 */
public interface GivenObjectCostValueDao {
    List<ObjectCostValueVo> queryObjectCostValueVo(BaseVo node, long st, long et, int cycle, List<Integer> energyTypes);

}

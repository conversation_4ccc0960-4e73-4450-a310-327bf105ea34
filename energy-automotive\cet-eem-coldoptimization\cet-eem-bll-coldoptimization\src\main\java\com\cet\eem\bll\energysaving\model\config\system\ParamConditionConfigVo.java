package com.cet.eem.bll.energysaving.model.config.system;

import com.cet.eem.bll.energysaving.model.config.ParameterConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : ParamConditonConfigVo
 * @Description : 参数配置查询返回值
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-29 14:27
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ParamConditionConfigVo {
    private ParameterConfigVo config;
    private List<ConditionParam> params;
}
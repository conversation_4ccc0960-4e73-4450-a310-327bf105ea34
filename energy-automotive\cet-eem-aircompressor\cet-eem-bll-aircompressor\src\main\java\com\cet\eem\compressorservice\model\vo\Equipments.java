package com.cet.eem.compressorservice.model.vo;

import com.cet.eem.compressorservice.model.Aircompressor;
import com.cet.eem.compressorservice.model.Coolingtower;
import com.cet.eem.compressorservice.model.Dryingmachine;
import com.cet.eem.compressorservice.model.Pump;
import com.cet.eem.model.model.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName : RoomVo
 * <AUTHOR> yangy
 * @Date: 2022-04-02 11:05
 */
@Setter
@Getter
public class Equipments extends BaseEntity {
    List<Aircompressor> aircompressor_model;
    List<Pump> pump_model;
    List<Coolingtower> coolingtower_model;
    List<Dryingmachine> dryingmachine_model;

    public List<Aircompressor> getAircompressor_model() {
        if (Objects.isNull(aircompressor_model)) {
            return Collections.emptyList();
        }
        return aircompressor_model;
    }

    public List<Pump> getPumpNofilter() {
        if (Objects.isNull(pump_model)) {
            return Collections.emptyList();
        }
        return pump_model;
    }

    /**
     * 泵过滤出21类别的 数据
     *
     * @return
     */
    public List<Pump> getPump_model() {
        if (Objects.isNull(pump_model)) {
            return Collections.emptyList();
        }
        return pump_model.stream().filter(item -> Objects.equals(item.getFunctiontype(), 21)).collect(Collectors.toList());
    }

    public List<Coolingtower> getCoolingtower_model() {
        if (Objects.isNull(coolingtower_model)) {
            return Collections.emptyList();
        }
        return coolingtower_model;
    }

    public List<Dryingmachine> getDryingmachine_model() {
        if (Objects.isNull(dryingmachine_model)) {
            return Collections.emptyList();
        }
        return dryingmachine_model;
    }
}

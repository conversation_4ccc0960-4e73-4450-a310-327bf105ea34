package com.cet.eem.depletion.service.impl;

import com.cet.eem.bll.common.dao.node.EnergySupplyDao;
import com.cet.eem.common.exception.ValidationException;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.DataLogData;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.model.event.EventCondition;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.depletion.config.DepletionCfg;
import com.cet.eem.depletion.dao.DepletionDataDao;
import com.cet.eem.depletion.model.*;
import com.cet.eem.depletion.model.Constant.Constant;
import com.cet.eem.depletion.model.Constant.QuantityDef;
import com.cet.eem.depletion.model.vo.DepletionCfgVo;
import com.cet.eem.depletion.service.DepletionDivideForCataphoresisService;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.quantity.service.QuantityManageService;
import com.cet.eem.toolkit.CollectionUtils;
import com.google.common.collect.ImmutableList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class DepletionDivideForCataphoresisServiceImpl implements DepletionDivideForCataphoresisService {

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    QuantityManageService quantityManageService;

    @Autowired
    DepletionDataDao depletionDataDao;

    @Autowired
    EnergySupplyDao energySupplyDao;

    @Autowired
    DepletionCfg depletionCfg;

    @Autowired
    PartittionCommonService commonService;
    protected static final Logger logger = LoggerFactory.getLogger(DepletionDivideForCataphoresisServiceImpl.class);

    private static final String LOG_KEY = "[空耗划分计算-电泳池]";

    public static final Long ONE_DAY = 86400000L;


    //默认倍率
    @Value("${cet.eem.depletion.multiplyingpower:1.5}")
    public Double MULTIPLYINGPOWER;
    //默认节拍
    @Value("${cet.eem.depletion.takttime:77.4}")
    public Double TAKTTIME;
    //默认允许时间，单位：s
    @Value("${cet.eem.depletion.allowtime:1200}")
    public Long ALLOWTIME;


    List<MesPassPointDto> mesPassPointList = new ArrayList<>();
    List<Long> startTimeList = new ArrayList<>();
    List<Long> endTimeList = new ArrayList<>();
    List<DataLogData> tempDataLog = new ArrayList<>();
    List<DataLogData> standardTempDataLog = new ArrayList<>();
    List<Long> timeListForPass = new ArrayList<>();
    List<TempRiseInfoDto> tempRiseInfoList = new ArrayList<>();
    List<DepletionPartitionDto> depletionPartitionList = new ArrayList<>();

    @Override
    public void depletionDivide(Long startTime, Long endTime) throws IOException {
        logger.info("{}：开始执行升温信息和空耗时段的处理和转存，本次计算的起止时间为 {}~{}", LOG_KEY, TimeUtil.timestamp2LocalDateTime(startTime), TimeUtil.timestamp2LocalDateTime(endTime));
        List<CataphoresisDto> cataphoresisList = depletionDataDao.queryCataphoresis(Collections.singletonList(Constant.LINEBODYTYPE_CATA));
        for (CataphoresisDto item : cataphoresisList) {
            dataPrepareForDepletionDivide(startTime, endTime, item);
            dealTimeList();
            calcRisePeriod(startTime, item);
            //升温空耗划分
            heatingDepletion(item, startTime);
            //过车空耗划分
            passingDepletion(item, startTime);
        }
        modelServiceUtils.writeData(tempRiseInfoList, TempRiseInfoDto.class);
        logger.info("{}：升温信息入库，数量：{}", LOG_KEY, tempRiseInfoList.size());
        //朱峰：持续时间不超过5min的，过滤
        depletionPartitionList = depletionPartitionList.stream()
                .filter(x -> x.getDuration() > 300)
                .map(item -> {
                    if (Objects.equals(item.getDepletiontype(), Constant.DEPLTIONETYPE_HEATING)){
                        item.setStarttime(item.getStarttime() + ALLOWTIME * 1000);
                    }
                    return item;
                })
                .collect(Collectors.toList());
        modelServiceUtils.writeData(depletionPartitionList, DepletionPartitionDto.class);
        logger.info("{}：空耗划分信息入库，数量：{}", LOG_KEY, depletionPartitionList.size());
        logger.info("{}：升温信息和空耗时段的处理和转存完成", LOG_KEY);
        clearDataList();
    }

    /**
     * 准备升温信息、空耗划分所需模型和定时记录数据
     */
    private void dataPrepareForDepletionDivide(Long startTime, Long endTime, CataphoresisDto item) throws IOException {
        //检查电泳池属性是否完全，否则写入默认
        item.setAllowtime(Objects.nonNull(item.getAllowtime()) ? item.getAllowtime() : ALLOWTIME);
        item.setMultiplyingpower(Objects.nonNull(item.getMultiplyingpower()) ? item.getMultiplyingpower() : MULTIPLYINGPOWER);
        item.setTakttime(Objects.nonNull(item.getTakttime()) ? item.getTakttime() : TAKTTIME);
        //过车数据B08仅为洪流热水洗一个电泳池的数据，没有多个电泳池的数据，后续需要考虑过车数据和电泳池的匹配。当前只按照仅有一个电泳池来考虑
        List<DepletionCfgVo> depletionCfgs = depletionCfg.getDepletionCfg();
        Optional<DepletionCfgVo> first = depletionCfgs.stream().filter(x -> Objects.equals(Constant.CATAPHORESIS, x.getObjectLabel())
                && Objects.equals(item.getId(), x.getObjectID())).findFirst();
        if (!first.isPresent()) {
            logger.info("{}：未找到id：{}的事件配置记录", LOG_KEY, item.getId());
            throw new ValidationException("未找到id：" + item.getId() + "的事件配置记录");
        }
        DepletionCfgVo depletionCfgVo = first.get();
        mesPassPointList = depletionDataDao.queryMesPassPointData(startTime, endTime, Collections.singletonList(depletionCfgVo.getStationCode())).stream()
                .sorted(Comparator.comparing(MesPassPointDto::getPasstime)).collect(Collectors.toList());
        Map<Integer, List<TrendDataVo>> dataLogMap = depletionDataDao.queryQuantityData(Collections.singletonList(new BaseVo(item.getId(), item.getModelLabel())), startTime, endTime,
                ImmutableList.of(QuantityDef.getTempOfWaterTank(), QuantityDef.getStandardTempOfWaterTank()));

        startTimeList = depletionDataDao.queryProductionLineEventTime(startTime, endTime, createEventCondition(depletionCfgVo, Constant.EVENTCLASSES, Constant.EVENTTYPE_OPEN));
        endTimeList = depletionDataDao.queryProductionLineEventTime(startTime, endTime, createEventCondition(depletionCfgVo, Constant.EVENTCLASSES, Constant.EVENTTYPE_CLOSE));
        tempDataLog = dealDataLog(dataLogMap, QuantityDef.getTempOfWaterTank().getId());
        standardTempDataLog = dealDataLog(dataLogMap, QuantityDef.getStandardTempOfWaterTank().getId());
    }

    private void clearDataList() {
        mesPassPointList.clear();
        startTimeList.clear();
        endTimeList.clear();
        tempDataLog.clear();
        standardTempDataLog.clear();
        timeListForPass.clear();
        tempRiseInfoList.clear();
        depletionPartitionList.clear();
    }

    private List<DataLogData> dealDataLog(Map<Integer, List<TrendDataVo>> dataLogMap, Integer quantityID) {
        List<TrendDataVo> trendDataVos = dataLogMap.get(quantityID);
        if (trendDataVos.isEmpty()) {
            logger.info("{}：物理量-{}的数据为空", LOG_KEY, quantityID);
            return new ArrayList<>();
        }
        return trendDataVos.get(0).getDataList();
    }

    private EventCondition createEventCondition(DepletionCfgVo item, Integer eventClasses, Integer eventType) {
        EventCondition eventCondition = new EventCondition();
        eventCondition.setChannelId(item.getChannelID());
        eventCondition.setStationId(item.getStationID());
        eventCondition.setEventClasses(Collections.singletonList(eventClasses));
        eventCondition.setEventTypes(Collections.singletonList(eventType));
        eventCondition.setKeyWord(item.getKeyWord());
        return eventCondition;
    }

    /**
     * 获取指定时间戳对应的温度数据
     */
    private Double getAppointTemp(List<DataLogData> tempOfWaterTankDataLogs, Long time) {
        if (tempOfWaterTankDataLogs.isEmpty()) {
            return null;
        }
        //直接按照时刻匹配对应时刻的温度
        Optional<DataLogData> first = tempOfWaterTankDataLogs.stream().filter(x -> Objects.equals(x.getTime(), time)).findFirst();
        if (first.isPresent()) {
            return first.get().getValue();
        } else {
            //匹配不到则寻找临近的第一个时刻的温度
            Optional<DataLogData> next = tempOfWaterTankDataLogs.stream().filter(x -> (x.getTime() > time - 300000L && x.getTime() < time + 300000L)).findFirst();
            return next.map(DataLogData::getValue).orElse(null);
        }

    }

    /**
     * 开关机时刻的定时记录特殊，仅保存开关机变化的时间戳，如果没有变化，则会一直保持之前变化时刻的时间戳
     * 如12/14点开机，在12~14点之间的记录，会一直保持在12点
     * 所以需要去重
     */
    private List<DataLogData> getTimeList(List<DataLogData> item, Long startTime, Long endTime) {
        List<Double> collect = item.stream().map(DataLogData::getValue).filter(x -> x >= startTime / 1000 && x < endTime / 1000).distinct().collect(Collectors.toList());
        List<DataLogData> list = new ArrayList<>();
        for (Double x : collect) {
            //注意，startTimeDataLog和endTimeDataLog查出来的开关机时刻是秒时间戳，转为毫秒时间戳
            x *= 1000;
            DataLogData dataLogData = new DataLogData(x.longValue(), x);
            list.add(dataLogData);
        }
        return list;
    }

    /**
     * 取出过车时间进行排序
     */
    private void dealTimeList() {
        timeListForPass.addAll(mesPassPointList.stream().map(MesPassPointDto::getPasstime).collect(Collectors.toList()));
        timeListForPass = timeListForPass.stream().sorted().collect(Collectors.toList());
    }

    /**
     * 从目标时间list中找到与指定time相邻的时间戳
     */
    private Long getNextTime(List<Long> timeList, Long time, int offset) {
        List<Long> collect = new ArrayList<>(timeList);
        collect.add(time);
        collect = collect.stream().sorted().collect(Collectors.toList());
        for (int i = 0; i < collect.size(); i++) {
            if (Objects.equals(time, collect.get(i))) {
                if (i + offset <= 0) {
                    return collect.get(0);
                } else if (i + offset >= collect.size()) {
                    return collect.get(collect.size() - 1);
                } else {
                    return collect.get(i + offset);
                }
            }
        }
        return time;
    }

    /**
     * 计算升温信息
     */
    private void calcRisePeriod(Long logTime, CataphoresisDto item) {
        if (startTimeList.isEmpty() || CollectionUtils.isEmpty(standardTempDataLog)) {
            logger.info("{}：开机时间为空，温度信息为空,不计算升温信息", LOG_KEY);
            return;
        }
        for (Long startTime : startTimeList) {
            TempRiseInfoDto res = new TempRiseInfoDto();
            //温度设定值，正常情况下不会一直变化，取一个即可
            Double standardTemp = standardTempDataLog.get(0).getValue();
            //温度达标的时间，为空耗开始的时间，从温度定时记录中过滤 时间>开机时间 且 温度>达标温度的记录
            Long closeTime;
            if(endTimeList.isEmpty()){
                closeTime = logTime + ONE_DAY;
            }else {
                closeTime = getNextTime(endTimeList, startTime, 1);
            }
            Long finalCloseTime = closeTime;
            Optional<DataLogData> first = tempDataLog.stream().filter(x -> x.getTime() >= startTime && x.getTime() <= finalCloseTime && x.getValue() >= standardTemp).findFirst();
            Long endTime = first.isPresent() ? first.get().getTime() : startTime;
            List<Long> collect = timeListForPass.stream().filter(x -> x >= startTime && x <= finalCloseTime).collect(Collectors.toList());
            Long passTime = getNextTime(collect, startTime, 1);

            res.setStarttime(startTime);
            res.setEndtime(endTime);
            res.setDuration((endTime - startTime) / 1000);
            if (!Objects.equals(startTime, passTime)) {
                res.setFirstcarpasstime(passTime);
            }
            res.setFirstcarpasstime(passTime);
            res.setTemperature(getAppointTemp(tempDataLog, startTime));
            res.setAllowtime(item.getAllowtime());
            res.setLogtime(TimeUtil.getFirstTimeOfDay(logTime));
            res.setObjectid(item.getId());
            res.setObjectlabel(item.getModelLabel());
            res.setProjectid(1L);
            tempRiseInfoList.add(res);
        }
    }

    /**
     * 结束空耗
     */
    private void stoppingDepletion(CataphoresisDto item, Long logTime) {
        if (endTimeList.isEmpty()) {
            logger.info("{}：关机时间为空，不计算结束空耗", LOG_KEY);
            return;
        }
        for (Long endTime : endTimeList) {
            //找到关机前的最后一个过车时间
            Long lastPassingTime = getNextTime(timeListForPass, endTime, -1);
            //理论关机时间 = 最后一个过车时间+计划节拍*倍率
            Long theoretical = lastPassingTime + Math.round(item.getTakttime() * 1000 * item.getMultiplyingpower());
            //当时理论关机时间 >= 实际关机时间，不存在空耗，跳过
            if (theoretical >= endTime) {
                continue;
            }

            DepletionPartitionDto res = new DepletionPartitionDto();

            res.setStarttime(theoretical);
            res.setEndtime(endTime);
            res.setDuration((endTime - theoretical) / 1000);
            res.setDepletiontype(Constant.DEPLTIONETYPE_STOPPING);
            res.setLogtime(TimeUtil.getFirstTimeOfDay(logTime));
            res.setProjectid(1L);
            res.setObjectid(item.getId());
            res.setObjectlabel(item.getModelLabel());

            depletionPartitionList.add(res);
        }
    }

    /**
     * 过车空耗
     */
    private void passingDepletion(CataphoresisDto item, Long logTime) {
        if (CollectionUtils.isEmpty(mesPassPointList)) {
            return;
        }
        List<MesPassPointDto> passPointDtos;
        //有开机 ,关机 不保证有
        if (CollectionUtils.isNotEmpty(startTimeList)) {
            for (Long st : startTimeList) {
                Long end = getNextTime(endTimeList, st, 1);
                //只有开机记录
                if (Objects.equals(st, end)) {
                    passPointDtos = mesPassPointList.stream().filter(pass -> st <= pass.getPasstime()).collect(Collectors.toList());
                } else {
                    passPointDtos = mesPassPointList.stream().filter(pass -> st <= pass.getPasstime() && pass.getPasstime() <= end).collect(Collectors.toList());
                }
                passingDepletionExcuet(passPointDtos, item, logTime);
            }
            //有关机 无开机
        } else if (CollectionUtils.isNotEmpty(endTimeList) && CollectionUtils.isEmpty(startTimeList)) {
            for (Long end : endTimeList) {
                passPointDtos = mesPassPointList.stream().filter(pass -> pass.getPasstime() <= end).collect(Collectors.toList());
                passingDepletionExcuet(passPointDtos, item, logTime);
            }
            //无开机 无关机 有过车
        } else if (CollectionUtils.isEmpty(startTimeList) && CollectionUtils.isEmpty(endTimeList)) {
            Boolean lastDayIsOpen = commonService.getLastDayIsOpen(item, logTime);
            if (!lastDayIsOpen) {
                return;
            }
            passPointDtos = mesPassPointList;
            passingDepletionExcuet(passPointDtos, item, logTime);
        }
    }

    private void passingDepletionExcuet(List<MesPassPointDto> passPointDtos, CataphoresisDto item, Long logTime) {
        if (passPointDtos.size() < 2) {
            return;
        }
        for (int i = 0; i < passPointDtos.size() - 1; i++) {
            MesPassPointDto first = passPointDtos.get(i);
            MesPassPointDto next = passPointDtos.get(i + 1);
            //理论下一个进车时间 = 上一台进车时刻+计划节拍*倍率
            Long theoretical = first.getPasstime() + Math.round(item.getTakttime() * 1000 * item.getMultiplyingpower());
            //当时实际下一个进车时间 <= 理论时间时，不存在空耗，跳过
            if (next.getPasstime() <= theoretical) {
                continue;
            }
            DepletionPartitionDto res = new DepletionPartitionDto();
            res.setStarttime(theoretical);
            res.setEndtime(next.getPasstime());
            res.setDuration((next.getPasstime() - theoretical) / 1000);
            res.setDepletiontype(Constant.DEPLTIONETYPE_PASSING);
            res.setLogtime(TimeUtil.getFirstTimeOfDay(logTime));
            res.setProjectid(1L);
            res.setObjectid(item.getId());
            res.setObjectlabel(item.getModelLabel());
            depletionPartitionList.add(res);
        }
    }

    /**
     * 升温空耗   开机不干活
     */
    private void heatingDepletion(CataphoresisDto item, Long logTime) {
        if (CollectionUtils.isNotEmpty(startTimeList)) {
            for (Long openTime : startTimeList) {
                //温度设定值，转存情况下不会一直变化，取一个即可
                Long tempTime = null;
                if (CollectionUtils.isNotEmpty(standardTempDataLog)) {
                    Double standardTemp = standardTempDataLog.get(0).getValue();
                    //温度达标的时间，为空耗开始的时间，从温度定时记录中过滤 时间>开机时间 且 温度>达标温度的记录
                    tempTime = tempDataLog.stream().filter(x -> x.getTime() >= openTime && x.getValue() >= standardTemp).map(DataLogData::getTime).findFirst().orElse(null);
                }
                //温度达标时间
                Long startTime = Objects.nonNull(tempTime) ? tempTime : openTime;
                //关机时间
                Long closeTime = getNextTime(endTimeList, openTime, 1);
                //温度达标后，第一个过车时间为空耗结束时间
                Long passTime = getNextTime(timeListForPass.stream().filter(x -> x >= openTime && x <= closeTime).collect(Collectors.toList()), openTime, 1);
                Long allowTime = item.getAllowtime() * 1000;

                //每天的最后一次开机，需要检查是否存在不关机的情况，当时getNextTime返回的结果与温度达标时间一致时，认为最后一次开机并且温度达标后，没有进车，视为忘记关机
                //有开机 无关机 无过车
                if (Objects.equals(openTime, closeTime) && Objects.equals(openTime, passTime)) {
                    DepletionPartitionDto partitionDto = getDepletionPartitionDto(item, openTime, logTime + ONE_DAY, Constant.DEPLTIONETYPE_HEATING);
                    depletionPartitionList.add(partitionDto);
                    continue;
                }
                //有开机,有关机 ,但是无过车信息
                if (!Objects.equals(openTime, closeTime) && Objects.equals(openTime, passTime)) {
                    DepletionPartitionDto partitionDto = getDepletionPartitionDto(item, openTime, closeTime, Constant.DEPLTIONETYPE_HEATING);
                    depletionPartitionList.add(partitionDto);
                    continue;
                }
                //有开机 ,有关机 有过车信息   --- 有开机 无关机 有过车  同样逻辑  第一台车同样逻辑 最后一台车 不算
                if (!Objects.equals(openTime, passTime)) {
                    //温度达标时间 + 允许时间 >= 过车时间，说明无空耗，跳过
                    if (startTime + allowTime >= passTime) {
                        continue;
                    }
                    DepletionPartitionDto res = new DepletionPartitionDto();
                    res.setStarttime(startTime);
                    res.setEndtime(passTime);
                    res.setDuration((passTime - startTime - allowTime > 0 ? passTime - startTime - allowTime : 0) / 1000);
                    res.setDepletiontype(Constant.DEPLTIONETYPE_HEATING);
                    res.setLogtime(TimeUtil.getFirstTimeOfDay(logTime));
                    res.setProjectid(1L);
                    res.setObjectid(item.getId());
                    res.setObjectlabel(item.getModelLabel());
                    depletionPartitionList.add(res);
                }
            }
            //只有关机 没有开机
        } else if (CollectionUtils.isNotEmpty(endTimeList)) {
            //无过车 电泳池中不算
            if (CollectionUtils.isEmpty(mesPassPointList)) {
                return;
            }
            for (Long endTime : endTimeList) {
                if(CollectionUtils.isEmpty(startTimeList) || endTime > startTimeList.get(0)){
                    List<MesPassPointDto> passInfos = mesPassPointList.stream().filter(pass -> pass.getPasstime() <= endTime).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(passInfos)) {
                        continue;
                    }
                    MesPassPointDto first = passInfos.get(0);
                    DepletionPartitionDto partitionDto = getDepletionPartitionDto(item, logTime, first.getPasstime(), Constant.DEPLTIONETYPE_HEATING);
                    depletionPartitionList.add(partitionDto);
                }

            }
            //当天 无关机 无开机
        } else if (CollectionUtils.isEmpty(endTimeList) && CollectionUtils.isEmpty(startTimeList)) {
            Boolean isOpen = commonService.getLastDayIsOpen(item, logTime);
            if (!isOpen) {
                return;
            }
            if (CollectionUtils.isEmpty(mesPassPointList)) {
                DepletionPartitionDto partitionDto = getDepletionPartitionDto(item, logTime, logTime + ONE_DAY, Constant.DEPLTIONETYPE_HEATING);
                depletionPartitionList.add(partitionDto);
            } else {
                MesPassPointDto passPointDto = mesPassPointList.get(0);
                DepletionPartitionDto partitionDto = getDepletionPartitionDto(item, logTime, passPointDto.getPasstime(), Constant.DEPLTIONETYPE_HEATING);
                depletionPartitionList.add(partitionDto);
            }
        }
    }


    /*
     * 开机后忘记关机的情形
     * 如12日22点开机，23点达标，第二天进车，按照13日零点做空耗切分
     * */
    private void specialTreatmentB(CataphoresisDto item, Long logTime, Long startTime) {
        DepletionPartitionDto res = new DepletionPartitionDto();
        res.setStarttime(startTime);
        res.setEndtime(TimeUtil.getFirstTimeOfDay(logTime + ONE_DAY));
        res.setDuration((res.getEndtime() - res.getStarttime()) / Constant.ONE_S);
        res.setDepletiontype(Constant.DEPLTIONETYPE_HEATING);
        res.setLogtime(TimeUtil.getFirstTimeOfDay(logTime));
        res.setObjectid(item.getId());
        res.setProjectid(1L);
        res.setObjectlabel(item.getModelLabel());
        depletionPartitionList.add(res);
    }

    /**
     * 开机时间不在本天
     * 12日22点开机，23点达标，第二天进车，13日1点进车，则用第一次进车和第一次开机作比较
     */
    private void specialTreatmentC(CataphoresisDto item, Long logTime, Long endTime) {
        DepletionPartitionDto res = new DepletionPartitionDto();
        res.setStarttime(TimeUtil.getFirstTimeOfDay(logTime));
        res.setEndtime(endTime);
        res.setDuration((endTime - TimeUtil.getFirstTimeOfDay(logTime)) / 1000);
        res.setDepletiontype(Constant.DEPLTIONETYPE_HEATING);
        res.setLogtime(TimeUtil.getFirstTimeOfDay(logTime));
        res.setObjectid(item.getId());
        res.setObjectlabel(item.getModelLabel());
        res.setProjectid(1L);
        depletionPartitionList.add(res);
    }


    private DepletionPartitionDto getDepletionPartitionDto(CataphoresisDto item, Long st, Long end, Integer type) {
        DepletionPartitionDto res = new DepletionPartitionDto();
        res.setStarttime(st);
        res.setEndtime(end);
        res.setDuration((end - st) / 1000);
        res.setDepletiontype(type);
        res.setLogtime(TimeUtil.getFirstTimeOfDay(st));
        res.setProjectid(1L);
        res.setObjectid(item.getId());
        res.setObjectlabel(item.getModelLabel());
        return res;
    }
}

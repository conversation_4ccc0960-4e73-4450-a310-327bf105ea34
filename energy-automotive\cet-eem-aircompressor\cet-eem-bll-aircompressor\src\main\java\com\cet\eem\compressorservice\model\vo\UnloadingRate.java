package com.cet.eem.compressorservice.model.vo;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * @ClassName : UnloadingRate
 * <AUTHOR> yangy
 * @Date: 2022-04-08 10:19
 */
@Data
public class UnloadingRate {
    Long airCompressorId;

    List<PlanTimeData> data;

    public UnloadingRate() {
    }

    public UnloadingRate(List<PlanTimeData> data) {
        this.data = data;
    }

    public UnloadingRate(Long airCompressorId) {
        this.airCompressorId = airCompressorId;
        this.data=new ArrayList<>();
    }

    public List<PlanTimeData> getData() {
        if (CollectionUtils.isNotEmpty(data)){
            Collections.sort(data, Comparator.comparing(PlanTimeData::getTime));
        }
        return data;
    }
}

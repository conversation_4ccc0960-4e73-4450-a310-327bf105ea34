package com.cet.eem.bll.compressoroptimization.model.strategy;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : AiCompressorStrategyWithLayer
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-24 16:07
 */
@Getter
@Setter
public class AiCompressorStrategyWithLayer extends AiCompressorStrategy{
    @JsonProperty("strategyobjectconfig_model")
    private List<StrategyObjectConfig> strategyObjectConfigs;
}
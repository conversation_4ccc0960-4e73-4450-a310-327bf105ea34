package com.cet.eem.bll.energysaving.dao.aioptimization.impl;

import com.cet.eem.bll.energysaving.dao.aioptimization.AiStartStopStrategyDao;
import com.cet.eem.bll.energysaving.model.aioptimization.AiStartStopStrategy;
import com.cet.eem.common.model.Page;
import com.cet.eem.common.model.ResultWithTotal;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : AiStartStopStrategyDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-04-19 10:42
 */
@Repository
public class AiStartStopStrategyDaoImpl extends ModelDaoImpl<AiStartStopStrategy> implements AiStartStopStrategyDao {

    @Override
    public List<AiStartStopStrategy> queryAiStartStopStrategy(Long refrigeratingSystemId, List<Integer> type) {
        LambdaQueryWrapper<AiStartStopStrategy> wrapper = LambdaQueryWrapper.of(AiStartStopStrategy.class);
        if (Objects.nonNull(refrigeratingSystemId)) {
            wrapper.eq(AiStartStopStrategy::getRefrigeratingSystemId, refrigeratingSystemId);
        }
        if (CollectionUtils.isNotEmpty(type)) {
            wrapper.in(AiStartStopStrategy::getStrategyType, type);
        }
        List<AiStartStopStrategy> aiStartStopStrategies = this.selectList(wrapper);
        if (CollectionUtils.isEmpty(aiStartStopStrategies)) {
            return Collections.emptyList();
        }
        List<AiStartStopStrategy> result = new ArrayList<>();
        Map<Integer, List<AiStartStopStrategy>> map = aiStartStopStrategies.stream().collect(Collectors.groupingBy(AiStartStopStrategy::getStrategyType));
        for (Map.Entry<Integer, List<AiStartStopStrategy>> entry : map.entrySet()) {
            List<AiStartStopStrategy> collect = entry.getValue().stream().sorted(Comparator.comparing(AiStartStopStrategy::getUpdateTime).reversed()).collect(Collectors.toList());
            result.add(collect.get(0));
        }
        return result;
    }

    @Override
    public List<AiStartStopStrategy> queryAiStartStopStrategy(Long refrigeratingSystemId, List<Integer> type, LocalDateTime st, LocalDateTime et) {
        LambdaQueryWrapper<AiStartStopStrategy> wrapper = LambdaQueryWrapper.of(AiStartStopStrategy.class);
        wrapper.ge(AiStartStopStrategy::getOperationTime,st)
                .lt(AiStartStopStrategy::getOperationTime,et);
        if (Objects.nonNull(refrigeratingSystemId)) {
            wrapper.eq(AiStartStopStrategy::getRefrigeratingSystemId, refrigeratingSystemId);
        }
        if (CollectionUtils.isNotEmpty(type)) {
            wrapper.in(AiStartStopStrategy::getStrategyType, type);
        }
        List<AiStartStopStrategy> aiStartStopStrategies = this.selectList(wrapper);
        if (CollectionUtils.isEmpty(aiStartStopStrategies)) {
            return Collections.emptyList();
        }
        return aiStartStopStrategies;
    }

    @Override
    public List<AiStartStopStrategy> queryAiStartStopStrategy(List<Long> refrigeratingSystemId, List<Integer> type, LocalDateTime st, LocalDateTime et) {
        LambdaQueryWrapper<AiStartStopStrategy> wrapper = LambdaQueryWrapper.of(AiStartStopStrategy.class);
        wrapper.ge(AiStartStopStrategy::getOperationTime,st)
                .lt(AiStartStopStrategy::getOperationTime,et);
        if (CollectionUtils.isNotEmpty(refrigeratingSystemId)) {
            wrapper.in(AiStartStopStrategy::getRefrigeratingSystemId, refrigeratingSystemId);
        }
        if (CollectionUtils.isNotEmpty(type)) {
            wrapper.in(AiStartStopStrategy::getStrategyType, type);
        }
        List<AiStartStopStrategy> aiStartStopStrategies = this.selectList(wrapper);
        if (CollectionUtils.isEmpty(aiStartStopStrategies)) {
            return Collections.emptyList();
        }
        return aiStartStopStrategies;
    }

    @Override
    public ResultWithTotal<List<AiStartStopStrategy>>  queryAiStartStopStrategyWithPage(Long refrigeratingSystemId, List<Integer> type, LocalDateTime st, LocalDateTime et, Page page) {
        LambdaQueryWrapper<AiStartStopStrategy> wrapper = LambdaQueryWrapper.of(AiStartStopStrategy.class);
        wrapper.ge(AiStartStopStrategy::getOperationTime,st)
                .lt(AiStartStopStrategy::getOperationTime,et);
        if (Objects.nonNull(refrigeratingSystemId)) {
            wrapper.eq(AiStartStopStrategy::getRefrigeratingSystemId, refrigeratingSystemId);
        }
        if (CollectionUtils.isNotEmpty(type)) {
            wrapper.in(AiStartStopStrategy::getStrategyType, type);
        }
        wrapper.orderByDesc(AiStartStopStrategy::getOperationTime);
        return this.selectPage(wrapper, page);

    }
}
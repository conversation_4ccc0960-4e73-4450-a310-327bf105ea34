package com.cet.eem.depletion.service.impl;

import com.cet.eem.bll.common.dao.node.EnergySupplyDao;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.DataLogData;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.depletion.dao.DepletionDataDao;
import com.cet.eem.depletion.model.*;
import com.cet.eem.depletion.model.Constant.Constant;
import com.cet.eem.depletion.model.Constant.QuantityDef;
import com.cet.eem.depletion.service.DepletionDivideService;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.quantity.service.QuantityManageService;
import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import com.google.common.collect.ImmutableList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class DepletionDivideServiceImpl implements DepletionDivideService {

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    QuantityManageService quantityManageService;

    @Autowired
    DepletionDataDao depletionDataDao;

    @Autowired
    EnergySupplyDao energySupplyDao;

    protected static final Logger logger = LoggerFactory.getLogger(DepletionDivideServiceImpl.class);

    private static final String LOG_KEY = "[空耗划分计算]";

    public static final Long ONE_DAY = 86400000L;

    //默认倍率
    @Value("${cet.eem.depletion.multiplyingpower:1.5}")
    public static Double MULTIPLYINGPOWER;
    //默认节拍
    @Value("${cet.eem.depletion.takttime:77.4}")
    public static Double TAKTTIME;
    //默认允许时间，单位：s
    @Value("${cet.eem.depletion.allowtime:1200}")
    public static Long ALLOWTIME;

    List<CataphoresisDto> cataphoresisList = new ArrayList<>();
    List<MesPassPointDto> mesPassPointList = new ArrayList<>();
    List<DatalogValue> startTimeDataLog = new ArrayList<>();
    List<DatalogValue> endTimeDataLog = new ArrayList<>();
    List<DatalogValue> tempDataLog = new ArrayList<>();
    List<DatalogValue> standardTempDataLog = new ArrayList<>();
    List<Long> timeListForPass = new ArrayList<>();
    List<TempRiseInfoDto> tempRiseInfoList = new ArrayList<>();
    List<DepletionPartitionDto> depletionPartitionList = new ArrayList<>();

    @Override
    public void depletionDivide(Long startTime, Long endTime) {
        logger.info("{}：开始执行升温信息和空耗时段的处理和转存，本次计算的起止时间为 {}~{}", LOG_KEY, TimeUtil.timestamp2LocalDateTime(startTime), TimeUtil.timestamp2LocalDateTime(endTime));
        dataPrepareForDepletionDivide(startTime, endTime);
        dealTimeList();
        calcRisePeriod(startTime);
        calcDepletionPartition(startTime);

        modelServiceUtils.writeData(tempRiseInfoList, TempRiseInfoDto.class);
        logger.info("{}：升温信息入库，数量：{}", LOG_KEY, tempRiseInfoList.size());
        modelServiceUtils.writeData(depletionPartitionList, DepletionPartitionDto.class);
        logger.info("{}：空耗划分信息入库，数量：{}", LOG_KEY, depletionPartitionList.size());
        logger.info("{}：升温信息和空耗时段的处理和转存完成", LOG_KEY);
        clearDataList();
    }

    /**
     * 准备升温信息、空耗划分所需模型和定时记录数据
     * */
    private void dataPrepareForDepletionDivide(Long startTime, Long endTime) {
        cataphoresisList = depletionDataDao.queryCataphoresis();
        if (cataphoresisList.isEmpty() ){
            logger.info("{}：查询不到电泳池模型信息，退出转存任务", LOG_KEY);
            return;
        }
        //检查电泳池属性是否完全，否则写入默认
        for (CataphoresisDto item : cataphoresisList){
            item.setAllowtime(Objects.nonNull(item.getAllowtime()) ? item.getAllowtime() : ALLOWTIME);
            item.setMultiplyingpower(Objects.nonNull(item.getMultiplyingpower()) ? item.getMultiplyingpower() : MULTIPLYINGPOWER);
            item.setTakttime(Objects.nonNull(item.getTakttime()) ? item.getTakttime() : TAKTTIME);
        }
        mesPassPointList = depletionDataDao.queryMesPassPointData(startTime, endTime).stream()
                .sorted(Comparator.comparing(MesPassPointDto::getPasstime)).collect(Collectors.toList());
        //过车数据B08仅为洪流热水洗一个电泳池的数据，没有多个电泳池的数据，后续需要考虑过车数据和电泳池的匹配。当前只按照仅有一个电泳池来考虑
        CataphoresisDto collect = cataphoresisList.get(0);
        Map<Integer, List<TrendDataVo>> dataLogMap = depletionDataDao.queryQuantityData(Collections.singletonList(new BaseVo(collect.getId(), collect.getModelLabel())), startTime, endTime,
                ImmutableList.of(QuantityDef.getProductionLineStartupTime(), QuantityDef.getProductionLineShutDownTime(),
                        QuantityDef.getTempOfWaterTank(), QuantityDef.getStandardTempOfWaterTank()));

        if (mesPassPointList.isEmpty() || dataLogMap.isEmpty()){
            logger.info("{}：查询不到mes过车数据 或 电泳池定时记录数据，退出转存任务", LOG_KEY);
            return;
        }
        startTimeDataLog = getTimeList(dealDataLog(dataLogMap, QuantityDef.getProductionLineStartupTime().getId()), startTime, endTime);
        endTimeDataLog = getTimeList(dealDataLog(dataLogMap, QuantityDef.getProductionLineShutDownTime().getId()), startTime, endTime);
        tempDataLog = dealDataLog(dataLogMap, QuantityDef.getTempOfWaterTank().getId());
        standardTempDataLog = dealDataLog(dataLogMap, QuantityDef.getStandardTempOfWaterTank().getId());

    }
    
    private void clearDataList(){
        cataphoresisList.clear();
        mesPassPointList.clear();
        startTimeDataLog.clear();
        endTimeDataLog.clear();
        tempDataLog.clear();
        standardTempDataLog.clear();
        timeListForPass.clear();
        tempRiseInfoList.clear();
        depletionPartitionList.clear();
    }

    private List<DatalogValue> dealDataLog(Map<Integer, List<TrendDataVo>> dataLogMap, Integer quantityID){
        List<TrendDataVo> trendDataVos = dataLogMap.get(quantityID);
        if (trendDataVos.isEmpty()){
            logger.info("{}：物理量-{}的数据为空", LOG_KEY, quantityID);
            return new ArrayList<>();
        }
        return trendDataVos.get(0).getDataList();
    }

    /**
     * 获取指定时间戳对应的温度数据
     * */
    private Double getAppointTemp(List<DatalogValue> tempOfWaterTankDataLogs, Long time){
        if (tempOfWaterTankDataLogs.isEmpty()){
            return null;
        }
        //直接按照时刻匹配对应时刻的温度
        Optional<DatalogValue> first = tempOfWaterTankDataLogs.stream().filter(x -> Objects.equals(x.getTime(), time)).findFirst();
        if (first.isPresent()){
            return first.get().getValue();
        }else {
            //匹配不到则寻找临近的第一个时刻的温度
            Optional<DatalogValue> next = tempOfWaterTankDataLogs.stream().filter(x -> (x.getTime() > time - 300000L && x.getTime() < time + 300000L)).findFirst();
            return next.map(DatalogValue::getValue).orElse(null);
        }

    }

    /**
    * 开关机时刻的定时记录特殊，仅保存开关机变化的时间戳，如果没有变化，则会一直保持之前变化时刻的时间戳
    * 如12/14点开机，在12~14点之间的记录，会一直保持在12点
    * 所以需要去重
    * */
    private List<DatalogValue> getTimeList(List<DatalogValue> item, Long startTime, Long endTime){
        List<Double> collect = item.stream().map(DatalogValue::getValue).filter(x -> x>=startTime/1000 && x<endTime/1000).distinct().collect(Collectors.toList());
        List<DatalogValue> list = new ArrayList<>();
        for (Double x : collect) {
            //注意，startTimeDataLog和endTimeDataLog查出来的开关机时刻是秒时间戳，转为毫秒时间戳
            x *= 1000;
            DatalogValue dataLogData = new DatalogValue();
            dataLogData.setTime(x.longValue());
            dataLogData.setValue(x);
            list.add(dataLogData);
        }
        return list;
    }

    /**
    * 取出过车时间进行排序
    * */
    private void dealTimeList(){
        timeListForPass.addAll(mesPassPointList.stream().map(MesPassPointDto::getPasstime).collect(Collectors.toList()));
        timeListForPass = timeListForPass.stream().sorted().collect(Collectors.toList());
    }
    
    /**
     * 从目标时间list中找到与指定time相邻的时间戳
     * */
    private Long getNextTime(List<Long> timeList, Long time, int offset){
        List<Long> collect = new ArrayList<>(timeList);
        collect.add(time);
        collect = collect.stream().sorted().collect(Collectors.toList());
        for (int i=0;i<collect.size();i++){
            if (Objects.equals(time, collect.get(i)) && i+offset >=0 && i+offset <timeList.size()){
                return collect.get(i+offset);
            }
        }
        return time;
    }

    /**
    * 计算升温信息
    * */
    private void calcRisePeriod(Long logTime){
        for (CataphoresisDto item : cataphoresisList){
            for (DatalogValue dataLogData : startTimeDataLog){
                TempRiseInfoDto res = new TempRiseInfoDto();
                Long startTime = Math.round(dataLogData.getValue());
                //温度设定值，正常情况下不会一直变化，取一个即可
                Double standardTemp = standardTempDataLog.get(0).getValue();
                //温度达标的时间，为空耗开始的时间，从温度定时记录中过滤 时间>开机时间 且 温度>达标温度的记录
                Optional<DatalogValue> first = tempDataLog.stream().filter(x -> x.getTime() > dataLogData.getValue() && x.getValue() >= standardTemp).findFirst();
                Long endTime = first.isPresent() ? first.get().getTime() : dataLogData.getTime();
                Long passTime = getNextTime(timeListForPass, startTime,1 );
                if (startTime>=endTime){ continue;}

                res.setStarttime(startTime);
                res.setEndtime(endTime);
                res.setDuration((endTime - startTime) / 1000);
                res.setFirstcarpasstime(passTime);
                res.setTemperature(getAppointTemp(tempDataLog, startTime));
                res.setAllowtime(item.getAllowtime());
                res.setLogtime(TimeUtil.getFirstTimeOfDay(logTime));
                res.setObjectid(item.getId());
                res.setObjectlabel(item.getModelLabel());
                res.setProjectid(1L);

                tempRiseInfoList.add(res);
            }
        }
    }

    /**
     * 计算空耗划分
     * */
    private void calcDepletionPartition(Long logTime){
        for (CataphoresisDto item : cataphoresisList){
            //升温空耗划分
            heatingDepletion(item, logTime);
            //结束空耗划分
            stoppingDepletion(item, logTime);
            //过车空耗划分
            passingDepletion(item, logTime);
        }
    }

    /**
     * 结束空耗
     * */
    private void stoppingDepletion(CataphoresisDto item, Long logTime) {
        for (DatalogValue dataLogData : endTimeDataLog){
            //找到关机前的最后一个过车时间
            Long lastPassingTime = getNextTime(timeListForPass, Math.round(dataLogData.getValue()), -1);
            //理论关机时间 = 最后一个过车时间+计划节拍*倍率
            Long theoretical = lastPassingTime + Math.round(item.getTakttime()*1000 * item.getMultiplyingpower());
            //当时理论关机时间 >= 实际关机时间，不存在空耗，跳过
            if (theoretical >= dataLogData.getValue()){ continue;}

            DepletionPartitionDto res = new DepletionPartitionDto();

            res.setStarttime(theoretical);
            res.setEndtime(Math.round(dataLogData.getValue()));
            res.setDuration((Math.round(dataLogData.getValue()) - theoretical)/1000);
            res.setDepletiontype(Constant.WASTETYPE_STOPPING);
            res.setLogtime(TimeUtil.getFirstTimeOfDay(logTime));
            res.setProjectid(1L);
            res.setObjectid(item.getId());
            res.setObjectlabel(item.getModelLabel());

            depletionPartitionList.add(res);
        }
    }

    /**
     * 过车空耗
     * */
    private void passingDepletion(CataphoresisDto item, Long logTime) {
        for (int i=0;i<mesPassPointList.size()-1;i++){
            MesPassPointDto first = mesPassPointList.get(i);
            MesPassPointDto next = mesPassPointList.get(i+1);

            //理论下一个进车时间 = 上一台进车时刻+计划节拍*倍率
            Long theoretical = first.getPasstime() + Math.round(item.getTakttime()*1000 * item.getMultiplyingpower());
            //当时实际下一个进车时间 <= 理论时间时，不存在空耗，跳过
            if (next.getPasstime() <= theoretical){ continue;}

            DepletionPartitionDto res = new DepletionPartitionDto();
            if (theoretical.equals(next.getPasstime())){continue;}

            res.setStarttime(theoretical);
            res.setEndtime(next.getPasstime());
            res.setDuration((next.getPasstime() - theoretical)/1000);
            res.setDepletiontype(Constant.WASTETYPE_PASSING);
            res.setLogtime(TimeUtil.getFirstTimeOfDay(logTime));
            res.setProjectid(1L);
            res.setObjectid(item.getId());
            res.setObjectlabel(item.getModelLabel());

            depletionPartitionList.add(res);
        }
    }

    /**
     * 升温空耗
     * */
    private void heatingDepletion(CataphoresisDto item, Long logTime) {
        for (DatalogValue dataLogData : startTimeDataLog){
            DepletionPartitionDto res = new DepletionPartitionDto();
            //温度设定值，转存情况下不会一直变化，取一个即可
            Double standardTemp = standardTempDataLog.get(0).getValue();
            //温度达标的时间，为空耗开始的时间，从温度定时记录中过滤 时间>开机时间 且 温度>达标温度的记录
            Optional<DatalogValue> first = tempDataLog.stream().filter(x -> x.getTime() > dataLogData.getValue() && x.getValue() >= standardTemp).findFirst();
            Long startTime = first.isPresent() ? first.get().getTime() : dataLogData.getTime();
            //温度达标后，第一个过车时间为空耗结束时间
            Long endTime = getNextTime(timeListForPass, startTime, 1);
            Long allowTime = item.getAllowtime();
            if (startTime + allowTime < endTime){continue;}

            res.setStarttime(startTime);
            res.setEndtime(endTime);
            res.setDuration((endTime - startTime - allowTime > 0 ? endTime - startTime - allowTime : 0)/1000);
            res.setDepletiontype(Constant.WASTETYPE_HEATING);
            res.setLogtime(TimeUtil.getFirstTimeOfDay(logTime));
            res.setProjectid(1L);
            res.setObjectid(item.getId());
            res.setObjectlabel(item.getModelLabel());

            depletionPartitionList.add(res);
        }
    }
}

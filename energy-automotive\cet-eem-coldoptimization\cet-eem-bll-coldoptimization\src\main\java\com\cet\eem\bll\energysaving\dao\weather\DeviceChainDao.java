package com.cet.eem.bll.energysaving.dao.weather;

import com.cet.eem.bll.common.model.domain.subject.energysaving.DeviceChain;
import com.cet.eem.bll.common.model.ext.subject.energysaving.DeviceChainWithSubLayer;
import com.cet.eem.dao.BaseModelDao;

import java.util.List;

/**
 * @ClassName : Device<PERSON>hainDao
 * @Description : 设备连锁
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-17 16:33
 */
public interface DeviceChainDao extends BaseModelDao<DeviceChain> {
    List<DeviceChain> queryDeviceChain(Long roomId,Long projectId);
    List<DeviceChainWithSubLayer> queryDeviceChainWithDetail(List<Long> chainIds,Long projectId);
    Devi<PERSON><PERSON><PERSON>n queryDeivceChain(Long roomId,String name,Long chainId,Long projectId);
    List<DeviceChainWithSubLayer> queryDeviceChainDetail(List<Long> roomIds);
}

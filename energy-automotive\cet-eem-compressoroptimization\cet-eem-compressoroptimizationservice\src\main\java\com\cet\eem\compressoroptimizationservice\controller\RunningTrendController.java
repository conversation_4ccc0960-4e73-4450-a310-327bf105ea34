package com.cet.eem.compressoroptimizationservice.controller;

import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.bll.compressoroptimization.model.trend.BaseVoWithDataId;
import com.cet.eem.bll.compressoroptimization.model.trend.CompressorTrendSearchVo;
import com.cet.eem.bll.compressoroptimization.model.trend.TrendDataVoWithUnit;
import com.cet.eem.bll.compressoroptimization.service.config.RunningTrendService;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.Result;
import com.cet.eem.common.model.datalog.TrendSearchVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * @ClassName : RunningTrendController
 * @Description : 运行趋势
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-17 09:59
 */
@Api(value = "RunningTrendController", tags = {"运行趋势"})
@RestController
@RequestMapping(value = "/eem/v1/compressorOptimization/trend")
public class RunningTrendController {
    @Autowired
    RunningTrendService runningTrendService;

    @ApiOperation("查询空压站房")
    @PostMapping(value = "/roomList")
    public Result<List<BaseVo>> queryCompressorRoom() {
        return Result.ok(runningTrendService.queryCompressorRoom(GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation("查询测点节点树")
    @PostMapping(value = "/tree/dataId")
    public Result<List<BaseVoWithDataId>> queryTreeWithDataId(@RequestParam Long roomId) {

        return Result.ok(runningTrendService.queryTreeWithDataId(roomId, GlobalInfoUtils.getProjectId(),System.currentTimeMillis()));

    }

    @ApiOperation("查询测点数据")
    @PostMapping(value = "/data")
    public Result<List<TrendDataVoWithUnit>> queryHistory(@RequestBody CompressorTrendSearchVo dto) {
        return Result.ok(runningTrendService.queryTrendData(dto, GlobalInfoUtils.getProjectId()));

    }
}
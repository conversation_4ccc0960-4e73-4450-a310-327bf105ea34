package com.cet.eem.bll.energysaving.model.aioptimization;

import com.cet.eem.bll.energysaving.model.weather.PumpVo;
import com.cet.eem.common.model.BaseVo;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : DeviceCurrentStatus
 * @Description : 设备当前状态
 * <AUTHOR> jiangzixuan
 * @Date: 2022-04-15 13:46
 */
@Getter
@Setter
public class DeviceCurrentStatus extends PumpVo {
    /**
     * 负载率
     */
    private Double loadRate;
    /**
     * 频率
     */
    private Double frequency;
    /**
     * 冷冻水供回水温差
     */
    private Double freezingWaterTemp;
    /**
     * 冷却水供回水温差
     */
    private Double coolingWaterTemp;
    /**
     * 是否开启
     */
    private Boolean isStart;
    /**
     * 冷机类型
     */
    private String  mainType;
}
package com.cet.eem.depletion.model;

import com.cet.eem.depletion.model.Constant.Constant;
import com.cet.eem.model.model.BaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CataphoresisDto extends BaseEntity {
    //倍率
    public Double multiplyingpower;
    //节拍
    public Double takttime;
    //空耗允许时间
    public Long allowtime;

    //空耗线体类型
    public Integer linebodytype;

    public CataphoresisDto() {
        this.modelLabel = Constant.CATAPHORESIS;
    }
}

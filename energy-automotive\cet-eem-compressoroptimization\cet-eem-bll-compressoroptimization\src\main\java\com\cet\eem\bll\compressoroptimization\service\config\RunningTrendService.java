package com.cet.eem.bll.compressoroptimization.service.config;

import com.cet.eem.bll.compressoroptimization.model.trend.BaseVoWithDataId;
import com.cet.eem.bll.compressoroptimization.model.trend.CompressorTrendSearchVo;
import com.cet.eem.bll.compressoroptimization.model.trend.TrendDataVoWithUnit;
import com.cet.eem.common.model.BaseVo;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @ClassName : RunningTrendService
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-25 09:18
 */
public interface RunningTrendService {
    /**
     * 查询空压机房
     *
     * @param projectId
     * @return
     */
    List<BaseVo> queryCompressorRoom(Long projectId);

    /**
     * 查询测点节点树
     *
     * @param roomId
     * @param projectId
     * @return
     */
    List<BaseVoWithDataId> queryTreeWithDataId(Long roomId, Long projectId,Long time);

    /**
     * 查询测点数据
     *
     * @param dto
     * @param projectId
     * @return
     */
    List<TrendDataVoWithUnit> queryTrendData(CompressorTrendSearchVo dto, Long projectId);
}
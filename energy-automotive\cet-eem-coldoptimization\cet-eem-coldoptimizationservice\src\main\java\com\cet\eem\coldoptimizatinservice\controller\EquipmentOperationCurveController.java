package com.cet.eem.coldoptimizatinservice.controller;

import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.bll.energysaving.model.equipmentoperation.CurveDataVo;
import com.cet.eem.bll.energysaving.model.equipmentoperation.TrendListVo;
import com.cet.eem.bll.energysaving.service.aioptimization.EquipmentCurveService;
import com.cet.eem.common.model.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * @ClassName : EquipmentOperationCurveController
 * @Description : 设备运行曲线的接口
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-16 10:22
 */
@Api(value = "EquipmentOperationCurveController", tags = {"设备运行曲线的接口"})
@RestController
@RequestMapping(value = "/eem/v1/equipmentCurve")
public class EquipmentOperationCurveController {
    @Autowired
    EquipmentCurveService equipmentCurveService;

    @ApiOperation(value = "冷机运行效率")
    @PostMapping(value = "/refrigerator/operatingEfficiency", produces = "application/json")
    public Result<List<TrendListVo>> queryOperatingEfficiencyOfRefrigerator(@RequestParam Long chainId) {
        return Result.ok(equipmentCurveService.queryMainsOperatingEfficiency(chainId, GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "冷机运行功率")
    @PostMapping(value = "/refrigerator/operatingPower", produces = "application/json")
    public Result<List<TrendListVo>> queryOperatingPowerOfRefrigerator( @RequestParam Long chainId) {
        return Result.ok(equipmentCurveService.queryMainsOperatingPower(chainId, GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "冷冻泵匹配功率")
    @PostMapping(value = "/refrigerationPump/matchingPower", produces = "application/json")
    public Result<List<TrendListVo>> queryMatchingPowerOfRefrigerationPump( @RequestParam Long chainId) {
        return Result.ok(equipmentCurveService.queryMatchingPowerOfRefrigerationPump(chainId, GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "冷却泵匹配功率")
    @PostMapping(value = "/coolPump/matchingPower", produces = "application/json")
    public Result<List<TrendListVo>> queryMatchingPowerOfCoolPump(@RequestParam Long chainId) {
        return Result.ok(equipmentCurveService.queryMatchingPowerOfCoolingPump(chainId, GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "冷却塔匹配功率")
    @PostMapping(value = "/coolTower/matchingPower", produces = "application/json")
    public Result<List<TrendListVo>> queryMatchingPowerOfCoolTower( @RequestParam Long chainId) {
        return Result.ok(equipmentCurveService.queryMatchingPowerOfCoolTower(chainId, GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "冷冻泵频率与功率关系")
    @PostMapping(value = "/refrigerationPump/frequencyAndPower", produces = "application/json")
    public Result<List<TrendListVo>> queryFrequencyAndPowerOfRefrigerationPump( @RequestParam Long chainId) {
        return Result.ok(equipmentCurveService.queryFrequencyAndPowerOfRefrigerationPump(chainId, GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "冷却泵频率与功率关系")
    @PostMapping(value = "/coolPump/frequencyAndPower", produces = "application/json")
    public Result<List<TrendListVo>> queryFrequencyAndPowerOfCoolPump(@RequestParam Long chainId) {
        return Result.ok(equipmentCurveService.queryFrequencyAndPowerOfCoolPump(chainId, GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "冷却塔频率与功率关系")
    @PostMapping(value = "/coolTower/frequencyAndPower", produces = "application/json")
    public Result<List<TrendListVo>> queryFrequencyAndPowerOfCoolTower( @RequestParam Long chainId) {
        return Result.ok(equipmentCurveService.queryFrequencyAndPowerOfCoolTower(chainId, GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "冷却供、回水温度与制冷站功率关系")
    @PostMapping(value = "/supplyAndReturnWater/tempAndPower", produces = "application/json")
    public Result<List<TrendListVo>> queryTempAndPowerOfSupplyAndReturnWater( @RequestParam Long chainId) {
        return Result.ok(equipmentCurveService.queryTempAndPowerOfSupplyAndReturnWater(chainId, GlobalInfoUtils.getProjectId()));
    }
}
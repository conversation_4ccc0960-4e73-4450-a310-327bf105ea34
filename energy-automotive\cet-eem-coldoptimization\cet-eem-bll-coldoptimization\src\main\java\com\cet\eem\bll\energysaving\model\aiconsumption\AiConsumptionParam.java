package com.cet.eem.bll.energysaving.model.aiconsumption;

import com.cet.eem.bll.common.model.energy.EnergyParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : AiConsumptionParam
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-05-31 13:44
 */
@Getter
@Setter
public class AiConsumptionParam extends EnergyParam {
    @ApiModelProperty("分析指标类型,1是能耗，2是cop")
    private Integer analysisIndicatorType;
}
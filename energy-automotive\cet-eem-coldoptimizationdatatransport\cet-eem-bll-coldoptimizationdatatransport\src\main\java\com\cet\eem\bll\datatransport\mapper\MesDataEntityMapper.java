package com.cet.eem.bll.datatransport.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cet.eem.bll.datatransport.model.mesdata.MesDataEntityVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface MesDataEntityMapper extends BaseMapper<MesDataEntityVo>{
    @Select("select workshop_name as workshipname, shift_name as shiftname, plan_num_now as plannum, complete_num_now as completenum, \n" +
            "            takt_time as takttime, sys_createtime as syscreatetime\n" +
            "from andon_info_plan aiph where aiph.sys_createtime > #{startTime, jdbcType=TIMESTAMP} and aiph.sys_createtime < #{endTime, jdbcType=TIMESTAMP}\n")
    List<MesDataEntityVo> queryMesData(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}

package com.cet.eem.depletion.dao.impl;

import com.cet.eem.depletion.dao.TempriseinfoDao;
import com.cet.eem.depletion.model.Constant.Constant;
import com.cet.eem.depletion.model.TempRiseInfoDto;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class TempriseinfoDaoImpl implements TempriseinfoDao {
    @Autowired
    ModelServiceUtils modelServiceUtils;


    @Override
    public List<TempRiseInfoDto> queryByParam(Long st, Long end, Long objId, String label) {
        QueryCondition queryCondition1 = new QueryConditionBuilder<>(Constant.TEMPPRISEINFO)
                .eq(Constant.OBJECTID, objId)
                .eq(Constant.OBJECTLABEL, label)
                .ge(Constant.LOGTIME, st)
                .lt(Constant.LOGTIME, end)
                .build();
        return modelServiceUtils.query(queryCondition1, TempRiseInfoDto.class);
    }
}

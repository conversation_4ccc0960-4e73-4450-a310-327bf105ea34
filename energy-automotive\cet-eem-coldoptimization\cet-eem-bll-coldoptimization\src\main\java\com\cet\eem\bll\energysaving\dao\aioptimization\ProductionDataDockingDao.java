package com.cet.eem.bll.energysaving.dao.aioptimization;

import com.cet.eem.bll.energysaving.model.dataentryquery.ProductionDataDocking;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.dao.BaseModelDao;

import java.util.List;

/**
 * @ClassName : ProductionDataDockingDao
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-09-15 09:54
 */
public interface ProductionDataDockingDao extends BaseModelDao<ProductionDataDocking> {
  /**
   *
   * @param nodes
   * @return
   */
  List<ProductionDataDocking> queryByRoomId(List<BaseVo> nodes,Long st,Long et);
}
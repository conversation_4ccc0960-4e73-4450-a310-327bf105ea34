package com.cet.eem.depletion.dao.impl;

import com.cet.eem.depletion.dao.DepletionconsumptionDao;
import com.cet.eem.depletion.model.Constant.Constant;
import com.cet.eem.depletion.model.DepletionConsumptionDto;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import com.cet.eem.toolkit.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
public class DepletionconsumptionDaoImpl implements DepletionconsumptionDao {
    @Autowired
    ModelServiceUtils modelServiceUtils;


    @Override
    public List<DepletionConsumptionDto> queryByParam(Long st, Long end, Long id, String modelLabel) {
        QueryCondition condition = new QueryConditionBuilder<>(Constant.DEPLETIONCONSUMPTION)
                .eq(Constant.OBJECTID, id)
                .eq(Constant.OBJECTLABEL, modelLabel)
                .ge(Constant.LOGTIME, st)
                .lt(Constant.LOGTIME, end)
                .build();
        return modelServiceUtils.query(condition, DepletionConsumptionDto.class);
    }

    @Override
    public List<DepletionConsumptionDto> queryByParam(Long st, Long end, Long objId, String label, List<Integer> depletiontype) {
        QueryCondition condition = new QueryConditionBuilder<>(Constant.DEPLETIONCONSUMPTION)
                .eq(Constant.OBJECTID, objId)
                .eq(Constant.OBJECTLABEL, label)
                .ge(Constant.LOGTIME, st)
                .lt(Constant.LOGTIME, end)
                .in(Constant.DEPLETIONTYPE, depletiontype)
                .build();
        return modelServiceUtils.query(condition, DepletionConsumptionDto.class);
    }

    @Override
    public List<DepletionConsumptionDto> queryByDepletionid(List<Long> depletionids) {
        if (CollectionUtils.isEmpty(depletionids)){
            return Collections.emptyList();
        }
        QueryCondition condition = new QueryConditionBuilder<>(Constant.DEPLETIONCONSUMPTION)
                .in(Constant.DEPLETIONID, depletionids)
                .build();
        return modelServiceUtils.query(condition, DepletionConsumptionDto.class);
    }
}

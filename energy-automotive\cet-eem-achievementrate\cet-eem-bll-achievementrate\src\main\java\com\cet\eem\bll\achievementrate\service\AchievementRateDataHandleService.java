package com.cet.eem.bll.achievementrate.service;

import com.cet.eem.bll.achievementrate.model.data.EnergyEfficiencyDataPlan;
import com.cet.eem.bll.achievementrate.model.data.ObjectCostValuePlan;
import com.cet.eem.bll.achievementrate.model.data.UnitObjectCostValuePlan;
import com.cet.eem.bll.common.model.domain.subject.energy.EnergyConsumptionPlan;

import java.util.List;

/**
 * @ClassName : AchievementRateDataHandleService
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-12-07 09:06
 */
public interface AchievementRateDataHandleService {
    void writeEnergyConsumptionPlanByCycle(List<EnergyConsumptionPlan> energyConsumptionPlans, Long projectId);
    void writeEffDataByCycle(List<EnergyEfficiencyDataPlan> dataList, Long projectId);

    void writeObjectCostValuePlanByCycle(List<ObjectCostValuePlan> dataList, Long projectId);
    void writeUnitObjectCostValuePlanByCycle(List<UnitObjectCostValuePlan> dataList, Long projectId);
    void writeEnergyConsumptionPlanDataByCycle(List<EnergyConsumptionPlan> energyConsumptionPlans, Long projectId);
}
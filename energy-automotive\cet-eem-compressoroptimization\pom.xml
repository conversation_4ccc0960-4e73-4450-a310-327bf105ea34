<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cet.eem</groupId>
        <artifactId>eem-base-service-parent</artifactId>
        <version>4.0.4.42</version>
        <relativePath/>
    </parent>
    <groupId>com.cet.eem</groupId>
    <artifactId>cet-eem-compressoroptimization</artifactId>
    <packaging>pom</packaging>
    <version>1.13.011-am.branch_1.4</version>
    <modules>
        <module>cet-eem-bll-compressoroptimization</module>
        <module>cet-eem-compressoroptimizationservice</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>
    <dependencies>
        <!-- 数据访问层 -->
        <dependency>
            <groupId>com.cet.eem</groupId>
            <artifactId>cet-eem-dal-starter</artifactId>
        </dependency>

        <!-- eem基础业务层 -->
        <dependency>
            <groupId>com.cet.eem</groupId>
            <artifactId>cet-eem-bll-base-starter</artifactId>
        </dependency>

        <!-- eem核心业务层 -->
        <dependency>
            <groupId>com.cet.eem</groupId>
            <artifactId>cet-eem-bll-core-starter</artifactId>
        </dependency>


    </dependencies>

    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <name>cet Repository</name>
            <url>http://10.12.135.231:9081/repository/cet</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>cet Repository</name>
            <url>http://10.12.135.231:9081/repository/cet</url>
        </snapshotRepository>
    </distributionManagement>

</project>
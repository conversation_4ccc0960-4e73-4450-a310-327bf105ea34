package com.cet.eem.bll.energysaving.model.config.system;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.bll.energysaving.model.def.ColdOptimizationLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : NumericalCondition
 * @Description : 数值条件
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-28 15:06
 */
@Getter
@Setter
@ModelLabel(ColdOptimizationLabelDef.NUMERICAL_CONDITION)
public class NumericalCondition extends BaseEntity {
    @JsonProperty(ColdOptimizationLabelDef.CONTROL_SCHEME_ID)
    private Long controlSchemeId;
    @JsonProperty(ColumnDef.C_OBJECT_ID)
    private Long objectId;
    @JsonProperty(ColumnDef.C_OBJECT_Label)
    private String objectLabel;
    private Double value;
    @JsonProperty(ColdOptimizationLabelDef.TIME_VALUE)
    private Long timeValue;
    @JsonProperty(ColdOptimizationLabelDef.COMPARISON_OBJECT_ID)
    private Long comparisonObjectId;
    @JsonProperty(ColdOptimizationLabelDef.COMPARISON_LABEL)
    private String comparisonlabel;
    @JsonProperty(ColdOptimizationLabelDef.DATA_TYPE)
    private Integer dataType;
    @JsonProperty(ColdOptimizationLabelDef.OPERATION_SYMBOL)
    private Integer operationSymbol;
    @JsonProperty(ColdOptimizationLabelDef.OPERATION_SYMBOL_OF_TIME)
    private Integer operationSymbolOfTime;
    @JsonProperty(ColdOptimizationLabelDef.SELF_COMPARISON)
    private Boolean selfComparison;

    public NumericalCondition() {
        this.modelLabel = ColdOptimizationLabelDef.NUMERICAL_CONDITION;
    }
}
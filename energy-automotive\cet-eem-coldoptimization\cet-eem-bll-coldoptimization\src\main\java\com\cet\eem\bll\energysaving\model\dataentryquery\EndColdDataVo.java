package com.cet.eem.bll.energysaving.model.dataentryquery;

import com.cet.eem.common.model.datalog.DataLogData;
import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName : EndColdDataVo
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-04-08 15:16
 */
@Getter
@Setter
public class EndColdDataVo {
    private Long objectId;
    private String objectLabel;
    /**
     * 冷冻管瞬时冷量
     */
    private List<DatalogValue> endCold;
    /**
     * mes系统对接数据
     */
    private List<MesData> mesData;

}
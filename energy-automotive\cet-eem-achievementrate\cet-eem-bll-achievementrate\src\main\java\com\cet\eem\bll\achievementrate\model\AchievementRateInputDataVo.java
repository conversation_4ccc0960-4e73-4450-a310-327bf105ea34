package com.cet.eem.bll.achievementrate.model;

import com.cet.eem.bll.achievementrate.model.data.EnergyEfficiencyDataPlan;
import com.cet.eem.bll.achievementrate.model.data.ObjectCostValuePlan;
import com.cet.eem.bll.achievementrate.model.data.UnitObjectCostValuePlan;
import com.cet.eem.bll.achievementrate.model.def.AchievementRateDataDef;
import com.cet.eem.bll.common.def.DataEntryTypeDef;
import com.cet.eem.bll.common.model.data.SystemDataNode;
import com.cet.eem.bll.common.model.domain.subject.energy.EnergyConsumption;
import com.cet.eem.bll.common.model.domain.subject.energy.EnergyConsumptionPlan;
import com.cet.eem.common.exception.ValidationException;
import com.cet.eem.common.utils.TimeUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName : AchievementRateInputDataVo
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-11-20 14:58
 */
@Getter
@Setter
public class AchievementRateInputDataVo {
    @ApiModelProperty("数据类型")
    @NotNull(
            message = "数据类型不能为空"
    )
    private Integer dataEntryType;
    @ApiModelProperty("间隔周期")
    @NotNull(
            message = "间隔周期不能为空"
    )
    private Integer aggregationCycle;
    @ApiModelProperty("关联的模型")
    @NotNull(
            message = "关联的模型不能为空"
    )
    private String objectLabel;
    @ApiModelProperty("关联的模型ID")
    @NotNull(
            message = "关联的模型ID不能为空"
    )
    private Long objectId;
    @ApiModelProperty("录入的节点")
    private List<AchievementRateDataNode> dataNodes;
    @ApiModelProperty("开始时间")
    @NotNull(
            message = "开始时间不能为空"
    )
    private LocalDateTime startTime;
    @ApiModelProperty("结束时间")
    @NotNull(
            message = "结束时间不能为空"
    )
    private LocalDateTime endTime;

    public List<EnergyEfficiencyDataPlan> generateEnergyConsumption() {
        if (!dataEntryType.equals(AchievementRateDataDef.EFF_PLAN)) {
            throw new ValidationException("非单耗数据");
        }
        List<EnergyEfficiencyDataPlan> energyEfficiencyDataPlanList = new ArrayList<>(dataNodes.size());
        for (AchievementRateDataNode dataNode : dataNodes) {
            EnergyEfficiencyDataPlan plan = new EnergyEfficiencyDataPlan();
            plan.setId(dataNode.getId());
            plan.setValue(dataNode.getValue());
            plan.setLogTime(TimeUtil.localDateTime2timestamp(dataNode.getLogTime()));
            plan.setAggregationCycle(aggregationCycle);
            plan.setObjectLabel(objectLabel);
            plan.setObjectId(objectId);
            plan.setEnergyEfficiencySetId(dataNode.getEffSetId());
            plan.setEnergyType(dataNode.getEnergyType());
            plan.setProductType(dataNode.getProductType());
            energyEfficiencyDataPlanList.add(plan);
        }
        return energyEfficiencyDataPlanList;
    }

    public List<ObjectCostValuePlan> generateObjectCostValuePlan() {
        if (!dataEntryType.equals(AchievementRateDataDef.PRODUCT_PLAN)) {
            throw new ValidationException("非单耗数据");
        }
        List<ObjectCostValuePlan> energyEfficiencyDataPlanList = new ArrayList<>(dataNodes.size());
        for (AchievementRateDataNode dataNode : dataNodes) {
            ObjectCostValuePlan plan = new ObjectCostValuePlan();
            plan.setId(dataNode.getId());
            plan.setValue(dataNode.getValue());
            plan.setLogTime(TimeUtil.localDateTime2timestamp(dataNode.getLogTime()));
            plan.setAggregationCycle(aggregationCycle);
            plan.setObjectLabel(objectLabel);
            plan.setObjectId(objectId);
            plan.setEnergyType(dataNode.getEnergyType());
            energyEfficiencyDataPlanList.add(plan);
        }
        return energyEfficiencyDataPlanList;
    }
    public List<UnitObjectCostValuePlan> generateUnitObjectCostValuePlan() {
        if (!dataEntryType.equals(AchievementRateDataDef.UNIT_OBJECT_COST_PLAN)) {
            throw new ValidationException("非单耗数据");
        }
        List<UnitObjectCostValuePlan> energyEfficiencyDataPlanList = new ArrayList<>(dataNodes.size());
        for (AchievementRateDataNode dataNode : dataNodes) {
            UnitObjectCostValuePlan plan = new UnitObjectCostValuePlan();
            plan.setId(dataNode.getId());
            plan.setValue(dataNode.getValue());
            plan.setLogTime(TimeUtil.localDateTime2timestamp(dataNode.getLogTime()));
            plan.setAggregationCycle(aggregationCycle);
            plan.setObjectLabel(objectLabel);
            plan.setObjectId(objectId);
            plan.setEnergyType(dataNode.getEnergyType());
            plan.setProductType(dataNode.getProductType());
            energyEfficiencyDataPlanList.add(plan);
        }
        return energyEfficiencyDataPlanList;
    }

    public List<EnergyConsumptionPlan> generateEnergyConsumptionPlan() {
        if (!dataEntryType.equals(AchievementRateDataDef.ENERGY_CONSUMPTION_PLAN)) {
            throw new ValidationException("非能耗计划数据");
        }
        List<EnergyConsumptionPlan> energyConsumptionList = new ArrayList<>(dataNodes.size());
        for (AchievementRateDataNode dataNode : dataNodes) {
            EnergyConsumptionPlan energyConsumption = new EnergyConsumptionPlan();
            energyConsumption.setId(dataNode.getId());
            energyConsumption.setValue(dataNode.getValue());
            energyConsumption.setLogtime(TimeUtil.localDateTime2timestamp(dataNode.getLogTime()));
            energyConsumption.setAggregationCycle(aggregationCycle);
            energyConsumption.setObjectLabel(objectLabel);
            energyConsumption.setObjectId(objectId);
            energyConsumption.setEnergyType(dataNode.getEnergyType());
            energyConsumptionList.add(energyConsumption);
        }
        return energyConsumptionList;
    }
}
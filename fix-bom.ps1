# Fix BOM character issue
$filePath = "energy-solution-fusion\eem-solution-transformer\eem-solution-transformer-core\src\main\java\com\cet\eem\fusion\transformer\core\impl\TransformerOverviewServiceImpl.java"

# Read file as bytes
$bytes = [System.IO.File]::ReadAllBytes($filePath)

# Check if BOM exists (EF BB BF)
if ($bytes.Length -ge 3 -and $bytes[0] -eq 239 -and $bytes[1] -eq 187 -and $bytes[2] -eq 191) {
    Write-Host "BOM found, removing..." -ForegroundColor Yellow
    # Remove BOM (first 3 bytes)
    $newBytes = $bytes[3..($bytes.Length-1)]
    [System.IO.File]::WriteAllBytes($filePath, $newBytes)
    Write-Host "BOM removed successfully!" -ForegroundColor Green
} else {
    Write-Host "No BOM found" -ForegroundColor Green
}

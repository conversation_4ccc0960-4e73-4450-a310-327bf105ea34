package com.cet.eem.bll.energysaving.model.cetml;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : ColdLoadQueryParam
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-06-07 11:30
 */
@Getter
@Setter
public class ColdLoadQueryParam {
    @JsonProperty("project_id")
    private Long projectId;
    @JsonProperty("predict_data")
    private List<ColdLoadParam> predictData;
}
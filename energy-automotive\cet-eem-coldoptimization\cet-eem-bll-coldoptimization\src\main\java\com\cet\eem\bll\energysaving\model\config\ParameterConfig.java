package com.cet.eem.bll.energysaving.model.config;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.bll.energysaving.model.def.ColdOptimizationLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : ParameterConfig
 * @Description : 参数配置
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-16 13:34
 */
@Getter
@Setter
@ModelLabel(ColdOptimizationLabelDef.PARAMETER_CONFIG)
public class ParameterConfig extends BaseEntity {
    public ParameterConfig(){
        this.modelLabel=ColdOptimizationLabelDef.PARAMETER_CONFIG;
    }
    /**
     * 系统id
     */
    @JsonProperty(ColdOptimizationLabelDef.SYSTEM_ID)
    private Long systemId;
    /**
     * 冷机切换优先级配置
     */
    @JsonProperty(ColdOptimizationLabelDef.COLD_SWITCH_PRIORITY)
    private Integer coldSwitchPriority;
    /**
     * 冷机启停时间间隔	库里存s，页面需要转成分钟
     */
    @JsonProperty(ColdOptimizationLabelDef.START_STOP_TIME_INTERVAL)
    private Long startStopTimeInterval;

    /**
     * 切换冷机运行时长差	库里存s，页面需要转成小时
     */
    @JsonProperty(ColdOptimizationLabelDef.RUNTIME_DIFF)
    private Long runtimeDifference;
    /**
     * 项目id
     */
    @JsonProperty(ColumnDef.PROJECT_ID)
    private Long projectId;
    /**
     * 一天冷机启停次数上限
     */
    @JsonProperty(ColdOptimizationLabelDef.START_STOP_TIME)
    private Integer startStopTime;
    /**
     * 高温结束时间
     */
    @JsonProperty(ColdOptimizationLabelDef.SUMMER_END_DAY)
    private Integer summerEndDay;
    /**
     * 高温结束时间
     */
    @JsonProperty(ColdOptimizationLabelDef.SUMMER_END_MONTH)
    private Integer summerEndMonth;
    /**
     * 高温开始时间
     */
    @JsonProperty(ColdOptimizationLabelDef.SUMMER_START_DAY)
    private Integer summerStartDay;
    /**
     * 高温开始时间
     */
    @JsonProperty(ColdOptimizationLabelDef.SUMMER_START_MONTH)
    private Integer summerStartMonth;
    /**
     * 生产时段温度
     */
    @JsonProperty(ColdOptimizationLabelDef.WORK_PERIOD_TEMP)
    private Double workPeriodTemp;
    /**
     * 非生产时段最低温
     */
    @JsonProperty(ColdOptimizationLabelDef.REST_PERIOD_TEMP_MIN)
    private Double restPeriodTempMin;
    /**
     * 生产时段温度
     */
    @JsonProperty(ColdOptimizationLabelDef.REST_PERIOD_TEMP_MAX)
    private Double restPeriodTempMax;
    /**
     * 冷冻水供水压力最大值
     */
    @JsonProperty(ColdOptimizationLabelDef.CHILL_WATER_SUPPLY_PRESS_MAX)
    private Double chilledWaterSupplyPressureMax;
    /**
     * 冷冻水供水压力最小值
     */
    @JsonProperty(ColdOptimizationLabelDef.CHILL_WATER_SUPPLY_PRESS_MIN)
    private Double chilledWaterSupplyPressureMin;

    /**
     * 冷冻水回水温差最小值
     */
    @JsonProperty(ColdOptimizationLabelDef.CHILL_WATER_RETURN_PRESS_DIFF_MIN)
    private Double chilledWaterReturnPressureDiffMin;
    /**
     * 冷冻水回水温差最大值
     */
    @JsonProperty(ColdOptimizationLabelDef.CHILL_WATER_RETURN_PRESS_DIFF_MAX)
    private Double chilledWaterReturnPressureDiffMax;
    /**
     * 冷却水供水温度最大值
     */
    @JsonProperty(ColdOptimizationLabelDef.COOL_WATER_SUPPLY_TEMP_MAX)
    private Double coolWaterSupplyTempMax;

    /**
     * 冷却水供水温度最小值
     */
    @JsonProperty(ColdOptimizationLabelDef.COOL_WATER_SUPPLY_TEMP_MIN)
    private Double coolWaterSupplyTempMin;
    /**
     * 供回水温差
     */
    @JsonProperty(ColdOptimizationLabelDef.SUPPLY_RETURN_WATER_DIFF)
    private Double supplyReturnWaterTempDiff;

}
package com.cet.eem.bll.energysaving.dao.colddata.impl;

import com.cet.eem.bll.common.model.domain.subject.energysaving.ColdActual;
import com.cet.eem.bll.energysaving.dao.colddata.ColdActualDao;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import com.cet.eem.model.tool.ModelServiceUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/22
 */
@Repository
public class ColdActualDaoImpl extends ModelDaoImpl<ColdActual> implements ColdActualDao {
    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Override
    public void writeColdActualDataList(List<ColdActual> coldActualDataList) {
        if (CollectionUtils.isEmpty(coldActualDataList)) {
            return;
        }

        Set<Integer> coldLoadTypes = new HashSet<>();
        Set<Integer> dataTypes = new HashSet<>();
        Set<Long> roomIds = new HashSet<>();
        Set<Long> logTimes = new HashSet<>();
        for (ColdActual data : coldActualDataList) {
            coldLoadTypes.add(data.getColdLoadType());
            dataTypes.add(data.getPredictDataType());
            roomIds.add(data.getRoomId());
            logTimes.add(TimeUtil.localDateTime2timestamp(data.getLogTime()));
        }

        List<ColdActual> oldEnergyEfficiencyData = queryColdActualData(coldLoadTypes, dataTypes, logTimes, roomIds);
        for (ColdActual data : coldActualDataList) {
            Optional<ColdActual> any = oldEnergyEfficiencyData.stream().filter(it -> Objects.equals(data.getProjectId(), it.getProjectId()) &&
                            Objects.equals(data.getRoomId(), it.getRoomId()) &&
                            Objects.equals(data.getColdLoadType(), it.getColdLoadType()) &&
                            Objects.equals(data.getPredictDataType(), it.getPredictDataType()) &&
                            Objects.equals(data.getLogTime(), it.getLogTime()))
                    .findAny();

            any.ifPresent(totalEnergyConsumptionPredict -> data.setId(totalEnergyConsumptionPredict.getId()));
        }

        List<ColdActual> updateDataList = coldActualDataList.stream().filter(it -> !Objects.isNull(it.getId())).collect(Collectors.toList());
        updateDataList(updateDataList);

        List<ColdActual> addDataList = coldActualDataList.stream().filter(it -> Objects.isNull(it.getId())).collect(Collectors.toList());
        addDataList(addDataList);
    }

    @Override
    public List<ColdActual> queryColdActualData(Collection<Integer> coldLoadTypes, Collection<Integer> dataTypes, Collection<Long> logTimes,
                                                Collection<Long> roomIds) {
        LambdaQueryWrapper<ColdActual> wrapper = LambdaQueryWrapper.of(ColdActual.class)
                .in(ColdActual::getRoomId, roomIds)
                .in(ColdActual::getColdLoadType, coldLoadTypes)
                .in(ColdActual::getPredictDataType, dataTypes)
                .in(ColdActual::getLogTime, logTimes);

        return this.selectList(wrapper);
    }

    @Override
    public List<ColdActual> queryColdActualData(Collection<Integer> coldLoadTypes, Collection<Integer> dataTypes,
                                                LocalDateTime st, LocalDateTime et, Long roomId) {
        LambdaQueryWrapper<ColdActual> wrapper = LambdaQueryWrapper.of(ColdActual.class)
                .eq(ColdActual::getRoomId, roomId)
                .in(ColdActual::getColdLoadType, coldLoadTypes)
                .in(ColdActual::getPredictDataType, dataTypes);
        if (Objects.nonNull(st)){
            wrapper.ge(ColdActual::getLogTime,st);
        }
        if (Objects.nonNull(et)){
            wrapper.lt(ColdActual::getLogTime,et);
        }
        List<ColdActual> actuals = this.selectList(wrapper);
        if (CollectionUtils.isEmpty(actuals)){
            return Collections.emptyList();
        }
        return actuals;

    }

    @Override
    public List<ColdActual> queryColdActualData(Collection<Integer> coldLoadTypes, Collection<Integer> dataTypes, LocalDateTime st, LocalDateTime et, Collection<Long> roomId) {
        LambdaQueryWrapper<ColdActual> wrapper = LambdaQueryWrapper.of(ColdActual.class)
                .in(ColdActual::getRoomId, roomId)
                .in(ColdActual::getColdLoadType, coldLoadTypes)
                .in(ColdActual::getPredictDataType, dataTypes);
        if (Objects.nonNull(st)){
            wrapper.ge(ColdActual::getLogTime,st);
        }
        if (Objects.nonNull(et)){
            wrapper.lt(ColdActual::getLogTime,et);
        }
        List<ColdActual> actuals = this.selectList(wrapper);
        if (CollectionUtils.isEmpty(actuals)){
            return Collections.emptyList();
        }
        return actuals;
    }

    private void addDataList(List<ColdActual> addDataList) {
        if (CollectionUtils.isEmpty(addDataList)) {
            return;
        }

        List<String> updateFields = Arrays.asList(ColumnDef.COLD_LOAD_TYPE,
                ColumnDef.LOG_TIME,
                ColumnDef.PREDICT_DATA_TYPE,
                ColumnDef.PROJECT_ID,
                ColumnDef.ROOM_ID,
                ColumnDef.VALUE);
        List<List<Object>> writeDataList = new ArrayList<>();
        for (ColdActual efData : addDataList) {
            writeDataList.add(Arrays.asList(efData.getColdLoadType(),
                    efData.getLogTime(),
                    efData.getPredictDataType(),
                    efData.getProjectId(),
                    efData.getRoomId(),
                    efData.getValue()));
        }

        modelServiceUtils.writeDataBatch(ModelLabelDef.COLD_ACTUAL, true, updateFields, writeDataList, null);
    }

    private void updateDataList(List<ColdActual> updateDataList) {
        if (CollectionUtils.isEmpty(updateDataList)) {
            return;
        }

        List<String> updateFields = Collections.singletonList(ColumnDef.VALUE);
        List<List<Object>> writeDataList = new ArrayList<>();
        List<List<Object>> filterDataList = new ArrayList<>();
        for (ColdActual efData : updateDataList) {
            writeDataList.add(Collections.singletonList(efData.getValue()));
            filterDataList.add(Collections.singletonList(efData.getId()));
        }
        modelServiceUtils.writeDataBatch(ModelLabelDef.COLD_ACTUAL, false, updateFields, writeDataList, filterDataList);
    }
}

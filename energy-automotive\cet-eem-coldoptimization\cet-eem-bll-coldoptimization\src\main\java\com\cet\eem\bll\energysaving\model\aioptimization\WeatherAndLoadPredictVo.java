package com.cet.eem.bll.energysaving.model.aioptimization;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @ClassName : WeatherAndLoadPredictVo
 * @Description : 气象和负荷预测返回值
 * <AUTHOR> ji<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-15 10:56
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class WeatherAndLoadPredictVo {
    private Long logTime;
    private Double temp;
    private Double humidity;
    private Double coldLoad;
    private Double electricPower;
    private Double copData;
}
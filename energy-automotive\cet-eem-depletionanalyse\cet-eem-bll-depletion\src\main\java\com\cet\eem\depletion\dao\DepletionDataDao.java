package com.cet.eem.depletion.dao;

import com.cet.eem.bll.common.model.batchnodes.PipeLine;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.model.event.EventCondition;
import com.cet.eem.depletion.model.AveragePassTimeDto;
import com.cet.eem.depletion.model.CataphoresisDto;
import com.cet.eem.depletion.model.DepletionPartitionDto;
import com.cet.eem.depletion.model.MesPassPointDto;
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;

import java.util.List;
import java.util.Map;

public interface DepletionDataDao {
    /**
     * 查询所有电泳池模型
     * */
    List<CataphoresisDto> queryCataphoresis(List<Integer> lineBodyType);

    /**
     * 查询指定时间的过车数据
     * */
    List<MesPassPointDto> queryMesPassPointData(Long startTime, Long endTime, List<String> stationCode);

    /**
     * 查询定时记录
     * */
    Map<Integer, List<TrendDataVo>> queryQuantityData(List<BaseVo> baseVos, Long startTime, Long endTime, List<QuantitySearchVo> quantitySearchVo);

    /**
     * 查询空耗划分记录
     * */
    List<DepletionPartitionDto> queryDepletionPartition(Long startTime, Long endTime);

    /**
     * 查询指定的管道
     * */
    List<PipeLine> queryPipeLine(List<Long> idList);

    /**
     * 查询产线的开机时间
     * 通过查询产线的事件，然后获取到事件时间
     * */
    List<Long> queryProductionLineEventTime(Long startTime, Long endTime, EventCondition condition);

    List<AveragePassTimeDto> queryAveragePassTime();
}

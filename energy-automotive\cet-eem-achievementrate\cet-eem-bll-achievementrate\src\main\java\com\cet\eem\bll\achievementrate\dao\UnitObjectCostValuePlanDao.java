package com.cet.eem.bll.achievementrate.dao;

import com.cet.eem.bll.achievementrate.model.data.UnitObjectCostValuePlan;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.dao.BaseModelDao;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * @ClassName : UnitObjectCostValuePlanImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-11-21 17:15
 */
public interface UnitObjectCostValuePlanDao extends BaseModelDao<UnitObjectCostValuePlan> {
    List<UnitObjectCostValuePlan> query(Long st, Long et, Collection<Integer> energyType, Collection<BaseVo> nodes, Integer cycle,  Collection<Integer> productTypes);
    List<UnitObjectCostValuePlan> query(LocalDateTime st, LocalDateTime et,Integer energyType, Collection<BaseVo> nodes, Integer cycle, Integer productType);
    void insertData(List<UnitObjectCostValuePlan> dataList);
    void writeData(List<UnitObjectCostValuePlan> dataList);
}

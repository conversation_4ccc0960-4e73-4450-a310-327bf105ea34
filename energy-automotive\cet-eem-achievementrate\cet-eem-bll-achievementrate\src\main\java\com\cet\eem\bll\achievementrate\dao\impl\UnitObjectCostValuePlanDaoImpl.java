package com.cet.eem.bll.achievementrate.dao.impl;

import com.cet.eem.bll.achievementrate.dao.UnitObjectCostValuePlanDao;
import com.cet.eem.bll.achievementrate.model.data.UnitObjectCostValuePlan;
import com.cet.eem.bll.achievementrate.model.def.AchievementRateDataLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import com.cet.eem.model.model.BaseEntity;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : UnitObjectCostValuePlanDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-11-21 17:15
 */
@Repository
public class UnitObjectCostValuePlanDaoImpl extends ModelDaoImpl<UnitObjectCostValuePlan> implements UnitObjectCostValuePlanDao {
    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Override
    public List<UnitObjectCostValuePlan> query(Long st, Long et, Collection<Integer> energyType, Collection<BaseVo> nodes, Integer cycle, Collection<Integer> productTypes) {
        if (CollectionUtils.isEmpty(nodes)) {
            return new ArrayList<>();
        }

        Map<String, List<BaseVo>> nodeMap = nodes.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        int group = 1;

        QueryConditionBuilder<BaseEntity> builder = new QueryConditionBuilder<>(AchievementRateDataLabelDef.UNIT_OBJECT_COST_PLAN)
                .composeMethod(true)
                .orderBy(ColumnDef.LOG_TIME)
                .removeOrderById();
        for (Map.Entry<String, List<BaseVo>> entry : nodeMap.entrySet()) {
            String label = entry.getKey();
            List<Long> ids = nodeMap.get(label).stream().map(BaseVo::getId).distinct().collect(Collectors.toList());
            builder.eq(ColumnDef.C_OBJECT_Label, label, group)
                    .in(ColumnDef.C_OBJECT_ID, ids, group)
                    .ge(ColumnDef.LOG_TIME, st, group)
                    .lt(ColumnDef.LOG_TIME, et, group)
                    .in(ColumnDef.ENERGY_TYPE, energyType, group)
                    .eq(ColumnDef.AGGREGATION_CYCLE, cycle, group)
                    .in(ColumnDef.PRODUCT_TYPE, productTypes, group);
            group++;
        }

        return modelServiceUtils.queryWithRedis(builder.build(), UnitObjectCostValuePlan.class);
    }

    @Override
    public List<UnitObjectCostValuePlan> query(LocalDateTime st, LocalDateTime et, Integer energyType, Collection<BaseVo> nodes, Integer cycle, Integer productType) {
        QueryConditionBuilder<BaseEntity> builder = new QueryConditionBuilder<>(AchievementRateDataLabelDef.UNIT_OBJECT_COST_PLAN)
                .orderBy(ColumnDef.LOG_TIME)
                .removeOrderById();
        if (CollectionUtils.isEmpty(nodes)) {
            builder.ge(ColumnDef.LOG_TIME, st)
                    .lt(ColumnDef.LOG_TIME, et)
                    .eq(ColumnDef.AGGREGATION_CYCLE, cycle);

            if (Objects.nonNull(energyType)) {
                builder.eq(ColumnDef.ENERGY_TYPE, energyType);
            }
            if (Objects.nonNull(productType)) {
                builder.eq(ColumnDef.PRODUCT_TYPE, productType);
            }
            return modelServiceUtils.query(builder.build(), UnitObjectCostValuePlan.class);
        }
        builder.composeMethod(true);
        Map<String, List<BaseVo>> nodeMap = nodes.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        int group = 1;
        for (Map.Entry<String, List<BaseVo>> entry : nodeMap.entrySet()) {
            String label = entry.getKey();
            List<Long> ids = nodeMap.get(label).stream().map(BaseVo::getId).distinct().collect(Collectors.toList());
            builder.eq(ColumnDef.C_OBJECT_Label, label, group)
                    .in(ColumnDef.C_OBJECT_ID, ids, group)
                    .ge(ColumnDef.LOG_TIME, st, group)
                    .lt(ColumnDef.LOG_TIME, et, group)
                    .eq(ColumnDef.AGGREGATION_CYCLE, cycle, group);
            if (Objects.nonNull(energyType)) {
                builder.eq(ColumnDef.ENERGY_TYPE, energyType, group);
            }
            if (Objects.nonNull(productType)) {
                builder.eq(ColumnDef.PRODUCT_TYPE, productType, group);
            }
            group++;
        }
        return modelServiceUtils.query(builder.build(), UnitObjectCostValuePlan.class);
    }

    @Override
    public void insertData(List<UnitObjectCostValuePlan> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        List<UnitObjectCostValuePlan> updateDataList = dataList.stream().filter(it -> !Objects.isNull(it.getId())).collect(Collectors.toList());
        updateDataList(updateDataList);

        List<UnitObjectCostValuePlan> addDataList = dataList.stream().filter(it -> Objects.isNull(it.getId())).collect(Collectors.toList());
        addDataList(addDataList);
    }

    @Override
    public void writeData(List<UnitObjectCostValuePlan> plans) {
        if (CollectionUtils.isEmpty(plans)) {
            return;
        }

        HashSet<BaseVo> nodes = new HashSet<>();
        HashSet<Long> logTimes = new HashSet<>();
        HashSet<Integer> energyTypes = new HashSet<>();
        HashSet<Integer> cycles = new HashSet<>();
        HashSet<Integer> productTypes = new HashSet<>();
        for (UnitObjectCostValuePlan energyConsumption : plans) {
            nodes.add(new BaseVo(energyConsumption.getObjectId(), energyConsumption.getObjectLabel()));
            logTimes.add(energyConsumption.getLogTime());
            energyTypes.add(energyConsumption.getEnergyType());
            cycles.add(energyConsumption.getAggregationCycle());
            productTypes.add(energyConsumption.getProductType());
        }
        List<UnitObjectCostValuePlan> oldObjectCostValuePlan = queryAllObjectCostValuePlan(nodes, logTimes, energyTypes, cycles, productTypes);
        for (UnitObjectCostValuePlan energyConsumption : plans) {
            Optional<UnitObjectCostValuePlan> any = oldObjectCostValuePlan.stream().filter(it -> checkEqual(energyConsumption, it)).findAny();
            any.ifPresent(it -> energyConsumption.setId(it.getId()));
        }

        insertData(plans);
    }

    private boolean checkEqual(UnitObjectCostValuePlan newObj, UnitObjectCostValuePlan oldObj) {
        return Objects.equals(newObj.getObjectLabel(), oldObj.getObjectLabel()) &&
                Objects.equals(newObj.getObjectId(), oldObj.getObjectId()) &&
                Objects.equals(newObj.getLogTime(), oldObj.getLogTime()) &&
                Objects.equals(newObj.getAggregationCycle(), oldObj.getAggregationCycle()) &&
                Objects.equals(newObj.getEnergyType(), oldObj.getEnergyType()) &&
                Objects.equals(newObj.getProductType(), oldObj.getProductType());
    }

    private List<UnitObjectCostValuePlan> queryAllObjectCostValuePlan(@NotEmpty Collection<BaseVo> nodes, @NotEmpty Collection<Long> logTimes,
                                                                      @NotEmpty Collection<Integer> energyTypes, @NotEmpty Collection<Integer> cycles,
                                                                      @NotEmpty Collection<Integer> productTypes) {
        Map<String, List<BaseVo>> nodeMap = nodes.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        LambdaQueryWrapper<UnitObjectCostValuePlan> wrapper = LambdaQueryWrapper.of(UnitObjectCostValuePlan.class);
        nodeMap.forEach((label, nodeList) -> {
            Set<Long> ids = nodeList.stream().map(BaseVo::getId).collect(Collectors.toSet());
            wrapper.or(it -> it.eq(UnitObjectCostValuePlan::getObjectLabel, label)
                    .in(UnitObjectCostValuePlan::getObjectId, ids)
                    .in(UnitObjectCostValuePlan::getLogTime, logTimes)
                    .in(UnitObjectCostValuePlan::getAggregationCycle, cycles)
                    .in(UnitObjectCostValuePlan::getEnergyType, energyTypes)
                    .in(UnitObjectCostValuePlan::getProductType, productTypes));
        });

        return this.selectListRedisWithOutOrderById(wrapper);

    }

    private void addDataList(List<UnitObjectCostValuePlan> addDataList) {
        if (CollectionUtils.isEmpty(addDataList)) {
            return;
        }

        List<String> updateFields = Arrays.asList(ColumnDef.C_OBJECT_ID,
                ColumnDef.C_OBJECT_Label,
                ColumnDef.AGGREGATION_CYCLE,
                ColumnDef.ENERGY_TYPE,
                ColumnDef.LOG_TIME,
                ColumnDef.VALUE,
                ColumnDef.PROJECT_ID,
                ColumnDef.PRODUCT_TYPE);
        List<List<Object>> writeDataList = new ArrayList<>();
        for (UnitObjectCostValuePlan data : addDataList) {
            writeDataList.add(Arrays.asList(data.getObjectId(),
                    data.getObjectLabel(),
                    data.getAggregationCycle(),
                    data.getEnergyType(),
                    data.getLogTime(),
                    data.getValue(),
                    data.getProjectId(),
                    data.getProductType()));
        }
        modelServiceUtils.writeDataBatch(AchievementRateDataLabelDef.UNIT_OBJECT_COST_PLAN, true, updateFields, writeDataList, null);
    }

    private void updateDataList(List<UnitObjectCostValuePlan> updateDataList) {
        if (CollectionUtils.isEmpty(updateDataList)) {
            return;
        }

        List<String> filterFields = Arrays.asList(ColumnDef.C_OBJECT_ID,
                ColumnDef.C_OBJECT_Label,
                ColumnDef.AGGREGATION_CYCLE,
                ColumnDef.ENERGY_TYPE,
                ColumnDef.LOG_TIME, ColumnDef.PRODUCT_TYPE);

        List<String> updateFields = Collections.singletonList(
                ColumnDef.VALUE);
        List<List<Object>> writeDataList = new ArrayList<>();
        List<List<Object>> filterDataList = new ArrayList<>();
        for (UnitObjectCostValuePlan data : updateDataList) {
            writeDataList.add(Collections.singletonList(
                    data.getValue()));
            filterDataList.add(Arrays.asList(data.getObjectId(),
                    data.getObjectLabel(),
                    data.getAggregationCycle(),
                    data.getEnergyType(),
                    data.getLogTime(), data.getProductType()));
        }
        modelServiceUtils.writeDataBatch(AchievementRateDataLabelDef.UNIT_OBJECT_COST_PLAN, false, updateFields, writeDataList, filterFields, filterDataList);
    }
}
package com.cet.eem.bll.energysaving.model.aioptimization;

import com.cet.eem.bll.energysaving.model.weather.PumpVo;
import com.cet.eem.common.model.BaseVo;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : StrategyAdvice
 * @Description : 优化策略建议
 * <AUTHOR> ji<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-04-15 14:12
 */
@Getter
@Setter
public class StrategyAdvice extends PumpVo {
    private Double outWaterTemp;
    private Boolean isStart;
    /**
     * 冷机类型
     */
    private String mainType;
}
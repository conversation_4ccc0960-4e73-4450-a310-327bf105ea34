package com.cet.eem.bll.achievementrate.model;

import com.cet.eem.common.model.BaseVo;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> jiangzixuan
 * @classname : BaseVoChoose
 * @description :
 * @date: 2024-02-07 15:11
 */
@Getter
@Setter
public class BaseVoChoose extends BaseEntity {
    @ApiModelProperty("是否选择")
    private Integer chooseState;

    @ApiModelProperty("子节点")
    private List<BaseVoChoose> children;
    @ApiModelProperty("父节点")
    private BaseVoChoose parentNode;
    @ApiModelProperty("子节点选择状态")
    private Integer childSelectState;
    @ApiModelProperty("开始节点")
    private Boolean startPoint;
    @ApiModelProperty("结束节点")
    private Boolean endPoint;
    @ApiModelProperty("关联表计")
    private List<Long> deviceIds;

    public BaseVoChoose() {
    }

    public BaseVoChoose(Long id, String modelLabel) {
        this.id = id;
        this.modelLabel = modelLabel;
    }
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof BaseVoChoose)) {
            return false;
        }
        BaseVoChoose baseVo = (BaseVoChoose) o;
        return Objects.equals(getId(), baseVo.getId()) &&
                Objects.equals(getModelLabel(), baseVo.getModelLabel());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId(), getModelLabel());
    }
}
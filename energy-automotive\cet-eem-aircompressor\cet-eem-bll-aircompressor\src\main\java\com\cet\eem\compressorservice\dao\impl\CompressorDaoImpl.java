package com.cet.eem.compressorservice.dao.impl;

import com.cet.eem.bll.common.model.demand.vo.ObjectLabel;
import com.cet.eem.bll.common.model.domain.object.event.SystemEvent;
import com.cet.eem.bll.common.model.domain.perception.logicaldevice.PecEventExtendVo;
import com.cet.eem.common.constant.EnumRoomType;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.compressorservice.dao.CompressorDao;
import com.cet.eem.compressorservice.model.Constant.Constant;
import com.cet.eem.compressorservice.model.vo.Equipments;
import com.cet.eem.compressorservice.model.vo.RoomVo;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.base.SingleModelConditionDTO;
import com.cet.eem.model.constant.ConditionOperator;
import com.cet.eem.model.model.BaseEntity;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * @ClassName : CompressorDaoImpl
 * <AUTHOR> yangy
 * @Date: 2022-04-02 10:15
 */
@Component
public class CompressorDaoImpl implements CompressorDao {

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Override
    public List<RoomVo> queryByRoomtype(Long projectId, Integer roomtype) {
        SingleModelConditionDTO subcondition = new SingleModelConditionDTO.Builder(NodeLabelDef.ROOM)
                .where(ColumnDef.ROOM_TYPE, ConditionOperator.EQ, EnumRoomType.COMPRESSOR_PLANT.getId()).build();
        QueryCondition condition = new QueryConditionBuilder(NodeLabelDef.PROJECT, projectId)
                .leftJoin(subcondition)
                .build();
        return modelServiceUtils.query(condition, RoomVo.class);
    }

    @Override
    public List<Equipments> queryByRoomId(Long roomId) {
        QueryCondition condition = new QueryConditionBuilder(NodeLabelDef.ROOM, roomId)
                .leftJoin(NodeLabelDef.AIR_COMPRESSOR)
                .leftJoin(NodeLabelDef.PUMP)
                .leftJoin(NodeLabelDef.COOLING_TOWER)
                .leftJoin(NodeLabelDef.DRYING_MACHINE)
                .build();
        return modelServiceUtils.query(condition, Equipments.class);
    }

    @Override
    public List<Equipments> queryByRoomIdList(List<Long> roomIdList) {
        if (CollectionUtils.isEmpty(roomIdList)) {
            return Collections.emptyList();
        }
        QueryCondition condition = new QueryConditionBuilder(NodeLabelDef.ROOM, roomIdList)
                .leftJoin(NodeLabelDef.AIR_COMPRESSOR)
                .leftJoin(NodeLabelDef.PUMP)
                .leftJoin(NodeLabelDef.COOLING_TOWER)
                .leftJoin(NodeLabelDef.DRYING_MACHINE)
                .build();
        return modelServiceUtils.query(condition, Equipments.class);
    }

    @Override
    public List<SystemEvent> querySysEventByLabels(List<ObjectLabel> labels, Long st) {
        QueryConditionBuilder<BaseEntity> condition = new QueryConditionBuilder<>(Constant.SYSTEMEVENT);
        /*Map<String, List<ObjectLabel>> listMap = labels.stream().collect(Collectors.groupingBy(ObjectLabel::getObjectlabel));
        listMap.forEach((key,val)->{
            List<Long> objectIds = val.stream().map(ObjectLabel::getObjectid).collect(Collectors.toList());
        });*/
        if (CollectionUtils.isEmpty(labels)) {
            return Collections.emptyList();
        }
        labels.forEach(label -> {
            condition.eq(ColumnDef.OBJECT_Label, label.getObjectlabel(), labels.indexOf(label) + 1);
            condition.eq(ColumnDef.OBJECT_ID, label.getObjectid(), labels.indexOf(label) + 1);
            condition.ge(Constant.EVENTTIME, st, labels.indexOf(label) + 1);
        });
        condition.composeMethod(true);
        return modelServiceUtils.query(condition.build(), SystemEvent.class);
    }

    @Override
    public List<PecEventExtendVo> queryPecEventByLabels(List<ObjectLabel> labels, Long st, Long end) {
        QueryConditionBuilder<BaseEntity> condition = new QueryConditionBuilder<>(Constant.PECEVENTEXTEND);
        if (CollectionUtils.isEmpty(labels)) {
            return Collections.emptyList();
        }
        labels.forEach(label -> {
            condition.eq(Constant.MONITOREDLABEL, label.getObjectlabel(), labels.indexOf(label) + 1);
            condition.eq(Constant.MONITOREDID, label.getObjectid(), labels.indexOf(label) + 1);
            condition.ge(Constant.EVENTTIME, st, labels.indexOf(label) + 1);
            condition.lt(Constant.EVENTTIME, end, labels.indexOf(label) + 1);
        });
        condition.composeMethod(true);
        return modelServiceUtils.query(condition.build(), PecEventExtendVo.class);
    }

}

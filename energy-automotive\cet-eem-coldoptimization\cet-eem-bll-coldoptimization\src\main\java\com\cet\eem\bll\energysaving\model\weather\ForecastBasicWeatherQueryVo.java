package com.cet.eem.bll.energysaving.model.weather;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI预测基础数据查询条件
 *
 * <AUTHOR>
 * @date 2021/12/13
 */
@Getter
@Setter
@ApiModel("AI预测基础数据查询条件")
public class ForecastBasicWeatherQueryVo {
    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("周期")
    private Integer cycle;

    @ApiModelProperty("项目id")
    private List<Long> projectIds;
    @ApiModelProperty("预测数据开始时间")
    private LocalDateTime startTimePredict;

    @ApiModelProperty("预测数据结束时间")
    private LocalDateTime endTimePredict;
}

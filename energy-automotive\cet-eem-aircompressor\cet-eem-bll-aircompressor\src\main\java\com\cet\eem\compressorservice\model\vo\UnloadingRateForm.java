package com.cet.eem.compressorservice.model.vo;

import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * @ClassName : UnloadingRateForm
 * <AUTHOR> yangy
 * @Date: 2022-04-08 10:10
 */
@Data
public class UnloadingRateForm {
    List<Long> ids;
    Integer aggregationCycle;
    Long startTime;
    Long endTime;

    public Integer getAggregationCycle() {
        if (Objects.equals(aggregationCycle, AggregationCycle.ONE_MONTH)) {
            return AggregationCycle.ONE_DAY;
        } else if (Objects.equals(aggregationCycle, AggregationCycle.ONE_DAY)) {
            return AggregationCycle.ONE_HOUR;
        }
        return aggregationCycle;
    }
}

package com.cet.eem.bll.compressoroptimization.service.config;

import com.cet.eem.bll.compressoroptimization.model.benefit.BenefitQueryParam;
import com.cet.eem.bll.compressoroptimization.model.benefit.ElectricalMonthData;
import com.cet.eem.bll.compressoroptimization.model.benefit.ElectricalYearData;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @ClassName : BenefitAnalysisService
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-25 16:17
 */
public interface BenefitAnalysisService {
    /**
     * 查询年数据
     * @param roomId
     * @param projectId
     * @return
     */
    ElectricalYearData queryElectricalYearData( Long roomId,Long projectId) throws IOException;

    /**
     * 查询月数据
     * @param queryParam
     * @return
     */
    List<ElectricalMonthData> queryElectricalMonthData(BenefitQueryParam queryParam,Long projectId) throws IOException;

    /**
     * 导出数据
     * @param queryParam
     * @param response
     * @param projectId
     */
    void exportMonthData( BenefitQueryParam queryParam, HttpServletResponse response,Long projectId) throws IOException;
}
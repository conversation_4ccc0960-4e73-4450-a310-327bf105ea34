package com.cet.eem.bll.compressoroptimization.model.strategy;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.bll.compressoroptimization.def.CompressorOptimizationLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : AiCompressorStrategy
 * @Description : 空压策略
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-17 11:29
 */
@Getter
@Setter
@ModelLabel(CompressorOptimizationLabelDef.CONTROL_STRATEGY)
public class AiCompressorStrategy extends BaseEntity {
    @ApiModelProperty("空压系统id")
    @JsonProperty("systemid")
    private Long systemId ;
    @ApiModelProperty("操作时间")
    @JsonProperty("operationtime")
    private Long operationTime ;
    @ApiModelProperty("策略类型")
    @JsonProperty("strategytype")
    private Integer strategyType ;
    @ApiModelProperty("策略发出时间(写入时间）")
    @JsonProperty("updatetime")
    private Long updateTime ;
    @JsonProperty(ColumnDef.PROJECT_ID)
    private Long projectId;
    public AiCompressorStrategy(){
        this.modelLabel=CompressorOptimizationLabelDef.CONTROL_STRATEGY;
    }
}
package com.cet.eem.model.param;


import com.cet.eem.def.AggregationCycle;
import com.cet.eem.model.vo.DataLogValue;
import com.cet.eem.util.TimeUtil;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/15 17:41
 */
@Getter
@Setter
@NoArgsConstructor
public class RepairPointParam {

    //典型日数据
    private List<DataLogValue> dataLogValues;
    //缺失起始时间
    private Long startTime;
    //缺失结束时间
    private Long endTime;

    //不需要传 真实值的起始值
    private Double startValue;
    //不需要传 真实值的结束值
    private Double endValue;

    /**
     * note 考虑dataLogValues不是5分钟间隔
     *
     * @return
     */
    public double genReferenceValue() {
        //获取距离0点的多少
        long referenceStartTime =genReferenceStartTime();
        long referenceEndTime = genReferenceEndTime();
        Map<Long, Double> referenceMap = dataLogValues.stream().collect(Collectors.toMap(DataLogValue::getTime, DataLogValue::getValue));
        //实际时间对应的参考时间
        Double referenceEndValue = genReferenceValue(referenceMap,referenceEndTime);
        Double referenceStartValue = genReferenceValue(referenceMap,referenceStartTime);
        if (Objects.isNull(referenceStartValue) || Objects.isNull(referenceEndValue)) {
            return 0d;
        }
        return referenceEndValue - referenceStartValue;
    }

    /**
     * 如果没有5分钟间隔的数据  取距离该时间最近的两个数据进行
     * todo 跨天时  endtime是0点 但是参考日只到23.55
     * @param referenceTime
     * @return
     */
    private Double genReferenceValue(Map<Long, Double> referenceMap, long referenceTime) {
        if (referenceMap.containsKey(referenceTime) && Objects.nonNull(referenceMap.get(referenceTime))) {
            return referenceMap.get(referenceTime);
        }
        //大于参考时间的第一个值
        Long eet = referenceMap.keySet().stream().sorted(Long::compare).filter(item -> item > referenceTime).findFirst().orElse(null);
        //小于参考时间的第一个值
        Long sst = referenceMap.keySet().stream().sorted(Comparator.comparing(item -> item, Comparator.reverseOrder())).filter(item -> item < referenceTime).findFirst().orElse(null);
        if (Objects.isNull(eet) || Objects.isNull(sst)) {
            return null;
        }
        Double eetValue = referenceMap.get(eet);
        Double sstValue = referenceMap.get(sst);
        if (Objects.isNull(eetValue) || Objects.isNull(sstValue)) {
            return null;
        }
        if (eetValue - sstValue > 0) {
            return ((double) (referenceTime - sst) / (double) (eet - sst)) * (eetValue - sstValue) + sstValue;
        }
        if (sstValue - eetValue < 0) {
            return null;
        }
        return sstValue;

    }

    public List<DataLogValue> genReferenceDataLog() {
        //获取距离0点的多少
        long referenceStartTime = genReferenceStartTime();
        long referenceEndTime = genReferenceEndTime();
        return dataLogValues.stream().filter(item -> item.getTime() >= referenceStartTime && item.getTime() <= referenceEndTime).collect(Collectors.toList());
    }

    public Long genReferenceStartTime() {
        //获取距离0点的多少
        Long st = TimeUtil.getCycleStartTime(startTime, AggregationCycle.ONE_DAY);
        return (startTime - st) + dataLogValues.get(0).getTime();
    }

    public Long genReferenceEndTime() {
        //获取距离0点的多少
        Long st = TimeUtil.getCycleStartTime(startTime, AggregationCycle.ONE_DAY);
        return (endTime - st) + dataLogValues.get(0).getTime();
    }

}

# 服务端口配置
server:
  port: 18093
  compression: # 对返回数据进行压缩
    enabled: true
    mime-types: application/json,application/xml,text/html,text/plain,text/css,application/x-javascript
    min-response-size: 10240 # 10Kb

spring:
  security:
    user:
      name: eem
      password: Ceiec4567%%
  profiles:
    active: 66
    include:
      - baseconfig
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  application:
    name: eem-base-loss-service
  servlet:
    # 上传文件配置，可保持不变
    multipart:
      enabled: true
      max-file-size: 101MB
      max-request-size: 101MB
      file-size-threshold: 10MB
  redis:
    host: ${web_ip}
    port: 26379
    jedis:
      pool:
        max-active: 8 # 最大连接数
        max-wait: -1 # 最大阻塞等待时间
        max-idle: 8 # 最大空闲
        min-idle: 0 # 最小空闲
    database: 1
    password: Ceiec4567%%
    client-type: jedis
  rabbitmq:
    host: ${web_ip}
    port: 5673
    username: eem
    password: Ceiec4567%%
    listener:
      simple:
        retry:
          enabled: true
          max-attempts: 5
          initial-interval: 1000
    virtual-host: '/'

cet:
  eem:
    auth:
      authType: "normal"
    common:
      enableCatchException: true
      file:
        fileRootPath: 'E:\tmpfiles'
        maxSize: 102400 # 单位为Kb
        maxExportSize: 10000
        validExtensions: 'image/png,image/jpeg,image/gif,image/bmp,application/pdf,application/x-tika-ooxml,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.presentationml.presentation,application/vnd.ms-powerpoint,application/zip,video/mp4,video/3gpp,video/x-matroska,video/x-msvideo,video/mpeg'
  log:
    enabled: false
  base-service:
    notice-service:
      url: ${web_ip}:5070
    model-service:
      url: ${web_ip}:8085
    device-data-service:
      url: ${web_ip}:5050
    cloud-auth-service:
      url: ${web_ip}:2014
    pec-node-service:
      url: ${web_ip}:8180
    workflow-service:
      url: ${web_ip}:8012
    only-report-service:
      url: ${web_ip}:4000
  feign:
    url:
      videoService: ${VIDEO-MANAGEMENT-SERVICE:************:8082}
      onlyReportService: ${ONLY-REPORT-DOCKER-SERVICE:************:4000}
      modelService: ${MODEL_SERVICE:************:8085}
      noticeService: ${NOTICE-SERVICE:************:5070}
enable:
  scheduling: true
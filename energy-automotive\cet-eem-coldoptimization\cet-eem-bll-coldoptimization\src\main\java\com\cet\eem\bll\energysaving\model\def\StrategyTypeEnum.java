package com.cet.eem.bll.energysaving.model.def;

import lombok.Getter;

import java.util.Objects;

/**
 * @ClassName : StrategyTypeEnum
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-29 16:58
 */
@Getter
public enum StrategyTypeEnum {
    UP(1, "开机"),
    OFF(2, "关机"),
    OPT(3, "调优");

    private int id;
    private String text;
    private StrategyTypeEnum(int id, String text) {
        this.id = id;
        this.text = text;
    }
    public static String valueOf(Integer code) {
        if (Objects.isNull(code)) {
            return "";
        }
        for (StrategyTypeEnum type : StrategyTypeEnum.values()) {
            if (Objects.equals(type.getId(), code)) {
                return type.getText();
            }
        }
        return "";

    }

}

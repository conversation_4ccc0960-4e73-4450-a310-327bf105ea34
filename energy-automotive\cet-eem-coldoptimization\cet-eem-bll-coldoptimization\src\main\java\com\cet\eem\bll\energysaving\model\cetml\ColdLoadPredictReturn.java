package com.cet.eem.bll.energysaving.model.cetml;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : ColdLoadPredictReturn
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-06-07 11:34
 */
@Getter
@Setter
public class ColdLoadPredictReturn {
    @JsonProperty("project_id")
    private Long projectId;
    @JsonProperty("predict_results")
    private List<ColdLoadPredict> predictResults;

}
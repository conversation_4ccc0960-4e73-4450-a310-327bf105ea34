# ResultWithTotal类修改
针对Service和Dao层的ResultWithTotal做修改统一修改为ApiResult，注意修改相关的import引用，修改成import com.cet.electric.commons.ApiResult;


# 针对UnitService修改
## UnitService是针对自定义单位的查询和兼容，之前是可以根据多个类型查询单位的，现在需要结合具体的业务场景，将能耗、产量分开不同的方法做查询适配
**能耗传值ProjectUnitClassify.ENERGY，产量则传值ProjectUnitClassify.PRODUCT**
修改前：
```java
    # 类的引用
    import com.cet.piem.service.UnitService;

    #类的注入
    @Resource
    private UnitService unitService;

    #具体方法
    # energyValueList是具体的数值
    # ProjectUnitClassify.ENERGY是能耗单位
    # dto.getEnergyType()是具体的能耗类型
    List<Double> energyValueList = teamGroupEnergyList.stream().map(TeamGroupEnergy::getValue).collect(Collectors.toList());
    UserDefineUnit unit = unitService.getUnit(energyValueList, ProjectUnitClassify.ENERGY, dto.getEnergyType());
```
修改后(请注意UserDefineUnitDTO是UserDefineUnit的父类，很多情况下直接替换新的实体类UserDefineUnitDTO就可以了，不要再使用子类UserDefineUnit)
```java
    import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
    import com.cet.eem.fusion.config.sdk.service.EnergyUnitService;
    import com.cet.eem.fusion.config.sdk.entity.unit.UserDefineUnitSearchDTO;

    @Resource
    private EnergyUnitService energyUnitService;

    #具体逻辑处理
    Double maxValue = teamGroupEnergyList.stream().map(TeamGroupEnergy::getValue).max(Double::compareTo).orElse(null);
    UserDefineUnitDTO userDefineUnitDTO = energyUnitService.queryUnitCoef(new UserDefineUnitSearchDTO(GlobalInfoUtils.getTenantId(), dto.getEnergyType(), ProjectUnitClassify.ENERGY, maxValue));
```

# 针对EemCloudAuthService修改
当前已经废弃了EemCloudAuthService，针对不同的使用场景需要使用不同的方法
## 根据用户id查询用户信息cloudAuthService.queryUserBatch(longs)
修改前：
```java
import com.cet.eem.service.EemCloudAuthService;
import com.cet.eem.common.model.Result;

@Resource
private EemCloudAuthService cloudAuthService;

Result<List<UserVo>> listResult = cloudAuthService.queryUserBatch(longs);
List<UserVo> userInfoList = listResult.getData();
```

修改后
```java
import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;
import com.cet.electric.matterhorn.cloud.authservice.common.util.i18n.ApiResultI18n;

@Resource
UserRestApi userRestApi;

ApiResultI18n<List<UserVo>> userQueryResult = userRestApi.getUsers(userIdList);
List<UserVo> userInfoList = userQueryResult.getData();
```
# 节点权限校验修改
## 针对 nodeAuthBffService.checkPartAuth(baseVo, GlobalInfoUtils.getUserId())
修改前：
```java
import com.cet.eem.auth.service.NodeAuthCheckService;

@Autowired
NodeAuthCheckService nodeAuthBffService;

    if (!nodeAuthBffService.checkPartAuth(baseVo, GlobalInfoUtils.getUserId())) {
        throw new ValidationException("无当前节点访问权限！");
    }
```
修改后：
```java
import com.cet.futureblue.i18n.LanguageUtil;
import com.cet.eem.fusion.common.entity.ParentParam;
import com.cet.eem.fusion.config.sdk.auth.service.NodeAuthCheckService;
import com.cet.eem.fusion.common.def.i18n.DataMaintainLangKeyDef;

@Autowired
private NodeAuthCheckService nodeAuthBffService;

    ParentParam parentParam = new ParentParam();
    parentParam.setRootNode(new BaseEntity(baseVo.getId(),baseVo.getModelLabel()));
    parentParam.setTenantId(GlobalInfoUtils.getTenantId());
    parentParam.setUserId(GlobalInfoUtils.getUserId());
    if (!nodeAuthBffService.checkCompleteOrganizationNodes(parentParam)) {
        throw new ValidationException(LanguageUtil.getMessage(DataMaintainLangKeyDef.Connect.NO_COMPLETE_AUTH));
    }
```

# 针对AuthUtils的修改
## com.cet.eem.auth.service.AuthUtils提供了针对权限的一系列修改，但是已经废弃。
### 针对queryAndCheckUser的修改
修改前：
```java
import com.cet.eem.auth.service.AuthUtils;

    @Autowired
    AuthUtils authUtils;

    UserVo user = authUtils.queryAndCheckUser(userId);
```
修改后：

```java
import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;
import com.cet.electric.matterhorn.cloud.authservice.common.util.i18n.ApiResultI18n;

    @Resource
    UserRestApi userRestApi;
    
    ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(userId);
    UserVo user = usrRes.getData();
```


# GlobalInfoUtils.getHttpResponse()修改
com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils不提供getHttpResponse()方法，需要在接口入参显示的声明并获取response，详细修改样例如下
修改前
```java
@ApiOperation(value = "根据项目下载签到点二维码")
@GetMapping("/downloadQrCode/project")
public ApiResult<Object> downProjectLoadQrCode(@RequestParam(name = "projectId") Long projectId) throws IOException {
    signInService.downProjectLoadQrCode(projectId, GlobalInfoUtils.getHttpResponse());
    return Result.ok();
}
```
修改后：
```java
@ApiOperation(value = "根据项目下载签到点二维码")
@GetMapping("/downloadQrCode/project")
public ApiResult<Object> downProjectLoadQrCode(@RequestParam(name = "projectId") Long projectId, HttpServletResponse response) throws IOException {
    signInService.downProjectLoadQrCode(projectId, response);
    return Result.ok();
}
```

# JsonUtil修改
com.cet.eem.common.util.JsonUtil;已经废弃，相关替代方案如下：

变更前：
```java
import com.cet.eem.common.util.JsonUtil;

List<InspectionWorkOrderDto> workOrderList = JsonUtil.mapList2BeanList(modelEntityList.getData(), InspectionWorkOrderDto.class);
```
变更后：
```java
import com.cet.eem.fusion.common.utils.JsonTransferUtils;

List<InspectionWorkOrderDto> workOrderList = JsonTransferUtils.parseList(modelEntityList.getData(), InspectionWorkOrderDto.class);
```
# CommonUtils.sort废弃
com.cet.eem.common.CommonUtils.sort已经废弃，相关替代方案如下：

变更前：
```java
import com.cet.eem.common.CommonUtils;

result.sort((v1, v2) -> CommonUtils.sort(v1.getCreateTime(), v2.getCreateTime(), false));
```
变更后：
```java
import com.cet.eem.fusion.common.utils.SortUtils;

result.sort((v1, v2) -> SortUtils.sort(v1.getCreateTime(), v2.getCreateTime(), false));
```

# 日志工具类修改commonUtilsService.writeAddOperationLogs()
变更前
```java
import com.cet.eem.bll.common.log.service.CommonUtilsService;

    @Autowired
    CommonUtilsService commonUtilsService;

    commonUtilsService.writeAddOperationLogs(OperationLogType.INSPECTOR_WORK_ORDER, "手动创建工单", dto, GlobalInfoUtils.getUserId());
```
变更后

```java
import com.cet.eem.fusion.config.sdk.service.log.EemLogService;

    @Autowired
    EemLogService eemLogService;

    ParentParam parentParam = new Parameter();
    eemLogService.writeAddOperationLogs(OperationLogType.INSPECTOR_WORK_ORDER, "手动创建工单",new Object[] {dto},parentParam);
```

# MessagePushUtils.pushToWeb变更
com.cet.eem.bll.common.util.MessagePushUtils.pushToWeb已经废弃
变更前
```java
import com.cet.eem.bll.common.util.MessagePushUtils;

    @Autowired
    MessagePushUtils messagePushUtils;

    messagePushUtils.pushToWeb(null, EnumSystemEventType.INSPECT_WORK_ORDER_OVER_TIME.getId(), desc, userIds, null, MessageTypeDef.SYSTEM_EVENT);
```
变更后：
```java
import com.cet.eem.fusion.common.utils.notice.WebNotification;

    @Resource
    private WebNotification webNotification;

    webNotification.pushToWeb(null, event.getEventType(), event.getDescription(),userIds, LossConstants.EVENT_CLASS, MessageTypeDef.SYSTEM_EVENT, tenantId, LOSS_EVENT_REDIRECT_PAGE_URL,GlobalInfoUtils.getTenantId(),null);
```

# 物理量查询方法（需要依赖eem-base-fusion-energy-sdk）
## 物理量对象获取方法QuantityObjectService
获取某个节点所有节点物理量对象数据
```java

    @Autowired
    QuantityObjectService quantityObjectService;

    List<QuantityObject> quantityObjects = quantityObjectService.queryQuantityObject(queryDTO.getNodes());
```
获取某个节点的指定物理量对象
```java

    @Autowired
    QuantityObjectService quantityObjectService;

    List<QuantityObject> quantityObjects = quantityObjectService.queryQuantityObject(queryDTO.getNodes(), queryDTO.getTemplates());
```
## 物理量映射对象数据QuantityObjectMapService
根据物理量对象获取物理量映射对象
```java
    @Autowired
    QuantityObjectMapService quantityObjectMapService;

    Set<Long> quantityObjectIds = quantityObjects.stream().map(BaseEntity::getId).collect(Collectors.toSet());
    List<QuantityObjectMap> quantityObjectMaps = quantityObjectMapService.queryQuantityObjectMapByQuantityObjectIds(quantityObjectIds);
```
## 获取物理量数据QuantityObjectDataService
获取指定时间区间的物理量数据
```java
    @Autowired
    QuantityObjectDataService quantityObjectDataService;

    List<QuantityAggregationData> dataList = quantityObjectDataService.queryQuantityData(searchVo.getStartTime(), searchVo.getEndTime(), quantityObjectIds, searchVo.getAggregationCycle(), searchVo.getAggregationType());
```
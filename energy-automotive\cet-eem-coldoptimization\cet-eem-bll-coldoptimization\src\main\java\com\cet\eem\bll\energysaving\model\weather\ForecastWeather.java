package com.cet.eem.bll.energysaving.model.weather;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/12/13
 */
@ApiModel("天气预报数据")
@Getter
@Setter
public class ForecastWeather {
    @ApiModelProperty("时间")
    private LocalDateTime logTime;

    @ApiModelProperty("湿度")
    private Double humidity;

    @ApiModelProperty("温度")
    private Double temp;

    @ApiModelProperty("类型，用于区分预测值还是实测值")
    private Integer type;
    private Integer cycle;
}

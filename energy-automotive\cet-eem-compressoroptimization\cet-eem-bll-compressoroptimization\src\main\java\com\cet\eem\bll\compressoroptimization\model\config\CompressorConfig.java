package com.cet.eem.bll.compressoroptimization.model.config;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.bll.compressoroptimization.def.CompressorOptimizationLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @ClassName : CompressorConfig
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-17 10:54
 */
@Getter
@Setter
@ModelLabel(CompressorOptimizationLabelDef.COMPRESSOR_CONFIG)
public class CompressorConfig extends BaseEntity {
    @ApiModelProperty("空压系统id")
    @JsonProperty("systemid")
    private Long systemId;
    @ApiModelProperty("系统压力下限")
    @JsonProperty("systempressuremin")
    private Double systemPressureMin;
    @ApiModelProperty("系统压力上限")
    @JsonProperty("systempressuremax")
    private Double systemPressureMax;
    @ApiModelProperty("系统压力下限确认时间（ms）")
    @JsonProperty("systempressureminchecktime")
    private Long systemPressureMinCheckTime;

    @ApiModelProperty("系统压力上限确认时间（ms）")
    @JsonProperty("systempressuremaxchecktime")
    private Long systemPressureMaxCheckTime;

    @ApiModelProperty("系统压力下限报警")
    @JsonProperty("systempressureminalarm")
    private Double systemPressureMinAlarm;

    @ApiModelProperty("系统压力上限报警")
    @JsonProperty("systempressuremaxalarm")
    private Double systemPressureMaxAlarm;
    @ApiModelProperty("螺杆空压机启停间隔时间（ms）")
    @JsonProperty("screwturnintervaltime")
    private Long screwTurnIntervalTime;
    @ApiModelProperty("螺杆空压机连续切换运行时间（ms）")
    @JsonProperty("screwoperationswitchtime")
    private Long screwOperationSwitchTime;
    @ApiModelProperty("低压报警值")
    @JsonProperty("screwlowpressurealarm")
    private Double screwLowPressureAlarm;
    @ApiModelProperty("高压报警值")
    @JsonProperty("screwhighpressurealarm")
    private Double screwHighPressureAlarm;
    @ApiModelProperty("离心空压机启停间隔时间（ms）")
    @JsonProperty("centrifugalturnintervaltime")
    private Long centrifugalTurnIntervalTime;
    @ApiModelProperty(" 离心空压机连续切换运行时间（ms）")
    @JsonProperty("centrifugaloperationswitchtime")
    private Long centrifugalOperationSwitchTime;

    @ApiModelProperty("低压报警值")
    @JsonProperty("centrifugallowpressurealarm")
    private Double centrifugalLowPressureAlarm;
    @ApiModelProperty("高压报警值")
    @JsonProperty("centrifugalhighpressurealarm")
    private Double centrifugalHighPressureAlarm;

    @ApiModelProperty("螺杆空压机连续启动时间间隔（ms）")
    @JsonProperty("screwstartintervaltime")
    private Long screwStartIntervalTime;
    @ApiModelProperty("螺杆空压机连续停机时间间隔（ms）")
    @JsonProperty("screwstopintervaltime")
    private Long screwStopIntervalTime;

    @ApiModelProperty("卸载停机保护")
    @JsonProperty("screwunloadingstopprotection")
    private Long screwUnloadingStopProtection;
    @ApiModelProperty("离心空压机连续启动时间间隔（ms）")
    @JsonProperty("centrifugalstartintervaltime")
    private Long centrifugalStartIntervalTime;
    @ApiModelProperty("离心空压机连续停机时间间隔（ms）")
    @JsonProperty("centrifugalstopintervaltime")
    private Long centrifugalStopIntervalTime;


    @JsonProperty(ColumnDef.PROJECT_ID)
    private Long projectId;

    public CompressorConfig() {
        this.modelLabel = CompressorOptimizationLabelDef.COMPRESSOR_CONFIG;

    }

    public CompressorConfig(Long projectId) {
        this.modelLabel = CompressorOptimizationLabelDef.COMPRESSOR_CONFIG;
        this.projectId = projectId;
    }
}
package com.cet.eem.bll.energysaving.model.def;

import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : CurveTypeDef
 * @Description : 运行曲线类型
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-27 11:30
 */
@Getter
@Setter
public class CurveTypeDef {
    public static final Integer COP_PERCENT = 1;
    public static final Integer POWER_PERCENT = 2;
    public static final Integer FREQUENCY_POWER = 3;
    public static final Integer SUPPLY_TEMP_POWER = 4;
    public static final Integer RETURN_TEMP_POWER = 5;
    public static final Integer COP_PERCENT_ACTUAL = 6;
    public static final Integer POWER_PERCENT_ACTUAL = 7;
    public static final Integer PERCENT_POWER = 8;
}
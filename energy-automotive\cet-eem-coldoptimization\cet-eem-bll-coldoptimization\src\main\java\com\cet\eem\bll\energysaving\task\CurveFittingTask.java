package com.cet.eem.bll.energysaving.task;

import com.cet.eem.bll.energysaving.handle.CurveFitting;
import com.cet.eem.common.utils.TimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Configuration
@EnableScheduling
public class CurveFittingTask {
    @Autowired
    CurveFitting curveFitting;

    @Scheduled(cron = "${cet.eem.task.energy-saving.curvefitting.internal}")
    public void dataCurve() {
        curveFitting.dataFitting(TimeUtil.timestamp2LocalDateTime(System.currentTimeMillis()));
    }
}

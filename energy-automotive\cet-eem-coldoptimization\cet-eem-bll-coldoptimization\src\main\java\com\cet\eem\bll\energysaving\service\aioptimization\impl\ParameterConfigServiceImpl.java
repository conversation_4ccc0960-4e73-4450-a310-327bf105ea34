package com.cet.eem.bll.energysaving.service.aioptimization.impl;

import com.alibaba.excel.EasyExcel;
import com.cet.eem.bll.common.dao.node.NodeDao;
import com.cet.eem.bll.common.dao.pipenetwork.PipeNetworkConnectionModelDao;
import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;

import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.bll.energysaving.dao.aioptimization.*;
import com.cet.eem.bll.energysaving.dao.weather.RefrigeratingSystemDao;
import com.cet.eem.bll.energysaving.handle.OperatingEfficiencyCurveListener;
import com.cet.eem.bll.energysaving.model.aioptimization.WorkSection;
import com.cet.eem.bll.energysaving.model.aioptimization.plc.ControlMode;
import com.cet.eem.bll.energysaving.model.config.*;
import com.cet.eem.bll.energysaving.model.config.system.*;
import com.cet.eem.bll.energysaving.model.def.ColdControlTypeDef;
import com.cet.eem.bll.energysaving.model.def.CurveTypeDef;
import com.cet.eem.bll.energysaving.model.def.ModelDef;
import com.cet.eem.bll.energysaving.model.def.QuantityDef;
import com.cet.eem.bll.energysaving.model.equipmentoperation.BasicData;
import com.cet.eem.bll.energysaving.model.weather.PumpVo;
import com.cet.eem.bll.energysaving.model.weather.QueryParam;
import com.cet.eem.bll.energysaving.service.aioptimization.AiPlcControlService;
import com.cet.eem.bll.energysaving.service.aioptimization.ParameterConfigService;
import com.cet.eem.bll.energysaving.service.trend.OperationTrendService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.constant.EnumOperationType;
import com.cet.eem.common.constant.ExcelType;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.exception.ValidationException;
import com.cet.eem.common.file.FileUtils;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.model.realtime.RealTimeValue;
import com.cet.eem.common.utils.PoiExcelUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.quantity.service.QuantityManageService;
import com.ibm.icu.text.DecimalFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : ParameterConfigServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-20 19:32
 */
@Slf4j
@Service
public class ParameterConfigServiceImpl implements ParameterConfigService {
    @Autowired
    WorkSectionDao workSectionDao;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    ParameterConfigDao parameterConfigDao;
    @Autowired
    EquipmentCurveDao equipmentCurveDao;
    @Autowired
    NodeDao nodeDao;
    @Autowired
    RefrigeratingSystemDao refrigeratingSystemDao;
    @Autowired
    OperationTrendService operationTrendService;
    @Autowired
    PipeNetworkConnectionModelDao connectionModelDao;
    @Autowired
    ControlSchemeDao controlSchemeDao;
    @Autowired
    NumericalConditionDao numericalConditionDao;
    @Autowired
    MesShiftDao mesShiftDao;
    @Autowired
    ControlModeDao controlModeDao;
    @Autowired
    QuantityManageService quantityManageService;
    @Autowired
    AiPlcControlService aiPlcControlService;
    public static final Double PERCENT = 100.0D;
    public static final Double TRUE_DATA = 1.0;
    public static final Long PLC_AUTO_DATA_ID = 9014189L;
    public static final Long PLC_AI_DATA_ID = 9014193L;
    public static final Integer START = 1;
    public static final String CATCH_ERROR = "线程等待出现问题！";
    public static final Integer WAIT = 5;

    @Override
    public List<WorkSection> queryWorkSection(BaseVo baseVo) {
        return workSectionDao.queryWorkSection(baseVo);
    }

    @Override
    public void writeWorkSection(Long objectId, String objectLabel, List<WorkSection> workSections) {

        List<WorkSection> workSections1 = workSectionDao.queryWorkSection(new BaseVo(objectId, objectLabel));
        if (CollectionUtils.isEmpty(workSections1) && CollectionUtils.isEmpty(workSections)) {
            return;
        }
        List<Long> ids = workSections1.stream().map(WorkSection::getId).collect(Collectors.toList());
        assembleData(new BaseVo(objectId, objectLabel), workSections);
        //新增时
        if (CollectionUtils.isEmpty(ids)) {
            modelServiceUtils.writeData(workSections);
            return;
        }
        List<Long> idList = workSections.stream().map(WorkSection::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        //考虑要删除掉已经存在的
        ids.removeAll(idList);
        if (CollectionUtils.isNotEmpty(ids)) {
            workSectionDao.deleteBatchIds(ids);
        }
        modelServiceUtils.writeData(workSections);
    }

    private void assembleData(BaseVo baseVo, List<WorkSection> workSections) {
        for (WorkSection workSection : workSections) {
            workSection.setObjectId(baseVo.getId());
            workSection.setObjectLabel(baseVo.getModelLabel());
        }
    }

    @Override
    public void deleteWorkSection(List<Long> ids) {
        workSectionDao.deleteBatchIds(ids);
    }

    @Override
    public void deleteWorkSections(List<BaseVo> nodes) {
        List<WorkSection> workSections = workSectionDao.queryWorkSections(nodes);
        if (CollectionUtils.isEmpty(workSections)) {
            return;
        }
        List<Long> ids = workSections.stream().map(WorkSection::getId).collect(Collectors.toList());
        workSectionDao.deleteBatchIds(ids);
    }

    @Override
    public void exportOperatingEfficiencyCurve(PumpVo baseVo, HttpServletResponse response) {
        String fileName = "运行效率曲线";
        try(Workbook workBook = PoiExcelUtils.createWorkBook(ExcelType.BIG_DATA);) {
            List<EquipmentCurve> equipmentCurves = equipmentCurveDao.queryEquipmentCurve(baseVo.getModelLabel(), baseVo.getId(), Arrays.asList(CurveTypeDef.COP_PERCENT,
                    CurveTypeDef.POWER_PERCENT, CurveTypeDef.PERCENT_POWER));
            List<EquipmentCurveVo> result = new ArrayList<>();
            handleWriteData(equipmentCurves, result);
            List<Integer> colWidth = Arrays.asList(18, 18, 18, 18, 18);
            PoiExcelUtils.createSheet(workBook, fileName, (sheet, baseCellStyle, rowIndex) -> {
                int rowNum = 0;
                writeHeader(workBook, sheet, baseCellStyle, rowNum++);
                writeData(sheet, baseCellStyle, rowNum, handleResult(result));
            }, colWidth);
            //新增设备下载模板没有名称的情况
            String name = "冷水主机";
            if (Objects.nonNull(baseVo.getName())) {
                name = baseVo.getName();
            }
            FileUtils.downloadExcel(response, workBook, name + "的运行曲线模板" + LocalDateTime.now().format(TimeUtil.SECONDTIMEFORMAT), CommonUtils.APPLICATION_MSEXCEL);
        } catch (Exception e) {
            log.error("导出数据异常", e);
            throw new ValidationException(e.getMessage());
        }

    }

    /**
     * 组合数据，存的时候百分比和cop。功率分开存，导出同样的冷负荷百分对应值需要放到一起
     *
     * @param result
     * @return
     */
    private List<EquipmentCurveVo> handleResult(List<EquipmentCurveVo> result) {
        List<EquipmentCurveVo> curveVos = new ArrayList<>();
        Map<Double, List<EquipmentCurveVo>> map = result.stream().collect(Collectors.groupingBy(EquipmentCurveVo::getColdPercent));
        for (Map.Entry<Double, List<EquipmentCurveVo>> entry : map.entrySet()) {
            List<EquipmentCurveVo> value = entry.getValue();
            if (CollectionUtils.isNotEmpty(value) && value.size() > 1) {
                EquipmentCurveVo curveVo = value.get(0);
                EquipmentCurveVo curveVo1 = value.get(1);
                if (Objects.isNull(curveVo.getCop()) && Objects.nonNull(curveVo1.getCop())) {
                    curveVo.setCop(curveVo1.getCop());
                    curveVos.add(curveVo);
                } else if (Objects.isNull(curveVo.getPower()) && Objects.nonNull(curveVo1.getPower())) {
                    curveVo.setPower(curveVo1.getPower());
                    curveVos.add(curveVo);
                }
            }
        }
        return curveVos.stream().sorted(Comparator.comparing(EquipmentCurveVo::getColdPercent)).collect(Collectors.toList());
    }

    @Override
    public void importOperatingEfficiencyCurve(Long id, String label, MultipartFile file) throws IOException, ValidationException {
        List<EquipmentCurveVo> equipmentCurveVos = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), OperatingEfficiencyCurveVo.class, new OperatingEfficiencyCurveListener(equipmentCurveVos)).sheet().doRead();
        if (CollectionUtils.isEmpty(equipmentCurveVos)) {
            deleteOperatingEfficiencyCurve(Collections.singletonList(new BaseVo(id, label)));
        }
        checkColdLoadRate(equipmentCurveVos);
        List<EquipmentCurve> writeData = new ArrayList<>();
        List<EquipmentCurve> oldData = equipmentCurveDao.queryEquipmentCurve(label, id, Arrays.asList(CurveTypeDef.COP_PERCENT, CurveTypeDef.POWER_PERCENT, CurveTypeDef.PERCENT_POWER));
        transWriterData(writeData, equipmentCurveVos, id, label, oldData);
        modelServiceUtils.writeData(writeData);
    }

    /**
     * 感觉不能重复录，测试肯定会测这种情况
     *
     * @param equipmentCurveVos
     * @throws
     */
    private void checkColdLoadRate(List<EquipmentCurveVo> equipmentCurveVos) throws ValidationException {
        Map<Double, List<EquipmentCurveVo>> map = equipmentCurveVos.stream().collect(Collectors.groupingBy(EquipmentCurveVo::getColdPercent));
        for (Map.Entry<Double, List<EquipmentCurveVo>> entry : map.entrySet()) {
            if (entry.getValue().size() > 1) {
                throw new ValidationException("输入的冷量百分比为" + entry.getValue().get(0).getColdPercent() + "的内容有重复值!");
            }
        }
    }

    @Override
    public void deleteOperatingEfficiencyCurve(List<BaseVo> baseVo) {
        List<EquipmentCurve> equipmentCurves = equipmentCurveDao.queryCurveList(baseVo, Arrays.asList(CurveTypeDef.COP_PERCENT, CurveTypeDef.POWER_PERCENT));
        if (CollectionUtils.isEmpty(equipmentCurves)) {
            return;
        }
        List<Long> ids = equipmentCurves.stream().map(EquipmentCurve::getId).collect(Collectors.toList());
        equipmentCurveDao.deleteBatchIds(ids);
    }

    /**
     * 和库里已经有的数据进行比较，有历史记录加上id
     *
     * @param writeData
     * @param equipmentCurveVos
     * @param id
     * @param label
     * @param oldData
     */
    private void transWriterData(List<EquipmentCurve> writeData, List<EquipmentCurveVo> equipmentCurveVos, Long id, String label, List<EquipmentCurve> oldData) {
        List<BasicData> copPercent = new ArrayList<>();
        List<BasicData> powerPercent = new ArrayList<>();
        List<BasicData> percentPower = new ArrayList<>();
        for (EquipmentCurveVo curveVo : equipmentCurveVos) {
            copPercent.add(new BasicData(CommonUtils.calcDouble(curveVo.getColdPercent(), PERCENT, EnumOperationType.DIVISION.getId()), curveVo.getCop()));
            powerPercent.add(new BasicData(CommonUtils.calcDouble(curveVo.getColdPercent(), PERCENT, EnumOperationType.DIVISION.getId()), curveVo.getPower()));
            percentPower.add(new BasicData(curveVo.getColdLoad(), curveVo.getPower()));
        }
        EquipmentCurve equipmentCurve = new EquipmentCurve(id, label,
                copPercent, CurveTypeDef.COP_PERCENT, System.currentTimeMillis(), GlobalInfoUtils.getProjectId());
        EquipmentCurve curve = new EquipmentCurve(id, label,
                powerPercent, CurveTypeDef.POWER_PERCENT, System.currentTimeMillis(), GlobalInfoUtils.getProjectId());
        EquipmentCurve equipmentCurve1 = new EquipmentCurve(id, label,
                percentPower, CurveTypeDef.PERCENT_POWER, System.currentTimeMillis(), GlobalInfoUtils.getProjectId());
        assembleNewData(equipmentCurve, oldData, CurveTypeDef.COP_PERCENT);
        assembleNewData(curve, oldData, CurveTypeDef.POWER_PERCENT);
        assembleNewData(equipmentCurve1, oldData, CurveTypeDef.PERCENT_POWER);
        writeData.add(curve);
        writeData.add(equipmentCurve);
        writeData.add(equipmentCurve1);
    }

    private void assembleNewData(EquipmentCurve equipmentCurve, List<EquipmentCurve> oldData, Integer curveType) {
        EquipmentCurve equipmentCurve2 = oldData.stream().filter(equipmentCurve1 -> Objects.equals(equipmentCurve1.getCurveType(), curveType))
                .findAny().orElse(new EquipmentCurve());
        equipmentCurve.setId(equipmentCurve2.getId());
        equipmentCurve.setPrimaryTermCoefficient(equipmentCurve2.getPrimaryTermCoefficient());
        equipmentCurve.setQuadraticTermCoefficient(equipmentCurve2.getQuadraticTermCoefficient());
        equipmentCurve.setConstant(equipmentCurve2.getConstant());
    }

    /**
     * 文件格式数据转换成表结构的格式
     *
     * @param equipmentCurves
     * @param result
     */
    private void handleWriteData(List<EquipmentCurve> equipmentCurves, List<EquipmentCurveVo> result) {
        for (EquipmentCurve curve : equipmentCurves) {
            List<BasicData> coordinate = curve.getCoordinate();
            if (CollectionUtils.isEmpty(coordinate)) {
                return;
            }
            List<BasicData> list = coordinate.stream().sorted(Comparator.comparing(BasicData::getXData))
                    .collect(Collectors.toList());
            addDataByType(list, curve.getCurveType(), result);
        }
    }

    /**
     * 根据曲线类型写入对应的字段赋值
     *
     * @param list
     * @param type
     * @param result
     */
    private void addDataByType(List<BasicData> list, Integer type, List<EquipmentCurveVo> result) {
        if (Objects.equals(type, CurveTypeDef.COP_PERCENT)) {
            for (BasicData basicData : list) {
                EquipmentCurveVo curveVo1 = result.stream().filter(equipmentCurveVo -> Objects.equals(equipmentCurveVo.getColdPercent(), basicData.getXData()))
                        .findAny().orElse(null);
                if (Objects.nonNull(curveVo1)) {
                    curveVo1.setCop(basicData.getYData());
                    continue;
                }
                EquipmentCurveVo curveVo = new EquipmentCurveVo();
                curveVo.setColdPercent(CommonUtils.calcDouble(basicData.getXData(), PERCENT, EnumOperationType.MULTIPLICATION.getId()));
                curveVo.setCop(basicData.getYData());
                result.add(curveVo);
            }

        } else if (Objects.equals(type, CurveTypeDef.POWER_PERCENT)) {
            for (BasicData basicData : list) {
                EquipmentCurveVo curveVo1 = result.stream().filter(equipmentCurveVo -> Objects.equals(equipmentCurveVo.getColdPercent(), basicData.getXData()))
                        .findAny().orElse(null);
                if (Objects.nonNull(curveVo1)) {
                    curveVo1.setPower(basicData.getYData());
                    continue;
                }
                EquipmentCurveVo curveVo = new EquipmentCurveVo();
                curveVo.setColdPercent(CommonUtils.calcDouble(basicData.getXData(), PERCENT, EnumOperationType.MULTIPLICATION.getId()));
                curveVo.setPower(basicData.getYData());
                result.add(curveVo);
            }
        } else {
            for (BasicData basicData : list) {
                EquipmentCurveVo curveVo1 = result.stream().filter(equipmentCurveVo -> Objects.equals(equipmentCurveVo.getPower(), basicData.getYData()))
                        .findAny().orElse(null);
                if (Objects.nonNull(curveVo1)) {
                    curveVo1.setColdLoad(basicData.getXData());
                    continue;
                }
                EquipmentCurveVo curveVo = new EquipmentCurveVo();
                curveVo.setColdLoad(basicData.getXData());
                curveVo.setPower(basicData.getYData());
                result.add(curveVo);
            }
        }
    }

    private void writeHeader(Workbook workBook, Sheet sheet, CellStyle baseCellStyle, int startRow) {
        LinkedHashMap<String, CellStyle> headerMap = new LinkedHashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        CellStyle requiredStyle = createRequiredStyle(workBook, baseCellStyle);
        headerMap.put("序号", baseCellStyle);
        headerMap.put("冷负荷率（%）", requiredStyle);
        headerMap.put("冷负荷", requiredStyle);
        headerMap.put("输入功率（kW）", requiredStyle);
        headerMap.put("cop", requiredStyle);
        PoiExcelUtils.createHeaderName(sheet, startRow, headerMap);
    }

    private CellStyle createRequiredStyle(Workbook workbook, CellStyle baseCellStyle) {
        CellStyle requiredCellStyle = workbook.createCellStyle();
        requiredCellStyle.cloneStyleFrom(baseCellStyle);
        Font font = PoiExcelUtils.createFont(workbook, true, null, null, (short) 1);
        requiredCellStyle.setFont(font);
        return requiredCellStyle;
    }

    private void writeData(Sheet sheet, CellStyle baseCellStyle, int rowNum, List<EquipmentCurveVo> result) {
        int col;

        for (EquipmentCurveVo item : result) {
            col = 0;
            Row row = PoiExcelUtils.createRow(sheet, rowNum);
            PoiExcelUtils.createCell(row, col++, baseCellStyle, rowNum);
            PoiExcelUtils.createCell(row, col++, baseCellStyle, handleNoneData(item.getColdPercent()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, handleNoneData(item.getColdLoad()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, handleNoneData(item.getPower()));
            PoiExcelUtils.createCell(row, col, baseCellStyle, handleNoneData(item.getCop()));
            rowNum++;
        }
    }

    /**
     * 保留2位
     *
     * @param value
     * @return
     */
    private String handleNoneData(Double value) {
        if (Objects.isNull(value)) {
            return CommonUtils.BLANK_STR;
        }
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(value);
    }

    private Long calculate(Double value, Long trans, Integer id) {
        if (Objects.isNull(value)) {
            return null;
        }
        Double aDouble = CommonUtils.calcDouble(value, trans.doubleValue(), id);
        return aDouble.longValue();
    }

    @Override
    public ParameterConfigVo writeMachineOperationConstraints(ParameterConfigVo config) {
        config.setProjectId(GlobalInfoUtils.getProjectId());
        ParameterConfig paramConfig = new ParameterConfig();
        BeanUtils.copyProperties(config, paramConfig);
        //单位转换库里存ms，输入实际是double类型
        paramConfig.setStartStopTimeInterval(calculate(config.getInterval(), TimeUtil.MINUTE, EnumOperationType.MULTIPLICATION.getId()));
        paramConfig.setRuntimeDifference(calculate(config.getDifference(), TimeUtil.HOUR, EnumOperationType.MULTIPLICATION.getId()));
        if (Objects.isNull(config.getId())) {
            List<ParameterConfig> parameterConfigs = modelServiceUtils.writeData(paramConfig, ParameterConfig.class);
            ParameterConfig parameterConfig = parameterConfigs.get(0);
            config.setId(parameterConfig.getId());
            return config;
        }
        ParameterConfig parameterConfig = parameterConfigDao.selectById(config.getId());
        parameterConfig.setColdSwitchPriority(config.getColdSwitchPriority());
        parameterConfig.setStartStopTimeInterval(calculate(config.getInterval(), TimeUtil.MINUTE, EnumOperationType.MULTIPLICATION.getId()));
        parameterConfig.setRuntimeDifference(calculate(config.getDifference(), TimeUtil.HOUR, EnumOperationType.MULTIPLICATION.getId()));
        parameterConfig.setStartStopTime(config.getStartStopTime());
        parameterConfig.setSummerEndDay(config.getSummerEndDay());
        parameterConfig.setSummerEndMonth(config.getSummerEndMonth());
        parameterConfig.setSummerStartDay(config.getSummerStartDay());
        parameterConfig.setSummerStartMonth(config.getSummerStartMonth());
        List<ParameterConfig> parameterConfigs = modelServiceUtils.writeData(parameterConfig, ParameterConfig.class);
        ParameterConfig config1 = parameterConfigs.get(0);
        config.setId(config1.getId());
        return config;
    }

    @Override
    public ParameterConfig writeSystemSupplyWaterConfig(ParameterConfig config) {
        config.setProjectId(GlobalInfoUtils.getProjectId());
        if (Objects.isNull(config.getId())) {
            List<ParameterConfig> parameterConfigs = modelServiceUtils.writeData(config, ParameterConfig.class);
            return parameterConfigs.get(0);
        }
        ParameterConfig parameterConfig = parameterConfigDao.selectById(config.getId());
        parameterConfig.setWorkPeriodTemp(config.getWorkPeriodTemp());
        parameterConfig.setRestPeriodTempMin(config.getRestPeriodTempMin());
        parameterConfig.setRestPeriodTempMax(config.getRestPeriodTempMax());
        parameterConfig.setChilledWaterSupplyPressureMax(config.getChilledWaterSupplyPressureMax());
        parameterConfig.setChilledWaterSupplyPressureMin(config.getChilledWaterSupplyPressureMin());
        parameterConfig.setChilledWaterReturnPressureDiffMin(config.getChilledWaterReturnPressureDiffMin());
        parameterConfig.setChilledWaterReturnPressureDiffMax(config.getChilledWaterReturnPressureDiffMax());
        parameterConfig.setCoolWaterSupplyTempMax(config.getCoolWaterSupplyTempMax());
        parameterConfig.setCoolWaterSupplyTempMin(config.getCoolWaterSupplyTempMin());
        parameterConfig.setSupplyReturnWaterTempDiff(config.getSupplyReturnWaterTempDiff());
        List<ParameterConfig> parameterConfigs = modelServiceUtils.writeData(parameterConfig, ParameterConfig.class);
        return parameterConfigs.get(0);
    }

    @Override
    public void writeAddAndSubConditionConfig(List<ConditionParam> params) {
        if (CollectionUtils.isEmpty(params)) {
            return;
        }
        Long systemId = params.get(0).getSystemId();
        RefrigeratingSystem refrigeratingSystem = refrigeratingSystemDao.selectById(systemId);
        List<BaseVo> mains = operationTrendService.queryColdWaterMainEngine(
                new QueryParam(refrigeratingSystem.getRoomId(), NodeLabelDef.ROOM), NodeLabelDef.COLD_WATER_MAINENGINE);
        List<PipeNetworkConnectionModel> connectionModels = connectionModelDao.getTopOrDownBatchWithNodes(mains, false, GlobalInfoUtils.getProjectId());
        List<BaseVo> pipelines = connectionModels.stream().filter(model -> Objects.equals(model.getOutflowLabel(), NodeLabelDef.PIPELINE))
                .map(model -> new BaseVo(model.getOutflowId(), model.getOutflowLabel()))
                .collect(Collectors.toList());
        mains.addAll(pipelines);
        handleControlSchemeData(systemId, mains, params);
    }

    @Override
    public ParamConditionConfigVo queryParamConditionConfig(Long systemId) {
        ParameterConfig parameterConfig = parameterConfigDao.queryConfig(systemId);
        ParameterConfigVo configVo = new ParameterConfigVo();
        BeanUtils.copyProperties(parameterConfig, configVo);
        Long interval = CommonUtils.calcLong(parameterConfig.getStartStopTimeInterval(), TimeUtil.MINUTE, EnumOperationType.DIVISION.getId());
        if (Objects.nonNull(interval)) {
            configVo.setInterval(interval.doubleValue());
        }
        if (Objects.nonNull(parameterConfig.getRuntimeDifference())) {
            Double value = CommonUtils.calcDouble(parameterConfig.getRuntimeDifference().doubleValue(), TimeUtil.HOUR.doubleValue(), EnumOperationType.DIVISION.getId());
            configVo.setDifference(value);
        }
        List<ControlScheme> controlSchemeList = controlSchemeDao.queryControlSchemeBySystemId(systemId);
        List<ConditionParam> conditionParams = assembleParamByType(ColdControlTypeDef.ADD, controlSchemeList);
        List<ConditionParam> params = assembleParamByType(ColdControlTypeDef.SUB, controlSchemeList);
        conditionParams.addAll(params);
        return new ParamConditionConfigVo(configVo, conditionParams);
    }

    @Override
    public List<MesShift> queryMesShift(BaseVo baseVo) {
        List<MesShift> mesShifts = mesShiftDao.queryMesShift(baseVo);
        handleViewData(mesShifts);
        return mesShifts;
    }

    @Override
    public List<MesShift> writeMesShift(List<MesShift> mesShifts, Long projectId) {
        if (CollectionUtils.isEmpty(mesShifts)) {
            return Collections.emptyList();
        }
        //编辑就是新增
        long l = System.currentTimeMillis();
        for (MesShift mesShift : mesShifts) {
            mesShift.setId(null);
            mesShift.setLogTime(l);
            mesShift.setProjectId(projectId);
            mesShift.setDuration(CommonUtils.calcDouble(mesShift.getHour(), TimeUtil.HOUR.doubleValue(), EnumOperationType.MULTIPLICATION.getId()).longValue());
        }
        return modelServiceUtils.writeData(mesShifts, MesShift.class);
    }


    @Override
    public ControlMode queryControlMode(Long systemId, Long projectId) {
        ControlMode controlMode = controlModeDao.queryControlModeBySystemId(systemId);
        RefrigeratingSystem system = refrigeratingSystemDao.selectById(systemId);
        //还要查询plc的模式
        List<BaseVo> plcNodes = aiPlcControlService.queryNodesByMonitor(system.getRoomId());
        Integer plcMode = getPlcMode(plcNodes);
        if (Objects.nonNull(controlMode)) {
            controlMode.setPlcMode(plcMode);
            return controlMode;
        }
        ControlMode mode = new ControlMode();
        mode.setMode(plcMode);
        mode.setProjectId(projectId);
        mode.setUpdateTime(System.currentTimeMillis());
        mode.setSystemId(systemId);
        List<ControlMode> controlModes = modelServiceUtils.writeData(Collections.singletonList(mode), ControlMode.class);
        ControlMode controlMode1 = controlModes.get(0);
        controlMode1.setPlcMode(plcMode);
        return controlMode1;
    }

    private Integer getPlcMode(List<BaseVo> plcNodes) {

        Map<Integer, List<RealTimeValue>> integerListMap = quantityManageService.queryRealTimeBath(
                aiPlcControlService.createQuantityDataBatchSearchVo(plcNodes, TimeUtil.getFirstTimeOfHour(LocalDateTime.now()), LocalDateTime.now(), AggregationCycle.FIVE_MINUTES,
                        Arrays.asList(QuantityDef.getPlcAi(), QuantityDef.getPlcAuto(),QuantityDef.getPlcManual())));
        List<RealTimeValue> realTimeValues = integerListMap.get(QuantityDef.getPlcAi().getId());
        if (CollectionUtils.isNotEmpty(realTimeValues) && Objects.equals(realTimeValues.get(0).getValue(), TRUE_DATA)) {
            return ModelDef.AI;
        }

        List<RealTimeValue> realTimeValue2 = integerListMap.get(QuantityDef.getPlcAuto().getId());
        if (CollectionUtils.isNotEmpty(realTimeValue2) && Objects.equals(realTimeValue2.get(0).getValue(), TRUE_DATA)) {
            return (ModelDef.PLC_AUTO);
        }
        List<RealTimeValue> realTimeValue3 = integerListMap.get(QuantityDef.getPlcManual().getId());
        if (CollectionUtils.isNotEmpty(realTimeValue3) && Objects.equals(realTimeValue3.get(0).getValue(), TRUE_DATA)) {
            return (ModelDef.PLC_MANUAL);
        }
        return null;
    }

    @Override
    public ControlMode writeControlMode(ControlMode controlModes) {
        controlModes.setUpdateTime(System.currentTimeMillis());
        List<ControlMode> controlMode = modelServiceUtils.writeData(Collections.singletonList(controlModes), ControlMode.class);
        ControlMode controlMode1 = controlMode.get(0);
        RefrigeratingSystem system = refrigeratingSystemDao.selectById(controlModes.getSystemId());
        //还要查询plc的模式
        List<BaseVo> plcNodes = aiPlcControlService.queryNodesByMonitor(system.getRoomId());

        //遥控
        if (Objects.equals(controlModes.getMode(), ModelDef.AI)) {
            aiPlcControlService.sendRemoteControlByDataId(PLC_AI_DATA_ID, plcNodes, START);
        } else if (Objects.equals(controlModes.getMode(), ModelDef.PLC_AUTO)) {
            aiPlcControlService.sendRemoteControlByDataId(PLC_AUTO_DATA_ID, plcNodes, START);
        }
        try {
            Thread.sleep(WAIT * TimeUtil.SECOND);
        } catch (Exception e) {
            log.error(CATCH_ERROR);
        }
        //这里由于plc的逻辑涉及到校验心跳包发送是否正确，所以不能通过接口返回值来确定切换是否执行成功
        Integer plcMode = getPlcMode(plcNodes);
        controlMode1.setPlcMode(plcMode);
        //再查询
        return controlMode1;
    }


    private void handleViewData(List<MesShift> mesShifts) {
        for (MesShift mesShift : mesShifts) {
            mesShift.setHour(CommonUtils.calcDouble(mesShift.getDuration().doubleValue(), TimeUtil.HOUR.doubleValue(), EnumOperationType.DIVISION.getId()));

        }
    }

    /**
     * 库里的配置转成界面的数据结构
     *
     * @param type
     * @param controlSchemeList
     * @return
     */
    private List<ConditionParam> assembleParamByType(Integer type, List<ControlScheme> controlSchemeList) {
        ControlScheme scheme = controlSchemeList.stream().filter(controlScheme -> Objects.equals(controlScheme.getControlType(), type))
                .findAny().orElse(null);
        if (Objects.isNull(scheme)) {
            return Collections.emptyList();
        }
        List<ConditionParam> conditionParams = new ArrayList<>();
        LogicJson expression = scheme.getExpression();
        if (Objects.isNull(expression)) {
            return Collections.emptyList();
        }
        List<Long> ids = expression.getExpressions().stream().map(SingleCondition::getObjectId)
                .collect(Collectors.toList());
        //查询
        List<NumericalCondition> conditions = numericalConditionDao.selectBatchIds(ids);
        for (NumericalCondition condition : conditions) {
            ConditionParam param = new ConditionParam();
            BeanUtils.copyProperties(condition, param);
            //ms转分钟
            param.setTimeValue(CommonUtils.calcLong(param.getTimeValue(), TimeUtil.MINUTE, EnumOperationType.DIVISION.getId()));
            param.setControlType(type);
            param.setSystemId(scheme.getRefrigeratingSystemId());
            conditionParams.add(param);
        }
        //这一步是因为写入数据是明确到了哪个设备，但是界面展示只需要对应设备类型的数据
        List<ConditionParam> result = new ArrayList<>();
        Map<String, Map<Integer, List<ConditionParam>>> map = conditionParams.stream().collect(Collectors.groupingBy(ConditionParam::getObjectLabel, Collectors.groupingBy(ConditionParam::getDataType)));
        for (Map.Entry<String, Map<Integer, List<ConditionParam>>> entry : map.entrySet()) {
            Map<Integer, List<ConditionParam>> value = entry.getValue();
            for (Map.Entry<Integer, List<ConditionParam>> listEntry : value.entrySet()) {
                List<ConditionParam> entryValue = listEntry.getValue();
                if (CollectionUtils.isNotEmpty(entryValue)) {
                    result.add(entryValue.get(0));
                }
            }
        }
        return result;
    }

    /**
     * @param systemId
     * @param mains
     * @param params
     */
    private void handleControlSchemeData(Long systemId, List<BaseVo> mains, List<ConditionParam> params) {
        List<ControlScheme> controlSchemes = controlSchemeDao.queryControlSchemeBySystemId(systemId);
        if (CollectionUtils.isEmpty(controlSchemes)) {
            //新增  需要先写方案，获得方案id，再写入条件内容，获得条件id再更新方案中expression。
            ControlScheme add = createSchemeByType(ColdControlTypeDef.ADD, systemId);
            ControlScheme sub = createSchemeByType(ColdControlTypeDef.SUB, systemId);
            controlSchemes = modelServiceUtils.writeData(Arrays.asList(add, sub), ControlScheme.class);
        }
        List<Long> schemeIds = controlSchemes.stream().map(ControlScheme::getId).collect(Collectors.toList());
        //拼接条件内容
        List<NumericalCondition> addCondition = createConditionsByType(ColdControlTypeDef.ADD, controlSchemes, mains, params);
        List<NumericalCondition> subCondition = createConditionsByType(ColdControlTypeDef.SUB, controlSchemes, mains, params);
        addCondition.addAll(subCondition);
        List<NumericalCondition> numericalConditions1 = numericalConditionDao.queryCondition(schemeIds);

        if (CollectionUtils.isNotEmpty(numericalConditions1)) {
            for (NumericalCondition condition : numericalConditions1) {
                NumericalCondition condition1 = addCondition.stream().filter(numericalCondition -> Objects.equals(numericalCondition.getControlSchemeId(), condition.getControlSchemeId())
                        && Objects.equals(numericalCondition.getObjectId(), condition.getObjectId())
                        && Objects.equals(numericalCondition.getObjectLabel(), condition.getObjectLabel())
                        && Objects.equals(numericalCondition.getDataType(), condition.getDataType()))
                        .findAny().orElse(new NumericalCondition());
                condition1.setId(condition.getId());

            }
        }
        List<NumericalCondition> numericalConditions = modelServiceUtils.writeData(addCondition, NumericalCondition.class);
        addSchemeExpression(numericalConditions, controlSchemes);
        //写入逻辑
        modelServiceUtils.writeData(controlSchemes);
    }

    private ControlScheme createSchemeByType(Integer type, Long systemId) {
        ControlScheme add = new ControlScheme();
        add.setRefrigeratingSystemId(systemId);
        long l = System.currentTimeMillis();
        add.setCreateTime(l);
        add.setControlType(type);
        add.setProjectId(GlobalInfoUtils.getProjectId());
        return add;
    }

    private void addSchemeExpression
            (List<NumericalCondition> numericalConditions, List<ControlScheme> controlSchemeList) {
        Map<Long, List<NumericalCondition>> map = numericalConditions.stream().collect(Collectors.groupingBy(NumericalCondition::getControlSchemeId));
        for (ControlScheme scheme : controlSchemeList) {
            List<NumericalCondition> conditions = map.get(scheme.getId());
            if (CollectionUtils.isEmpty(conditions)) {
                continue;
            }
            scheme.setExpression(assembleLogic(conditions));
        }
    }

    /**
     * @param conditions
     * @return
     */
    private LogicJson assembleLogic(List<NumericalCondition> conditions) {
        LogicJson logicJson = new LogicJson();
        logicJson.setComposeMethod(true);
        List<SingleCondition> expressions = new ArrayList<>();
        for (NumericalCondition condition : conditions) {
            SingleCondition singleCondition = new SingleCondition();
            singleCondition.setObjectId(condition.getId());
            singleCondition.setObjectLabel(condition.getModelLabel());
            singleCondition.setTagId(1);
            expressions.add(singleCondition);
        }
        logicJson.setExpressions(expressions);
        return logicJson;
    }

    private List<NumericalCondition> createConditionsByType(Integer type, List<ControlScheme> controlSchemeList,
                                                            List<BaseVo> mains, List<ConditionParam> params) {
        ControlScheme scheme = controlSchemeList.stream().filter(controlScheme -> Objects.equals(controlScheme.getControlType(), type))
                .findAny().orElse(null);
        if (Objects.isNull(scheme)) {
            return Collections.emptyList();
        }
        List<NumericalCondition> result = new ArrayList<>();
        //拼接主机的条件
        for (BaseVo baseVo : mains) {
            List<ConditionParam> paramList = params.stream().filter(conditionParam -> Objects.equals(conditionParam.getControlType(), type)
                    && Objects.equals(conditionParam.getObjectLabel(), baseVo.getModelLabel())).collect(Collectors.toList());
            for (ConditionParam param : paramList) {
                NumericalCondition condition = new NumericalCondition();
                condition.setDataType(param.getDataType());
                condition.setControlSchemeId(scheme.getId());
                condition.setObjectId(baseVo.getId());
                condition.setObjectLabel(baseVo.getModelLabel());
                condition.setOperationSymbol(param.getOperationSymbol());
                condition.setTimeValue(CommonUtils.calcLong(param.getTimeValue(), TimeUtil.MINUTE, EnumOperationType.MULTIPLICATION.getId()));
                condition.setValue(param.getValue());
                condition.setOperationSymbolOfTime(param.getOperationSymbolOfTime());
                condition.setSelfComparison(true);
                result.add(condition);
            }

        }
        return result;
    }
}
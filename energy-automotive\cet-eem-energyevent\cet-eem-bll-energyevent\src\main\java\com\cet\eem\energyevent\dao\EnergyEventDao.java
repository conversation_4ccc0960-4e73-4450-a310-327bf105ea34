package com.cet.eem.energyevent.dao;

import com.cet.eem.dao.BaseModelDao;
import com.cet.eem.energyevent.model.pojo.EnergyEvent;
import com.cet.eem.energyevent.model.vo.EnergyEventParam;
import com.cet.eem.energyevent.model.vo.EnergyEventStatus;
import com.cet.eem.energyevent.model.vo.ReferDataParam;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/24 9:07
 */
public interface EnergyEventDao extends BaseModelDao<EnergyEvent> {

    List<EnergyEvent> query(EnergyEventParam param);

    List<EnergyEvent> query(ReferDataParam param);


    void edit(List<EnergyEvent> energyEvents);

    void editStatus(List<EnergyEventStatus> energyEvents);

}

package com.cet.eem.depletion.service.impl;

import com.cet.eem.bll.common.dao.node.EnergySupplyDao;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.DataLogData;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.model.event.EventCondition;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.depletion.config.DepletionCfg;
import com.cet.eem.depletion.dao.DepletionDataDao;
import com.cet.eem.depletion.model.AveragePassTimeDto;
import com.cet.eem.depletion.model.CataphoresisDto;
import com.cet.eem.depletion.model.Constant.Constant;
import com.cet.eem.depletion.model.Constant.QuantityDef;
import com.cet.eem.depletion.model.DepletionPartitionDto;
import com.cet.eem.depletion.model.MesPassPointDto;
import com.cet.eem.depletion.model.vo.DepletionCfgVo;
import com.cet.eem.depletion.service.DepletionDivideForLineService;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.quantity.service.QuantityManageService;
import com.cet.electric.modelservice.plus.toolkit.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.sql.Time;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class DepletionDivideForLineServiceImpl implements DepletionDivideForLineService {

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    QuantityManageService quantityManageService;

    @Autowired
    DepletionDataDao depletionDataDao;

    @Autowired
    EnergySupplyDao energySupplyDao;

    @Autowired
    PartittionCommonService commonService;
    protected static final Logger logger = LoggerFactory.getLogger(DepletionDivideForLineServiceImpl.class);

    private static final String LOG_KEY = "[空耗划分计算-线体+空调]";

    public static final Long ONE_DAY = 86400000L;

    //默认倍率
    @Value("${cet.eem.depletion.multiplyingpower:1.5}")
    public Double MULTIPLYINGPOWER;
    //默认节拍
    @Value("${cet.eem.depletion.takttime:77.4}")
    public Double TAKTTIME;
    //默认允许时间，单位：s
    @Value("${cet.eem.depletion.allowtime:1200}")
    public Long ALLOWTIME;

    @Value("${cet.eem.depletion.depletiondivide-cron:-}")
    public String cron;
    @Value("${cet.eem.depletion.averagetime:3600}")
    public Long averagetime;

    @Autowired
    DepletionCfg depletionCfg;

    List<CataphoresisDto> cataphoresisList = new ArrayList<>();
    List<MesPassPointDto> mesPassPointList = new ArrayList<>();
    List<MesPassPointDto> mesPassPointListForNextStation = new ArrayList<>();
    List<Long> startTimeList = new ArrayList<>();
    List<Long> endTimeList = new ArrayList<>();
    List<DataLogData> tempDataLog = new ArrayList<>();
    List<Long> timeListForPass = new ArrayList<>();
    List<Long> timeListForNextStationPass = new ArrayList<>();
    List<DepletionPartitionDto> depletionPartitionList = new ArrayList<>();
    List<AveragePassTimeDto> averagePassTimeList = new ArrayList<>();

    @Override
    public void depletionDivide(Long startTime, Long endTime) throws IOException {
        logger.info("{}：开始执行空耗时段的处理和转存，本次计算的起止时间为 {}~{}", LOG_KEY, TimeUtil.timestamp2LocalDateTime(startTime), TimeUtil.timestamp2LocalDateTime(endTime));
        //只处理线体和线体空调类型的电泳池
        cataphoresisList = depletionDataDao.queryCataphoresis(Arrays.asList(Constant.LINEBODYTYPE_LINE, Constant.LINEBODYTYPE_AIRCONDITION));
        averagePassTimeList = depletionDataDao.queryAveragePassTime();
        List<DepletionCfgVo> depletionCfgs = depletionCfg.getDepletionCfg();
        if (cataphoresisList.isEmpty()) {
            logger.info("{}：查询不到电泳池模型信息，退出转存任务", LOG_KEY);
            return;
        }
        //检查电泳池属性是否完全，否则写入默认
        for (CataphoresisDto item : cataphoresisList) {
            logger.info("{}：当前处理电泳池id：{}的空耗数据", LOG_KEY, item.getId());
            item.setAllowtime(Objects.nonNull(item.getAllowtime()) ? item.getAllowtime() : ALLOWTIME);
            item.setMultiplyingpower(Objects.nonNull(item.getMultiplyingpower()) ? item.getMultiplyingpower() : MULTIPLYINGPOWER);
            item.setTakttime(Objects.nonNull(item.getTakttime()) ? item.getTakttime() : TAKTTIME);
            Optional<DepletionCfgVo> first = depletionCfgs.stream().filter(x -> Objects.equals(Constant.CATAPHORESIS, x.getObjectLabel())
                    && Objects.equals(item.getId(), x.getObjectID())).findFirst();
            if (!first.isPresent()) {
                logger.info("{}：未找到id：{}的事件配置记录", LOG_KEY, item.getId());
                continue;
            }
            DepletionCfgVo depletionCfgVo = first.get();

            dataPrepareForDepletionDivide(startTime, endTime, item, depletionCfgVo);

            dealTimeList();
            calcDepletionPartition(startTime, item, depletionCfgVo);
            //朱峰：持续时间不超过5min的，过滤
            depletionPartitionList = depletionPartitionList.stream().filter(x -> x.getDuration() > 300).collect(Collectors.toList());
            modelServiceUtils.writeData(depletionPartitionList, DepletionPartitionDto.class);
            logger.info("{}：电泳池：{}空耗划分信息入库，数量：{}", LOG_KEY, item.getId(), depletionPartitionList.size());

            clearDataList();
        }
        logger.info("{}：空耗时段的处理和转存完成", LOG_KEY);
    }

    /*
     * 查询空耗划分的各项数据
     * */
    private void dataPrepareForDepletionDivide(Long startTime, Long endTime, CataphoresisDto item, DepletionCfgVo depletionCfgVo) throws IOException {
        mesPassPointList = depletionDataDao.queryMesPassPointData(startTime, endTime, Collections.singletonList(depletionCfgVo.getStationCode())).stream()
                .sorted(Comparator.comparing(MesPassPointDto::getPasstime)).collect(Collectors.toList());
        mesPassPointListForNextStation = depletionDataDao.queryMesPassPointData(startTime, endTime, Collections.singletonList(depletionCfgVo.getNextStationCode())).stream()
                .sorted(Comparator.comparing(MesPassPointDto::getPasstime)).collect(Collectors.toList());
//        Map<Integer, List<TrendDataVo>> dataLogMap = depletionDataDao.queryQuantityData(Collections.singletonList(new BaseVo(item.getId(), item.getModelLabel())), startTime, endTime,
//                ImmutableList.of(QuantityDef.getTempOfWaterTank(), QuantityDef.getStandardTempOfWaterTank()));

        startTimeList = depletionDataDao.queryProductionLineEventTime(startTime, endTime, createEventCondition(depletionCfgVo, Constant.EVENTCLASSES, Constant.EVENTTYPE_OPEN));
        endTimeList = depletionDataDao.queryProductionLineEventTime(startTime, endTime, createEventCondition(depletionCfgVo, Constant.EVENTCLASSES, Constant.EVENTTYPE_CLOSE));
    }

    /**
     * 准备升温信息、空耗划分所需模型和定时记录数据
     */


    private List<DataLogData> dealDataLog(Map<Integer, List<TrendDataVo>> dataLogMap, Integer quantityID) {
        List<TrendDataVo> trendDataVos = dataLogMap.get(quantityID);
        if (trendDataVos.isEmpty()) {
            logger.info("{}：物理量-{}的数据为空", LOG_KEY, quantityID);
            return new ArrayList<>();
        }
        return trendDataVos.get(0).getDataList();
    }

    private EventCondition createEventCondition(DepletionCfgVo item, Integer eventClasses, Integer eventType) {
        EventCondition eventCondition = new EventCondition();
        eventCondition.setChannelId(item.getChannelID());
        eventCondition.setStationId(item.getStationID());
        eventCondition.setEventClasses(Collections.singletonList(eventClasses));
        eventCondition.setEventTypes(Collections.singletonList(eventType));
        eventCondition.setKeyWord(item.getKeyWord());
        return eventCondition;
    }

    /**
     * 从目标时间list中找到与指定time相邻的时间戳
     */
    private Long getNextTime(List<Long> timeList, Long time, int offset) {
        List<Long> collect = new ArrayList<>(timeList);
        collect.add(time);
        collect = collect.stream().sorted().collect(Collectors.toList());
        for (int i = 0; i < collect.size(); i++) {
            if (Objects.equals(time, collect.get(i))) {
                if (i + offset <= 0) {
                    return collect.get(0);
                } else if (i + offset >= collect.size()) {
                    return collect.get(collect.size() - 1);
                } else {
                    return collect.get(i + offset);
                }
            }
        }
        return time;
    }

    /**
     * 取出过车时间进行排序
     */
    private void dealTimeList() {
        mesPassPointList = mesPassPointList.stream().sorted(Comparator.comparing(MesPassPointDto::getPasstime)).collect(Collectors.toList());

        timeListForPass.addAll(mesPassPointList.stream().map(MesPassPointDto::getPasstime).collect(Collectors.toList()));
        timeListForPass = timeListForPass.stream().sorted().collect(Collectors.toList());

        timeListForNextStationPass.addAll(mesPassPointListForNextStation.stream().map(MesPassPointDto::getPasstime).collect(Collectors.toList()));
        timeListForNextStationPass = timeListForNextStationPass.stream().sorted().collect(Collectors.toList());

        startTimeList = startTimeList.stream().sorted().collect(Collectors.toList());
        endTimeList = endTimeList.stream().sorted().collect(Collectors.toList());
    }

    private void clearDataList() {
        mesPassPointList.clear();
        startTimeList.clear();
        endTimeList.clear();
        tempDataLog.clear();
        timeListForPass.clear();
        depletionPartitionList.clear();
    }

    /**
     * 计算空耗划分
     */
    private void calcDepletionPartition(Long logTime, CataphoresisDto item, DepletionCfgVo depletionCfgVo) {
        //过车空耗划分
        passingDepletion(item, logTime);
        //进车空耗划分
        enterDepletion(item, logTime);
        //过线空耗划分
        overDepletion(item, logTime, depletionCfgVo);
        //结束空耗划分
        stoppingDepletion(item, logTime);
    }

    /**
     * 过车空耗
     */
    private void passingDepletion(CataphoresisDto item, Long logTime) {
        List<MesPassPointDto> passPointDtos;
        //有开机 ,关机 不保证有
        if (CollectionUtils.isNotEmpty(startTimeList)) {
            for (Long st : startTimeList) {
                Long end = getNextTime(endTimeList, st, 1);
                //只有开机记录
                if (Objects.equals(st, end)) {
                    passPointDtos = mesPassPointList.stream().filter(pass -> st <= pass.getPasstime()).collect(Collectors.toList());
                } else {
                    //有关机
                    passPointDtos = mesPassPointList.stream().filter(pass -> st <= pass.getPasstime() && pass.getPasstime() <= end).collect(Collectors.toList());
                }
                passingDepletionExcuet(passPointDtos, item, logTime);
            }
            //有关机 无开机
        } else if (CollectionUtils.isNotEmpty(endTimeList) && CollectionUtils.isEmpty(startTimeList)) {
            for (Long end : endTimeList) {
                passPointDtos = mesPassPointList.stream().filter(pass -> pass.getPasstime() <= end).collect(Collectors.toList());
                passingDepletionExcuet(passPointDtos, item, logTime);
            }
            //无开机 无关机 有过车
        } else if (CollectionUtils.isEmpty(startTimeList) && CollectionUtils.isEmpty(endTimeList)) {
            //上次是否为开机
            Boolean lastDayIsOpen = commonService.getLastDayIsOpen(item, logTime);
            if (!lastDayIsOpen) {
                return;
            }
            passPointDtos = mesPassPointList;
            passingDepletionExcuet(passPointDtos, item, logTime);
        }
    }

    private void passingDepletionExcuet(List<MesPassPointDto> passPointDtos, CataphoresisDto item, Long logTime) {

        for (int i = 0; i < passPointDtos.size() - 1; i++) {
            MesPassPointDto first = passPointDtos.get(i);
            MesPassPointDto next = passPointDtos.get(i + 1);
            //理论下一个进车时间 = 上一台进车时刻+计划节拍*倍率
            Long theoretical = first.getPasstime() + Math.round(item.getTakttime() * 1000 * item.getMultiplyingpower());
            //当时实际下一个进车时间 <= 理论时间时，不存在空耗，跳过
            if (next.getPasstime() <= theoretical) {
                continue;
            }
            DepletionPartitionDto res = new DepletionPartitionDto();
            res.setStarttime(theoretical);
            res.setEndtime(next.getPasstime());
            res.setDuration((next.getPasstime() - theoretical) / 1000);
            res.setDepletiontype(Constant.DEPLTIONETYPE_PASSING);
            res.setLogtime(TimeUtil.getFirstTimeOfDay(logTime));
            res.setProjectid(1L);
            res.setObjectid(item.getId());
            res.setObjectlabel(item.getModelLabel());
            depletionPartitionList.add(res);
        }
    }

    /**
     * 结束空耗
     */
    private void stoppingDepletion(CataphoresisDto item, Long logTime) {
        if (CollectionUtils.isNotEmpty(startTimeList)) {
            if (CollectionUtils.isNotEmpty(endTimeList)){
                if (endTimeList.get(0) < startTimeList.get(0)){
                    DepletionPartitionDto res = getDepletionPartitionDto(logTime, endTimeList.get(0), Constant.DEPLTIONETYPE_STOPPING, item);
                    depletionPartitionList.add(res);
                }
            }
            String fuck = "马德烦死了";
            logger.info("{}: 时间：{}，进入结束空耗判断", fuck, TimeUtil.timestamp2LocalDateTime(logTime));
            logger.info("{}：开机时间：{}", fuck, startTimeList.toString());
            logger.info("{}：关机时间：{}", fuck, endTimeList.toString());
            for (Long startTime : startTimeList) {
                Long endTime = getNextTime(endTimeList, startTime, 1);
                logger.info("{}: 进来了，待判断开始时间：{}，待判断关机时间：{}", fuck,
                        TimeUtil.timestamp2LocalDateTime(startTime), TimeUtil.timestamp2LocalDateTime(endTime));
                //有开关机
                if (!Objects.equals(endTime, startTime)) {
                    logger.info("{}: 开机时间不等于关机时间", fuck);
                    List<MesPassPointDto> passInfos = mesPassPointList.stream().filter(pass -> startTime <= pass.getPasstime() && pass.getPasstime() <= endTime)
                            .sorted(Comparator.comparing(MesPassPointDto::getPasstime)).collect(Collectors.toList());
                    //无过车 不考虑结束空耗
                    if (CollectionUtils.isEmpty(passInfos)) {
                        logger.info("{}: 过车为空，跳过", fuck);
                        continue;
                    }
                    MesPassPointDto endCar = passInfos.get(passInfos.size() - 1);
                    logger.info("{}: 最后一个过车信息如下：{}", fuck, endCar.getPasstime());
                    //理论关机时间 = 最后一个过车时间+允许时间+水平过车时间
                    Long theoretical = endCar.getPasstime() + Math.round(item.getAllowtime() * 1000 + Math.round(averagetime * 1000));
                    logger.info("{}: 理论关机时间是：{}", fuck, TimeUtil.timestamp2LocalDateTime(theoretical));
                    //当时理论关机时间 >= 实际关机时间，不存在空耗，跳过
                    if (theoretical >= endTime) {
                        logger.info("{}: 关机时间是：{}，理论关机时间是：{}，允许时间是：{}，跳过结束空耗", fuck, endTime, theoretical, item.getAllowtime());
                        continue;
                    }
                    DepletionPartitionDto res = getDepletionPartitionDto(theoretical, endTime, Constant.DEPLTIONETYPE_STOPPING, item);
                    logger.info("{}: 空耗划分结果是：{}", fuck, res.toString());
                    depletionPartitionList.add(res);
                } else {//没有关机信号
                    logger.info("{}: 难道进入这里了吗", fuck);
                    List<MesPassPointDto> passInfos = mesPassPointList.stream().filter(pass -> startTime <= pass.getPasstime()).collect(Collectors.toList());
                    logger.info("{}: 过车信息如下：{}", fuck, mesPassPointList.toString());
                    if (CollectionUtils.isEmpty(passInfos)) {
                        continue;
                    }
                    MesPassPointDto endCar = passInfos.get(passInfos.size() - 1);
                    logger.info("{}: 最后一个过车信息如下：{}", fuck, endCar.toString());
                    DepletionPartitionDto res = getDepletionPartitionDto(endCar.getPasstime() + Math.round(item.getAllowtime() * 1000) + Math.round(averagetime * 1000),
                            logTime + ONE_DAY, Constant.DEPLTIONETYPE_STOPPING, item);
                    logger.info("{}: 空耗划分结果是：{}", fuck, res.toString());
                    depletionPartitionList.add(res);
                }
            }
        } else if (CollectionUtils.isNotEmpty(endTimeList) && CollectionUtils.isEmpty(startTimeList)) {
            for (Long endTime : endTimeList) {
                List<MesPassPointDto> passInfos = mesPassPointList.stream().filter(pass -> pass.getPasstime() <= endTime).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(passInfos)) {
                    DepletionPartitionDto res = getDepletionPartitionDto(logTime, endTime, Constant.DEPLTIONETYPE_STOPPING, item);
                    depletionPartitionList.add(res);
                } else {
                    MesPassPointDto endCar = passInfos.get(passInfos.size() - 1);
                    DepletionPartitionDto res = getDepletionPartitionDto(endCar.getPasstime() + Math.round(item.getAllowtime() * 1000) + Math.round(averagetime * 1000),
                            endTime, Constant.DEPLTIONETYPE_STOPPING, item);
                    depletionPartitionList.add(res);
                }
            }
        } else if (CollectionUtils.isEmpty(startTimeList) && CollectionUtils.isEmpty(endTimeList)) {
            Boolean lastDayIsOpen = commonService.getLastDayIsOpen(item, logTime);
            if (!lastDayIsOpen) {
                return;
            }
            if (CollectionUtils.isNotEmpty(mesPassPointList)) {
                MesPassPointDto endCar = mesPassPointList.get(mesPassPointList.size() - 1);
                DepletionPartitionDto res = getDepletionPartitionDto(endCar.getPasstime() + Math.round(item.getAllowtime() * 1000) + Math.round(averagetime * 1000),
                        logTime + ONE_DAY, Constant.DEPLTIONETYPE_STOPPING, item);
                depletionPartitionList.add(res);
            }
        }
    }


    /*
     * 进车空耗
     */
    private void enterDepletion(CataphoresisDto item, Long logTime) {
        List<Long> reachedTimes = null;
        if (Objects.equals(item.getLinebodytype(), Constant.LINEBODYTYPE_AIRCONDITION) && CollectionUtils.isNotEmpty(timeListForPass)) {
            //空调达标时刻
            reachedTimes = commonService.queryAirReachedTimes(item, logTime, logTime + Constant.ONE_DAY);
        }
        if (CollectionUtils.isNotEmpty(startTimeList)) {
            for (Long startTime : startTimeList) {
                //找过本次开机关机过程中的第一个过车时间
                Long endTime = getNextTime(endTimeList, startTime, 1);
                //没有关机 记录
                if (Objects.equals(startTime, endTime)) {
                    List<Long> passInfos = timeListForPass.stream().filter(x -> x >= startTime).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(passInfos)) {
                        DepletionPartitionDto res = getDepletionPartitionDto(startTime, logTime + ONE_DAY, Constant.DEPLTIONETYPE_ENTER, item);
                        depletionPartitionList.add(res);
                    } else {
                        Long firstCar = passInfos.get(0);
                        //空调的达标时间 或者线体的开机时间
                        Long reachedTime = commonService.getAirReachedTime(reachedTimes, startTime, firstCar);
                        //理论进车时间 = 开机时间+允许时间，视为进车空耗开始时间
                        Long theoretical = reachedTime + item.getAllowtime() * 1000;
                        if (theoretical >= firstCar) {
                            continue;
                        }
                        DepletionPartitionDto res = getDepletionPartitionDto(theoretical, firstCar, Constant.DEPLTIONETYPE_ENTER, item);
                        depletionPartitionList.add(res);
                    }
                } else {//有开机 有关机
                    List<Long> passInfos = timeListForPass.stream().filter(x -> x >= startTime && x <= endTime).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(passInfos)) {
                        DepletionPartitionDto res = getDepletionPartitionDto(startTime, endTime, Constant.DEPLTIONETYPE_ENTER, item);
                        depletionPartitionList.add(res);
                    } else {
                        Long firstCar = passInfos.get(0);
                        //空调的达标时间 或者线体的开机时间
                        Long reachedTime = commonService.getAirReachedTime(reachedTimes, startTime, firstCar);
                        //理论进车时间 = 开机时间+允许时间，视为进车空耗开始时间
                        Long theoretical = reachedTime + item.getAllowtime() * 1000;
                        if (theoretical >= firstCar) {
                            continue;
                        }
                        DepletionPartitionDto res = getDepletionPartitionDto(theoretical, firstCar, Constant.DEPLTIONETYPE_ENTER, item);
                        depletionPartitionList.add(res);
                    }
                }
            }
        } else if (CollectionUtils.isNotEmpty(endTimeList) && CollectionUtils.isEmpty(startTimeList)) {
            for (Long endTime : endTimeList) {
                List<MesPassPointDto> passInfos = mesPassPointList.stream().filter(pass -> pass.getPasstime() <= endTime).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(passInfos)) {
                    MesPassPointDto first = passInfos.get(0);
                    DepletionPartitionDto res = getDepletionPartitionDto(logTime, first.getPasstime(), Constant.DEPLTIONETYPE_ENTER, item);
                    depletionPartitionList.add(res);
                }
                //无过车  算结束空耗
            }
        } else if (CollectionUtils.isEmpty(startTimeList) && CollectionUtils.isEmpty(endTimeList)) {
            Boolean lastDayIsOpen = commonService.getLastDayIsOpen(item, logTime);
            if (!lastDayIsOpen) {
                return;
            }
            //无过车
            if (CollectionUtils.isEmpty(mesPassPointList)) {
                DepletionPartitionDto res = getDepletionPartitionDto(logTime, logTime + ONE_DAY, Constant.DEPLTIONETYPE_ENTER, item);
                depletionPartitionList.add(res);
            } else {
                MesPassPointDto first = mesPassPointList.get(0);
                DepletionPartitionDto res = getDepletionPartitionDto(logTime, first.getPasstime(), Constant.DEPLTIONETYPE_ENTER, item);
                depletionPartitionList.add(res);
            }
        }
    }


    /**
     * 获取最近的空调达标时刻
     *
     * @return
     */
    private Long getReachedTime(List<Long> reachedTimes, Long logTime) {
        if (CollectionUtils.isEmpty(reachedTimes)) {
            return logTime;
        }
        //空调达标时刻
        Long nextTime = getNextTime(reachedTimes, logTime, 1);
        if (!Objects.equals(nextTime, logTime)) {
            reachedTimes.remove(nextTime);
        }
        return nextTime;
    }

    /**
     * 获取空调温湿度达标的时间
     *
     * @param item
     * @param logTime
     * @return
     */
    private List<Long> getReachedTimes(CataphoresisDto item, Long logTime) {
        //获取温度的定时记录,湿度 等实时记录
        Map<Integer, List<TrendDataVo>> quantityData = depletionDataDao.queryQuantityData(Collections.singletonList(new BaseVo(item.getId(), item.getModelLabel())), logTime,
                logTime + Constant.ONE_DAY, Arrays.asList(QuantityDef.getTempOfAirCondition(), QuantityDef.getHumidityOfAirCondition(),
                        QuantityDef.getAirConditionerTemperatureSetting(), QuantityDef.getAirConditioningHumiditySetting()));
        //空调温度
        List<DataLogData> tempOfAirCondition = getDataLogDatas(quantityData, QuantityDef.getTempOfAirCondition().getId());
        //空调湿度
        List<DataLogData> humidityOfAirCondition = getDataLogDatas(quantityData, QuantityDef.getHumidityOfAirCondition().getId());
        //空调达标温度设定
        List<DataLogData> temperatureSetting = getDataLogDatas(quantityData, QuantityDef.getAirConditionerTemperatureSetting().getId());
        //空调达标湿度设定
        List<DataLogData> humiditySetting = getDataLogDatas(quantityData, QuantityDef.getAirConditioningHumiditySetting().getId());
        logger.info("空调id:{},当日温度值:{}", item.getId(), JsonTransferUtils.toJSONString(tempOfAirCondition));
        logger.info("空调id:{},当日湿度值:{}", item.getId(), JsonTransferUtils.toJSONString(humidityOfAirCondition));
        logger.info("空调id:{},当日达标温度设定值:{}", item.getId(), JsonTransferUtils.toJSONString(temperatureSetting));
        logger.info("空调id:{},当日空调达标湿度设定值:{}", item.getId(), JsonTransferUtils.toJSONString(humiditySetting));
        //符合温度的时间
        return conditionReached(tempOfAirCondition, humidityOfAirCondition, temperatureSetting, humiditySetting);
    }


    private DepletionPartitionDto getDepletionPartitionDto(Long st, Long end, Integer type, CataphoresisDto item) {
        DepletionPartitionDto res = new DepletionPartitionDto();
        res.setStarttime(st);
        res.setEndtime(end);
        res.setDuration((end - st) / Constant.ONE_S);
        res.setDepletiontype(type);
        res.setLogtime(TimeUtil.getFirstTimeOfDay(st));
        res.setProjectid(1L);
        res.setObjectid(item.getId());
        res.setObjectlabel(item.getModelLabel());
        return res;
    }

    /**
     * 温度是否打成
     *
     * @return
     */
    private List<Long> conditionReached(List<DataLogData> tempOfAirCondition, List<DataLogData> humidityOfAirCondition, List<DataLogData> temperatureSetting, List<DataLogData> humiditySetting) {
        if (CollectionUtils.isEmpty(tempOfAirCondition) || CollectionUtils.isEmpty(humidityOfAirCondition) || CollectionUtils.isEmpty(temperatureSetting) || CollectionUtils.isEmpty(humiditySetting)) {
            return Collections.emptyList();
        }
        //空调达标温度
        Double temperatureValue = temperatureSetting.get(0).getValue();
        //空调达标湿度
        Double humiditySetValue = humiditySetting.get(0).getValue();
        if (Objects.isNull(temperatureValue) || Objects.isNull(humiditySetValue)) {
            return Collections.emptyList();
        }
        Map<Long, Double> humidityOfAirMap = humidityOfAirCondition.stream().collect(Collectors.toMap(DataLogData::getTime, DataLogData::getValue, (v1, v2) -> v1));
        return tempOfAirCondition.stream().filter(temp -> {
            //空调温度
            Double value = temp.getValue();
            //空调湿度值
            Double humidityOfAirVal = humidityOfAirMap.get(temp.getTime());
            if (Objects.isNull(value) || Objects.isNull(humidityOfAirVal)) {
                return false;
            }
            //空调温度查
            double tempCal = Math.abs(value - temperatureValue);
            //湿度差
            double humidityCal = Math.abs(humidityOfAirVal - humiditySetValue);
            //线体运行后，空调温度±2摄氏度，湿度±5%，记线体满足达标条件。
            return tempCal <= 4 && humidityCal <= 10;
        }).map(DataLogData::getTime).sorted(Comparator.comparing(Long::longValue)).collect(Collectors.toList());
    }

    /**
     * 获取定时记录值
     *
     * @param quantityData
     * @param dataId
     * @return
     */
    private List<DataLogData> getDataLogDatas(Map<Integer, List<TrendDataVo>> quantityData, Integer dataId) {
        List<TrendDataVo> trendDataVos = quantityData.get(dataId);
        if (CollectionUtils.isEmpty(trendDataVos)) {
            return Collections.emptyList();
        }
        return trendDataVos.get(0).getDataList();
    }

    /*
     * 过线空耗
     */
    private void overDepletion(CataphoresisDto item, Long logTime, DepletionCfgVo depletionCfgVo) {
        if (Objects.isNull(mesPassPointList) || Objects.isNull(mesPassPointListForNextStation) || mesPassPointList.isEmpty() || mesPassPointListForNextStation.isEmpty()) {
            logger.info("{}：电泳池id：{}没有找到{}或{}的过车记录，跳过", LOG_KEY, depletionCfgVo.getStationCode(), depletionCfgVo.getNextStationCode(), item.getId());
            return;
        }
        Optional<AveragePassTimeDto> any = averagePassTimeList.stream().filter(x -> Objects.equals(x.getObjectid(), item.getId())
                && Objects.equals(x.getObjectlabel(), item.getModelLabel())).findAny();
        if (!any.isPresent()) {
            logger.info("{}：没有找到id：{}的水平过车时间", LOG_KEY, item.getId());
            return;
        }
        for (MesPassPointDto record : mesPassPointList) {
            mesPassPointList = mesPassPointList.stream().sorted(Comparator.comparing(MesPassPointDto::getPasstime)).collect(Collectors.toList());
            mesPassPointListForNextStation = mesPassPointListForNextStation.stream().sorted(Comparator.comparing(MesPassPointDto::getPasstime)).collect(Collectors.toList());
            //根据vin和skidno过滤同一工件的过车信息，应该只有一个
            List<Long> collect = mesPassPointListForNextStation.stream().filter(x ->
                    Objects.equals(x.getSkidno(), record.getSkidno()) && Objects.equals(x.getVin(), record.getVin())
            ).map(MesPassPointDto::getPasstime).collect(Collectors.toList());
            if (Objects.isNull(collect) || collect.isEmpty()) {
                logger.info("{}：没有找到vin：{}，skid：{}，在电泳池：{}之后的过车信息，跳过", LOG_KEY, record.getVin(), record.getSkidno(), record.getStationcode());
                continue;
            }
            //同一个工件在下一个电泳池的实际进车时间
            Long nextPassTime = collect.get(0);
            //同一个工件在下一个电泳池的理论进车时间 = 当前电泳池的进车时间 + 水平时间，视为过线空耗的开始时间
            Long theoretical = record.getPasstime() + any.get().getAveragetime() * 1000;
            if (theoretical >= nextPassTime) {
                continue;
            }

            DepletionPartitionDto res = new DepletionPartitionDto();
            res.setStarttime(theoretical);
            res.setEndtime(nextPassTime);
            res.setDuration((nextPassTime - theoretical) / 1000);
            res.setDepletiontype(Constant.DEPLTIONETYPE_OVER);
            res.setLogtime(TimeUtil.getFirstTimeOfDay(logTime));
            res.setProjectid(1L);
            res.setObjectid(item.getId());
            res.setObjectlabel(item.getModelLabel());
        }
    }

    /**
     * 开关机之间没有进车记录，需要处理成进车空耗
     */
    private void specialTreatmentA(CataphoresisDto item, Long logTime) {
        if (startTimeList.isEmpty() || endTimeList.isEmpty()) {
            logger.info("{}：电泳池id：{}开机或关机时间为空，不计算特殊进车空耗", LOG_KEY, item.getId());
            return;
        }
        for (Long startTime : startTimeList) {
            //往后寻找与开机时间最近的关机时间
            Long endTime = getNextTime(endTimeList, startTime, 1);
            if (startTime > endTime) {
                continue;
            }
            //寻找开关机时间内有无过车记录
            Optional<Long> any = timeListForPass.stream().filter(x -> x >= startTime && x <= endTime).findAny();
            //有则跳过
            if (any.isPresent()) {
                continue;
            }

            DepletionPartitionDto res = new DepletionPartitionDto();

            res.setStarttime(startTime);
            res.setEndtime(endTime);
            res.setDuration((endTime - startTime) / 1000);
            res.setDepletiontype(Constant.DEPLTIONETYPE_ENTER);
            res.setLogtime(TimeUtil.getFirstTimeOfDay(logTime));
            res.setProjectid(1L);
            res.setObjectid(item.getId());
            res.setObjectlabel(item.getModelLabel());

            depletionPartitionList.add(res);
        }
    }
}

package com.cet.eem.bll.energysaving.dao.weather;

import com.cet.eem.bll.common.model.domain.subject.energysaving.ColdPredict;
import com.cet.eem.dao.BaseModelDao;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * @ClassName : ColdPredictDao
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-16 16:35
 */
public interface ColdPredictDao extends BaseModelDao<ColdPredict> {
    /**
     * 查询制冷预测数据
     * @param st
     * @param et
     * @param type
     * @param predictType
     * @param roomId
     * @return
     */
    List<ColdPredict> querySingleDeviceData(LocalDateTime st,LocalDateTime et,Integer type,Integer predictType,Long roomId,Long projectId);
    /**
     * 查询制冷预测数据
     *
     * @param coldLoadTypes 负载端类型
     * @param dataTypes     数据类型
     * @param logTimes      时间
     * @param roomId      系统id
     * @return 查询到的制冷数据
     */
    List<ColdPredict> queryColdPredictData(Collection<Integer> coldLoadTypes, Collection<Integer> dataTypes, Collection<LocalDateTime> logTimes,
                                         Long roomId);

    /**
     * 查询制冷预测数据
     * @param st
     * @param et
     * @param type
     * @param predictType
     * @param roomId
     * @return
     */
    List<ColdPredict> queryColdPredictData(LocalDateTime st,LocalDateTime et,Integer type,List<Integer> predictType,Long roomId,Long projectId);

    /**
     * 查询制冷预测数据
     * @param coldLoadTypes
     * @param dataTypes
     * @param st
     * @param et
     * @param roomId
     * @return
     */
    List<ColdPredict> queryColdPredictData(Collection<Integer> coldLoadTypes, Collection<Integer> dataTypes, LocalDateTime st,LocalDateTime et,
                                           Long roomId);
    List<ColdPredict> queryColdPredictData(Collection<Integer> coldLoadTypes, Collection<Integer> dataTypes, LocalDateTime st,LocalDateTime et,
                                          List< Long> roomId);

    /**
     * 写入
     * @param predicts
     * @return
     */
    List<ColdPredict> write(List<ColdPredict> predicts);
}

package com.cet.eem.bll.achievementrate.dao.impl;

import com.cet.eem.bll.achievementrate.dao.GivenEnergyConsumptionPlanDao;
import com.cet.eem.bll.common.model.domain.subject.energy.EnergyConsumptionPlan;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import com.cet.eem.model.base.ConditionBlock;
import com.cet.eem.model.model.BaseEntity;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotEmpty;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : GivenEnergyConsumptionPlanDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-12-04 11:35
 */
@Repository
public class GivenEnergyConsumptionPlanDaoImpl extends ModelDaoImpl<EnergyConsumptionPlan> implements GivenEnergyConsumptionPlanDao {
    private final ModelServiceUtils modelServiceUtils;

    public GivenEnergyConsumptionPlanDaoImpl(ModelServiceUtils modelServiceUtils) {
        this.modelServiceUtils = modelServiceUtils;
    }



    @Override
    public List<EnergyConsumptionPlan> queryEnergyConsumptionBatch(Collection<BaseVo> nodes, long st, long et, int cycle, Collection<Integer> energyTypes) {
        if (CollectionUtils.isEmpty(nodes)) {
            return Collections.emptyList();
        }

        // 构造querycondition对象
        QueryConditionBuilder<BaseEntity> builder = new QueryConditionBuilder<>(ModelLabelDef.ENERGY_CONSUMPE_PLAN)
                .composeMethod(true)
                .removeOrderById();

        // 拼接参数
        int group = 1;
        Map<String, List<BaseVo>> nodeMap = nodes.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        for (Map.Entry<String, List<BaseVo>> node : nodeMap.entrySet()) {
            List<Long> ids = node.getValue().stream().map(BaseVo::getId).distinct().collect(Collectors.toList());
            builder.eq(ColumnDef.C_OBJECT_Label, node.getKey(), group)
                    .in(ColumnDef.C_OBJECT_ID, ids, group)
                    .ge(ColumnDef.LOG_TIME, st, group)
                    .lt(ColumnDef.LOG_TIME, et, group)
                    .eq(ColumnDef.AGGREGATION_CYCLE, cycle, group);

            if (CollectionUtils.isNotEmpty(energyTypes)) {
                builder.where(ColumnDef.ENERGY_TYPE, ConditionBlock.OPERATOR_IN, energyTypes, group);
            }

            group++;
        }

        return modelServiceUtils.query(builder.build(), EnergyConsumptionPlan.class);
    }

    @Override
    public void insertData(List<EnergyConsumptionPlan> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        List<EnergyConsumptionPlan> updateDataList = dataList.stream().filter(it -> !Objects.isNull(it.getId())).collect(Collectors.toList());
        updateDataList(updateDataList);

        List<EnergyConsumptionPlan> addDataList = dataList.stream().filter(it -> Objects.isNull(it.getId())).collect(Collectors.toList());
        addDataList(addDataList);
    }

    @Override
    public void writeEnergyConsumptionPlan(List<EnergyConsumptionPlan> plans) {
        if (CollectionUtils.isEmpty(plans)) {
            return;
        }

        HashSet<BaseVo> nodes = new HashSet<>();
        HashSet<Long> logTimes = new HashSet<>();
        HashSet<Integer> energyTypes = new HashSet<>();
        HashSet<Integer> cycles = new HashSet<>();
        for (EnergyConsumptionPlan energyConsumption : plans) {
            nodes.add(new BaseVo(energyConsumption.getObjectId(), energyConsumption.getObjectLabel()));
            logTimes.add(energyConsumption.getLogtime());
            energyTypes.add(energyConsumption.getEnergyType());
            cycles.add(energyConsumption.getAggregationCycle());
        }
        List<EnergyConsumptionPlan> oldObjectCostValuePlan = queryAllEnergyConsumptionPlan(nodes, logTimes, energyTypes, cycles);
        for (EnergyConsumptionPlan energyConsumption : plans) {
            Optional<EnergyConsumptionPlan> any = oldObjectCostValuePlan.stream().filter(it -> checkEqual(energyConsumption, it)).findAny();
            any.ifPresent(it -> energyConsumption.setId(it.getId()));
        }

        insertData(plans);
    }
    private boolean checkEqual(EnergyConsumptionPlan newObj, EnergyConsumptionPlan oldObj) {
        return Objects.equals(newObj.getObjectLabel(), oldObj.getObjectLabel()) &&
                Objects.equals(newObj.getObjectId(), oldObj.getObjectId()) &&
                Objects.equals(newObj.getLogtime(), oldObj.getLogtime()) &&
                Objects.equals(newObj.getAggregationCycle(), oldObj.getAggregationCycle()) &&
                Objects.equals(newObj.getEnergyType(), oldObj.getEnergyType());
    }
    private List<EnergyConsumptionPlan> queryAllEnergyConsumptionPlan(@NotEmpty Collection<BaseVo> nodes, @NotEmpty Collection<Long> logTimes,
                                                                      @NotEmpty Collection<Integer> energyTypes, @NotEmpty Collection<Integer> cycles) {
        Map<String, List<BaseVo>> nodeMap = nodes.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        LambdaQueryWrapper<EnergyConsumptionPlan> wrapper = LambdaQueryWrapper.of(EnergyConsumptionPlan.class);
        nodeMap.forEach((label, nodeList) -> {
            Set<Long> ids = nodeList.stream().map(BaseVo::getId).collect(Collectors.toSet());
            wrapper.or(it -> it.eq(EnergyConsumptionPlan::getObjectLabel, label)
                    .in(EnergyConsumptionPlan::getObjectId, ids)
                    .in(EnergyConsumptionPlan::getLogtime, logTimes)
                    .in(EnergyConsumptionPlan::getAggregationCycle, cycles)
                    .in(EnergyConsumptionPlan::getEnergyType,energyTypes));
        });

        return this.selectListRedisWithOutOrderById(wrapper);
    }
    private void addDataList(List<EnergyConsumptionPlan> addDataList) {
        if (CollectionUtils.isEmpty(addDataList)) {
            return;
        }

        List<String> updateFields = Arrays.asList(ColumnDef.C_OBJECT_ID,
                ColumnDef.C_OBJECT_Label,
                ColumnDef.AGGREGATION_CYCLE,
                ColumnDef.ENERGY_TYPE,
                ColumnDef.LOG_TIME,
                ColumnDef.VALUE);
        List<List<Object>> writeDataList = new ArrayList<>();
        for (EnergyConsumptionPlan data : addDataList) {
            writeDataList.add(Arrays.asList(data.getObjectId(),
                    data.getObjectLabel(),
                    data.getAggregationCycle(),
                    data.getEnergyType(),
                    data.getLogtime(),
                    data.getValue()));
        }
        modelServiceUtils.writeDataBatch(ModelLabelDef.ENERGY_CONSUMPE_PLAN, true, updateFields, writeDataList, null);
    }

    private void updateDataList(List<EnergyConsumptionPlan> updateDataList) {
        if (CollectionUtils.isEmpty(updateDataList)) {
            return;
        }

        List<String> filterFields = Arrays.asList(ColumnDef.C_OBJECT_ID,
                ColumnDef.C_OBJECT_Label,
                ColumnDef.AGGREGATION_CYCLE,
                ColumnDef.ENERGY_TYPE,
                ColumnDef.LOG_TIME);

        List<String> updateFields = Collections.singletonList(
                ColumnDef.VALUE);
        List<List<Object>> writeDataList = new ArrayList<>();
        List<List<Object>> filterDataList = new ArrayList<>();
        for (EnergyConsumptionPlan data : updateDataList) {
            writeDataList.add(Collections.singletonList(
                    data.getValue()));
            filterDataList.add(Arrays.asList(data.getObjectId(),
                    data.getObjectLabel(),
                    data.getAggregationCycle(),
                    data.getEnergyType(),
                    data.getLogtime()));
        }
        modelServiceUtils.writeDataBatch(ModelLabelDef.ENERGY_CONSUMPE_PLAN, false, updateFields, writeDataList, filterFields, filterDataList);
    }
}
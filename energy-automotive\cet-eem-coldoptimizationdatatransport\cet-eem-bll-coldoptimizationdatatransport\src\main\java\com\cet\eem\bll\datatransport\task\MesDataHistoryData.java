package com.cet.eem.bll.datatransport.task;

import com.cet.eem.bll.datatransport.handle.MesDataHandle;
import com.cet.eem.common.utils.TimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@Configuration
@EnableScheduling
public class MesDataHistoryData {
    @Autowired
    MesDataHandle mesDataHandle;

    @Scheduled(cron = "${cet.eem.mes.his.cron:-}")
    public void transportData() throws IOException {
        mesDataHandle.addMesHistoryData(TimeUtil.timestamp2LocalDateTime(System.currentTimeMillis()));
    }
}

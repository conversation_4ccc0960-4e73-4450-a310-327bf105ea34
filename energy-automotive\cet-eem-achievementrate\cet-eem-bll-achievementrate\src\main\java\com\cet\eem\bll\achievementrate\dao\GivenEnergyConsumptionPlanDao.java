package com.cet.eem.bll.achievementrate.dao;

import com.cet.eem.bll.common.model.domain.subject.energy.EnergyConsumptionPlan;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.dao.BaseModelDao;
import com.cet.eem.dao.ModelDaoImpl;

import java.util.Collection;
import java.util.List;

/**
 * @ClassName : GivenEnergyConsumptionPlanDao
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-12-04 11:34
 */
public interface GivenEnergyConsumptionPlanDao extends BaseModelDao<EnergyConsumptionPlan> {
    /**
     *
     * @param node
     * @param st
     * @param et
     * @param cycle
     * @param energyTypes
     * @return
     */
    List<EnergyConsumptionPlan> queryEnergyConsumptionBatch(Collection<BaseVo> node, long st, long et, int cycle, Collection<Integer> energyTypes);

    /**
     *
     * @param energyConsumptionPlans
     */
    void insertData(List<EnergyConsumptionPlan> energyConsumptionPlans);

    /**
     *
     * @param energyConsumptionPlans
     */
    void writeEnergyConsumptionPlan(List<EnergyConsumptionPlan> energyConsumptionPlans);
}
package com.cet.eem.achievementrateservice.controller;

import com.cet.eem.bll.achievementrate.model.AchievementConfig;
import com.cet.eem.bll.achievementrate.model.Constant;
import com.cet.eem.bll.achievementrate.model.QueryConfigVO;
import com.cet.eem.bll.achievementrate.model.RecalculateParam;
import com.cet.eem.bll.achievementrate.task.AttainmentRateTask;
import com.cet.eem.common.model.Result;
import io.swagger.annotations.Api;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/3/19 11:37
 */
@Api(value = "RecalculateController", tags = {"达成率重算管理接口"})
@RestController
@RequestMapping(value = "/eem/v1/recalculate")
public class RecalculateController {
    @Autowired
    AchievementConfig config;
    @Autowired
    AttainmentRateTask rateTask;
    @Autowired
    @Qualifier("eem-redis-template")
    RedisTemplate<String, String> redisTemplate;

    @GetMapping("/queryConfig")
    public Result<QueryConfigVO> queryConfig() {
        QueryConfigVO configVO = new QueryConfigVO();
        BeanUtils.copyProperties(config, configVO);
        return Result.ok(configVO);
    }

    @PostMapping("/recalculate")
    public Result<Void> recalculate(@RequestBody RecalculateParam param) {
        rateTask.recalculate(param);
        return Result.ok();
    }

    @GetMapping("/queryStatus")
    public Result<Boolean> queryStatus() {
        return Result.ok(Objects.equals(redisTemplate.hasKey(Constant.RECALCULATE_KEY),true));
    }
    @GetMapping("/test")
    public void test(){
        rateTask.execute();
    }
}

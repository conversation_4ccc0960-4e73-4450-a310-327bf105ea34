FROM *************/base/java:1.8-alpine-withfont
COPY --from=*************/base/arthas:latest /opt/arthas /opt/arthas
VOLUME /tmp
ARG artifactId
ARG VERSION
ADD ./target/${artifactId}-${VERSION}.war /${artifactId}.jar
ENV JAVA_OPTS=""
ENV JAR_FILE_NAME ${artifactId}
#Djava.security.egd  这个是用来防止springboot项目tomcat启动慢的问题（具体可搜索：随机数数与熵池策略）
ENTRYPOINT [ "sh", "-c", "java $JAVA_OPTS -Djava.security.egd=file:/dev/./urandom -jar -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 /$JAR_FILE_NAME.jar" ]
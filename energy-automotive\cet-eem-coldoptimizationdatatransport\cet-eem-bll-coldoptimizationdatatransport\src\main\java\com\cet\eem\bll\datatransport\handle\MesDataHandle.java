package com.cet.eem.bll.datatransport.handle;

import com.cet.eem.bll.common.model.domain.object.architecture.BuildingVo;
import com.cet.eem.bll.common.model.domain.subject.energy.plan.ProductionPlan;
import com.cet.eem.bll.common.model.domain.subject.generalrules.UnnaturalSetVo;
import com.cet.eem.bll.common.model.domain.subject.production.ProductionData;
import com.cet.eem.bll.common.service.UnnaturalTimeService;
import com.cet.eem.bll.common.service.product.ProductionDataService;
import com.cet.eem.bll.datatransport.config.DepletionCfg;
import com.cet.eem.bll.datatransport.mapper.MesDataEntityMapper;
import com.cet.eem.bll.datatransport.model.DepletionCfgVo;
import com.cet.eem.bll.datatransport.model.ModelDef;
import com.cet.eem.bll.datatransport.model.mesdata.AveragePassTimeVo;
import com.cet.eem.bll.datatransport.model.mesdata.MesDataEntityVo;
import com.cet.eem.bll.datatransport.model.mesdata.MesPassPointVo;
import com.cet.eem.bll.demand.model.entity.Project;
import com.cet.eem.bll.energy.service.predict.ProductionPlanService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.ParamUtils;
import com.cet.eem.common.constant.EnumOperationType;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.DataLogData;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.model.datalog.TrendSearchVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.common.service.RedisService;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import com.cet.eem.peccore.model.datalog.DataLogSearchDto;
import com.cet.eem.peccore.service.TrendService;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.devicedataservice.common.dto.config.DatalogMeasItemDTO;
import com.cet.electric.devicedataservice.core.service.PecCoreConfigService;
import com.cet.electric.modelservice.common.dto.EnumItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * mes系统生产计划数据转存
 */
@Service
@Slf4j
public class MesDataHandle {
    @Value("${cet.eem.mes.filepath}")
    private String filePath;
    @Value("${cet.eem.mes.cycle}")
    private String cycle;
    @Value("${cet.eem.mes.code}")
    private String projectCode;
    @Value("${cet.eem.mes.his.keep}")
    private Boolean keep;
    @Value("${cet.eem.mes.passpoint.url}")
    private String url;
    @Value("${cet.eem.mes.passpoint.user}")
    private String user;
    @Value("${cet.eem.mes.passpoint.password}")
    private String password;
    @Value("${cet.eem.mes.special.config}")
    private String config;
    private static final String LOG_KEY = "[MES数据转存]";

    @Autowired
    MesDataEntityMapper mesDataEntityMapper;


    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    ProductionDataService productionDataService;

    @Autowired
    ProductionPlanService productionPlanService;
    @Autowired
    RedisService redisService;
    private static final String MES_HIS_DATA = "meshisdata";
    private static final Integer PRODUCT_TYPE = 5004;
    private static final String CAR = "car";
    private final Long DATA_ID = 6000001L;
    //private final Long DATA_ID = 6000753L;
    @Autowired
    DepletionCfg depletionCfg;

    @Value("${cet.eem.mes.passpoint.lowlimit:0.8}")
    public Double lowLimit;

    @Value("${cet.eem.mes.passpoint.upperlimit:1.2}")
    public Double upperLimit;
    @Autowired
    protected TrendService trendService;
    @Autowired
    PecCoreConfigService pecCoreConfigService;
    @Autowired
    UnnaturalTimeService unnaturalTimeService;

    private Set<Integer> getCycle(String cycle) {
        Set<Integer> result = new HashSet<>();
        if (StringUtils.isNotEmpty(cycle)) {
            String[] split = cycle.split(",");
            for (String s : split) {
                result.add(Integer.valueOf(s));
            }
        }
        return result;
    }

    private Integer queryCarType() {
        Map<Integer, EnumItem> enumeration = modelServiceUtils.getEnumeration(ColumnDef.PRODUCT_TYPE);
        for (Map.Entry<Integer, EnumItem> entry : enumeration.entrySet()) {
            if (Objects.equals(entry.getValue().getPropertyLabel(), CAR)) {
                return entry.getKey();
            }
        }
        return null;
    }

    /*
     * 数据转存
     * 每一小时执行一次，每次查询（上次执行时刻，当前时刻）的mes数据
     * 取最新的一条数据，转存数据包括车间名称、班次、生产计划和已完成产量
     * */
    public void transport(LocalDateTime time) throws IOException {
        log.info("{}：开始执行MES数据转存", LOG_KEY);
        List<MesDataEntityVo> mesDataEntityVos = redisService.getListByStr(MES_HIS_DATA, MesDataEntityVo.class);
        if (CollectionUtils.isEmpty(mesDataEntityVos)) {
            return;
        }
        //零点的时间小时，数据计算，清零的小时数据计算，日的数据计算，清除数据的情况

        List<ProductionData> data = new ArrayList<>();
        List<ProductionPlan> plan = new ArrayList<>();
        Set<Integer> cycle = getCycle(this.cycle);
        Integer productType = queryCarType();
        if (cycle.contains(AggregationCycle.ONE_HOUR)) {
            LocalDateTime firstTimeOfHour = TimeUtil.getFirstTimeOfHour(time);
            calculateHourData(mesDataEntityVos, firstTimeOfHour, data, plan, productType);
        }
        Long projectID = getProjectID();
        //日数据的计算，有第二天的情况
        List<MesDataEntityVo> dataEntityVos = mesDataEntityVos.stream().sorted(Comparator.comparing(MesDataEntityVo::getLogtime))
                .collect(Collectors.toList());
        Long thisDay = TimeUtil.getFirstTimeOfDay(dataEntityVos.get(dataEntityVos.size() - 1).getLogtime());
        Long nowTime = TimeUtil.getFirstTimeOfDay(System.currentTimeMillis());
        Long lastTime = TimeUtil.addDateTimeByCycle(nowTime, AggregationCycle.ONE_DAY, -1);
        Long firstTimeOfDay = TimeUtil.getFirstTimeOfDay(dataEntityVos.get(0).getLogtime());
        log.info("当前转存数据时间为：{},计算的前一天时间为：{}",
                TimeUtil.format(thisDay, TimeUtil.LONG_TIME_FORMAT), TimeUtil.format(firstTimeOfDay, TimeUtil.LONG_TIME_FORMAT));
        if (!Objects.equals(firstTimeOfDay, thisDay)) {
            //不是同一天
            List<MesDataEntityVo> entityVos = dataEntityVos.stream().filter(mesDataEntityVo -> mesDataEntityVo.getLogtime() < nowTime
                    && mesDataEntityVo.getLogtime() >= lastTime)
                    .sorted(Comparator.comparing(MesDataEntityVo::getLogtime)).collect(Collectors.toList());
            Map<String, List<MesDataEntityVo>> map = entityVos.stream().collect(Collectors.groupingBy(mesDataEntityVo -> mesDataEntityVo.getObjectid() + "_" + mesDataEntityVo.getObjectlabel()));
            //需要加入项目，总装等于项目产量和计划
            for (Map.Entry<String, List<MesDataEntityVo>> entry : map.entrySet()) {
                MesDataEntityVo item = calculateMesData(entry.getValue());
                data.add(createProductionDataList(item, item.getObjectlabel(), item.getObjectid(), productType));
                plan.add(createProductionPlanList(item, item.getObjectlabel(), item.getObjectid(), productType));
                if (Objects.equals(item.getCode(), projectCode)) {
                    data.add(createProductionDataList(item, NodeLabelDef.PROJECT, projectID, productType));
                    plan.add(createProductionPlanList(item, NodeLabelDef.PROJECT, projectID, productType));
                }
            }
            List<MesDataEntityVo> nowData = redisService.getListByStr(MES_HIS_DATA, MesDataEntityVo.class);
            List<MesDataEntityVo> list = nowData.stream().filter(mesDataEntityVo -> mesDataEntityVo.getLogtime() >= nowTime)
                    .collect(Collectors.toList());
            redisService.deleteKey(MES_HIS_DATA);
            redisService.addListByStr(MES_HIS_DATA, list, 2, TimeUnit.DAYS);
        }
        log.info("Mes数据转存的是时间为：{},产量数据入库前数据为：{},计划数据入库前数据为：{}",
                TimeUtil.format(firstTimeOfDay, TimeUtil.DATE_TIME_FORMAT), JsonTransferUtils.toJSONString(data), JsonTransferUtils.toJSONString(plan));
        productionDataService.writeProductionDataList(data, projectID);
        productionPlanService.writeProductionPlanList(plan, projectID);
        log.info("{}：MES数据转换产量写入完成", LOG_KEY);
    }

    /**
     * 冲压车间数据逇转存
     *
     * @param time
     * @throws IOException
     */
    public void transportSpecial(LocalDateTime time) throws IOException {
        log.info("{}：开始执行冲压车间MES数据转存", LOG_KEY);
        Integer productType = queryCarType();
        Long projectID = getProjectID();
        //先查配置的非自然时间，然后查询当前的时间，算前一天的数据
        Long thisDay = TimeUtil.getFirstTimeOfDay(TimeUtil.localDateTime2timestamp(time));
        UnnaturalSetVo unnaturalSet = unnaturalTimeService.getUnnaturalSetVo(projectID, AggregationCycle.ONE_DAY);
        long endTimeByTime = unnaturalTimeService.convertUnnaturalTime(AggregationCycle.ONE_DAY, thisDay, unnaturalSet);
        Long startTime = TimeUtil.addDateTimeByCycle(endTimeByTime, AggregationCycle.ONE_DAY, -1);
        ProductionData productionData = queryDeviceData(startTime, endTimeByTime, productType);
        log.info("冲压车间Mes数据转存的是时间为：{},产量数据入库前数据为：{}",
                TimeUtil.format(startTime, TimeUtil.DATE_TIME_FORMAT), JsonTransferUtils.toJSONString(productionData));
        productionDataService.writeProductionDataList(Collections.singletonList(productionData), projectID);
        log.info("{}：冲压车间MES数据转换产量写入完成", LOG_KEY);
    }

    private ProductionData createProductionDataList(String label, Long id, Integer productType, LocalDateTime time, Integer data) {

        ProductionData productionData = new ProductionData();
        productionData.setAggregationCycle(AggregationCycle.ONE_DAY);
        productionData.setProductType(productType);
        productionData.setObjectId(id);
        productionData.setObjectLabel(label);
        productionData.setLogTime(TimeUtil.getFirstTimeOfDay(time));
        productionData.setValue(data.doubleValue());

        return productionData;
    }

    private ProductionData queryDeviceData(Long st, Long et, Integer productType) throws IOException {
        //获得冲压车间的信息，和相关的表计
        List<Long> deviceIds = new ArrayList<>();
        BaseVo baseVo = new BaseVo();
        assembleDeviceAndNode(deviceIds, baseVo);
        DataLogSearchDto searchDto = assembleTrendDataDto(deviceIds, st, et);
        List<TrendDataVo> trendDataVos = trendService.queryDataLogs(searchDto);
        int count = 0;
        for (TrendDataVo trendDataVo : trendDataVos) {
            List<DataLogData> value = trendDataVo.getDataList();
            if (CollectionUtils.isEmpty(value)) {
                continue;
            }
            int i = 0;
            int j = i + 1;
            Double max = 0.0;
            while (i < value.size() - 1 && j < value.size()) {
                //先看看规律
                if (Objects.equals(value.get(i).getValue(), 0.0) && !Objects.equals(value.get(j).getValue(), 0.0)) {
                    //要找到0和不为了0的地方
                    for (int n = j; n < value.size(); n++) {
                        if (Objects.equals(value.get(n).getValue(), 0.0)) {
                            i = n;
                            j = i + 1;
                            break;
                        }
                        if (max < value.get(n).getValue()) {
                            max = value.get(n).getValue();
                        }
                        //走到最后了
                        if (Objects.equals(n, value.size() - 1)) {
                            i = n;
                            break;
                        }
                    }
                } else {
                    i++;
                    j++;
                }
            }
            count += max;
        }
        return createProductionDataList(baseVo.getModelLabel(), baseVo.getId(),
                productType, TimeUtil.timestamp2LocalDateTime(TimeUtil.getFirstTimeOfDay(st)), count);
    }


    private DataLogSearchDto assembleTrendDataDto(List<Long> deviceIds, Long st, Long et) {
        ApiResult<Map<Long, List<DatalogMeasItemDTO>>> deviceDatalogPoints = pecCoreConfigService.getDeviceDatalogPoints(deviceIds);
        ParamUtils.checkResultGeneric(deviceDatalogPoints);
        Map<Long, List<DatalogMeasItemDTO>> data = deviceDatalogPoints.getData();
        DataLogSearchDto dto = new DataLogSearchDto();
        dto.setCacheMonthNumber(0);
        dto.setDbInterval(0);
        dto.setCacheInterval(0);
        dto.setInterval(0);
        //2023-07-26 00:00:00
        dto.setStartTime(TimeUtil.format(st, TimeUtil.LONG_TIME_FORMAT));
        dto.setEndTime(TimeUtil.format(et, TimeUtil.LONG_TIME_FORMAT));
        List<TrendSearchVo> meterConfigs = new ArrayList<>();
        for (Map.Entry<Long, List<DatalogMeasItemDTO>> entry : data.entrySet()) {
            List<DatalogMeasItemDTO> value = entry.getValue();
            for (DatalogMeasItemDTO itemDTO : value) {
                if (Objects.equals(itemDTO.getDataId(), DATA_ID.intValue())) {
                    TrendSearchVo searchVo1 = new TrendSearchVo();
                    searchVo1.setDataId(DATA_ID);
                    searchVo1.setLogicalId(itemDTO.getLogicalDeviceIndex());
                    searchVo1.setDataTypeId(itemDTO.getDataTypeId());
                    searchVo1.setDeviceId(entry.getKey());
                    meterConfigs.add(searchVo1);
                }
            }


        }
        dto.setMeterConfigs(meterConfigs);
        return dto;
    }

    private void assembleDeviceAndNode(List<Long> deviceIds, BaseVo baseVo) throws IOException {
        log.info("{}：读取车间名称与编号对应关系", LOG_KEY);
        if (Objects.isNull(config) || StringUtils.isEmpty(config)) {
            log.info("{}：没有找到冲压车间名称与编号对应关系，路径：{}", LOG_KEY, filePath);
            return;
        }

        //冲压车间的计算逻辑不一样
        String[] split = config.split(",");
        String code = split[0];
        for (int i = 1; i < split.length; i++) {
            deviceIds.add(Long.parseLong(split[i]));
        }
        List<BuildingVo> buildingVos = modelServiceUtils.query((Long) null, NodeLabelDef.BUILDING, BuildingVo.class);

        Optional<BuildingVo> first = buildingVos.stream().filter(x -> Objects.nonNull(x.getCode()) && x.getCode().equals(code))
                .findFirst();
        if (!first.isPresent()) {
            log.info("{}：building模型中没有找到：{}", LOG_KEY, code);
            return;
        }
        baseVo.setId(first.get().getId());
        baseVo.setModelLabel(NodeLabelDef.BUILDING);

    }

    private MesDataEntityVo calculateMesData(List<MesDataEntityVo> entityVos) {
        //不同班组生产，数据会清除 --数据根据班组分组
        //清除是下一次生产清除或者生产完清除 --数据要判断是正常流程生产的结果从0-1-2有一个从0变化的过程
        Map<String, List<MesDataEntityVo>> mapByShift = entityVos.stream().collect(Collectors.groupingBy(MesDataEntityVo::getShiftname));
        int count = 0;
        int countPlan = 0;
        //不同班组的结果求和
        for (Map.Entry<String, List<MesDataEntityVo>> entry : mapByShift.entrySet()) {
            List<MesDataEntityVo> value = entry.getValue();
            int i = 0;
            int j = i + 1;
            int max = 0;
            int maxPlan = 0;
            while (i < value.size() - 1 && j < value.size()) {
                //先找0
                if (Objects.equals(value.get(i).getCompletenum(), 0) && !Objects.equals(value.get(j).getCompletenum(), 0)) {
                    //要找到0和不为了0的地方
                    for (int n = j; n < value.size(); n++) {
                        if (Objects.equals(value.get(n).getCompletenum(), 0)) {
                            i = n;
                            j = i + 1;
                            break;
                        }
                        if (max < value.get(n).getCompletenum()) {
                            max = value.get(n).getCompletenum();
                            maxPlan = value.get(n).getPlannum();
                        }
                        //走到最后了
                        if (Objects.equals(n, value.size() - 1)) {
                            i = n;
                            break;
                        }
                    }
                } else {
                    i++;
                    j++;
                }
            }
            count += max;
            countPlan += maxPlan;
        }
        MesDataEntityVo result = new MesDataEntityVo();
        BeanUtils.copyProperties(entityVos.get(0), result);
        result.setCompletenum(count);
        result.setPlannum(countPlan);
        return result;
    }

    private void calculateHourData(List<MesDataEntityVo> mesDataEntityVos, LocalDateTime firstTimeOfHour, List<ProductionData> data,
                                   List<ProductionPlan> plan, Integer productType) {
        Map<String, List<MesDataEntityVo>> map = mesDataEntityVos.stream().filter(mesDataEntityVo -> mesDataEntityVo.getLogtime() < TimeUtil.localDateTime2timestamp(firstTimeOfHour)
                && mesDataEntityVo.getLogtime() >
                TimeUtil.localDateTime2timestamp(TimeUtil.addDateTimeByCycle(firstTimeOfHour, AggregationCycle.ONE_HOUR, -1)))
                .sorted(Comparator.comparing(MesDataEntityVo::getLogtime))
                .collect(Collectors.groupingBy(mesDataEntityVo -> mesDataEntityVo.getObjectid() + "_" + mesDataEntityVo.getObjectlabel()));
        if (map.isEmpty()) {
            return;
        }
        for (Map.Entry<String, List<MesDataEntityVo>> entry : map.entrySet()) {
            List<MesDataEntityVo> value = entry.getValue();
            MesDataEntityVo lastHour = value.get(0);
            MesDataEntityVo thisHour = value.get(value.size() - 1);
            if (Objects.equals(thisHour.getCompletenum(), 0) && !Objects.equals(lastHour.getCompletenum(), 0)) {
                //当前小时为0，上个小时不为0
                List<MesDataEntityVo> sort = value.stream().filter(mesDataEntityVo -> mesDataEntityVo.getLogtime() > lastHour.getLogtime() && mesDataEntityVo.getCompletenum() > 0)
                        .sorted(Comparator.comparing(MesDataEntityVo::getCompletenum).reversed()).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(sort)) {
                    //意思是处理大于上个整点的数据，其他数据全是0，那这个小时数据也算0，这个logtime的时间是会比整点多一点的
                    data.add(createProductionData(thisHour, thisHour, productType));
                    plan.add(createProductionPlan(thisHour, thisHour, productType));
                } else {
                    data.add(createProductionData(thisHour, sort.get(0), productType));
                    plan.add(createProductionPlan(thisHour, sort.get(0), productType));
                }
                continue;
            }
            //普通情况
            data.add(createProductionData(lastHour, thisHour, productType));
            plan.add(createProductionPlan(lastHour, thisHour, productType));
        }
    }

    /**
     * 通过对应关系的文件，将车间的buildingid和mes记录对应起来
     */
    private List<MesDataEntityVo> dealWithData(List<MesDataEntityVo> mesData, Map<String, String> workShipCodeMap, List<BuildingVo> buildingVos) {
        List<MesDataEntityVo> result = new ArrayList<>();
        for (MesDataEntityVo item : mesData) {
            String code = workShipCodeMap.get(item.getWorkshipname());
            if (Objects.isNull(code)) {
                log.info("{}：对应关系文件中没有找到车间：{} 的记录", LOG_KEY, item.getWorkshipname());
                continue;
            }
            List<BuildingVo> collectForFloor = buildingVos.stream().filter(x -> Objects.nonNull(x.getCode()) && x.getCode().equals(code)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collectForFloor)) {
                log.info("{}：building模型中没有找到：{}", LOG_KEY, code);
                continue;
            }
            item.setObjectlabel(NodeLabelDef.BUILDING);
            item.setObjectid(collectForFloor.get(0).getId());
            item.setLogtime(TimeUtil.localDateTime2timestamp(item.getSyscreatetime()));
            item.setModelLabel(ModelDef.PRODUCTION_DATA_DOCKING);
            item.setCode(code);
            result.add(item);
        }
        return result;
    }

    /**
     * 获取车间名称与编号对应关系的txt
     * key-车间名称 value-车间编号
     */
    private Map<String, String> getWorkShipCodeMap() throws IOException {
        log.info("{}：读取车间名称与编号对应关系", LOG_KEY);
        Map<String, String> workShipCodeMap = new HashMap<>();
        List<String> allLines = Files.readAllLines(Paths.get(filePath));
        if (CollectionUtils.isEmpty(allLines)) {
            log.info("{}：没有找到车间名称与编号对应关系，路径：{}", LOG_KEY, filePath);
            return workShipCodeMap;
        }
        //冲压车间的计算逻辑不一样
        for (int i = 1; i < allLines.size(); i++) {
            String[] split = allLines.get(i).split(",");
            workShipCodeMap.put(split[0], split[1]);
        }
        return workShipCodeMap;
    }

    private ProductionData createProductionData(MesDataEntityVo lastHour, MesDataEntityVo thisHour, Integer productType) {
        ProductionData productionData = new ProductionData();
        productionData.setAggregationCycle(AggregationCycle.ONE_HOUR);
        productionData.setProductType(productType);
        productionData.setObjectId(lastHour.getObjectid());
        productionData.setObjectLabel(lastHour.getObjectlabel());
        productionData.setLogTime(TimeUtil.getFirstTimeOfHour(thisHour.getSyscreatetime()));
        productionData.setValue(CommonUtils.calcDouble(Double.valueOf(thisHour.getCompletenum()),
                Double.valueOf(lastHour.getCompletenum()), EnumOperationType.SUBTRACT.getId()));
        return productionData;
    }

    private ProductionPlan createProductionPlan(MesDataEntityVo lastHour, MesDataEntityVo thisHour, Integer productType) {

        ProductionPlan productionPlan = new ProductionPlan();
        productionPlan.setAggregationcycle(AggregationCycle.ONE_HOUR);
        productionPlan.setProducttype(productType);
        productionPlan.setObjectid(lastHour.getObjectid());
        productionPlan.setObjectlabel(lastHour.getObjectlabel());
        productionPlan.setLogtime(TimeUtil.getFirstTimeOfDay(thisHour.getSyscreatetime()));
        productionPlan.setValue(CommonUtils.calcDouble(Double.valueOf(thisHour.getPlannum()),
                Double.valueOf(lastHour.getPlannum()), EnumOperationType.SUBTRACT.getId()));

        return productionPlan;
    }


    private ProductionData createProductionDataList(MesDataEntityVo item, String label, Long id, Integer productType) {

        ProductionData productionData = new ProductionData();
        productionData.setAggregationCycle(AggregationCycle.ONE_DAY);
        productionData.setProductType(productType);
        productionData.setObjectId(id);
        productionData.setObjectLabel(label);
        productionData.setLogTime(TimeUtil.getFirstTimeOfDay(item.getSyscreatetime()));
        productionData.setValue(Double.valueOf(item.getCompletenum()));

        return productionData;
    }

    private ProductionPlan createProductionPlanList(MesDataEntityVo item, String label, Long id, Integer productType) {

        ProductionPlan productionPlan = new ProductionPlan();
        productionPlan.setAggregationcycle(AggregationCycle.ONE_DAY);
        productionPlan.setProducttype(productType);
        productionPlan.setObjectid(id);
        productionPlan.setObjectlabel(label);
        productionPlan.setLogtime(TimeUtil.getFirstTimeOfDay(item.getSyscreatetime()));
        productionPlan.setValue(Double.valueOf(item.getPlannum()));
        return productionPlan;
    }

    private Long getProjectID() {
        QueryCondition queryCondition = new QueryConditionBuilder<>(NodeLabelDef.PROJECT).build();
        return modelServiceUtils.query(queryCondition, Project.class).get(0).getId();
    }

    /*
     * 数据转存
     * 每一小时执行一次，每次查询（上次执行时刻，当前时刻）的mes数据
     * 取最新的一条数据，转存数据包括车间名称、班次、生产计划和已完成产量
     * */
    public void addMesHistoryData(LocalDateTime time) throws IOException {
        log.info("{}：开始执行MES历史数据缓存写入", LOG_KEY);
        List<MesDataEntityVo> mesData = mesDataEntityMapper.queryMesData(time.plusHours(-1), time);
        if (CollectionUtils.isEmpty(mesData)) {
            log.info("{}：没有最新的mes数据，退出缓存写入任务", LOG_KEY);
            return;
        }
        Map<String, String> workShipCodeMap = getWorkShipCodeMap();
        if (workShipCodeMap.isEmpty()) {
            log.info("{}：没有找到车间名称与编号对应关系，退出转存任务", LOG_KEY);
            return;
        }
        List<BuildingVo> buildingVos = modelServiceUtils.query((Long) null, NodeLabelDef.BUILDING, BuildingVo.class);
        List<MesDataEntityVo> mesDataEntityVos1 = dealWithData(mesData, workShipCodeMap, buildingVos);
        if (Boolean.TRUE.equals(keep)) {
            modelServiceUtils.writeData(mesDataEntityVos1, MesDataEntityVo.class);
            log.info("{}：MES数据写入完成", LOG_KEY);

        }
        //写到redis里
        List<MesDataEntityVo> mesDataEntityVos = redisService.getListByStr(MES_HIS_DATA, MesDataEntityVo.class);
        if (CollectionUtils.isEmpty(mesDataEntityVos)) {
            redisService.addListByStr(MES_HIS_DATA, mesDataEntityVos1, 2, TimeUnit.DAYS);
        } else {

            mesDataEntityVos1.addAll(mesDataEntityVos);
            redisService.addListByStr(MES_HIS_DATA, mesDataEntityVos1, 2, TimeUnit.DAYS);
        }

        log.info("{}：MES历史数据缓存写入完成", LOG_KEY);

    }

    /**
     * 转存mes系统过车信息
     * 每天执行一次，时间区间为[前天零点，当天零点)
     **/
    public void transportMesPassPoint(Long startTime, Long endTime) throws Exception {
        log.info("{}：开始执行MES过车数据转存", LOG_KEY);
        List<MesPassPointVo> mesPassPointVos = queryMesPassPoint(startTime, endTime);
        if (mesPassPointVos.isEmpty()) {
            log.info("{}：没有找到待转存的过车数据，{} ~ {}", LOG_KEY, startTime, endTime);
            return;
        }
        for (MesPassPointVo item : mesPassPointVos) {
            item.setId(0L);
        }

        modelServiceUtils.writeData(mesPassPointVos);
        log.info("{}:过车数据入库完成，数据条数：{}", LOG_KEY, mesPassPointVos.size());

    }

    private List<MesPassPointVo> queryMesPassPoint(Long startTime, Long endTime) throws Exception {
        List<MesPassPointVo> mesPassPointVos = new ArrayList<>();
        Class.forName("org.postgresql.Driver");
        Connection connection = DriverManager.getConnection(url, user, password);
        Timestamp st = Timestamp.valueOf(TimeUtil.timestamp2LocalDateTime(startTime));
        Timestamp et = Timestamp.valueOf(TimeUtil.timestamp2LocalDateTime(endTime));
        String sql = String.format("select pass_time as time, skidno, vin, station_code as stationcode from avi_passpoint ap " +
                        "where ap.pass_time >='%s' and ap.pass_time <'%s' and ap.vin !='88888888888888888' " +
                        "and ap.station_code in ('B02', 'B03', 'B04', 'B05','B09','B10', 'B13','B14', 'B15','B18','B19', 'B20', 'B24', 'B25')",
                st, et);
        Statement statement = connection.createStatement();
        ResultSet resultSet = statement.executeQuery(sql);
        while (resultSet.next()) {
            MesPassPointVo mesPassPointVo = new MesPassPointVo();
            mesPassPointVo.setVin(resultSet.getString("vin"));
            mesPassPointVo.setSkidno(resultSet.getString("skidno"));
            mesPassPointVo.setStationcode(resultSet.getString("stationcode"));
            String time = String.format(resultSet.getString("time"), "yyyy-MM-dd HH:mm:ss");
            Timestamp timestamp = Timestamp.valueOf(time);
            mesPassPointVo.setPasstime(timestamp.getTime());
            mesPassPointVos.add(mesPassPointVo);
        }
        connection.close();
        statement.close();
        return mesPassPointVos;
    }

    /*
     * 计算各个电泳池的平均过车时间
     * 默认计算三天内的数据
     * */
    public void calcAveragePassTime(Long startTime, Long endTime) throws Exception {
        log.info("{}：开始计算平均过线时间", LOG_KEY);
        List<MesPassPointVo> mesPassPointVoList = queryMesPassPointData(startTime, endTime);
        List<DepletionCfgVo> depletionCfg = this.depletionCfg.getDepletionCfg();
        if (mesPassPointVoList.isEmpty() || depletionCfg.isEmpty()) {
            log.info("{}：没有查询到指定时间内的过车信息或者空耗配置文件为空", LOG_KEY);
            return;
        }
        //按照电泳池编码进行分组
        Map<String, List<MesPassPointVo>> collect = mesPassPointVoList.stream().collect(Collectors.groupingBy(MesPassPointVo::getStationcode));
        List<AveragePassTimeVo> res = new ArrayList<>();
        for (DepletionCfgVo item : depletionCfg) {
            List<MesPassPointVo> mesPassPointVo1 = collect.get(item.getStationCode());
            List<MesPassPointVo> mesPassPointVo2 = collect.get(item.getNextStationCode());
            if (Objects.isNull(mesPassPointVo1) || Objects.isNull(mesPassPointVo2) || mesPassPointVo1.isEmpty() || mesPassPointVo2.isEmpty()) {
                log.info("{}：没有找到{}或{}的过车记录，跳过", LOG_KEY, item.getStationCode(), item.getNextStationCode());
                continue;
            }
            Long averagePassTime = dealAveragePassTime(mesPassPointVo1, mesPassPointVo2);
            AveragePassTimeVo averagePassTimeVo = new AveragePassTimeVo();
            averagePassTimeVo.setAveragetime(averagePassTime);
            averagePassTimeVo.setStationcode(item.getStationCode());
            averagePassTimeVo.setObjectid(item.getObjectID());
            averagePassTimeVo.setObjectlabel(item.getObjectLabel());
            res.add(averagePassTimeVo);
        }
        List<AveragePassTimeVo> asd = compareAveragePassTime(res);
        modelServiceUtils.writeData(asd);
        log.info("{}: 平均过车时间入库完成，数据条数：{}", LOG_KEY, asd.size());
    }

    private List<MesPassPointVo> queryMesPassPointData(Long startTime, Long endTime) {
        QueryCondition queryCondition = new QueryConditionBuilder<>(ModelDef.PASS_POINT_INFO)
                .ge("passtime", startTime)
                .lt("passtime", endTime)
                .build();
        return modelServiceUtils.query(queryCondition, MesPassPointVo.class);
    }

    private List<AveragePassTimeVo> queryAveragePassTime() {
        QueryCondition queryCondition = new QueryConditionBuilder<>(ModelDef.AVERAGE_PASS_TIME)
                .build();
        return modelServiceUtils.query(queryCondition, AveragePassTimeVo.class);
    }

    /**
     * 从目标时间list中找到与指定time相邻的时间戳
     */
    private Long getNextTime(List<Long> timeList, Long time, int offset) {
        List<Long> collect = new ArrayList<>(timeList);
        collect.add(time);
        collect = collect.stream().sorted().collect(Collectors.toList());
        for (int i = 0; i < collect.size(); i++) {
            if (Objects.equals(time, collect.get(i)) && i + offset >= 0 && i + offset < timeList.size()) {
                return collect.get(i + offset);
            }
        }
        return time;
    }

    /*
     * 计算平均过车时间，单位：s
     * @param mesPassPointVo1 当前电泳池的过车信息
     * @param mesPassPointVo2 下一个电泳池的过车信息
     * */
    private Long dealAveragePassTime(List<MesPassPointVo> mesPassPointVo1, List<MesPassPointVo> mesPassPointVo2) {
        mesPassPointVo1 = mesPassPointVo1.stream().sorted(Comparator.comparing(MesPassPointVo::getPasstime)).collect(Collectors.toList());
        mesPassPointVo2 = mesPassPointVo2.stream().sorted(Comparator.comparing(MesPassPointVo::getPasstime)).collect(Collectors.toList());
        Long res = 0L;
        for (MesPassPointVo item : mesPassPointVo1) {
            //根据vin和skidno过滤同一工件的过车信息，应该只有一个
            List<Long> collect = mesPassPointVo2.stream().filter(x ->
                    Objects.equals(x.getSkidno(), item.getSkidno()) && Objects.equals(x.getVin(), item.getVin())
            ).map(MesPassPointVo::getPasstime).collect(Collectors.toList());
            if (Objects.isNull(collect) || collect.isEmpty()) {
                log.info("{}：没有找到vin：{}，skid：{}，在电泳池：{}之后的过车信息，跳过", LOG_KEY, item.getVin(), item.getSkidno(), item.getStationcode());
                continue;
            }
            res = res + (collect.get(0) - item.getPasstime()) / 1000;
        }
        return res / mesPassPointVo1.size();
    }

    /*
     * 将本次的平均过车时间 与 数据库中的记录，进行比较
     * 当新记录属于[旧记录的0.8，旧记录的1.2]时，更新；反之不动
     * */
    private List<AveragePassTimeVo> compareAveragePassTime(List<AveragePassTimeVo> nowData) {
        List<AveragePassTimeVo> oldData = queryAveragePassTime();
        List<AveragePassTimeVo> res = new ArrayList<>();
        if (Objects.isNull(oldData) || oldData.isEmpty()) {
            return nowData;
        }
        for (AveragePassTimeVo item : nowData) {
            Optional<AveragePassTimeVo> any = oldData.stream().filter(x -> Objects.equals(x.getStationcode(), item.getStationcode())).findAny();
            if (!any.isPresent()) {
                //如果该电泳池没有历史记录，则直接插入
                res.add(item);
                continue;
            }
            AveragePassTimeVo averagePassTimeVo = any.get();
            //当新记录属于[旧记录的0.8，旧记录的1.2]时，更新；反之不动
            if (item.getAveragetime() >= averagePassTimeVo.getAveragetime() * lowLimit
                    && item.getAveragetime() <= averagePassTimeVo.getAveragetime() * upperLimit) {
                res.add(item);
            } else {
                res.add(averagePassTimeVo);
            }
        }
        return res;
    }
}

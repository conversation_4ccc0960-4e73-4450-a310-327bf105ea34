package com.cet.eem.bll.datatransport.task;

import com.cet.eem.bll.datatransport.handle.MesDataHandle;
import com.cet.eem.common.utils.TimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;

@Component
@Configuration
@EnableScheduling
public class MesDataTransport {
    @Autowired
    MesDataHandle mesDataHandle;

    @Value("${cet.eem.mes.passpoint.interval:3}")
    public int interval;

    @Scheduled(cron = "${cet.eem.mes.cron:-}")
    public void transportData() throws IOException {
        mesDataHandle.transport(TimeUtil.timestamp2LocalDateTime(System.currentTimeMillis()));
    }
    @Scheduled(cron = "${cet.eem.mes.special.cron:-}")
    public void transportSpecialData() throws IOException {
        mesDataHandle.transportSpecial(TimeUtil.timestamp2LocalDateTime(System.currentTimeMillis()));
    }
    @Scheduled(cron = "${cet.eem.mes.passpoint.cron:-}")
    public void transportMesPassPointData() throws Exception {
        Long endTime = TimeUtil.getFirstTimeOfDay(System.currentTimeMillis());
        mesDataHandle.transportMesPassPoint(endTime-86400000L, endTime);
        mesDataHandle.calcAveragePassTime(endTime-86400000L * interval, endTime);
    }
}

package com.cet.eem.bll.energysaving.model.cetml;

import com.cet.eem.common.definition.ColumnDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName : PumpFitObjectData
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-06-29 17:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PumpFitObjectData {
    @JsonProperty(ColumnDef.OBJECT_ID)
    private Long objectId;
    @JsonProperty("pump_fit_data")
    private List<PumpFitData> pumpFitData;
}
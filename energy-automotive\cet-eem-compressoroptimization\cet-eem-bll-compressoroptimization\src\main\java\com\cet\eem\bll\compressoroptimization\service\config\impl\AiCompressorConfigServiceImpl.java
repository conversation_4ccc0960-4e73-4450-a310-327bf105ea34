package com.cet.eem.bll.compressoroptimization.service.config.impl;

import com.cet.eem.bll.common.dao.node.NodeDao;
import com.cet.eem.bll.common.model.ext.modelentity.EemQueryCondition;
import com.cet.eem.bll.compressoroptimization.dao.CompressorConfigDao;
import com.cet.eem.bll.compressoroptimization.dao.CompressorSystemDao;
import com.cet.eem.bll.compressoroptimization.model.config.CompressorConfig;
import com.cet.eem.bll.compressoroptimization.model.config.CompressorSystem;
import com.cet.eem.bll.compressoroptimization.service.config.AiCompressorConfigService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.constant.EnumOperationType;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.base.ConditionBlock;
import com.cet.eem.model.base.ConditionBlockCompose;
import com.cet.eem.model.base.SingleModelConditionDTO;
import com.cet.eem.model.tool.ModelServiceUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName : AiCompressorConfigServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-20 15:56
 */
@Service
public class AiCompressorConfigServiceImpl implements AiCompressorConfigService {
    @Autowired
    CompressorSystemDao compressorSystemDao;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    NodeDao nodeDao;
    @Autowired
    CompressorConfigDao compressorConfigDao;
    public static final Integer COMPRESSOR = 4;

    @Override
    public CompressorSystem querySystemUseAi(Long systemId, Long projectId) {
        CompressorSystem system = compressorSystemDao.queryCompressorSystem(systemId, projectId);
        if (Objects.isNull(system)) {
            return new CompressorSystem();
        }
        return system;
    }

    @Override
    public List<BaseVo> getRoom(Long projectId) {
        //根据项目id查询房间
        List<BaseVo> baseVos = modelServiceUtils.query(createQueryCondition(projectId), BaseVo.class);
        if (CollectionUtils.isNotEmpty(baseVos) && CollectionUtils.isNotEmpty(baseVos.get(0).getChildren())) {
            return baseVos.get(0).getChildren();

        }
        return Collections.emptyList();
    }

    /**
     * 查询空压机房
     *
     * @param projectId
     * @return
     */
    private EemQueryCondition createQueryCondition(Long projectId) {
        EemQueryCondition queryCondition = new EemQueryCondition();
        queryCondition.setRootID(projectId);
        queryCondition.setRootLabel(NodeLabelDef.PROJECT);

        SingleModelConditionDTO dto = new SingleModelConditionDTO();
        dto.setModelLabel(NodeLabelDef.ROOM);

        ConditionBlockCompose filter = new ConditionBlockCompose();
        ConditionBlock block = new ConditionBlock(ColumnDef.ROOM_TYPE, ConditionBlock.OPERATOR_EQ, COMPRESSOR);
        filter.setExpressions(Collections.singletonList(block));
        dto.setFilter(filter);
        queryCondition.setSubLayerConditions(Collections.singletonList(dto));
        queryCondition.setTreeReturnEnable(true);
        return queryCondition;
    }

    /**
     * 根据项目id查询空压机房和底下的设备
     *
     * @param projectId
     * @return
     */
    private List<BaseVo> getRoomWithDevice(Long projectId) {
        EemQueryCondition queryCondition = createQueryCondition(projectId);
        SingleModelConditionDTO air = new SingleModelConditionDTO();
        air.setModelLabel(NodeLabelDef.AIR_COMPRESSOR);
        queryCondition.setSubLayerConditions(Arrays.asList(queryCondition.getSubLayerConditions().get(0), air));
        List<BaseVo> baseVos = modelServiceUtils.query(queryCondition, BaseVo.class);
        //取第一个空压机房的设备
        if (CollectionUtils.isNotEmpty(baseVos) && CollectionUtils.isNotEmpty(baseVos.get(0).getChildren())) {
            return baseVos.get(0).getChildren().get(0).getChildren();

        }
        return Collections.emptyList();
    }

    /**
     * 根据房间id获取空压机信息
     *
     * @param roomId
     * @return
     */
    private List<BaseVo> getDevice(Long roomId) {
        EemQueryCondition queryCondition = new EemQueryCondition();
        queryCondition.setRootID(roomId);
        queryCondition.setRootLabel(NodeLabelDef.ROOM);
        SingleModelConditionDTO air = new SingleModelConditionDTO();
        air.setModelLabel(NodeLabelDef.AIR_COMPRESSOR);
        queryCondition.setSubLayerConditions(Collections.singletonList(air));
        queryCondition.setTreeReturnEnable(true);
        List<BaseVo> baseVos = modelServiceUtils.query(queryCondition, BaseVo.class);
        if (CollectionUtils.isNotEmpty(baseVos) && CollectionUtils.isNotEmpty(baseVos.get(0).getChildren())) {
            return baseVos.get(0).getChildren();

        }
        return Collections.emptyList();
    }

    @Override
    public List<CompressorSystem> queryCompressorUseAi(Long systemId, Long projectId) {
        List<BaseVo> compressors = new ArrayList<>();
        Long roomId = null;
        //分成有systemid和没有systemid的情况，有systemid针对的是多空压机系统，目前按单个空压机系统，只需要传入项目id
        if (Objects.isNull(systemId)) {
            //查询空压机房
            compressors = getRoomWithDevice(projectId);
        } else {
            CompressorSystem compressorSystem = compressorSystemDao.queryCompressorSystem(systemId, projectId);
            if (Objects.nonNull(compressorSystem)) {
                roomId = compressorSystem.getObjectId();
                compressors = getDevice(roomId);
            }
        }
        if (CollectionUtils.isEmpty(compressors)) {
            return Collections.emptyList();
        }
        //查询空压机的智控ai详情
        List<CompressorSystem> compressorSystems = compressorSystemDao.queryCompressorDevice(compressors, projectId);
        if (CollectionUtils.isEmpty(compressorSystems)) {
            //没有数据，根据空压机信息进行拼接
            return assembleCompressorSystemWithDevice(compressors, projectId);

        }
        //拼接名称
        return assembleCompressorWithDevice(compressorSystems, compressors, projectId);
    }

    private List<CompressorSystem> assembleCompressorSystemWithDevice(List<BaseVo> compressors, Long projectId) {
        List<CompressorSystem> compressorSystems = new ArrayList<>();
        List<BaseVo> baseVos = nodeDao.queryNodeName(compressors);
        for (BaseVo baseVo : baseVos) {
            CompressorSystem system = new CompressorSystem(projectId, null, baseVo.getId(), baseVo.getModelLabel(), baseVo.getName());
            compressorSystems.add(system);
        }
        return compressorSystems;
    }

    private List<CompressorSystem> assembleCompressorWithDevice(List<CompressorSystem> compressorSystems, List<BaseVo> compressors, Long projectId) {
        List<CompressorSystem> result = new ArrayList<>();
        List<BaseVo> baseVos = nodeDao.queryNodeName(compressors);
        Map<BaseVo, CompressorSystem> compressorSystemMap = compressorSystems.stream().collect(Collectors.toMap(compressorSystem -> new BaseVo(compressorSystem.getObjectId(), compressorSystem.getObjectLabel()),
                Function.identity()));
        for (BaseVo baseVo : baseVos) {
            CompressorSystem system = compressorSystemMap.get(baseVo);
            if (Objects.isNull(system)) {
                system = new CompressorSystem(projectId, false, baseVo.getId(), baseVo.getModelLabel(), baseVo.getName());
            } else {
                system.setName(baseVo.getName());
            }
            result.add(system);
        }
        return result;
    }

    @Override
    public CompressorConfig queryCompressorConfig(Long systemId, Long projectId) {
        CompressorConfig compressorConfig = compressorConfigDao.queryCompressorConfig(systemId, projectId);
        if (Objects.isNull(compressorConfig)) {
            return new CompressorConfig();
        }
        transCompressorConfig(compressorConfig, true,true);
        transCompressorConfig(compressorConfig, true,false);
        return compressorConfig;
    }

    private void transCompressorConfig(CompressorConfig compressorConfig, Boolean ifQuery,Boolean isNormalConfig) {
        int id = EnumOperationType.DIVISION.getId();
        if (Boolean.FALSE.equals(ifQuery)) {
            id = EnumOperationType.MULTIPLICATION.getId();
        }
        if (Boolean.TRUE.equals(isNormalConfig)){
            //s
            compressorConfig.setSystemPressureMinCheckTime(CommonUtils.calcLong(
                    compressorConfig.getSystemPressureMinCheckTime(), TimeUtil.SECOND, id));
            compressorConfig.setSystemPressureMaxCheckTime(CommonUtils.calcLong(
                    compressorConfig.getSystemPressureMaxCheckTime(), TimeUtil.SECOND, id));
            //min
            compressorConfig.setScrewTurnIntervalTime(CommonUtils.calcLong(
                    compressorConfig.getScrewTurnIntervalTime(), TimeUtil.MINUTE, id));
            //h
            compressorConfig.setScrewOperationSwitchTime(CommonUtils.calcLong(
                    compressorConfig.getScrewOperationSwitchTime(), TimeUtil.HOUR, id));
            //min
            compressorConfig.setCentrifugalTurnIntervalTime(CommonUtils.calcLong(
                    compressorConfig.getCentrifugalTurnIntervalTime(), TimeUtil.MINUTE, id));
            //h
            compressorConfig.setCentrifugalOperationSwitchTime(CommonUtils.calcLong(
                    compressorConfig.getCentrifugalOperationSwitchTime(), TimeUtil.HOUR, id));
        }else {
            //s
            compressorConfig.setScrewStartIntervalTime(CommonUtils.calcLong(
                    compressorConfig.getScrewStartIntervalTime(), TimeUtil.SECOND, id));
            compressorConfig.setScrewStopIntervalTime(CommonUtils.calcLong(
                    compressorConfig.getScrewStopIntervalTime(), TimeUtil.SECOND, id));
            compressorConfig.setScrewUnloadingStopProtection(CommonUtils.calcLong(
                    compressorConfig.getScrewUnloadingStopProtection(), TimeUtil.SECOND, id));
            compressorConfig.setCentrifugalStopIntervalTime(CommonUtils.calcLong(
                    compressorConfig.getCentrifugalStopIntervalTime(), TimeUtil.SECOND, id));
            compressorConfig.setCentrifugalStartIntervalTime(CommonUtils.calcLong(
                    compressorConfig.getCentrifugalStartIntervalTime(), TimeUtil.SECOND, id));
        }



    }

    @Override
    public CompressorSystem editSystemUseAi(CompressorSystem compressorSystem, Long projectId) {
        //分新增和编辑
        if (Objects.isNull(compressorSystem.getId())) {
            //新增
            List<BaseVo> room = getRoom(projectId);
            if (CollectionUtils.isNotEmpty(room)) {
                CompressorSystem system = new CompressorSystem(projectId, compressorSystem.getUseAi(), room.get(0).getId(), NodeLabelDef.ROOM);
                compressorSystemDao.insert(system);
            }
        } else {
            CompressorSystem system = compressorSystemDao.queryCompressorSystem(compressorSystem.getId(), projectId);
            system.setUseAi(compressorSystem.getUseAi());
            compressorSystemDao.updateById(system);
        }
        return compressorSystemDao.queryCompressorSystem(compressorSystem.getId(), projectId);
    }

    @Override
    public List<CompressorSystem> editCompressorUseAi(List<CompressorSystem> compressorControls, Long projectId) {
        if (CollectionUtils.isEmpty(compressorControls)) {
            return Collections.emptyList();
        }
        List<BaseVo> nodes = compressorControls.stream().map(compressorSystem -> new BaseVo(compressorSystem.getObjectId(), compressorSystem.getObjectLabel()))
                .distinct().collect(Collectors.toList());
        if (Objects.isNull(compressorControls.get(0).getId())) {
            modelServiceUtils.writeData(compressorControls);
        } else {

            List<CompressorSystem> compressorSystems = compressorSystemDao.queryCompressorDevice(nodes, projectId);
            updateCompressorSystem(compressorSystems, compressorControls);
            modelServiceUtils.writeData(compressorSystems);
        }
        return compressorSystemDao.queryCompressorDevice(nodes, projectId);
    }

    @Override
    public CompressorConfig editCompressorConfig(CompressorConfig compressorConfig, Boolean isNormalConfig, Long projectId) {
        if (Objects.isNull(compressorConfig.getId())) {
            //新增
            compressorConfig.setProjectId(projectId);
            //还需要查询systemid
            CompressorSystem system = compressorSystemDao.queryCompressorSystem(null, projectId);
            compressorConfig.setSystemId(system.getId());
            transCompressorConfig(compressorConfig, false,isNormalConfig);
            compressorConfigDao.insert(compressorConfig);
        } else {
            //普通配置--true是普通配置
            CompressorConfig config = compressorConfigDao.queryCompressorConfig(compressorConfig.getSystemId(), projectId);
            if (Boolean.TRUE.equals(isNormalConfig)) {
                config.setSystemPressureMin(compressorConfig.getSystemPressureMin());
                config.setSystemPressureMax(compressorConfig.getSystemPressureMax());
                config.setSystemPressureMinCheckTime(compressorConfig.getSystemPressureMinCheckTime());
                config.setSystemPressureMaxCheckTime(compressorConfig.getSystemPressureMaxCheckTime());
                config.setSystemPressureMaxAlarm(compressorConfig.getSystemPressureMaxAlarm());
                config.setSystemPressureMinAlarm(compressorConfig.getSystemPressureMinAlarm());
                config.setScrewTurnIntervalTime(compressorConfig.getScrewTurnIntervalTime());
                config.setScrewOperationSwitchTime(compressorConfig.getScrewOperationSwitchTime());
                config.setScrewLowPressureAlarm(compressorConfig.getScrewLowPressureAlarm());
                config.setScrewHighPressureAlarm(compressorConfig.getScrewHighPressureAlarm());
                config.setCentrifugalTurnIntervalTime(compressorConfig.getCentrifugalTurnIntervalTime());
                config.setCentrifugalOperationSwitchTime(compressorConfig.getCentrifugalOperationSwitchTime());
                config.setCentrifugalLowPressureAlarm(compressorConfig.getCentrifugalLowPressureAlarm());
                config.setCentrifugalHighPressureAlarm(compressorConfig.getCentrifugalHighPressureAlarm());

            } else {
                config.setScrewStartIntervalTime(compressorConfig.getScrewStartIntervalTime());
                config.setScrewStopIntervalTime(compressorConfig.getScrewStopIntervalTime());
                config.setScrewUnloadingStopProtection(compressorConfig.getScrewUnloadingStopProtection());
                config.setCentrifugalStartIntervalTime(compressorConfig.getCentrifugalStartIntervalTime());
                config.setCentrifugalStopIntervalTime(compressorConfig.getCentrifugalStopIntervalTime());
            }
            transCompressorConfig(config, false,isNormalConfig);
            compressorConfigDao.updateById(config);
        }
        CompressorConfig config = compressorConfigDao.queryCompressorConfig(compressorConfig.getSystemId(), projectId);
        transCompressorConfig(config, true,true);
        transCompressorConfig(config, true,false);
        return config;
    }

    private void updateCompressorSystem(List<CompressorSystem> old, List<CompressorSystem> compressorControls) {
        for (CompressorSystem system : old) {
            Optional<CompressorSystem> any = compressorControls.stream().filter(compressorSystem -> Objects.equals(compressorSystem.getObjectId(), system.getObjectId())
                    && Objects.equals(compressorSystem.getObjectLabel(), system.getObjectLabel()))
                    .findAny();
            any.ifPresent(compressorSystem -> system.setUseAi(compressorSystem.getUseAi()));
        }

    }

}
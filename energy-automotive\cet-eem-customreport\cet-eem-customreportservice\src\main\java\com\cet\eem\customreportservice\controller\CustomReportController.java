package com.cet.eem.customreportservice.controller;

import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.common.model.Result;
import com.cet.eem.customreport.model.ReportData;
import com.cet.eem.customreport.model.ReportParam;
import com.cet.eem.customreport.service.CustomReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/6
 * @Descruption 定制报表
 */

@Api(value = "CustomReportController", tags = "定制报表")
@RequestMapping(value = "/eem/v1/report")
@RestController
public class CustomReportController {
    @Autowired
    CustomReportService customReportService;

    @ApiOperation(value = "查询报表")
    @PostMapping("/reportByTime")
    public Result<List<ReportData>> queryReport(@RequestBody ReportParam reportParam) {
        return Result.ok(customReportService.queryReport(reportParam,GlobalInfoUtils.getUserId(), GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "导出报表")
    @PostMapping("/export")
    public void exportReport(@RequestBody ReportParam reportParam) {
        customReportService.exportReport(reportParam, GlobalInfoUtils.getUserId(), GlobalInfoUtils.getProjectId());
    }
}

package com.cet.eem.fusion.transformer.core.utils;

import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.utils.TimeUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName : DateUtil
 * <AUTHOR> yangy
 * @Date: 2022-03-15 17:24
 */
public class TransformerDateUtil {
    public static void main(String[] args) {
        LocalDateTime firstDayOfNextMonth = TimeUtil.getFirstDayOfNextMonth(LocalDateTime.now());
        List<LocalDateTime> timeRange = TimeUtil.getTimeRange(TimeUtil.getFirstDayOfThisMonth(LocalDateTime.now()), TimeUtil.getFirstDayOfNextMonth(LocalDateTime.now()), 12);
        System.out.println(timeRange);

    }

    public static LocalDateTime getFirstTimeOfLastDay(LocalDate localDate) {
        localDate = localDate.plusDays(-1);
        return LocalDateTime.of(localDate, LocalTime.MIN);
    }

    public static List<Long> getTimeRange(long st, long et, Integer cycle) {
        LocalDateTime startTime = TimeUtil.timestamp2LocalDateTime(st);
        LocalDateTime endTime = TimeUtil.timestamp2LocalDateTime(et);
        List<LocalDateTime> localDateTimes = generateTimeValueOfPeriod(startTime, endTime, cycle);
        List<Long> result = new ArrayList<>();
        for (LocalDateTime localDateTime : localDateTimes) {
            result.add(TimeUtil.localDateTime2timestamp(localDateTime));
        }
        return result;
    }

    public static List<LocalDateTime> generateTimeValueOfPeriod(LocalDateTime startTime, LocalDateTime endTime, Integer cycle) {
        List<LocalDateTime> result = new ArrayList<>();
        if (cycle == AggregationCycle.ONE_YEAR) {
            for (LocalDateTime i = startTime; i.isBefore(endTime) || i.equals(endTime); i = i.plusYears(1L)) {
                result.add(i);
            }
        } else if (cycle == AggregationCycle.ONE_MONTH) {
            for (LocalDateTime i = startTime; i.isBefore(endTime) || i.equals(endTime); i = i.plusMonths(1L)) {
                result.add(i);
            }
        } else if (cycle == AggregationCycle.ONE_DAY) {
            for (LocalDateTime i = startTime; i.isBefore(endTime) || i.equals(endTime); i = i.plusDays(1L)) {
                result.add(i);
            }
        }
        if (!result.contains(endTime)) {
            result.add(endTime);
        }
        return result;
    }
}

package com.cet.eem.bll.datatransport.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cet.eem.bll.datatransport.model.mesdata.MesPassPointVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface MesPassPointMapper extends BaseMapper<MesPassPointVo>{

    @Select("select pass_time as time, skidno, vin, station_code as stationcode from avi_passpoint ap\n" +
            "where \n" +
            "ap.pass_time >=  #{startTime, jdbcType=TIMESTAMP} and \n" +
            "ap.pass_time < #{endTime, jdbcType=TIMESTAMP} and\n" +
            "ap.vin != '88888888888888888' and\n" +
            "ap.station_code = 'B02'")
    List<MesPassPointVo> queryMesPassPoint(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}

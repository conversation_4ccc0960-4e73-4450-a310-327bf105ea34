package com.cet.eem.bll.compressoroptimization.service.config.impl;

import com.cet.eem.bll.common.dao.node.EnergySupplyDao;
import com.cet.eem.bll.common.dao.node.MeasureByDao;
import com.cet.eem.bll.common.dao.node.NodeDao;
import com.cet.eem.bll.common.model.domain.object.physicalquantity.MeasuredbyVo;
import com.cet.eem.bll.common.model.ext.modelentity.EemQueryCondition;
import com.cet.eem.bll.common.model.node.relations.EnergySupplyToPo;
import com.cet.eem.bll.common.service.impl.PecCoreService;
import com.cet.eem.bll.compressoroptimization.dao.CompressorConfigDao;
import com.cet.eem.bll.compressoroptimization.def.AirCompressorAttrDef;
import com.cet.eem.bll.compressoroptimization.def.DataIdInfoUnitEnum;
import com.cet.eem.bll.compressoroptimization.model.trend.*;
import com.cet.eem.bll.compressoroptimization.service.config.AiCompressorConfigService;
import com.cet.eem.bll.compressoroptimization.service.config.RunningTrendService;
import com.cet.eem.bll.fegin.DatalogMeasItemDTO;
import com.cet.eem.bll.fegin.PecCoreConfigRestApi;
import com.cet.eem.common.ParamUtils;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.definition.SplitCharDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.model.datalog.TrendSearchVo;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.base.SingleModelConditionDTO;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.peccore.model.datalog.DataLogSearchDto;
import com.cet.eem.peccore.service.TrendService;
import com.cet.electric.commons.ApiResult;
//import com.cet.electric.matterhorn.devicedataservice.api.PecCoreConfigRestApi;
//import com.cet.electric.matterhorn.devicedataservice.common.entity.station.DatalogMeasItemDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : RunningTrendServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-25 09:29
 */
@Service
public class RunningTrendServiceImpl implements RunningTrendService {
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    NodeDao nodeDao;
    @Autowired
    CompressorConfigDao compressorConfigDao;
    @Autowired
    EnergySupplyDao energySupplyDao;
    @Autowired
    MeasureByDao measureByDao;
    @Autowired
    PecCoreService pecCoreService;
//    @Autowired
//    PecCoreConfigService pecCoreConfigService;

    @Autowired
    PecCoreConfigRestApi pecCoreConfigService;
    @Autowired
    protected TrendService trendService;
    @Autowired
    AiCompressorConfigService aiCompressorConfigService;
    public static final Integer COMPRESSOR = 4;
    public static final Set<Integer> PIPE_DATA_ID = new HashSet<>(Arrays.asList(6007211, 6004024, 6008032));
    public static final Set<Integer> SCREW_DATA_ID = new HashSet<>(Arrays.asList(6007210,
            6008031, 6000749, 6008038, 6008036, 6000561, 6000750, 6000751, 6000752, 6000753, 6000756, 6000757, 6000758)
    );
    public static final Set<Integer> CENTER_DATA_ID = new HashSet<>(Arrays.asList(6000747, 6000748, 6007210,
            6008031, 6000749, 6008038, 6008036, 6000561, 6000750, 6000751, 6000752, 6000753, 6000754, 6000755, 6000756, 6000757, 6000758)
    );


    @Override
    public List<BaseVo> queryCompressorRoom(Long projectId) {
        List<BaseVo> room = aiCompressorConfigService.getRoom(projectId);
        if (CollectionUtils.isNotEmpty(room)) {
            return room;

        }
        return Collections.emptyList();
    }

    @Override
    public List<BaseVoWithDataId> queryTreeWithDataId(Long roomId, Long projectId, Long time) {
        //查询总管--关联房间 设备组属性增加treeid字段，设置为       dataTypeId_dataId 普通的是label——id
        BaseVo pipeLine = null;
        List<EnergySupplyToPo> energySupplyToPos = energySupplyDao.queryEnergySupplyTo(
                Collections.singletonList(new BaseVo(roomId, NodeLabelDef.ROOM)), time, EnergySupplyToPo.class);
        if (CollectionUtils.isNotEmpty(energySupplyToPos)) {
            Optional<BaseVo> any = energySupplyToPos.stream().filter(energySupplyToPo -> Objects.equals(energySupplyToPo.getObjectlabel(), NodeLabelDef.PIPELINE))
                    .map(energySupplyToPo -> new BaseVo(energySupplyToPo.getObjectid(), energySupplyToPo.getObjectlabel())).findAny();
            if (any.isPresent()) {
                pipeLine = any.get();
            }
        }
        //查询空压机
        List<AirCompressorVo> airs = getDevice(roomId);
        List<BaseVo> allNodes = getAllNodes(pipeLine, airs);
        //根据节点查询关联的表计
        List<MeasuredbyVo> measuredbyVos = measureByDao.queryMeasureBy(allNodes);
        Map<Integer, List<DatalogMeasItemDTO>> dataIdMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(measuredbyVos)) {
            //查询表计的测点信息
            List<Integer> deviceIds = measuredbyVos.stream().map(x -> Integer.valueOf(x.getMeasuredby().intValue())).distinct().collect(Collectors.toList());
            ApiResult<Map<Integer, List<DatalogMeasItemDTO>>> deviceDatalogPoints = pecCoreConfigService.getDeviceDatalogPoints(deviceIds,null);
            ParamUtils.checkResultGeneric(deviceDatalogPoints);
            dataIdMap = deviceDatalogPoints.getData();
        }
        Map<BaseVo, List<MeasuredbyVo>> measureMap = measuredbyVos.stream().collect(Collectors
                .groupingBy(measuredbyVo -> new BaseVo(measuredbyVo.getMonitoredid(), measuredbyVo.getMonitoredlabel())));
        //分组后处理，拼接父节点是管道or空压机节点，子节点是测点信息
        BaseVoWithDataId baseVoWithDataId = assemblePipeLine(pipeLine, measureMap.get(pipeLine), dataIdMap);
        List<BaseVoWithDataId> baseVoWithDataIds = assemblePipeLine(airs, measureMap, dataIdMap);
        if (Objects.isNull(baseVoWithDataId) && CollectionUtils.isEmpty(baseVoWithDataIds)) {
            return Collections.emptyList();
        } else if (Objects.isNull(baseVoWithDataId)) {
            return baseVoWithDataIds;
        } else if (CollectionUtils.isEmpty(baseVoWithDataIds)) {
            return Collections.singletonList(baseVoWithDataId);
        }
        baseVoWithDataIds.add(0, baseVoWithDataId);
        return baseVoWithDataIds;
    }

    /**
     * 根据房间id查询空压机，增加空压机类型的字段
     * @param roomId
     * @return
     */
    private List<AirCompressorVo> getDevice(Long roomId) {
        EemQueryCondition queryCondition = new EemQueryCondition();
        queryCondition.setRootID(roomId);
        queryCondition.setRootLabel(NodeLabelDef.ROOM);
        SingleModelConditionDTO air = new SingleModelConditionDTO();
        air.setModelLabel(NodeLabelDef.AIR_COMPRESSOR);
        queryCondition.setSubLayerConditions(Collections.singletonList(air));
        queryCondition.setTreeReturnEnable(true);
        List<AirCompressorVo> baseVos = modelServiceUtils.query(queryCondition, AirCompressorVo.class);
        if (CollectionUtils.isNotEmpty(baseVos) && CollectionUtils.isNotEmpty(baseVos.get(0).getChildren())) {
            return baseVos.get(0).getChildren();

        }
        return Collections.emptyList();
    }

    @Override
    public List<TrendDataVoWithUnit> queryTrendData(CompressorTrendSearchVo searchDto, Long projectId) {
        //调用趋势曲线查询的内容
        List<TrendDataVo> result = trendService.queryDataLogs(assembleTrendDataDto(searchDto));
        List<CompressorDataIdSearchVo> meterConfigs = searchDto.getMeterConfigs();
        List<TrendDataVoWithUnit> dataVoWithUnits = new ArrayList<>();
        for (TrendDataVo dataVo : result) {
            TrendDataVoWithUnit dataVoWithUnit = new TrendDataVoWithUnit();
            BeanUtils.copyProperties(dataVo, dataVoWithUnit);
            Optional<CompressorDataIdSearchVo> any = meterConfigs.stream()
                    .filter(compressorDataIdSearchVo -> Objects.equals(compressorDataIdSearchVo.getDataId(), dataVo.getDataId())
                            && Objects.equals(compressorDataIdSearchVo.getDeviceId(), dataVo.getDeviceId())).findAny();
            if (any.isPresent()) {
                String value = DataIdInfoUnitEnum.valueOf(dataVo.getDataId().intValue());
                //拼接名称，父节点名称+测点名称+单位
                if (Objects.nonNull(value) && StringUtils.isNotEmpty(value)) {
                    value = "(" + value + ")";
                }
                dataVoWithUnit.setName(any.get().getParentName() + "-" + any.get().getName() + value);
            }
            dataVoWithUnits.add(dataVoWithUnit);
        }
        return dataVoWithUnits;
    }

    private DataLogSearchDto assembleTrendDataDto(CompressorTrendSearchVo searchDto) {
        DataLogSearchDto dto = new DataLogSearchDto();
        dto.setCacheMonthNumber(0);
        dto.setDbInterval(0);
        dto.setCacheInterval(0);
        dto.setInterval(0);
        //2023-07-26 00:00:00
        dto.setStartTime(TimeUtil.format(searchDto.getStartTime(), TimeUtil.LONG_TIME_FORMAT));
        dto.setEndTime(TimeUtil.format(searchDto.getEndTime(), TimeUtil.LONG_TIME_FORMAT));
        List<CompressorDataIdSearchVo> searchVos = searchDto.getMeterConfigs();
        List<TrendSearchVo> meterConfigs = new ArrayList<>();
        for (CompressorDataIdSearchVo searchVo : searchVos) {
            TrendSearchVo searchVo1 = new TrendSearchVo();
            BeanUtils.copyProperties(searchVo, searchVo1);
            meterConfigs.add(searchVo1);
        }
        dto.setMeterConfigs(meterConfigs);
        return dto;
    }

    private List<BaseVoWithDataId> assemblePipeLine(List<AirCompressorVo> airs, Map<BaseVo, List<MeasuredbyVo>> measureMap,
                                                    Map<Integer, List<DatalogMeasItemDTO>> dataIdMap) {
        if (measureMap.isEmpty() || dataIdMap.isEmpty() || CollectionUtils.isEmpty(airs)) {
            return Collections.emptyList();
        }
        List<BaseVoWithDataId> result = new ArrayList<>();
        for (AirCompressorVo air : airs) {
            List<MeasuredbyVo> measuredbyVos = measureMap.get(new BaseVo(air.getId(), air.getModelLabel()));
            if (CollectionUtils.isEmpty(measuredbyVos)) {
                continue;
            }
            Set<Long> deviceIds = measuredbyVos.stream().map(MeasuredbyVo::getMeasuredby).collect(Collectors.toSet());

            Set<Integer> dataIdList = new HashSet<>();
            //区分不同类型的空压机，对应不同的测点信息
            if (Objects.equals(air.getAirCompressorAttr(), AirCompressorAttrDef.SCREW_MACHINE)) {
                dataIdList = SCREW_DATA_ID;
            } else if (Objects.equals(air.getAirCompressorAttr(), AirCompressorAttrDef.CENTRIFUGAL_MACHINE)) {
                dataIdList = CENTER_DATA_ID;
            }
            BaseVoWithDataId baseVoWithDataId = new BaseVoWithDataId();
            BeanUtils.copyProperties(air, baseVoWithDataId);
            baseVoWithDataId.setTreeId(air.getModelLabel() + SplitCharDef.UNDERLINE + air.getId());
            baseVoWithDataId.setMeterConfigs(assembleMeterConfigs(deviceIds, dataIdMap, dataIdList, baseVoWithDataId.getTreeId()));
            result.add(baseVoWithDataId);
        }
        return result;
    }

    private List<CompressorDataIdSearchVo> assembleMeterConfigs(Set<Long> deviceIds, Map<Integer, List<DatalogMeasItemDTO>> dataIdMap,
                                                                Set<Integer> dataIdList, String parentTreeId) {
        List<CompressorDataIdSearchVo> meterConfigs = new ArrayList<>();
        for (Long deviceId : deviceIds) {
            List<DatalogMeasItemDTO> measureNodeInfos = dataIdMap.get(deviceId);
            if (CollectionUtils.isEmpty(measureNodeInfos)) {
                continue;
            }
            List<DatalogMeasItemDTO> nodeInfos = measureNodeInfos.stream()
                    .filter(measureNodeInfo -> dataIdList.contains(measureNodeInfo.getDataId())).collect(Collectors.toList());
            for (DatalogMeasItemDTO info : nodeInfos) {
                CompressorDataIdSearchVo searchVo = new CompressorDataIdSearchVo();
                searchVo.setDataId(info.getDataId().longValue());
                searchVo.setDataTypeId(info.getDataTypeId());
                searchVo.setDeviceId(deviceId);
                searchVo.setLogicalId(info.getLogicalDeviceIndex());
                searchVo.setName(info.getParaName());
                searchVo.setTreeId(parentTreeId + SplitCharDef.UNDERLINE + searchVo.getDataTypeId() + SplitCharDef.UNDERLINE +
                        searchVo.getDeviceId() + SplitCharDef.UNDERLINE + searchVo.getDataId() + SplitCharDef.UNDERLINE + searchVo.getLogicalId());
                meterConfigs.add(searchVo);
            }
        }
        return meterConfigs;
    }

    private BaseVoWithDataId assemblePipeLine(BaseVo pipeLine, List<MeasuredbyVo> measuredbyVos, Map<Integer, List<DatalogMeasItemDTO>> dataIdMap) {
        if (CollectionUtils.isEmpty(measuredbyVos) || dataIdMap.isEmpty()) {
            return null;
        }
        List<BaseVo> baseVos = nodeDao.queryNodeName(Collections.singletonList(pipeLine));
        Set<Long> deviceIds = measuredbyVos.stream().map(MeasuredbyVo::getMeasuredby).collect(Collectors.toSet());
        BaseVoWithDataId baseVoWithDataId = new BaseVoWithDataId();
        BeanUtils.copyProperties(baseVos.get(0), baseVoWithDataId);
        baseVoWithDataId.setTreeId(pipeLine.getModelLabel() + SplitCharDef.UNDERLINE + pipeLine.getId());
        baseVoWithDataId.setMeterConfigs(assembleMeterConfigs(deviceIds, dataIdMap, PIPE_DATA_ID, baseVoWithDataId.getTreeId()));

        return baseVoWithDataId;
    }

    private List<BaseVo> getAllNodes(BaseVo pipeLine, List<AirCompressorVo> airs) {
        List<BaseVo> result = new ArrayList<>();
        if (Objects.nonNull(pipeLine)) {
            result.add(pipeLine);
        }
        if (CollectionUtils.isNotEmpty(airs)) {
            List<BaseVo> airList = airs.stream().map(airCompressorVo -> new BaseVo(airCompressorVo.getId(), airCompressorVo.getModelLabel()))
                    .distinct().collect(Collectors.toList());
            result.addAll(airList);
        }
        return result;
    }
}
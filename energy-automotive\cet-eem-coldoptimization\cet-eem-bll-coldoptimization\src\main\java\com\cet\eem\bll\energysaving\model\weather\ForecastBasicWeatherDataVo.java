package com.cet.eem.bll.energysaving.model.weather;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * AI预测基础数据
 *
 * <AUTHOR>
 * @date 2021/12/13
 */
@Getter
@Setter
@ApiModel("天气预测数据")
public class ForecastBasicWeatherDataVo {
    @ApiModelProperty("天气预报预测数据-小时")
    private List<ForecastWeather> forecastPredictWeathers;
    @ApiModelProperty("天气预报预测数据-15分钟")
    private List<ForecastWeather> predictWeathers;
    @ApiModelProperty("天气预报历史数据")
    private List<ForecastWeather> forecastActualWeathers;

    @ApiModelProperty("实测天气数据")
    private List<MeasureWeather> measureWeathers;

    @ApiModelProperty("项目id")
    private Long projectId;
}

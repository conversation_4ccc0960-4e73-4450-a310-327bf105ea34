package com.cet.eem.bll.energysaving.model.weather;



import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName : PumpVo
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-16 09:35
 */
@Getter
@Setter
public class PumpVo  {

    @NotNull
    protected Long id;
    @NotNull
    protected String modelLabel;

    protected String name;
    @JsonProperty("functiontype")
    private Integer functionType;
    private List<PumpVo> children;
    /**
     * 额定功率
     */
    @JsonProperty("ratedmotorpower")
    private Double ratedMotorPower;
    /**
     * 冷水机组属性
     */
    @JsonProperty("engineattr")
    private Integer engineAttr;
    /**
     * 额定制冷量
     */
    @JsonProperty("ratedrefrigeration")
    private Double ratedRefrigeration;
    /**
     * 额定风量
     */
    @JsonProperty("ratedairvolume")
    private Double ratedAirVolume;
}
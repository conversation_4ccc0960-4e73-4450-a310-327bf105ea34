package com.cet.eem.bll.compressoroptimization.model.config;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.bll.compressoroptimization.def.CompressorOptimizationLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : CompressorControl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-07-17 10:11
 */
@Getter
@Setter
@ModelLabel(CompressorOptimizationLabelDef.COMPRESSOR_CONTROL)
public class CompressorControl extends BaseEntity {
    @ApiModelProperty("是否使用ai预测")
    @JsonProperty(CompressorOptimizationLabelDef.USE_AI)
    private Boolean useAi;

    @ApiModelProperty("空压机id")
    @JsonProperty(ColumnDef.OBJECTID)
    private Long objectId;

    @JsonProperty(ColumnDef.OBJECTLABEL)
    private String objectLabel;

    public CompressorControl() {
        this.modelLabel = CompressorOptimizationLabelDef.COMPRESSOR_CONTROL;
    }
}
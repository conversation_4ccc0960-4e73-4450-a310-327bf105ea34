package com.cet.eem.energyevent.model.vo;

import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.energyevent.model.Constant;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/7/24 14:27
 */
@Getter
@Setter
public class ReferDataParam {
    private Long startTime;
    private Long endTime;
    private Long deviceId;
    private Long dataId;
    private Integer logicalId;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long logTime;
    private Integer eventType;
    private Integer status;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer interval;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer timeStep;

    public Integer getInterval() {
        if (Objects.isNull(interval)){
            return Constant.INTERVAL_FIFTEEN;
        }
        return interval;
    }

    public Integer getTimeStep() {
        if (Objects.isNull(timeStep)){
            return AggregationCycle.FIFTEEN_MINUTES;
        }
        return timeStep;
    }
}

package com.cet.eem.depletion.dao;

import com.cet.eem.depletion.model.DepletionConsumptionDto;

import java.util.List;

public interface DepletionconsumptionDao {
    List<DepletionConsumptionDto> queryByParam(Long st,Long end, Long id, String modelLabel);

    List<DepletionConsumptionDto> queryByParam(Long st, Long end, Long objId, String label, List<Integer> depletiontype);

    List<DepletionConsumptionDto> queryByDepletionid(List<Long> depletionids);
}

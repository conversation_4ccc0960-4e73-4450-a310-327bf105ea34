package com.cet.eem;

import com.cet.eem.model.param.RepairPointParam;
import com.cet.eem.model.vo.DataLogValue;
import com.cet.eem.service.impl.PointSupplyServiceImpl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/5 8:54
 */
public class TestMain {

    public static void main(String[] args) {
        Long startTime = 1722331200000L;
        Long endTime = 1722334200000L;
        Double startValue = 686d;
        Double endValue = 159d;

        List<DataLogValue> dataLogValues=new ArrayList<>();
        double value =1d;
        for (long time = 1720540800000L; time<=1720540800000L+24*60*60*1000L; time=time+5*60*1000){
            DataLogValue dataLogValue=new DataLogValue();
            dataLogValue.setTime(time);
            dataLogValue.setValue(value++);
            dataLogValues.add(dataLogValue);
        }
        RepairPointParam repairPointParam=new RepairPointParam();
        repairPointParam.setDataLogValues(dataLogValues);
        repairPointParam.setStartTime(1722268800000L);
        repairPointParam.setEndTime(1722355200000L);

        int i=1;
    }
}

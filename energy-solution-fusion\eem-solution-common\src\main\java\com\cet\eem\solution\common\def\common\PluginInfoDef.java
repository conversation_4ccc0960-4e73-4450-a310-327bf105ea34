package com.cet.eem.solution.common.def.common;

/**
 * <AUTHOR>  (2025/7/17 15:20)
 */
public class PluginInfoDef {
    /**
     * 产品名称
     */
    public static final String PRODUCT_NAME = "eem-solution";

    /**
     * demo插件相关常量定义
     */
    public static class Demo {
        /**
         * demo插件名称前缀
         */
        public static final String PLUGIN_NAME_PREFIX = "eem-solution-demo";
        /**
         * demo模块接口路径前缀
         */
        public static final String INTERFACE_PREFIX = "/eem/solution/demo";
    }

    /**
     * 空压系统插件相关常量定义
     */
    public static class AirCompress {
        /**
         * 空压系统插件名称前缀
         */
        public static final String PLUGIN_NAME_PREFIX = "eem-solution-air-compress";
        /**
         * 空压系统模块接口路径前缀
         */
        public static final String INTERFACE_PREFIX = "/eem/solution/air-compress";
    }

    /**
     * 变压器能效分析插件相关常量定义
     */
    public static class Transformer {
        /**
         * 变压器能效分析插件名称前缀
         */
        public static final String PLUGIN_NAME_PREFIX = "eem-solution-transformer";
        /**
         * 变压器能效分析模块接口路径前缀
         */
        public static final String INTERFACE_PREFIX = "/eem/solution/transformer";
    }
}

package com.cet.eem.bll.energysaving.model.aioptimization;

import com.cet.eem.bll.energysaving.model.def.ColdOptimizationLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : NodeWithPumpType
 * @Description : 携带泵类型
 * <AUTHOR> jiangzixuan
 * @Date: 2022-04-19 11:42
 */
@Getter
@Setter
public class NodeWithPumpType extends BaseVo {
    @JsonProperty(ColdOptimizationLabelDef.FUNCTION_TYPE)
    private Integer functionType;
}
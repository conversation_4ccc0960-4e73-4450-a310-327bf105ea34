package com.cet.eem.bll.achievementrate.dao.impl;

import com.cet.eem.bll.achievementrate.dao.ObjectCostValuePlanDao;
import com.cet.eem.bll.achievementrate.model.CostcheckitemVO;
import com.cet.eem.bll.achievementrate.model.data.ObjectCostValuePlan;
import com.cet.eem.bll.achievementrate.model.def.AchievementRateDataLabelDef;
import com.cet.eem.bll.common.model.domain.subject.generalrules.FeeScheme;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.model.BaseEntity;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : ObjectCostValuePlanDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-11-21 17:14
 */
@Repository
public class ObjectCostValuePlanDaoImpl extends ModelDaoImpl<ObjectCostValuePlan> implements ObjectCostValuePlanDao {
    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Override
    public List<ObjectCostValuePlan> query(Long st, Long et, Collection<Integer> energyType, Collection<BaseVo> nodes, Integer cycle) {
        if (CollectionUtils.isEmpty(nodes)) {
            return new ArrayList<>();
        }

        Map<String, List<BaseVo>> nodeMap = nodes.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        int group = 1;

        QueryConditionBuilder<BaseEntity> builder = new QueryConditionBuilder<>(AchievementRateDataLabelDef.PRODUCT_PLAN)
                .composeMethod(true)
                .orderBy(ColumnDef.LOG_TIME)
                .removeOrderById();
        for (Map.Entry<String, List<BaseVo>> entry : nodeMap.entrySet()) {
            String label = entry.getKey();
            List<Long> ids = nodeMap.get(label).stream().map(BaseVo::getId).distinct().collect(Collectors.toList());
            builder.eq(ColumnDef.C_OBJECT_Label, label, group)
                    .in(ColumnDef.C_OBJECT_ID, ids, group)
                    .ge(ColumnDef.LOG_TIME, st, group)
                    .lt(ColumnDef.LOG_TIME, et, group)
                    .in(ColumnDef.ENERGY_TYPE, energyType, group)
                    .eq(ColumnDef.AGGREGATION_CYCLE, cycle, group);
            group++;
        }

        return modelServiceUtils.queryWithRedis(builder.build(), ObjectCostValuePlan.class);
    }

    @Override
    public void insertObjectCostValuePlan(List<ObjectCostValuePlan> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        List<ObjectCostValuePlan> updateDataList = dataList.stream().filter(it -> !Objects.isNull(it.getId())).collect(Collectors.toList());
        updateDataList(updateDataList);

        List<ObjectCostValuePlan> addDataList = dataList.stream().filter(it -> Objects.isNull(it.getId())).collect(Collectors.toList());
        addDataList(addDataList);
    }

    @Override
    public void writeObjectCostValuePlan(List<ObjectCostValuePlan> plans) {
        if (CollectionUtils.isEmpty(plans)) {
            return;
        }

        HashSet<BaseVo> nodes = new HashSet<>();
        HashSet<Long> logTimes = new HashSet<>();
        HashSet<Integer> energyTypes = new HashSet<>();
        HashSet<Integer> cycles = new HashSet<>();
        for (ObjectCostValuePlan energyConsumption : plans) {
            nodes.add(new BaseVo(energyConsumption.getObjectId(), energyConsumption.getObjectLabel()));
            logTimes.add(energyConsumption.getLogTime());
            energyTypes.add(energyConsumption.getEnergyType());
            cycles.add(energyConsumption.getAggregationCycle());
        }
        List<ObjectCostValuePlan> oldObjectCostValuePlan = queryAllObjectCostValuePlan(nodes, logTimes, energyTypes, cycles);
        for (ObjectCostValuePlan energyConsumption : plans) {
            Optional<ObjectCostValuePlan> any = oldObjectCostValuePlan.stream().filter(it -> checkEqual(energyConsumption, it)).findAny();
            any.ifPresent(it -> energyConsumption.setId(it.getId()));
        }

        insertObjectCostValuePlan(plans);
    }

    @Override
    public List<FeeScheme> queryFeeSchemesByType(Integer type) {
        QueryConditionBuilder<BaseEntity> builder = QueryConditionBuilder.of(ModelLabelDef.FEE_SCHEME);
        if (Objects.nonNull(type)) {
            builder.eq(ColumnDef.ENERGY_TYPE, type);
        }
        return modelServiceUtils.query(builder.build(), FeeScheme.class);
    }

    @Override
    public List<CostcheckitemVO> queryCostCheckPlanByFeeSchemeIds(List<Long> feeIds) {
        if (CollectionUtils.isEmpty(feeIds)) {
            return Collections.emptyList();
        }
        QueryCondition condition = QueryConditionBuilder.of(ModelLabelDef.COST_CHECK_ITEM)
                .leftJoin(ModelLabelDef.COST_CHECK_PLAN)
                .in(ColumnDef.FEE_SCHEME_ID, feeIds)
                .build();
        return modelServiceUtils.query(condition, CostcheckitemVO.class);
    }

    @Override
    public List<ObjectCostValuePlan> query(LocalDateTime st, LocalDateTime end, Integer cycle, Integer energyType, List<BaseVo> nodes) {
        LambdaQueryWrapper<ObjectCostValuePlan> wrapper = LambdaQueryWrapper.of(ObjectCostValuePlan.class);
        if (CollectionUtils.isEmpty(nodes)) {
            if (Objects.nonNull(energyType)) {
                wrapper.eq(ObjectCostValuePlan::getEnergyType, energyType);
            }
            wrapper.ge(ObjectCostValuePlan::getLogTime, st).lt(ObjectCostValuePlan::getLogTime, end).eq(ObjectCostValuePlan::getAggregationCycle, cycle);
            return this.selectListRedisWithOutOrderById(wrapper);
        }
        Map<String, List<BaseVo>> nodeMap = nodes.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        for (Map.Entry<String, List<BaseVo>> entry : nodeMap.entrySet()) {
            List<BaseVo> baseVos = entry.getValue();
            List<Long> ids = baseVos.stream().map(BaseVo::getId).distinct().collect(Collectors.toList());
            wrapper.or(or -> {
                or.eq(ObjectCostValuePlan::getObjectLabel, entry.getKey())
                        .in(ObjectCostValuePlan::getObjectId, ids)
                        .ge(ObjectCostValuePlan::getLogTime, st)
                        .lt(ObjectCostValuePlan::getLogTime, end)
                        .eq(ObjectCostValuePlan::getAggregationCycle, cycle);
                if (Objects.nonNull(energyType)) {
                    or.eq(ObjectCostValuePlan::getEnergyType, energyType);
                }
            });
        }
        return this.selectListRedisWithOutOrderById(wrapper);
    }

    private boolean checkEqual(ObjectCostValuePlan newObj, ObjectCostValuePlan oldObj) {
        return Objects.equals(newObj.getObjectLabel(), oldObj.getObjectLabel()) &&
                Objects.equals(newObj.getObjectId(), oldObj.getObjectId()) &&
                Objects.equals(newObj.getLogTime(), oldObj.getLogTime()) &&
                Objects.equals(newObj.getAggregationCycle(), oldObj.getAggregationCycle()) &&
                Objects.equals(newObj.getEnergyType(), oldObj.getEnergyType());
    }

    private List<ObjectCostValuePlan> queryAllObjectCostValuePlan(@NotEmpty Collection<BaseVo> nodes, @NotEmpty Collection<Long> logTimes,
                                                                  @NotEmpty Collection<Integer> energyTypes, @NotEmpty Collection<Integer> cycles) {
        Map<String, List<BaseVo>> nodeMap = nodes.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        LambdaQueryWrapper<ObjectCostValuePlan> wrapper = LambdaQueryWrapper.of(ObjectCostValuePlan.class);
        nodeMap.forEach((label, nodeList) -> {
            Set<Long> ids = nodeList.stream().map(BaseVo::getId).collect(Collectors.toSet());
            wrapper.or(it -> it.eq(ObjectCostValuePlan::getObjectLabel, label)
                    .in(ObjectCostValuePlan::getObjectId, ids)
                    .in(ObjectCostValuePlan::getLogTime, logTimes)
                    .in(ObjectCostValuePlan::getAggregationCycle, cycles)
                    .in(ObjectCostValuePlan::getEnergyType, energyTypes));
        });

        return this.selectListRedisWithOutOrderById(wrapper);
    }

    private void addDataList(List<ObjectCostValuePlan> addDataList) {
        if (CollectionUtils.isEmpty(addDataList)) {
            return;
        }

        List<String> updateFields = Arrays.asList(ColumnDef.C_OBJECT_ID,
                ColumnDef.C_OBJECT_Label,
                ColumnDef.AGGREGATION_CYCLE,
                ColumnDef.ENERGY_TYPE,
                ColumnDef.LOG_TIME,
                ColumnDef.PROJECT_ID,
                ColumnDef.VALUE);
        List<List<Object>> writeDataList = new ArrayList<>();
        for (ObjectCostValuePlan data : addDataList) {
            writeDataList.add(Arrays.asList(data.getObjectId(),
                    data.getObjectLabel(),
                    data.getAggregationCycle(),
                    data.getEnergyType(),
                    data.getLogTime(),
                    data.getProjectId(),
                    data.getValue()));
        }
        modelServiceUtils.writeDataBatch(AchievementRateDataLabelDef.PRODUCT_PLAN, true, updateFields, writeDataList, null);
    }

    private void updateDataList(List<ObjectCostValuePlan> updateDataList) {
        if (CollectionUtils.isEmpty(updateDataList)) {
            return;
        }

        List<String> filterFields = Arrays.asList(ColumnDef.C_OBJECT_ID,
                ColumnDef.C_OBJECT_Label,
                ColumnDef.AGGREGATION_CYCLE,
                ColumnDef.ENERGY_TYPE,
                ColumnDef.LOG_TIME);

        List<String> updateFields = Collections.singletonList(
                ColumnDef.VALUE);
        List<List<Object>> writeDataList = new ArrayList<>();
        List<List<Object>> filterDataList = new ArrayList<>();
        for (ObjectCostValuePlan data : updateDataList) {
            writeDataList.add(Collections.singletonList(
                    data.getValue()));
            filterDataList.add(Arrays.asList(data.getObjectId(),
                    data.getObjectLabel(),
                    data.getAggregationCycle(),
                    data.getEnergyType(),
                    data.getLogTime()));
        }
        modelServiceUtils.writeDataBatch(AchievementRateDataLabelDef.PRODUCT_PLAN, false, updateFields, writeDataList, filterFields, filterDataList);
    }
}
package com.cet.eem.energyevent.model.pojo;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.energyevent.model.Constant;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/7/23 9:16
 */
@Getter
@Setter
@ModelLabel(Constant.ENERGYEVENT)
public class EnergyEvent  extends BaseEntity {
    @JsonProperty(Constant.LOGICALID)
    private Integer logicalId;
    @JsonProperty(Constant.EVENTTYPE)
    private Integer eventType;
    private String description;
    @JsonProperty(Constant.DATAID)
    private Long dataId;
    @JsonProperty(Constant.DEVICEID)
    private Long deviceId;
    @JsonProperty(Constant.EVENTTIME)
    private Long eventTime;
    @JsonProperty(Constant.STARTTIME)
    private Long startTime;
    @JsonProperty(Constant.ENDTIME)
    private Long endTime;
    private Integer status;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EnergyEvent event = (EnergyEvent) o;
        return Objects.equals(logicalId, event.logicalId) && Objects.equals(eventType, event.eventType) && Objects.equals(dataId, event.dataId)
                && Objects.equals(deviceId, event.deviceId) && Objects.equals(startTime, event.startTime) && Objects.equals(status, event.status);
    }

    @Override
    public int hashCode() {
        return Objects.hash(logicalId, eventType, dataId, deviceId, startTime, status);
    }
}

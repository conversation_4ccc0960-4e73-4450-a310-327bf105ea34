package com.cet.eem.energyevent.dao.impl;

import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import com.cet.eem.energyevent.dao.EnergyEventDao;
import com.cet.eem.energyevent.model.pojo.EnergyEvent;
import com.cet.eem.energyevent.model.vo.EnergyEventParam;
import com.cet.eem.energyevent.model.vo.EnergyEventStatus;
import com.cet.eem.energyevent.model.vo.ReferDataParam;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/7/24 9:07
 */
@Component
public class EnergyEventDaoImpl extends ModelDaoImpl<EnergyEvent> implements EnergyEventDao {

    @Override
    public List<EnergyEvent> query(EnergyEventParam param) {
        LambdaQueryWrapper<EnergyEvent> wrapper = LambdaQueryWrapper.of(EnergyEvent.class);
        wrapper.ge(EnergyEvent::getStartTime, param.getStartTime());
        wrapper.le(EnergyEvent::getStartTime, param.getEndTime());
        if (CollectionUtils.isNotEmpty(param.getNodeIds())) {
            wrapper.in(EnergyEvent::getDeviceId, param.getNodeIds());
        }
        if (Objects.nonNull(param.getEventType())) {
            wrapper.eq(EnergyEvent::getEventType, param.getEventType());
        }
        if (Objects.nonNull(param.getStatus())) {
            wrapper.eq(EnergyEvent::getStatus, param.getStatus());
        }
        wrapper.or(it->{
            it.ge(EnergyEvent::getEndTime, param.getStartTime());
            it.le(EnergyEvent::getEndTime, param.getEndTime());
            if (CollectionUtils.isNotEmpty(param.getNodeIds())) {
                it.in(EnergyEvent::getDeviceId, param.getNodeIds());
            }
            if (Objects.nonNull(param.getEventType())) {
                it.eq(EnergyEvent::getEventType, param.getEventType());
            }
            if (Objects.nonNull(param.getStatus())) {
                it.eq(EnergyEvent::getStatus, param.getStatus());
            }
        });
        return selectList(wrapper);
    }

    @Override
    public List<EnergyEvent> query(ReferDataParam param) {
        LambdaQueryWrapper<EnergyEvent> wrapper = LambdaQueryWrapper.of(EnergyEvent.class);
        wrapper.ge(EnergyEvent::getStartTime, param.getStartTime());
        wrapper.le(EnergyEvent::getEndTime, param.getEndTime());
        if (Objects.nonNull(param.getEventType())) {
            wrapper.eq(EnergyEvent::getEventType, param.getEventType());
        }
        if (Objects.nonNull(param.getStatus())) {
            wrapper.eq(EnergyEvent::getStatus, param.getStatus());
        }
        if (Objects.nonNull(param.getDataId())) {
            wrapper.eq(EnergyEvent::getDataId, param.getDataId());
        }
        if (Objects.nonNull(param.getDeviceId())) {
            wrapper.eq(EnergyEvent::getDeviceId, param.getDeviceId());
        }
        if (Objects.nonNull(param.getLogicalId())) {
            wrapper.eq(EnergyEvent::getLogicalId, param.getLogicalId());
        }
        return selectList(wrapper);
    }

    @Override
    public void edit(List<EnergyEvent> energyEvents) {
        if (CollectionUtils.isEmpty(energyEvents)) {
            return;
        }
        modelServiceUtils.writeData(energyEvents);
    }

    @Override
    public void editStatus(List<EnergyEventStatus> energyEvents) {
        if (CollectionUtils.isEmpty(energyEvents)) {
            return;
        }
        modelServiceUtils.writeData(energyEvents, EnergyEventStatus.class);
    }

}

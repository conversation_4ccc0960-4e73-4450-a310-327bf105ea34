package com.cet.eem.energyevent.model.enums;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2024/7/24 11:05
 */
public enum StatusEnum {
    PROCESSED(1, "自动处理"),
    UNTREATED(2, "手动处理"),
    UN_HANDLE(3, "未处理");
    private final Integer type;
    private final String desc;
    public final static List<Integer> noGroupStatus = Arrays.asList(StatusEnum.UN_HANDLE.type, StatusEnum.UNTREATED.type);

    public final static List<Integer> groupStatus = Collections.singletonList(StatusEnum.PROCESSED.type);

    StatusEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public static String getDesc(Integer type) {
        StatusEnum typeEnum = Stream.of(StatusEnum.values()).filter(item -> Objects.equals(item.type, type)).findFirst().orElse(null);
        if (Objects.isNull(typeEnum)) {
            return UNTREATED.desc;
        }
        return typeEnum.desc;
    }

}

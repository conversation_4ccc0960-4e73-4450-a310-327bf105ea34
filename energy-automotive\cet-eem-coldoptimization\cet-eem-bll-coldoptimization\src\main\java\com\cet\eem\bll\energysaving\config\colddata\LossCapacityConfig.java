package com.cet.eem.bll.energysaving.config.colddata;

import com.cet.eem.common.utils.TimeUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/8/19
 */
@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "cet.eem.task.energy-saving.loss")
@Slf4j
public class LossCapacityConfig {
    /**
     * 全局开始时间
     */
    private String overallStartTime;

    /**
     * 最大查询跨度天数
     */
    private Integer maxQueryDay;
    /**
     * 温度最大值，超出过滤
     */
    private Double tempLimit;
    /**
     * 解析全局开始时间
     *
     * @return 全局开始时间
     */
    public LocalDateTime parseOverallStartTime() {
        if (StringUtils.isBlank(overallStartTime)) {
            log.error("制冷系统管损配置全局开始时间为空！");
            return null;
        }
        log.info("制冷系统管损配置全局开始时间为[{}]", overallStartTime);
        return TimeUtil.parse(overallStartTime, TimeUtil.LONG_TIME_FORMAT);
    }


}

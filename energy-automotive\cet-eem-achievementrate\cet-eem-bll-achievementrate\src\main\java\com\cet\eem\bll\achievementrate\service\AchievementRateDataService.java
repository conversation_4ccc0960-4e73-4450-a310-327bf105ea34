package com.cet.eem.bll.achievementrate.service;

import com.cet.eem.bll.achievementrate.model.*;
import com.cet.eem.bll.common.model.domain.subject.energy.ProjectEnergyType;
import com.cet.eem.bll.common.model.domain.subject.energyefficiency.EnergyEfficiencySetVo;
import com.cet.eem.bll.common.model.ext.modelentity.EemQueryCondition;
import com.cet.eem.common.model.BaseVo;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @ClassName : AchievementRateDataService
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-11-22 13:48
 */
public interface AchievementRateDataService {
    /**
     * 查询
     *
     * @param queryVo
     * @return
     */
    List<AchievementRateDataNodeReturnVo> queryAchievementRateData(AchievementRateQueryVo queryVo);

    /**
     * web录入数据
     *
     * @param inputDataVo
     * @return
     */
    List<AchievementRateReturnVo> inputAchievementRateData(AchievementRateInputDataVo inputDataVo);

    /**
     * 导出数据
     *
     * @param queryVo
     */
    void exportAchievementRateData(AchievementRateQueryVo queryVo);

    /**
     * 导入数据
     *
     * @param multipartFile
     * @param dataEntryType
     */
    void importAchievementRateData(MultipartFile multipartFile, int dataEntryType);

    /**
     * 查询达成率公式
     * @return
     */
    String queryFormula();

    /**
     * 查询达成率对比数据
     * @param queryVo
     * @return
     */
    AchievementRateReturnDataWithUnit queryAchievementRateCompareData(AchievementRateQueryVo queryVo);

    /**
     * 导出达成率对比数据
     * @param queryVo
     */
    void exportAchievementRateCompareData(AchievementRateQueryVo queryVo);
    List<EnergyEfficiencySetVo> queryEffSetList(EffQueryVo effQueryVo);
    List<FeeTypesReturnVo> queryEnergyType( BaseVo baseVo);

    /**
     * 查询成本的节点树
     * @param queryCondition
     * @param userId
     * @param projectId
     * @return
     */
    List<BaseVo> queryNodeTree(EemQueryCondition queryCondition, Long userId,Long projectId);

    /**
     * 查询成本项全部相关的能源类型
     * @param projectId
     * @return
     */
    List<ProjectEnergyType> queryEnergyTypeByProjectId(Long projectId);

    /**
     * 查询指标的节点树
     * @param queryCondition
     * @param userId
     * @param projectId
     * @param effSetId
     * @return
     */
    List<BaseVo> queryNodeTree(EemQueryCondition queryCondition, Long userId,Long projectId,Long effSetId);
}

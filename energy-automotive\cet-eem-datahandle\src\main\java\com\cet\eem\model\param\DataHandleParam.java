package com.cet.eem.model.param;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/8/6 15:16
 */
@Getter
@Setter
@NoArgsConstructor
public class DataHandleParam {

    //翻转值
    private double turnOverEng;
    //每分钟最大差值
    private double maxDifEng;
    //定时记录进度 默认两位小数
    private int scale=2;

    public DataHandleParam(double turnOverEng, double maxDifEng) {
        this.turnOverEng = turnOverEng;
        this.maxDifEng = maxDifEng;
    }
}

package com.cet.eem.bll.achievementrate.task;

import com.cet.eem.auth.service.impl.CommonAuthService;
import com.cet.eem.bll.achievementrate.config.RecalculateAspect;
import com.cet.eem.bll.achievementrate.dao.AchievementRateDao;
import com.cet.eem.bll.achievementrate.dao.EnergyEfficiencyDataPlanDao;
import com.cet.eem.bll.achievementrate.dao.ObjectCostValuePlanDao;
import com.cet.eem.bll.achievementrate.dao.UnitObjectCostValuePlanDao;
import com.cet.eem.bll.achievementrate.model.*;
import com.cet.eem.bll.achievementrate.model.data.EnergyEfficiencyDataPlan;
import com.cet.eem.bll.achievementrate.model.data.ObjectCostValuePlan;
import com.cet.eem.bll.achievementrate.model.data.UnitObjectCostValuePlan;
import com.cet.eem.bll.achievementrate.model.def.AchievementRateType;
import com.cet.eem.bll.achievementrate.model.pojo.AchievementRate;
import com.cet.eem.bll.common.dao.product.ProductionDataDao;
import com.cet.eem.bll.common.dao.product.UnitCostDao;
import com.cet.eem.bll.common.model.domain.subject.energy.EnergyConsumption;
import com.cet.eem.bll.common.model.domain.subject.energy.EnergyConsumptionPlan;
import com.cet.eem.bll.common.model.domain.subject.energyefficiency.EnergyEfficiencyData;
import com.cet.eem.bll.common.model.domain.subject.production.ObjectCosValue;
import com.cet.eem.bll.common.model.domain.subject.production.UnitObjectCostValue;
import com.cet.eem.bll.common.util.TreeNodeUtils;
import com.cet.eem.bll.energy.dao.EnergyEfficiencyDataDao;
import com.cet.eem.bll.energy.dao.consumption.EnergyConsumptionDao;
import com.cet.eem.bll.energy.dao.consumption.EnergyConsumptionPlanDao;
import com.cet.eem.common.exception.ValidationException;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/3/15 9:23
 */
@Slf4j
@Component
@EnableScheduling
public class AttainmentRateTask {
    @Value("${cet.eem.achievement.type:2}")
    private Integer achievementType;
    @Value("${cet.eem.achievement.projectid:1}")
    private Long projectId;
    @Value("${cet.eem.achievement.initTime:2024-01-01}")
    private String initTime;
    @Value("${cet.eem.achievement.timeStep:1}")
    private Long timeStep;
    @Value("${cet.eem.achievement.partSize:50}")
    private Integer partSize;
    @Autowired
    EnergyConsumptionDao energyConsumptionDao;
    @Autowired
    EnergyConsumptionPlanDao energyConsumptionPlanDao;
    @Autowired
    AchievementRateDao achievementRateDao;
    @Autowired
    ObjectCostValuePlanDao objectCostValuePlanDao;
    @Autowired
    ProductionDataDao productionDataDao;
    @Autowired
    EnergyEfficiencyDataDao energyEfficiencyDataDao;
    @Autowired
    EnergyEfficiencyDataPlanDao energyEfficiencyDataPlanDao;
    @Autowired
    UnitObjectCostValuePlanDao unitObjectCostValuePlanDao;
    @Autowired
    UnitCostDao unitCostDao;
    @Autowired
    AchievementConfig config;
    @Autowired
    RecalculateAspect aspect;
    @Autowired
    CommonAuthService commonAuthService;

    @Scheduled(cron = "${cet.eem.achievement.interval:-}")
    public void execute() {
        log.info("[达成率数据转存]开始执行!开始时间为:{}", LocalDateTime.now());
        long timeMillis = System.currentTimeMillis();
        LocalDateTime end = TimeUtil.getFirstTimeOfDay(LocalDateTime.now());
        List<BaseVo> baseVos = commonAuthService.queryProjectList(Collections.singletonList(projectId));
        List<BaseVo> nodes = new ArrayList<>();
        TreeNodeUtils.flatMapNodes(baseVos, nodes);
        List<List<BaseVo>> nodeList = Lists.partition(nodes, partSize);
        //查询达成率数据.如果有 则表示已经执行过了.
        for (Integer type : AchievementRateType.TYPES) {
            LocalDateTime st = CollectionUtils.isNotEmpty(achievementRateDao.selectAchievementByType(type)) ? end.plusDays(-1) : TimeUtil.parse(initTime, TimeUtil.DATE_TIME_FORMAT);
            List<TimeVo> times = getTimes(st, end, timeStep, AggregationCycle.ONE_MONTH, new ArrayList<>());
            for (TimeVo time : times) {
                EnergyEfficiencyParam param = getEnergyEfficiencyParam(time.getSt(), time.getEnd());
                for (List<BaseVo> node : nodeList) {
                    param.setNodes(node);
                    commonExecute(param, type);
                }
            }
        }
        log.info("[达成率数据转存]执行结束!结束时间为:{},耗时为:{}毫秒", LocalDateTime.now(), System.currentTimeMillis() - timeMillis);
    }

    /**
     * 重算
     */
    @Async
    public void recalculate(RecalculateParam param) {
        try {
            log.info("重算执行开始!");
            aspect.before();
            chcekParam(param);
            List<List<BaseVo>> nodeList = Lists.partition(param.getNodes(), partSize);
            EnergyEfficiencyParam query = new EnergyEfficiencyParam();
            BeanUtils.copyProperties(param, query);
            //时间分批处理 按月处理
            List<TimeVo> times = getTimes(TimeUtil.timestamp2LocalDateTime(param.getStartTime()), TimeUtil.timestamp2LocalDateTime(param.getEndTime()),
                    timeStep, AggregationCycle.ONE_MONTH, new ArrayList<>());
            for (TimeVo time : times) {
                query.setSt(time.getSt());
                query.setEnd(time.getEnd());
                //节点分组处理
                for (List<BaseVo> nodes : nodeList) {
                    query.setNodes(nodes);
                    commonExecute(query, param.getType());
                }
            }
        } catch (Exception e) {
            log.error("重算执行失败!{}", e.getMessage());
        } finally {
            aspect.after();
            log.info("本次重算执行完成!");
        }
    }


    private List<TimeVo> getTimes(LocalDateTime time, LocalDateTime end, Long rang, Integer cycle, List<TimeVo> times) {
        if (time.isAfter(end)) {
            return times;
        }
        TimeVo timeVo = new TimeVo();
        LocalDateTime next;
        switch (cycle) {
            case AggregationCycle.ONE_MONTH:
                next = time.plusMonths(rang);
                break;
            case AggregationCycle.ONE_DAY:
                next = time.plusDays(rang);
                break;
            default:
                next = time.plusDays(rang);
                break;
        }
        if (next.isAfter(end)) {
            timeVo.setSt(time);
            timeVo.setEnd(end);
            times.add(timeVo);
            return times;
        }
        if (next.isEqual(end)) {
            timeVo.setSt(time);
            timeVo.setEnd(end);
            times.add(timeVo);
            return times;
        }
        if (next.isBefore(end)) {
            timeVo.setSt(time);
            timeVo.setEnd(next);
            times.add(timeVo);
            getTimes(next, end, rang, cycle, times);
            return times;
        }
        return times;
    }

    /**
     * 对参数进行校验
     *
     * @param param
     */
    private void chcekParam(RecalculateParam param) {
        List<BaseVo> nodes = param.getNodes();
        if (nodes.size() > config.getMaxNodeSize()) {
            throw new ValidationException("当前重算节点超过最大配置节点:" + config.getMaxNodeSize());
        }
    }

    /**
     * 定时任务和页面重算共同调用
     *
     * @param param
     */
    private void commonExecute(EnergyEfficiencyParam param, Integer type) {
        switch (type) {
            case AchievementRateType.TOTAL_ENERGY_CONSUMPTION_ACHIEVEMENT_RATE:
                //能耗达成率数据处理
                energyConservation(Constant.CYCLES, param);
                return;
            case AchievementRateType.TOTAL_COST_ACHIEVEMENT_RATE:
                //成本达成率
                costValueConservation(Constant.CYCLES, param);
                return;
            case AchievementRateType.ACHIEVEMENT_RATE_OF_ENERGY_CONSUMPTION_PER_UNIT:
                //单台能耗达成率
                energyEfficiencyConservation(Constant.CYCLES, param);
                return;
            case AchievementRateType.UNIT_COST_ACHIEVEMENT_RATIO:
                //单台目标成本
                unitobjectcostvalueConservation(Constant.CYCLES, param);
                return;
            default:
        }
    }

    /**
     * 单台成本达成率
     */
    public void unitobjectcostvalueConservation(List<Integer> cycles, EnergyEfficiencyParam query) {
        log.info("[达成率数据转存]单台成本达成率本次执行开始!时间为:{}", LocalDateTime.now());
        long timeMillis = System.currentTimeMillis();
        //保持统一的param不被更改
        EnergyEfficiencyParam param = new EnergyEfficiencyParam();
        BeanUtils.copyProperties(query, param);
        List<AchievementRate> rateList = cycles.stream().map(cycle -> {
            switch (cycle) {
                case AggregationCycle.ONE_DAY:
                    return unitobjectcostvalueConservation(getParam(param.getSt(), param.getEnd(), cycle, query.getNodes(), param));
                case AggregationCycle.ONE_MONTH:
                    LocalDateTime dayOfThisMonth = TimeUtil.getFirstDayOfThisMonth(param.getSt());
                    //如果是1号 计算上月的
                    if (dayOfThisMonth.isEqual(param.getSt())) {
                        dayOfThisMonth = dayOfThisMonth.plusMonths(-1);
                    }
                    LocalDateTime dayOfNextMonth = TimeUtil.getFirstDayOfNextMonth(param.getEnd());
                    return unitobjectcostvalueConservation(getParam(dayOfThisMonth, dayOfNextMonth, cycle, query.getNodes(), param));
                case AggregationCycle.ONE_YEAR:
                    LocalDateTime dayOfThisYear = TimeUtil.getFirstDayOfThisYear(param.getSt());
                    //如果是年开始 计算上年的
                    if (dayOfThisYear.isEqual(param.getSt())) {
                        dayOfThisYear = dayOfThisYear.plusYears(-1);
                    }
                    LocalDateTime dayOfNextYear = TimeUtil.getFirstDayOfNextYear(param.getEnd());
                    return unitobjectcostvalueConservation(getParam(dayOfThisYear, dayOfNextYear, cycle, query.getNodes(), param));
                default:
                    return null;
            }
        }).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        achievementRateDao.writeDatas(rateList);
        log.debug("当前更新单台达成率数据为:{}", JsonTransferUtils.toJSONString(rateList));
        log.info("[达成率数据转存]单台成本达成率本次执行结束!时间为:{}", LocalDateTime.now());
        log.info("[达成率数据转存]单台成本达成率转存总共耗时:{}毫秒,共更新数据:{}条", System.currentTimeMillis() - timeMillis, rateList.size());
    }

    /**
     * 不同周期下 的开始时间 ,结束时间 周期数据重新赋值
     *
     * @param st
     * @param end
     * @param cycle
     * @param param
     * @return
     */
    private EnergyEfficiencyParam getParam(LocalDateTime st, LocalDateTime end, Integer cycle, List<BaseVo> nodes, EnergyEfficiencyParam param) {
        param.setSt(st);
        param.setEnd(end);
        param.setCycle(cycle);
        param.setNodes(nodes);
        return param;
    }

    /**
     * 单台目标成本数据获取
     *
     * @param param
     * @return
     */
    private List<AchievementRate> unitobjectcostvalueConservation(EnergyEfficiencyParam param) {
        List<UnitObjectCostValuePlan> plans = unitObjectCostValuePlanDao.query(param.getSt(), param.getEnd(), param.getEnergType(),
                param.getNodes(), param.getCycle(), param.getProductType());
        if (CollectionUtils.isEmpty(plans)) {
            return Collections.emptyList();
        }
        List<BaseVo> baseVos = plans.stream().map(item -> new BaseVo(item.getObjectId(), item.getObjectLabel())).distinct().collect(Collectors.toList());
        param.setNodes(baseVos);
        LambdaQueryWrapper<UnitObjectCostValue> wrapper = achievementRateDao.getUnitEffWrapper(param);
        List<UnitObjectCostValue> datas = unitCostDao.selectList(wrapper);
        Map<Integer, List<UnitObjectCostValuePlan>> productTypePlans = plans.stream().filter(item -> Objects.nonNull(item.getProductType())).collect(Collectors.groupingBy(UnitObjectCostValuePlan::getProductType));
        Map<Integer, List<UnitObjectCostValue>> productTypeDatas = datas.stream().filter(item->StringUtils.isBlank(item.getDimTagIds())).filter(item -> Objects.nonNull(item.getProductType())).collect(Collectors.groupingBy(UnitObjectCostValue::getProductType));
        List<AchievementRate> dataList = new ArrayList<>();
        for (Map.Entry<Integer, List<UnitObjectCostValuePlan>> planMap : productTypePlans.entrySet()) {
            Integer productType = planMap.getKey();
            List<UnitObjectCostValue> costValues = productTypeDatas.get(planMap.getKey());
            if (CollectionUtils.isEmpty(costValues)) {
                continue;
            }
            //能源类型->节点->时间->数据
            Map<Integer, Map<BaseVo, Map<Long, Double>>> plan = planMap.getValue().stream().collect(Collectors.groupingBy(UnitObjectCostValuePlan::getEnergyType,
                    Collectors.groupingBy(item -> new BaseVo(item.getObjectId(), item.getObjectLabel()),
                            Collectors.toMap(UnitObjectCostValuePlan::getLogTime, item -> Objects.isNull(item.getValue()) ? 0D : item.getValue(), (v1, v2) -> v1))));
            //能源类型->节点->时间->数据
            Map<Integer, Map<BaseVo, Map<Long, Double>>> data = costValues.stream().filter(item -> Objects.nonNull(item.getValue()))
                    .collect(Collectors.groupingBy(UnitObjectCostValue::getEnergyType, Collectors.groupingBy(item -> new BaseVo(item.getObjectId(), item.getObjectLabel()),
                            Collectors.toMap(UnitObjectCostValue::getLogTime, item -> Objects.isNull(item.getValue()) ? 0D : item.getValue(), (v1, v2) -> v1))));
            List<AchievementRate> rates = commonConservation(plan, data, param.getCycle(), AchievementRateType.UNIT_COST_ACHIEVEMENT_RATIO);
            rates.forEach(rate -> rate.setProducttype(productType));
            dataList.addAll(rates);
        }
        return dataList;
    }


    /**
     * 单台能耗达成率
     */
    public void energyEfficiencyConservation(List<Integer> cycles, EnergyEfficiencyParam query) {
        log.info("[达成率数据转存]单台能耗达成率本次执行开始!时间为:{}", LocalDateTime.now());
        long timeMillis = System.currentTimeMillis();
        //保持统一的param不被更改
        EnergyEfficiencyParam param = new EnergyEfficiencyParam();
        BeanUtils.copyProperties(query, param);
        List<AchievementRate> rateList = cycles.stream().map(cycle -> {
            switch (cycle) {
                case AggregationCycle.ONE_DAY:
                    return energyEfficiencyConservationExcuet(getParam(param.getSt(), param.getEnd(), cycle, query.getNodes(), param));
                case AggregationCycle.ONE_MONTH:
                    LocalDateTime dayOfThisMonth = TimeUtil.getFirstDayOfThisMonth(param.getSt());
                    //如果是1号 计算上月的
                    if (dayOfThisMonth.isEqual(param.getSt())) {
                        dayOfThisMonth = dayOfThisMonth.plusMonths(-1);
                    }
                    LocalDateTime dayOfNextMonth = TimeUtil.getFirstDayOfNextMonth(param.getEnd());
                    return energyEfficiencyConservationExcuet(getParam(dayOfThisMonth, dayOfNextMonth, cycle, query.getNodes(), param));
                case AggregationCycle.ONE_YEAR:
                    LocalDateTime dayOfThisYear = TimeUtil.getFirstDayOfThisYear(param.getSt());
                    //如果是年开始 计算上年的
                    if (dayOfThisYear.isEqual(param.getSt())) {
                        dayOfThisYear = dayOfThisYear.plusYears(-1);
                    }
                    LocalDateTime dayOfNextYear = TimeUtil.getFirstDayOfNextYear(param.getEnd());
                    return energyEfficiencyConservationExcuet(getParam(dayOfThisYear, dayOfNextYear, cycle, query.getNodes(), param));
                default:
                    return null;
            }
        }).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        achievementRateDao.writeDatas(rateList);
        log.debug("当前更新单台能耗达成率数据为:{}", JsonTransferUtils.toJSONString(rateList));
        log.info("[达成率数据转存]单台能耗达成率本次执行结束!时间为:{}", LocalDateTime.now());
        log.info("[达成率数据转存]单台能耗达成率转存总共耗时:{}毫秒,共更新数据:{}条", System.currentTimeMillis() - timeMillis, rateList.size());

    }

    /**
     * 单台目标能耗处理
     */
    private List<AchievementRate> energyEfficiencyConservationExcuet(EnergyEfficiencyParam param) {
        List<EnergyEfficiencyDataPlan> effPlans = energyEfficiencyDataPlanDao.query(param);
        if (CollectionUtils.isEmpty(effPlans)) {
            return Collections.emptyList();
        }
        List<BaseVo> baseVos = effPlans.stream().map(item -> new BaseVo(item.getObjectId(), item.getObjectLabel())).distinct().collect(Collectors.toList());
        param.setNodes(baseVos);
        List<EnergyEfficiencyData> effDatas = energyEfficiencyDataDao.selectList(achievementRateDao.getEffWrapper(param));
        //指标ID->产品类型->数据
        Map<Long, Map<Integer, List<EnergyEfficiencyDataPlan>>> effIdProductTypePlans = effPlans.stream().filter(item -> Objects.nonNull(item.getEnergyEfficiencySetId())
                && Objects.nonNull(item.getProductType())).collect(Collectors.groupingBy(EnergyEfficiencyDataPlan::getEnergyEfficiencySetId, Collectors.groupingBy(EnergyEfficiencyDataPlan::getProductType)));
        //指标ID->产品类型->数据
        Map<Long, Map<Integer, List<EnergyEfficiencyData>>> effIdProductTypeDatas = effDatas.stream().filter(item -> Objects.nonNull(item.getEnergyEfficiencySetId())
                && Objects.nonNull(item.getProductType())).collect(Collectors.groupingBy(EnergyEfficiencyData::getEnergyEfficiencySetId, Collectors.groupingBy(EnergyEfficiencyData::getProductType)));
        return getEffAchievementRates(effIdProductTypePlans, effIdProductTypeDatas, param.getCycle());

    }

    /**
     * 单台能耗数据处理
     *
     * @return
     */
    private List<AchievementRate> getEffAchievementRates(Map<Long, Map<Integer, List<EnergyEfficiencyDataPlan>>> effIdProductTypePlans,
                                                         Map<Long, Map<Integer, List<EnergyEfficiencyData>>> effIdProductTypeDatas, Integer cycle) {
        List<AchievementRate> achievementRates = new ArrayList<>();
        for (Map.Entry<Long, Map<Integer, List<EnergyEfficiencyDataPlan>>> planMap : effIdProductTypePlans.entrySet()) {
            Long effId = planMap.getKey();
            Map<Integer, List<EnergyEfficiencyData>> dataMap = effIdProductTypeDatas.get(effId);
            if (Objects.isNull(dataMap)) {
                continue;
            }
            planMap.getValue().forEach((productType, plans) -> {
                List<EnergyEfficiencyData> datas = dataMap.get(productType);
                if (Objects.isNull(datas)) {
                    return;
                }
                //能源类别->节点->时间->值
                Map<Integer, Map<BaseVo, Map<Long, Double>>> plan = plans.stream()
                        .collect(Collectors.groupingBy(EnergyEfficiencyDataPlan::getEnergyType, Collectors.groupingBy(item -> new BaseVo(item.getObjectId(), item.getObjectLabel()),
                                Collectors.toMap(EnergyEfficiencyDataPlan::getLogTime, item -> Objects.isNull(item.getValue()) ? 0D : item.getValue(), (v1, v2) -> v1))));
                //能源类别->节点->时间->值
                Map<Integer, Map<BaseVo, Map<Long, Double>>> data = datas.stream()
                        .collect(Collectors.groupingBy(EnergyEfficiencyData::getEnergyType, Collectors.groupingBy(item -> new BaseVo(item.getObjectId(), item.getObjectLabel()),
                                Collectors.toMap(item -> TimeUtil.localDateTime2timestamp(item.getLogTime()), item -> Objects.isNull(item.getValue()) ? 0D : item.getValue(), (v1, v2) -> v1))));
                List<AchievementRate> rates = commonConservation(plan, data, cycle, AchievementRateType.ACHIEVEMENT_RATE_OF_ENERGY_CONSUMPTION_PER_UNIT);
                rates.forEach(rate -> {
                    rate.setProducttype(productType);
                    rate.setIndicatortype(effId);
                });
                achievementRates.addAll(rates);
            });
        }
        return achievementRates;
    }

    /**
     * 查询数据参数的封装
     *
     * @param st
     * @param end
     * @return
     */
    private EnergyEfficiencyParam getEnergyEfficiencyParam(LocalDateTime st, LocalDateTime end) {
        EnergyEfficiencyParam param = new EnergyEfficiencyParam();
        param.setSt(st);
        param.setEnd(end);
        return param;
    }


    /**
     * 成本数据处理
     */
    public void costValueConservation(List<Integer> cycles, EnergyEfficiencyParam param) {
        log.info("[达成率数据转存]成本达成率本次执行开始!时间为:{}", LocalDateTime.now());
        long timeMillis = System.currentTimeMillis();
        List<AchievementRate> rateList = cycles.stream().map(cycle -> {
            switch (cycle) {
                case AggregationCycle.ONE_DAY:
                    return getCostValueDatas(param.getSt(), param.getEnd(), cycle, param.getEnergType(), param.getNodes());
                case AggregationCycle.ONE_MONTH:
                    LocalDateTime dayOfThisMonth = TimeUtil.getFirstDayOfThisMonth(param.getSt());
                    //如果是1号 计算上月的
                    if (dayOfThisMonth.isEqual(param.getSt())) {
                        dayOfThisMonth = dayOfThisMonth.plusMonths(-1);
                    }
                    LocalDateTime dayOfNextMonth = TimeUtil.getFirstDayOfNextMonth(param.getEnd());
                    return getCostValueDatas(dayOfThisMonth, dayOfNextMonth, cycle, param.getEnergType(), param.getNodes());
                case AggregationCycle.ONE_YEAR:
                    LocalDateTime dayOfThisYear = TimeUtil.getFirstDayOfThisYear(param.getSt());
                    if (dayOfThisYear.isEqual(param.getSt())) {
                        dayOfThisYear = dayOfThisYear.plusYears(-1);
                    }
                    LocalDateTime dayOfNextYear = TimeUtil.getFirstDayOfNextYear(param.getEnd());
                    return getCostValueDatas(dayOfThisYear, dayOfNextYear, cycle, param.getEnergType(), param.getNodes());
                default:
                    return null;
            }
        }).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        achievementRateDao.writeDatas(rateList);
        log.debug("当前更新成本达成率数据为:{}", JsonTransferUtils.toJSONString(rateList));
        log.info("[达成率数据转存]成本达成率本次执行结束!时间为:{}", LocalDateTime.now());
        log.info("[达成率数据转存]成本达成率转存总共耗时:{}毫秒,共更新数据:{}条", System.currentTimeMillis() - timeMillis, rateList.size());

    }

    /**
     * 成本达成率数据封装
     *
     * @param st
     * @param end
     * @param cycle
     * @param energType
     * @param nodes
     * @return
     */
    private List<AchievementRate> getCostValueDatas(LocalDateTime st, LocalDateTime end, Integer cycle, Integer energType, List<BaseVo> nodes) {
        List<ObjectCostValuePlan> costValuePlans = objectCostValuePlanDao.query(st, end, cycle, energType, nodes);
        if (CollectionUtils.isEmpty(costValuePlans)) {
            return Collections.emptyList();
        }
        List<BaseVo> baseVos = costValuePlans.stream().map(item -> new BaseVo(item.getObjectId(), item.getObjectLabel())).distinct().collect(Collectors.toList());
        List<Integer> energys = costValuePlans.stream().map(ObjectCostValuePlan::getEnergyType).distinct().collect(Collectors.toList());
        List<ObjectCosValue> objectCosValues = achievementRateDao.queryObjectCostValues(st, end, cycle, energys, baseVos);
        objectCosValues = objectCosValues.stream().filter(item -> StringUtils.isBlank(item.getDimtagids())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(objectCosValues)) {
            return Collections.emptyList();
        }
        //能源类别->节点->时间->值
        Map<Integer, Map<BaseVo, Map<Long, Double>>> planMap = costValuePlans.stream()
                .collect(Collectors.groupingBy(ObjectCostValuePlan::getEnergyType, Collectors.groupingBy(item -> new BaseVo(item.getObjectId(),
                        item.getObjectLabel()), Collectors.toMap(ObjectCostValuePlan::getLogTime, item -> Objects.isNull(item.getValue()) ? 0D : item.getValue(), (v1, v2) -> v1))));
        //能源类别->节点->时间->值
        Map<Integer, Map<BaseVo, Map<Long, Double>>> dataMaps = objectCosValues.stream()
                .collect(Collectors.groupingBy(ObjectCosValue::getEnergytype, Collectors.groupingBy(item -> new BaseVo(item.getObjectid(),
                        item.getObjectlabel()), Collectors.toMap(ObjectCosValue::getLogtime, item -> Objects.isNull(item.getValue()) ? 0D : item.getValue(), (v1, v2) -> v1))));
        return commonConservation(planMap, dataMaps, cycle, AchievementRateType.TOTAL_COST_ACHIEVEMENT_RATE);

    }


    /**
     * 能耗达成率数据处理
     *
     * @param cycles
     */
    public void energyConservation(List<Integer> cycles, EnergyEfficiencyParam param) {
        long timeMillis = System.currentTimeMillis();
        log.info("[达成率数据转存]能耗达成率本次执行开始!时间为:{}", LocalDateTime.now());
        List<AchievementRate> rateList = cycles.stream().map(cycle -> {
            switch (cycle) {
                case AggregationCycle.ONE_DAY:
                    return getDatas(param.getSt(), param.getEnd(), cycle, param.getEnergType(), param.getNodes());
                case AggregationCycle.ONE_MONTH:
                    LocalDateTime dayOfThisMonth = TimeUtil.getFirstDayOfThisMonth(param.getSt());
                    if (dayOfThisMonth.isEqual(param.getSt())) {
                        dayOfThisMonth = dayOfThisMonth.plusMonths(-1);
                    }
                    LocalDateTime dayOfNextMonth = TimeUtil.getFirstDayOfNextMonth(param.getEnd());
                    return getDatas(dayOfThisMonth, dayOfNextMonth, cycle, param.getEnergType(), param.getNodes());
                case AggregationCycle.ONE_YEAR:
                    LocalDateTime dayOfThisYear = TimeUtil.getFirstDayOfThisYear(param.getSt());
                    if (dayOfThisYear.isEqual(param.getSt())) {
                        dayOfThisYear = dayOfThisYear.plusYears(-1);
                    }
                    LocalDateTime dayOfNextYear = TimeUtil.getFirstDayOfNextYear(param.getEnd());
                    return getDatas(dayOfThisYear, dayOfNextYear, cycle, param.getEnergType(), param.getNodes());
                default:
                    return null;
            }
        }).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        achievementRateDao.writeDatas(rateList);
        log.debug("当前更新能耗达成率数据为:{}", JsonTransferUtils.toJSONString(rateList));
        log.info("[达成率数据转存]能耗达成率本次执行结束!时间为:{}", LocalDateTime.now());
        log.info("[达成率数据转存]能耗达成率转存总共耗时:{}毫秒,共更新数据:{}条", System.currentTimeMillis() - timeMillis, rateList.size());
    }

    /**
     * 通用的处理方法
     *
     * @param planMap
     * @param dataMap
     */
    private List<AchievementRate> commonConservation(Map<Integer, Map<BaseVo, Map<Long, Double>>> planMap, Map<Integer, Map<BaseVo, Map<Long, Double>>> dataMap, Integer cycle, Integer type) {
        List<AchievementRate> datas = new ArrayList<>();
        planMap.forEach((energy, planValue) -> {
            //节点,时间,值
            Map<BaseVo, Map<Long, Double>> dataValue = dataMap.get(energy);
            if (Objects.isNull(planValue) || Objects.isNull(dataValue)) {
                return;
            }
            planValue.forEach(((baseVo, planTimeData) -> {
                Map<Long, Double> timeData = dataValue.get(baseVo);
                if (Objects.isNull(timeData)) {
                    return;
                }
                List<AchievementRate> rateList = planTimeData.entrySet().stream().map(map -> {
                    Double value = timeData.get(map.getKey());
                    Double planVal = map.getValue();
                    if (Objects.isNull(value) && Objects.isNull(planVal)) {
                        return null;
                    }
                    return getAchievementRate(baseVo, energy, cycle, getRate(value, planVal), map.getKey(), type);
                }).filter(Objects::nonNull).collect(Collectors.toList());
                datas.addAll(rateList);
            }));
        });
        return datas;
    }

    /**
     * 获取日度能耗达成率数据
     *
     * @param st
     * @param end
     * @return
     */
    private List<AchievementRate> getDatas(LocalDateTime st, LocalDateTime end, Integer cycle, Integer energyType, List<BaseVo> nodes) {
        List<EnergyConsumptionPlan> planList = energyConsumptionPlanDao.selectList(achievementRateDao.getPlanWrapper(st, end, cycle, nodes, energyType));
        if (CollectionUtils.isEmpty(planList)) {
            return Collections.emptyList();
        }
        List<Integer> energys = planList.stream().map(EnergyConsumptionPlan::getEnergyType).distinct().collect(Collectors.toList());
        List<BaseVo> nodevos = planList.stream().map(item -> new BaseVo(item.getObjectId(), item.getObjectLabel())).distinct().collect(Collectors.toList());
        List<EnergyConsumption> consumptionList = energyConsumptionDao.queryEnergyConsumption(nodevos, st, end, cycle, energys);
        if (CollectionUtils.isEmpty(consumptionList)) {
            return Collections.emptyList();
        }
        //能源类别->节点->时间->值
        Map<Integer, Map<BaseVo, Map<Long, Double>>> planMap = planList.stream()
                .collect(Collectors.groupingBy(EnergyConsumptionPlan::getEnergyType, Collectors.groupingBy(item -> new BaseVo(item.getObjectId(),
                        item.getObjectLabel()), Collectors.toMap(EnergyConsumptionPlan::getLogtime, item -> Objects.isNull(item.getValue()) ? 0D : item.getValue(), (v1, v2) -> v1))));
        //能源类别->节点->时间->值
        Map<Integer, Map<BaseVo, Map<Long, Double>>> dataMap = consumptionList.stream()
                .collect(Collectors.groupingBy(EnergyConsumption::getEnergytype, Collectors.groupingBy(item -> new BaseVo(item.getObjectid(),
                        item.getObjectlabel()), Collectors.toMap(EnergyConsumption::getLogtime, item -> Objects.isNull(item.getUsage()) ? 0D : item.getUsage(), (v1, v2) -> v1))));
        return commonConservation(planMap, dataMap, cycle, AchievementRateType.TOTAL_ENERGY_CONSUMPTION_ACHIEVEMENT_RATE);
    }


    /**
     * 数据封装
     *
     * @param node
     * @param energy
     * @param cycle
     * @param rateVal
     * @param time
     * @param type
     * @return
     */
    private AchievementRate getAchievementRate(BaseVo node, Integer energy, Integer cycle, Double rateVal, Long time, Integer type) {
        AchievementRate rate = new AchievementRate();
        rate.setEnergytype(energy);
        rate.setLogtime(time);
        rate.setObjectid(node.getId());
        rate.setProducttype(Constant.ZERO_INT);
        rate.setIndicatortype(Constant.ZERO_LONG);
        rate.setObjectlabel(node.getModelLabel());
        rate.setAggregationcycle(cycle);
        rate.setProjectid(projectId);
        rate.setUpdatetime(System.currentTimeMillis());
        rate.setRate(rateVal);
        rate.setType(type);
        return rate;
    }

    /**
     * @param realValue
     * @param planValue
     * @return
     */
    private Double getRate(Double realValue, Double planValue) {
        if (Objects.isNull(realValue) || Objects.isNull(planValue) || Objects.equals(realValue, Constant.ZERO_DOUBLE) || Objects.equals(planValue, Constant.ZERO_DOUBLE)) {
            return null;
        }
        if (Objects.equals(achievementType, Constant.ONE)) {
            //达成率=(2-实际值/计划值)*100%
            return Constant.TWO - realValue / planValue;
        }
        //实际值/计划值
        return realValue / planValue;
    }


}

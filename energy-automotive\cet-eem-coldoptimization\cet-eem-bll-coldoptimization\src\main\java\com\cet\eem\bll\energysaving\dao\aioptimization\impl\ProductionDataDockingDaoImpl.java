package com.cet.eem.bll.energysaving.dao.aioptimization.impl;

import com.cet.eem.bll.energysaving.dao.aioptimization.ProductionDataDockingDao;
import com.cet.eem.bll.energysaving.model.dataentryquery.ProductionDataDocking;
import com.cet.eem.bll.energysaving.model.def.ColdOptimizationLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.dao.ModelDaoImpl;
import com.cet.eem.model.model.BaseEntity;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName : ProductionDataDockingDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-09-15 09:55
 */
@Repository
public class ProductionDataDockingDaoImpl extends ModelDaoImpl<ProductionDataDocking> implements ProductionDataDockingDao {
    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Override
    public List<ProductionDataDocking> queryByRoomId(List<BaseVo> nodes,Long st,Long et) {
        if (CollectionUtils.isEmpty(nodes)) {
            return Collections.emptyList();
        }
        Map<String, List<BaseVo>> nodeMap = nodes.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        int group = 1;

        QueryConditionBuilder<BaseEntity> builder = new QueryConditionBuilder<>(ColdOptimizationLabelDef.PRODUCTION_DATA_DOCKING)
                .composeMethod(true);
        for (Map.Entry<String, List<BaseVo>> entry : nodeMap.entrySet()) {
            String label = entry.getKey();
            List<Long> ids = nodeMap.get(label).stream().map(BaseVo::getId).distinct().collect(Collectors.toList());
            builder.eq(ColumnDef.C_OBJECT_Label, label, group);
            builder.in(ColumnDef.C_OBJECT_ID, ids, group);
            builder.ge(ColumnDef.LOG_TIME, st, group);
            builder.lt(ColumnDef.LOG_TIME, et, group);
            group++;
        }

        return modelServiceUtils.query(builder.build(), ProductionDataDocking.class);

    }
}